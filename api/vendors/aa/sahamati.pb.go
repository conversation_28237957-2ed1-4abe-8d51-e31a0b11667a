// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendors/aa/sahamati.proto

package aa

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GenerateAccessTokenResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccessToken      string `protobuf:"bytes,1,opt,name=access_token,proto3" json:"access_token,omitempty"`
	ExpiresIn        int64  `protobuf:"varint,2,opt,name=expires_in,proto3" json:"expires_in,omitempty"`
	RefreshExpiresIn int64  `protobuf:"varint,3,opt,name=refresh_expires_in,proto3" json:"refresh_expires_in,omitempty"`
	IdToken          string `protobuf:"bytes,4,opt,name=id_token,proto3" json:"id_token,omitempty"`
	NotBeforePolicy  int64  `protobuf:"varint,5,opt,name=not_before_policy,json=not-before-policy,proto3" json:"not_before_policy,omitempty"`
	Scope            string `protobuf:"bytes,6,opt,name=scope,proto3" json:"scope,omitempty"`
	Error            string `protobuf:"bytes,7,opt,name=error,proto3" json:"error,omitempty"`
	ErrorDescription string `protobuf:"bytes,8,opt,name=error_description,proto3" json:"error_description,omitempty"`
	AccessTokenV2    string `protobuf:"bytes,9,opt,name=access_token_v2,json=accessToken,proto3" json:"access_token_v2,omitempty"`
	ExpiresInV2      int64  `protobuf:"varint,10,opt,name=expires_in_v2,json=expiresIn,proto3" json:"expires_in_v2,omitempty"`
}

func (x *GenerateAccessTokenResponse) Reset() {
	*x = GenerateAccessTokenResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_aa_sahamati_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateAccessTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateAccessTokenResponse) ProtoMessage() {}

func (x *GenerateAccessTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_aa_sahamati_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateAccessTokenResponse.ProtoReflect.Descriptor instead.
func (*GenerateAccessTokenResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_aa_sahamati_proto_rawDescGZIP(), []int{0}
}

func (x *GenerateAccessTokenResponse) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *GenerateAccessTokenResponse) GetExpiresIn() int64 {
	if x != nil {
		return x.ExpiresIn
	}
	return 0
}

func (x *GenerateAccessTokenResponse) GetRefreshExpiresIn() int64 {
	if x != nil {
		return x.RefreshExpiresIn
	}
	return 0
}

func (x *GenerateAccessTokenResponse) GetIdToken() string {
	if x != nil {
		return x.IdToken
	}
	return ""
}

func (x *GenerateAccessTokenResponse) GetNotBeforePolicy() int64 {
	if x != nil {
		return x.NotBeforePolicy
	}
	return 0
}

func (x *GenerateAccessTokenResponse) GetScope() string {
	if x != nil {
		return x.Scope
	}
	return ""
}

func (x *GenerateAccessTokenResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

func (x *GenerateAccessTokenResponse) GetErrorDescription() string {
	if x != nil {
		return x.ErrorDescription
	}
	return ""
}

func (x *GenerateAccessTokenResponse) GetAccessTokenV2() string {
	if x != nil {
		return x.AccessTokenV2
	}
	return ""
}

func (x *GenerateAccessTokenResponse) GetExpiresInV2() int64 {
	if x != nil {
		return x.ExpiresInV2
	}
	return 0
}

type FetchCrEntityDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IdentityToken string `protobuf:"bytes,1,opt,name=identity_token,json=identitytoken,proto3" json:"identity_token,omitempty"`
}

func (x *FetchCrEntityDetailsRequest) Reset() {
	*x = FetchCrEntityDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_aa_sahamati_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchCrEntityDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchCrEntityDetailsRequest) ProtoMessage() {}

func (x *FetchCrEntityDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_aa_sahamati_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchCrEntityDetailsRequest.ProtoReflect.Descriptor instead.
func (*FetchCrEntityDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_aa_sahamati_proto_rawDescGZIP(), []int{1}
}

func (x *FetchCrEntityDetailsRequest) GetIdentityToken() string {
	if x != nil {
		return x.IdentityToken
	}
	return ""
}

type FetchCrEntityDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ver        string      `protobuf:"bytes,1,opt,name=ver,proto3" json:"ver,omitempty"`
	Timestamp  string      `protobuf:"bytes,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	TxnId      string      `protobuf:"bytes,3,opt,name=txn_id,json=txnid,proto3" json:"txn_id,omitempty"`
	Requester  *Requester  `protobuf:"bytes,4,opt,name=requester,proto3" json:"requester,omitempty"`
	EntityInfo *EntityInfo `protobuf:"bytes,5,opt,name=entity_info,json=entityinfo,proto3" json:"entity_info,omitempty"`
	Error      string      `protobuf:"bytes,6,opt,name=error,json=status,proto3" json:"error,omitempty"`
	Message    string      `protobuf:"bytes,7,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *FetchCrEntityDetailsResponse) Reset() {
	*x = FetchCrEntityDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_aa_sahamati_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchCrEntityDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchCrEntityDetailsResponse) ProtoMessage() {}

func (x *FetchCrEntityDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_aa_sahamati_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchCrEntityDetailsResponse.ProtoReflect.Descriptor instead.
func (*FetchCrEntityDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_aa_sahamati_proto_rawDescGZIP(), []int{2}
}

func (x *FetchCrEntityDetailsResponse) GetVer() string {
	if x != nil {
		return x.Ver
	}
	return ""
}

func (x *FetchCrEntityDetailsResponse) GetTimestamp() string {
	if x != nil {
		return x.Timestamp
	}
	return ""
}

func (x *FetchCrEntityDetailsResponse) GetTxnId() string {
	if x != nil {
		return x.TxnId
	}
	return ""
}

func (x *FetchCrEntityDetailsResponse) GetRequester() *Requester {
	if x != nil {
		return x.Requester
	}
	return nil
}

func (x *FetchCrEntityDetailsResponse) GetEntityInfo() *EntityInfo {
	if x != nil {
		return x.EntityInfo
	}
	return nil
}

func (x *FetchCrEntityDetailsResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

func (x *FetchCrEntityDetailsResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type Requester struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Id   string `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *Requester) Reset() {
	*x = Requester{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_aa_sahamati_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Requester) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Requester) ProtoMessage() {}

func (x *Requester) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_aa_sahamati_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Requester.ProtoReflect.Descriptor instead.
func (*Requester) Descriptor() ([]byte, []int) {
	return file_api_vendors_aa_sahamati_proto_rawDescGZIP(), []int{3}
}

func (x *Requester) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Requester) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type EntityInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name          string        `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Id            string        `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	Code          string        `protobuf:"bytes,3,opt,name=code,proto3" json:"code,omitempty"`
	EntityHandle  string        `protobuf:"bytes,4,opt,name=entity_handle,json=entityhandle,proto3" json:"entity_handle,omitempty"`
	Identifiers   []*Identifier `protobuf:"bytes,5,rep,name=identifiers,json=Identifiers,proto3" json:"identifiers,omitempty"`
	BaseUrl       string        `protobuf:"bytes,6,opt,name=base_url,json=baseurl,proto3" json:"base_url,omitempty"`
	WebviewUrl    string        `protobuf:"bytes,7,opt,name=webview_url,json=webviewurl,proto3" json:"webview_url,omitempty"`
	FiTypes       []string      `protobuf:"bytes,8,rep,name=fi_types,json=fitypes,proto3" json:"fi_types,omitempty"`
	Certificate   *Certificate  `protobuf:"bytes,9,opt,name=certificate,proto3" json:"certificate,omitempty"`
	TokenInfo     *TokenInfo    `protobuf:"bytes,10,opt,name=token_info,json=tokeninfo,proto3" json:"token_info,omitempty"`
	Gsp           string        `protobuf:"bytes,11,opt,name=gsp,proto3" json:"gsp,omitempty"`
	Signature     *Signature    `protobuf:"bytes,12,opt,name=signature,proto3" json:"signature,omitempty"`
	InboundPorts  []string      `protobuf:"bytes,13,rep,name=inbound_ports,json=inboundports,proto3" json:"inbound_ports,omitempty"`
	OutboundPorts []string      `protobuf:"bytes,14,rep,name=outbound_ports,json=outboundports,proto3" json:"outbound_ports,omitempty"`
	Ips           []string      `protobuf:"bytes,15,rep,name=ips,proto3" json:"ips,omitempty"`
	CredentialsPk string        `protobuf:"bytes,16,opt,name=credentialsPk,proto3" json:"credentialsPk,omitempty"`
}

func (x *EntityInfo) Reset() {
	*x = EntityInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_aa_sahamati_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EntityInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntityInfo) ProtoMessage() {}

func (x *EntityInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_aa_sahamati_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntityInfo.ProtoReflect.Descriptor instead.
func (*EntityInfo) Descriptor() ([]byte, []int) {
	return file_api_vendors_aa_sahamati_proto_rawDescGZIP(), []int{4}
}

func (x *EntityInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *EntityInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *EntityInfo) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *EntityInfo) GetEntityHandle() string {
	if x != nil {
		return x.EntityHandle
	}
	return ""
}

func (x *EntityInfo) GetIdentifiers() []*Identifier {
	if x != nil {
		return x.Identifiers
	}
	return nil
}

func (x *EntityInfo) GetBaseUrl() string {
	if x != nil {
		return x.BaseUrl
	}
	return ""
}

func (x *EntityInfo) GetWebviewUrl() string {
	if x != nil {
		return x.WebviewUrl
	}
	return ""
}

func (x *EntityInfo) GetFiTypes() []string {
	if x != nil {
		return x.FiTypes
	}
	return nil
}

func (x *EntityInfo) GetCertificate() *Certificate {
	if x != nil {
		return x.Certificate
	}
	return nil
}

func (x *EntityInfo) GetTokenInfo() *TokenInfo {
	if x != nil {
		return x.TokenInfo
	}
	return nil
}

func (x *EntityInfo) GetGsp() string {
	if x != nil {
		return x.Gsp
	}
	return ""
}

func (x *EntityInfo) GetSignature() *Signature {
	if x != nil {
		return x.Signature
	}
	return nil
}

func (x *EntityInfo) GetInboundPorts() []string {
	if x != nil {
		return x.InboundPorts
	}
	return nil
}

func (x *EntityInfo) GetOutboundPorts() []string {
	if x != nil {
		return x.OutboundPorts
	}
	return nil
}

func (x *EntityInfo) GetIps() []string {
	if x != nil {
		return x.Ips
	}
	return nil
}

func (x *EntityInfo) GetCredentialsPk() string {
	if x != nil {
		return x.CredentialsPk
	}
	return ""
}

type Identifier struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Category string `protobuf:"bytes,1,opt,name=category,proto3" json:"category,omitempty"`
	Type     string `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *Identifier) Reset() {
	*x = Identifier{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_aa_sahamati_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Identifier) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Identifier) ProtoMessage() {}

func (x *Identifier) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_aa_sahamati_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Identifier.ProtoReflect.Descriptor instead.
func (*Identifier) Descriptor() ([]byte, []int) {
	return file_api_vendors_aa_sahamati_proto_rawDescGZIP(), []int{5}
}

func (x *Identifier) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *Identifier) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

type Certificate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Alg string `protobuf:"bytes,1,opt,name=alg,proto3" json:"alg,omitempty"`
	E   string `protobuf:"bytes,2,opt,name=e,proto3" json:"e,omitempty"`
	Kid string `protobuf:"bytes,3,opt,name=kid,proto3" json:"kid,omitempty"`
	Kty string `protobuf:"bytes,4,opt,name=kty,proto3" json:"kty,omitempty"`
	N   string `protobuf:"bytes,5,opt,name=n,proto3" json:"n,omitempty"`
	Use string `protobuf:"bytes,6,opt,name=use,proto3" json:"use,omitempty"`
}

func (x *Certificate) Reset() {
	*x = Certificate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_aa_sahamati_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Certificate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Certificate) ProtoMessage() {}

func (x *Certificate) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_aa_sahamati_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Certificate.ProtoReflect.Descriptor instead.
func (*Certificate) Descriptor() ([]byte, []int) {
	return file_api_vendors_aa_sahamati_proto_rawDescGZIP(), []int{6}
}

func (x *Certificate) GetAlg() string {
	if x != nil {
		return x.Alg
	}
	return ""
}

func (x *Certificate) GetE() string {
	if x != nil {
		return x.E
	}
	return ""
}

func (x *Certificate) GetKid() string {
	if x != nil {
		return x.Kid
	}
	return ""
}

func (x *Certificate) GetKty() string {
	if x != nil {
		return x.Kty
	}
	return ""
}

func (x *Certificate) GetN() string {
	if x != nil {
		return x.N
	}
	return ""
}

func (x *Certificate) GetUse() string {
	if x != nil {
		return x.Use
	}
	return ""
}

type TokenInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url      string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Maxcalls int64  `protobuf:"varint,2,opt,name=maxcalls,proto3" json:"maxcalls,omitempty"`
	Desc     string `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
}

func (x *TokenInfo) Reset() {
	*x = TokenInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_aa_sahamati_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TokenInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenInfo) ProtoMessage() {}

func (x *TokenInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_aa_sahamati_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenInfo.ProtoReflect.Descriptor instead.
func (*TokenInfo) Descriptor() ([]byte, []int) {
	return file_api_vendors_aa_sahamati_proto_rawDescGZIP(), []int{7}
}

func (x *TokenInfo) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *TokenInfo) GetMaxcalls() int64 {
	if x != nil {
		return x.Maxcalls
	}
	return 0
}

func (x *TokenInfo) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

type Signature struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SignValue string `protobuf:"bytes,1,opt,name=sign_value,json=signValue,proto3" json:"sign_value,omitempty"`
}

func (x *Signature) Reset() {
	*x = Signature{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_aa_sahamati_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Signature) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Signature) ProtoMessage() {}

func (x *Signature) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_aa_sahamati_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Signature.ProtoReflect.Descriptor instead.
func (*Signature) Descriptor() ([]byte, []int) {
	return file_api_vendors_aa_sahamati_proto_rawDescGZIP(), []int{8}
}

func (x *Signature) GetSignValue() string {
	if x != nil {
		return x.SignValue
	}
	return ""
}

var File_api_vendors_aa_sahamati_proto protoreflect.FileDescriptor

var file_api_vendors_aa_sahamati_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x61, 0x61,
	0x2f, 0x73, 0x61, 0x68, 0x61, 0x6d, 0x61, 0x74, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x0a, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x61, 0x61, 0x22, 0xfd, 0x02, 0x0a, 0x1b,
	0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x61,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12,
	0x1e, 0x0a, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x5f, 0x69, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x5f, 0x69, 0x6e, 0x12,
	0x2e, 0x0a, 0x12, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72,
	0x65, 0x73, 0x5f, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x72, 0x65, 0x66,
	0x72, 0x65, 0x73, 0x68, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x5f, 0x69, 0x6e, 0x12,
	0x1a, 0x0a, 0x08, 0x69, 0x64, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x69, 0x64, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x2c, 0x0a, 0x11, 0x6e,
	0x6f, 0x74, 0x5f, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x6e, 0x6f, 0x74, 0x2d, 0x62, 0x65, 0x66, 0x6f,
	0x72, 0x65, 0x2d, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f,
	0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x2c, 0x0a, 0x11, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x11, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x5f, 0x76, 0x32, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x20, 0x0a, 0x0d, 0x65, 0x78, 0x70,
	0x69, 0x72, 0x65, 0x73, 0x5f, 0x69, 0x6e, 0x5f, 0x76, 0x32, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x49, 0x6e, 0x22, 0x44, 0x0a, 0x1b, 0x46,
	0x65, 0x74, 0x63, 0x68, 0x43, 0x72, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x69, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x22, 0x84, 0x02, 0x0a, 0x1c, 0x46, 0x65, 0x74, 0x63, 0x68, 0x43, 0x72, 0x45, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x76, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x74, 0x78, 0x6e, 0x69, 0x64, 0x12, 0x33, 0x0a, 0x09, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x61, 0x61, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x65, 0x72, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x72, 0x12, 0x37,
	0x0a, 0x0b, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x61, 0x61,
	0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x15, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18,
	0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x2f, 0x0a, 0x09, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0xb6, 0x04, 0x0a, 0x0a, 0x45, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x68,
	0x61, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x38, 0x0a, 0x0b, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x73, 0x2e, 0x61, 0x61, 0x2e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69,
	0x65, 0x72, 0x52, 0x0b, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x12,
	0x19, 0x0a, 0x08, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x62, 0x61, 0x73, 0x65, 0x75, 0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x65,
	0x62, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x77, 0x65, 0x62, 0x76, 0x69, 0x65, 0x77, 0x75, 0x72, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x66,
	0x69, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x66,
	0x69, 0x74, 0x79, 0x70, 0x65, 0x73, 0x12, 0x39, 0x0a, 0x0b, 0x63, 0x65, 0x72, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x61, 0x61, 0x2e, 0x43, 0x65, 0x72, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x63, 0x65, 0x72, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x65, 0x12, 0x34, 0x0a, 0x0a, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e,
	0x61, 0x61, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x67, 0x73, 0x70, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x67, 0x73, 0x70, 0x12, 0x33, 0x0a, 0x09, 0x73, 0x69, 0x67,
	0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x61, 0x61, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x23,
	0x0a, 0x0d, 0x69, 0x6e, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18,
	0x0d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x6e, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x70, 0x6f,
	0x72, 0x74, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x6f, 0x75, 0x74, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x5f,
	0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x6f, 0x75, 0x74,
	0x62, 0x6f, 0x75, 0x6e, 0x64, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x70,
	0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x70, 0x73, 0x12, 0x24, 0x0a, 0x0d,
	0x63, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x50, 0x6b, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73,
	0x50, 0x6b, 0x22, 0x3c, 0x0a, 0x0a, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72,
	0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x22, 0x71, 0x0a, 0x0b, 0x43, 0x65, 0x72, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x61, 0x6c, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x61, 0x6c,
	0x67, 0x12, 0x0c, 0x0a, 0x01, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x01, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x69,
	0x64, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x74, 0x79, 0x12, 0x0c, 0x0a, 0x01, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x01,
	0x6e, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x73, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x75, 0x73, 0x65, 0x22, 0x4d, 0x0a, 0x09, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75,
	0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x61, 0x78, 0x63, 0x61, 0x6c, 0x6c, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6d, 0x61, 0x78, 0x63, 0x61, 0x6c, 0x6c, 0x73, 0x12, 0x12,
	0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65,
	0x73, 0x63, 0x22, 0x2a, 0x0a, 0x09, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x4e,
	0x0a, 0x25, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x73, 0x2e, 0x61, 0x61, 0x5a, 0x25, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x61, 0x61, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendors_aa_sahamati_proto_rawDescOnce sync.Once
	file_api_vendors_aa_sahamati_proto_rawDescData = file_api_vendors_aa_sahamati_proto_rawDesc
)

func file_api_vendors_aa_sahamati_proto_rawDescGZIP() []byte {
	file_api_vendors_aa_sahamati_proto_rawDescOnce.Do(func() {
		file_api_vendors_aa_sahamati_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendors_aa_sahamati_proto_rawDescData)
	})
	return file_api_vendors_aa_sahamati_proto_rawDescData
}

var file_api_vendors_aa_sahamati_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_api_vendors_aa_sahamati_proto_goTypes = []interface{}{
	(*GenerateAccessTokenResponse)(nil),  // 0: vendors.aa.GenerateAccessTokenResponse
	(*FetchCrEntityDetailsRequest)(nil),  // 1: vendors.aa.FetchCrEntityDetailsRequest
	(*FetchCrEntityDetailsResponse)(nil), // 2: vendors.aa.FetchCrEntityDetailsResponse
	(*Requester)(nil),                    // 3: vendors.aa.Requester
	(*EntityInfo)(nil),                   // 4: vendors.aa.EntityInfo
	(*Identifier)(nil),                   // 5: vendors.aa.Identifier
	(*Certificate)(nil),                  // 6: vendors.aa.Certificate
	(*TokenInfo)(nil),                    // 7: vendors.aa.TokenInfo
	(*Signature)(nil),                    // 8: vendors.aa.Signature
}
var file_api_vendors_aa_sahamati_proto_depIdxs = []int32{
	3, // 0: vendors.aa.FetchCrEntityDetailsResponse.requester:type_name -> vendors.aa.Requester
	4, // 1: vendors.aa.FetchCrEntityDetailsResponse.entity_info:type_name -> vendors.aa.EntityInfo
	5, // 2: vendors.aa.EntityInfo.identifiers:type_name -> vendors.aa.Identifier
	6, // 3: vendors.aa.EntityInfo.certificate:type_name -> vendors.aa.Certificate
	7, // 4: vendors.aa.EntityInfo.token_info:type_name -> vendors.aa.TokenInfo
	8, // 5: vendors.aa.EntityInfo.signature:type_name -> vendors.aa.Signature
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_api_vendors_aa_sahamati_proto_init() }
func file_api_vendors_aa_sahamati_proto_init() {
	if File_api_vendors_aa_sahamati_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_vendors_aa_sahamati_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateAccessTokenResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_aa_sahamati_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchCrEntityDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_aa_sahamati_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchCrEntityDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_aa_sahamati_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Requester); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_aa_sahamati_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EntityInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_aa_sahamati_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Identifier); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_aa_sahamati_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Certificate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_aa_sahamati_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TokenInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_aa_sahamati_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Signature); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendors_aa_sahamati_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendors_aa_sahamati_proto_goTypes,
		DependencyIndexes: file_api_vendors_aa_sahamati_proto_depIdxs,
		MessageInfos:      file_api_vendors_aa_sahamati_proto_msgTypes,
	}.Build()
	File_api_vendors_aa_sahamati_proto = out.File
	file_api_vendors_aa_sahamati_proto_rawDesc = nil
	file_api_vendors_aa_sahamati_proto_goTypes = nil
	file_api_vendors_aa_sahamati_proto_depIdxs = nil
}
