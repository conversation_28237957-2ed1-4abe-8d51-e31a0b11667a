// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendors/aa/sahamati.proto

package aa

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GenerateAccessTokenResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateAccessTokenResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateAccessTokenResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateAccessTokenResponseMultiError, or nil if none found.
func (m *GenerateAccessTokenResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateAccessTokenResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccessToken

	// no validation rules for ExpiresIn

	// no validation rules for RefreshExpiresIn

	// no validation rules for IdToken

	// no validation rules for NotBeforePolicy

	// no validation rules for Scope

	// no validation rules for Error

	// no validation rules for ErrorDescription

	// no validation rules for AccessTokenV2

	// no validation rules for ExpiresInV2

	if len(errors) > 0 {
		return GenerateAccessTokenResponseMultiError(errors)
	}

	return nil
}

// GenerateAccessTokenResponseMultiError is an error wrapping multiple
// validation errors returned by GenerateAccessTokenResponse.ValidateAll() if
// the designated constraints aren't met.
type GenerateAccessTokenResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateAccessTokenResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateAccessTokenResponseMultiError) AllErrors() []error { return m }

// GenerateAccessTokenResponseValidationError is the validation error returned
// by GenerateAccessTokenResponse.Validate if the designated constraints
// aren't met.
type GenerateAccessTokenResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateAccessTokenResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateAccessTokenResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateAccessTokenResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateAccessTokenResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateAccessTokenResponseValidationError) ErrorName() string {
	return "GenerateAccessTokenResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateAccessTokenResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateAccessTokenResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateAccessTokenResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateAccessTokenResponseValidationError{}

// Validate checks the field values on FetchCrEntityDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchCrEntityDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchCrEntityDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchCrEntityDetailsRequestMultiError, or nil if none found.
func (m *FetchCrEntityDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchCrEntityDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IdentityToken

	if len(errors) > 0 {
		return FetchCrEntityDetailsRequestMultiError(errors)
	}

	return nil
}

// FetchCrEntityDetailsRequestMultiError is an error wrapping multiple
// validation errors returned by FetchCrEntityDetailsRequest.ValidateAll() if
// the designated constraints aren't met.
type FetchCrEntityDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchCrEntityDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchCrEntityDetailsRequestMultiError) AllErrors() []error { return m }

// FetchCrEntityDetailsRequestValidationError is the validation error returned
// by FetchCrEntityDetailsRequest.Validate if the designated constraints
// aren't met.
type FetchCrEntityDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchCrEntityDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchCrEntityDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchCrEntityDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchCrEntityDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchCrEntityDetailsRequestValidationError) ErrorName() string {
	return "FetchCrEntityDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchCrEntityDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchCrEntityDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchCrEntityDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchCrEntityDetailsRequestValidationError{}

// Validate checks the field values on FetchCrEntityDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchCrEntityDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchCrEntityDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchCrEntityDetailsResponseMultiError, or nil if none found.
func (m *FetchCrEntityDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchCrEntityDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Ver

	// no validation rules for Timestamp

	// no validation rules for TxnId

	if all {
		switch v := interface{}(m.GetRequester()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchCrEntityDetailsResponseValidationError{
					field:  "Requester",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchCrEntityDetailsResponseValidationError{
					field:  "Requester",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequester()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchCrEntityDetailsResponseValidationError{
				field:  "Requester",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEntityInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchCrEntityDetailsResponseValidationError{
					field:  "EntityInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchCrEntityDetailsResponseValidationError{
					field:  "EntityInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEntityInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchCrEntityDetailsResponseValidationError{
				field:  "EntityInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Error

	// no validation rules for Message

	if len(errors) > 0 {
		return FetchCrEntityDetailsResponseMultiError(errors)
	}

	return nil
}

// FetchCrEntityDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by FetchCrEntityDetailsResponse.ValidateAll() if
// the designated constraints aren't met.
type FetchCrEntityDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchCrEntityDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchCrEntityDetailsResponseMultiError) AllErrors() []error { return m }

// FetchCrEntityDetailsResponseValidationError is the validation error returned
// by FetchCrEntityDetailsResponse.Validate if the designated constraints
// aren't met.
type FetchCrEntityDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchCrEntityDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchCrEntityDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchCrEntityDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchCrEntityDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchCrEntityDetailsResponseValidationError) ErrorName() string {
	return "FetchCrEntityDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchCrEntityDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchCrEntityDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchCrEntityDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchCrEntityDetailsResponseValidationError{}

// Validate checks the field values on Requester with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Requester) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Requester with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RequesterMultiError, or nil
// if none found.
func (m *Requester) ValidateAll() error {
	return m.validate(true)
}

func (m *Requester) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Id

	if len(errors) > 0 {
		return RequesterMultiError(errors)
	}

	return nil
}

// RequesterMultiError is an error wrapping multiple validation errors returned
// by Requester.ValidateAll() if the designated constraints aren't met.
type RequesterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RequesterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RequesterMultiError) AllErrors() []error { return m }

// RequesterValidationError is the validation error returned by
// Requester.Validate if the designated constraints aren't met.
type RequesterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RequesterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RequesterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RequesterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RequesterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RequesterValidationError) ErrorName() string { return "RequesterValidationError" }

// Error satisfies the builtin error interface
func (e RequesterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRequester.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RequesterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RequesterValidationError{}

// Validate checks the field values on EntityInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EntityInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EntityInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EntityInfoMultiError, or
// nil if none found.
func (m *EntityInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *EntityInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Id

	// no validation rules for Code

	// no validation rules for EntityHandle

	for idx, item := range m.GetIdentifiers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, EntityInfoValidationError{
						field:  fmt.Sprintf("Identifiers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, EntityInfoValidationError{
						field:  fmt.Sprintf("Identifiers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return EntityInfoValidationError{
					field:  fmt.Sprintf("Identifiers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for BaseUrl

	// no validation rules for WebviewUrl

	if all {
		switch v := interface{}(m.GetCertificate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EntityInfoValidationError{
					field:  "Certificate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EntityInfoValidationError{
					field:  "Certificate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCertificate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EntityInfoValidationError{
				field:  "Certificate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTokenInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EntityInfoValidationError{
					field:  "TokenInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EntityInfoValidationError{
					field:  "TokenInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTokenInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EntityInfoValidationError{
				field:  "TokenInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Gsp

	if all {
		switch v := interface{}(m.GetSignature()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EntityInfoValidationError{
					field:  "Signature",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EntityInfoValidationError{
					field:  "Signature",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSignature()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EntityInfoValidationError{
				field:  "Signature",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CredentialsPk

	if len(errors) > 0 {
		return EntityInfoMultiError(errors)
	}

	return nil
}

// EntityInfoMultiError is an error wrapping multiple validation errors
// returned by EntityInfo.ValidateAll() if the designated constraints aren't met.
type EntityInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EntityInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EntityInfoMultiError) AllErrors() []error { return m }

// EntityInfoValidationError is the validation error returned by
// EntityInfo.Validate if the designated constraints aren't met.
type EntityInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EntityInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EntityInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EntityInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EntityInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EntityInfoValidationError) ErrorName() string { return "EntityInfoValidationError" }

// Error satisfies the builtin error interface
func (e EntityInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEntityInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EntityInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EntityInfoValidationError{}

// Validate checks the field values on Identifier with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Identifier) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Identifier with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in IdentifierMultiError, or
// nil if none found.
func (m *Identifier) ValidateAll() error {
	return m.validate(true)
}

func (m *Identifier) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Category

	// no validation rules for Type

	if len(errors) > 0 {
		return IdentifierMultiError(errors)
	}

	return nil
}

// IdentifierMultiError is an error wrapping multiple validation errors
// returned by Identifier.ValidateAll() if the designated constraints aren't met.
type IdentifierMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IdentifierMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IdentifierMultiError) AllErrors() []error { return m }

// IdentifierValidationError is the validation error returned by
// Identifier.Validate if the designated constraints aren't met.
type IdentifierValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IdentifierValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IdentifierValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IdentifierValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IdentifierValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IdentifierValidationError) ErrorName() string { return "IdentifierValidationError" }

// Error satisfies the builtin error interface
func (e IdentifierValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIdentifier.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IdentifierValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IdentifierValidationError{}

// Validate checks the field values on Certificate with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Certificate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Certificate with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CertificateMultiError, or
// nil if none found.
func (m *Certificate) ValidateAll() error {
	return m.validate(true)
}

func (m *Certificate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Alg

	// no validation rules for E

	// no validation rules for Kid

	// no validation rules for Kty

	// no validation rules for N

	// no validation rules for Use

	if len(errors) > 0 {
		return CertificateMultiError(errors)
	}

	return nil
}

// CertificateMultiError is an error wrapping multiple validation errors
// returned by Certificate.ValidateAll() if the designated constraints aren't met.
type CertificateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CertificateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CertificateMultiError) AllErrors() []error { return m }

// CertificateValidationError is the validation error returned by
// Certificate.Validate if the designated constraints aren't met.
type CertificateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CertificateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CertificateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CertificateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CertificateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CertificateValidationError) ErrorName() string { return "CertificateValidationError" }

// Error satisfies the builtin error interface
func (e CertificateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCertificate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CertificateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CertificateValidationError{}

// Validate checks the field values on TokenInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TokenInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TokenInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TokenInfoMultiError, or nil
// if none found.
func (m *TokenInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *TokenInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Url

	// no validation rules for Maxcalls

	// no validation rules for Desc

	if len(errors) > 0 {
		return TokenInfoMultiError(errors)
	}

	return nil
}

// TokenInfoMultiError is an error wrapping multiple validation errors returned
// by TokenInfo.ValidateAll() if the designated constraints aren't met.
type TokenInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TokenInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TokenInfoMultiError) AllErrors() []error { return m }

// TokenInfoValidationError is the validation error returned by
// TokenInfo.Validate if the designated constraints aren't met.
type TokenInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TokenInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TokenInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TokenInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TokenInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TokenInfoValidationError) ErrorName() string { return "TokenInfoValidationError" }

// Error satisfies the builtin error interface
func (e TokenInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTokenInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TokenInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TokenInfoValidationError{}

// Validate checks the field values on Signature with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Signature) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Signature with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SignatureMultiError, or nil
// if none found.
func (m *Signature) ValidateAll() error {
	return m.validate(true)
}

func (m *Signature) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SignValue

	if len(errors) > 0 {
		return SignatureMultiError(errors)
	}

	return nil
}

// SignatureMultiError is an error wrapping multiple validation errors returned
// by Signature.ValidateAll() if the designated constraints aren't met.
type SignatureMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SignatureMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SignatureMultiError) AllErrors() []error { return m }

// SignatureValidationError is the validation error returned by
// Signature.Validate if the designated constraints aren't met.
type SignatureValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SignatureValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SignatureValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SignatureValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SignatureValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SignatureValidationError) ErrorName() string { return "SignatureValidationError" }

// Error satisfies the builtin error interface
func (e SignatureValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSignature.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SignatureValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SignatureValidationError{}
