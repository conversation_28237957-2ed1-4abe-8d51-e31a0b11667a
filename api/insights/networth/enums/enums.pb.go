//go:generate gen_sql -types=AssetType

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/insights/networth/enums/enums.proto

package enums

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// NetWorthDashBoardType is an enum used to identify the type of dashboard to render.
type NetWorthDashBoardType int32

const (
	NetWorthDashBoardType_NET_WORTH_DASHBOARD_TYPE_UNSPECIFIED NetWorthDashBoardType = 0
	// ASSETS type will load only the assets page.
	NetWorthDashBoardType_ASSETS NetWorthDashBoardType = 1
	// NET_WORTH type will load the net worth page.
	NetWorthDashBoardType_NET_WORTH NetWorthDashBoardType = 2
)

// Enum value maps for NetWorthDashBoardType.
var (
	NetWorthDashBoardType_name = map[int32]string{
		0: "NET_WORTH_DASHBOARD_TYPE_UNSPECIFIED",
		1: "ASSETS",
		2: "NET_WORTH",
	}
	NetWorthDashBoardType_value = map[string]int32{
		"NET_WORTH_DASHBOARD_TYPE_UNSPECIFIED": 0,
		"ASSETS":                               1,
		"NET_WORTH":                            2,
	}
)

func (x NetWorthDashBoardType) Enum() *NetWorthDashBoardType {
	p := new(NetWorthDashBoardType)
	*p = x
	return p
}

func (x NetWorthDashBoardType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NetWorthDashBoardType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_insights_networth_enums_enums_proto_enumTypes[0].Descriptor()
}

func (NetWorthDashBoardType) Type() protoreflect.EnumType {
	return &file_api_insights_networth_enums_enums_proto_enumTypes[0]
}

func (x NetWorthDashBoardType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NetWorthDashBoardType.Descriptor instead.
func (NetWorthDashBoardType) EnumDescriptor() ([]byte, []int) {
	return file_api_insights_networth_enums_enums_proto_rawDescGZIP(), []int{0}
}

type AssetType int32

const (
	AssetType_ASSET_TYPE_UNSPECIFIED AssetType = 0
	// saving account managed by Fi and connected via Account Aggregator
	AssetType_ASSET_TYPE_SAVINGS_ACCOUNTS AssetType = 1
	// fixed/smart/recurring deposits managed by Fi and connected via Account Aggregator
	AssetType_ASSET_TYPE_FIXED_DEPOSITS AssetType = 2
	// EPF accounts connected by user
	AssetType_ASSET_TYPE_EPF AssetType = 3
	// Indian mutual fund investments
	AssetType_ASSET_TYPE_MUTUAL_FUND AssetType = 4
	// India listed securities. Stocks, Bonds, REITs, InvITs, Options, Futures etc
	AssetType_ASSET_TYPE_INDIAN_SECURITIES AssetType = 5
	// US listed securities
	AssetType_ASSET_TYPE_US_SECURITIES AssetType = 6
	// investments in p2p lending products
	AssetType_ASSET_TYPE_P2P_LENDING AssetType = 7
	// Alternate Investment Fund
	AssetType_ASSET_TYPE_AIF AssetType = 8
	// Private equity
	AssetType_ASSET_TYPE_PRIVATE_EQUITY AssetType = 9
	// Real Estate
	AssetType_ASSET_TYPE_REAL_ESTATE AssetType = 10
	// Art & Artefacts
	AssetType_ASSET_TYPE_ART_ARTEFACTS AssetType = 11
	// bonds
	AssetType_ASSET_TYPE_BONDS AssetType = 12
	// cash
	AssetType_ASSET_TYPE_CASH AssetType = 13
	// digital gold
	AssetType_ASSET_TYPE_DIGITAL_GOLD AssetType = 14
	// digital silver
	AssetType_ASSET_TYPE_DIGITAL_SILVER AssetType = 15
	// Portfolio Management Service (PMS)
	AssetType_ASSET_TYPE_PORTFOLIO_MANAGEMENT_SERVICE AssetType = 16
	// Public Provident Fund (PPF)
	AssetType_ASSET_TYPE_PUBLIC_PROVIDENT_FUND AssetType = 17
	// Employee Stock Options (ESOP)
	AssetType_ASSET_TYPE_EMPLOYEE_STOCK_OPTION AssetType = 18
	// National Pension Scheme
	AssetType_ASSET_TYPE_NPS           AssetType = 19
	AssetType_ASSET_TYPE_USSTOCKS      AssetType = 20
	AssetType_ASSET_TYPE_INDIAN_STOCKS AssetType = 21
	// outstanding balance across all credit cards
	AssetType_ASSET_TYPE_CREDIT_CARD_OUTSTANDING AssetType = 22
	// outstanding home loan amount
	AssetType_ASSET_TYPE_HOME_LOAN AssetType = 23
	// outstanding personal loan amount
	AssetType_ASSET_TYPE_PERSONAL_LOAN AssetType = 24
	// outstanding vehicle loan amount
	AssetType_ASSET_TYPE_VEHICLE_LOAN AssetType = 25
	// outstanding education loan amount
	AssetType_ASSET_TYPE_EDUCATION_LOAN AssetType = 26
	// other loan types not covered as any specific liability type
	AssetType_ASSET_TYPE_OTHER_LOAN AssetType = 27
	// Gadgets
	AssetType_ASSET_TYPE_GADGETS AssetType = 28
	// Vehicles
	AssetType_ASSET_TYPE_VEHICLES AssetType = 29
	// Crypto
	AssetType_ASSET_TYPE_CRYPTO AssetType = 30
	// Furniture
	AssetType_ASSET_TYPE_FURNITURE AssetType = 31
	// Collectibles
	AssetType_ASSET_TYPE_COLLECTIBLES AssetType = 32
	// Jewellery
	AssetType_ASSET_TYPE_JEWELLERY AssetType = 33
	AssetType_ASSET_TYPE_OTHERS    AssetType = 34
)

// Enum value maps for AssetType.
var (
	AssetType_name = map[int32]string{
		0:  "ASSET_TYPE_UNSPECIFIED",
		1:  "ASSET_TYPE_SAVINGS_ACCOUNTS",
		2:  "ASSET_TYPE_FIXED_DEPOSITS",
		3:  "ASSET_TYPE_EPF",
		4:  "ASSET_TYPE_MUTUAL_FUND",
		5:  "ASSET_TYPE_INDIAN_SECURITIES",
		6:  "ASSET_TYPE_US_SECURITIES",
		7:  "ASSET_TYPE_P2P_LENDING",
		8:  "ASSET_TYPE_AIF",
		9:  "ASSET_TYPE_PRIVATE_EQUITY",
		10: "ASSET_TYPE_REAL_ESTATE",
		11: "ASSET_TYPE_ART_ARTEFACTS",
		12: "ASSET_TYPE_BONDS",
		13: "ASSET_TYPE_CASH",
		14: "ASSET_TYPE_DIGITAL_GOLD",
		15: "ASSET_TYPE_DIGITAL_SILVER",
		16: "ASSET_TYPE_PORTFOLIO_MANAGEMENT_SERVICE",
		17: "ASSET_TYPE_PUBLIC_PROVIDENT_FUND",
		18: "ASSET_TYPE_EMPLOYEE_STOCK_OPTION",
		19: "ASSET_TYPE_NPS",
		20: "ASSET_TYPE_USSTOCKS",
		21: "ASSET_TYPE_INDIAN_STOCKS",
		22: "ASSET_TYPE_CREDIT_CARD_OUTSTANDING",
		23: "ASSET_TYPE_HOME_LOAN",
		24: "ASSET_TYPE_PERSONAL_LOAN",
		25: "ASSET_TYPE_VEHICLE_LOAN",
		26: "ASSET_TYPE_EDUCATION_LOAN",
		27: "ASSET_TYPE_OTHER_LOAN",
		28: "ASSET_TYPE_GADGETS",
		29: "ASSET_TYPE_VEHICLES",
		30: "ASSET_TYPE_CRYPTO",
		31: "ASSET_TYPE_FURNITURE",
		32: "ASSET_TYPE_COLLECTIBLES",
		33: "ASSET_TYPE_JEWELLERY",
		34: "ASSET_TYPE_OTHERS",
	}
	AssetType_value = map[string]int32{
		"ASSET_TYPE_UNSPECIFIED":                  0,
		"ASSET_TYPE_SAVINGS_ACCOUNTS":             1,
		"ASSET_TYPE_FIXED_DEPOSITS":               2,
		"ASSET_TYPE_EPF":                          3,
		"ASSET_TYPE_MUTUAL_FUND":                  4,
		"ASSET_TYPE_INDIAN_SECURITIES":            5,
		"ASSET_TYPE_US_SECURITIES":                6,
		"ASSET_TYPE_P2P_LENDING":                  7,
		"ASSET_TYPE_AIF":                          8,
		"ASSET_TYPE_PRIVATE_EQUITY":               9,
		"ASSET_TYPE_REAL_ESTATE":                  10,
		"ASSET_TYPE_ART_ARTEFACTS":                11,
		"ASSET_TYPE_BONDS":                        12,
		"ASSET_TYPE_CASH":                         13,
		"ASSET_TYPE_DIGITAL_GOLD":                 14,
		"ASSET_TYPE_DIGITAL_SILVER":               15,
		"ASSET_TYPE_PORTFOLIO_MANAGEMENT_SERVICE": 16,
		"ASSET_TYPE_PUBLIC_PROVIDENT_FUND":        17,
		"ASSET_TYPE_EMPLOYEE_STOCK_OPTION":        18,
		"ASSET_TYPE_NPS":                          19,
		"ASSET_TYPE_USSTOCKS":                     20,
		"ASSET_TYPE_INDIAN_STOCKS":                21,
		"ASSET_TYPE_CREDIT_CARD_OUTSTANDING":      22,
		"ASSET_TYPE_HOME_LOAN":                    23,
		"ASSET_TYPE_PERSONAL_LOAN":                24,
		"ASSET_TYPE_VEHICLE_LOAN":                 25,
		"ASSET_TYPE_EDUCATION_LOAN":               26,
		"ASSET_TYPE_OTHER_LOAN":                   27,
		"ASSET_TYPE_GADGETS":                      28,
		"ASSET_TYPE_VEHICLES":                     29,
		"ASSET_TYPE_CRYPTO":                       30,
		"ASSET_TYPE_FURNITURE":                    31,
		"ASSET_TYPE_COLLECTIBLES":                 32,
		"ASSET_TYPE_JEWELLERY":                    33,
		"ASSET_TYPE_OTHERS":                       34,
	}
)

func (x AssetType) Enum() *AssetType {
	p := new(AssetType)
	*p = x
	return p
}

func (x AssetType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AssetType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_insights_networth_enums_enums_proto_enumTypes[1].Descriptor()
}

func (AssetType) Type() protoreflect.EnumType {
	return &file_api_insights_networth_enums_enums_proto_enumTypes[1]
}

func (x AssetType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AssetType.Descriptor instead.
func (AssetType) EnumDescriptor() ([]byte, []int) {
	return file_api_insights_networth_enums_enums_proto_rawDescGZIP(), []int{1}
}

// Defines the type of data included in the networth data file
type NetworthDataFileType int32

const (
	NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_UNSPECIFIED NetworthDataFileType = 0
	// File contains comprehensive networth data including assets, liabilities, and analysis
	NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_NETWORTH_DATA NetworthDataFileType = 1
	// File contains bank account transactions data
	NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_BANK_DETAILS_DATA NetworthDataFileType = 2
)

// Enum value maps for NetworthDataFileType.
var (
	NetworthDataFileType_name = map[int32]string{
		0: "NETWORTH_DATA_FILE_TYPE_UNSPECIFIED",
		1: "NETWORTH_DATA_FILE_TYPE_NETWORTH_DATA",
		2: "NETWORTH_DATA_FILE_TYPE_BANK_DETAILS_DATA",
	}
	NetworthDataFileType_value = map[string]int32{
		"NETWORTH_DATA_FILE_TYPE_UNSPECIFIED":       0,
		"NETWORTH_DATA_FILE_TYPE_NETWORTH_DATA":     1,
		"NETWORTH_DATA_FILE_TYPE_BANK_DETAILS_DATA": 2,
	}
)

func (x NetworthDataFileType) Enum() *NetworthDataFileType {
	p := new(NetworthDataFileType)
	*p = x
	return p
}

func (x NetworthDataFileType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NetworthDataFileType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_insights_networth_enums_enums_proto_enumTypes[2].Descriptor()
}

func (NetworthDataFileType) Type() protoreflect.EnumType {
	return &file_api_insights_networth_enums_enums_proto_enumTypes[2]
}

func (x NetworthDataFileType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NetworthDataFileType.Descriptor instead.
func (NetworthDataFileType) EnumDescriptor() ([]byte, []int) {
	return file_api_insights_networth_enums_enums_proto_rawDescGZIP(), []int{2}
}

var File_api_insights_networth_enums_enums_proto protoreflect.FileDescriptor

var file_api_insights_networth_enums_enums_proto_rawDesc = []byte{
	0x0a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x69, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2a, 0x5c, 0x0a, 0x15, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x44, 0x61,
	0x73, 0x68, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x24, 0x4e,
	0x45, 0x54, 0x5f, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x44, 0x41, 0x53, 0x48, 0x42, 0x4f, 0x41,
	0x52, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x53, 0x53, 0x45, 0x54, 0x53, 0x10,
	0x01, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x45, 0x54, 0x5f, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x10, 0x02,
	0x2a, 0xf2, 0x07, 0x0a, 0x09, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a,
	0x0a, 0x16, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x41, 0x53,
	0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x41, 0x56, 0x49, 0x4e, 0x47, 0x53,
	0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x53, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x41,
	0x53, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x49, 0x58, 0x45, 0x44, 0x5f,
	0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x53, 0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x53,
	0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x50, 0x46, 0x10, 0x03, 0x12, 0x1a,
	0x0a, 0x16, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x55, 0x54,
	0x55, 0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x10, 0x04, 0x12, 0x20, 0x0a, 0x1c, 0x41, 0x53,
	0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x44, 0x49, 0x41, 0x4e, 0x5f,
	0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x49, 0x45, 0x53, 0x10, 0x05, 0x12, 0x1c, 0x0a, 0x18,
	0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x53, 0x5f, 0x53, 0x45,
	0x43, 0x55, 0x52, 0x49, 0x54, 0x49, 0x45, 0x53, 0x10, 0x06, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x53,
	0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x32, 0x50, 0x5f, 0x4c, 0x45, 0x4e,
	0x44, 0x49, 0x4e, 0x47, 0x10, 0x07, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x49, 0x46, 0x10, 0x08, 0x12, 0x1d, 0x0a, 0x19, 0x41, 0x53,
	0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x52, 0x49, 0x56, 0x41, 0x54, 0x45,
	0x5f, 0x45, 0x51, 0x55, 0x49, 0x54, 0x59, 0x10, 0x09, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x53, 0x53,
	0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x4c, 0x5f, 0x45, 0x53, 0x54,
	0x41, 0x54, 0x45, 0x10, 0x0a, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x41, 0x52, 0x54, 0x5f, 0x41, 0x52, 0x54, 0x45, 0x46, 0x41, 0x43, 0x54,
	0x53, 0x10, 0x0b, 0x12, 0x14, 0x0a, 0x10, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x42, 0x4f, 0x4e, 0x44, 0x53, 0x10, 0x0c, 0x12, 0x13, 0x0a, 0x0f, 0x41, 0x53, 0x53,
	0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x41, 0x53, 0x48, 0x10, 0x0d, 0x12, 0x1b,
	0x0a, 0x17, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x49, 0x47,
	0x49, 0x54, 0x41, 0x4c, 0x5f, 0x47, 0x4f, 0x4c, 0x44, 0x10, 0x0e, 0x12, 0x1d, 0x0a, 0x19, 0x41,
	0x53, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x49, 0x47, 0x49, 0x54, 0x41,
	0x4c, 0x5f, 0x53, 0x49, 0x4c, 0x56, 0x45, 0x52, 0x10, 0x0f, 0x12, 0x2b, 0x0a, 0x27, 0x41, 0x53,
	0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x4f, 0x52, 0x54, 0x46, 0x4f, 0x4c,
	0x49, 0x4f, 0x5f, 0x4d, 0x41, 0x4e, 0x41, 0x47, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x45,
	0x52, 0x56, 0x49, 0x43, 0x45, 0x10, 0x10, 0x12, 0x24, 0x0a, 0x20, 0x41, 0x53, 0x53, 0x45, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x55, 0x42, 0x4c, 0x49, 0x43, 0x5f, 0x50, 0x52, 0x4f,
	0x56, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x10, 0x11, 0x12, 0x24, 0x0a,
	0x20, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x4d, 0x50, 0x4c,
	0x4f, 0x59, 0x45, 0x45, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0x12, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x4e, 0x50, 0x53, 0x10, 0x13, 0x12, 0x17, 0x0a, 0x13, 0x41, 0x53, 0x53, 0x45, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x53, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x10, 0x14,
	0x12, 0x1c, 0x0a, 0x18, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49,
	0x4e, 0x44, 0x49, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x10, 0x15, 0x12, 0x26,
	0x0a, 0x22, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x52, 0x45,
	0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4f, 0x55, 0x54, 0x53, 0x54, 0x41, 0x4e,
	0x44, 0x49, 0x4e, 0x47, 0x10, 0x16, 0x12, 0x18, 0x0a, 0x14, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x48, 0x4f, 0x4d, 0x45, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x10, 0x17,
	0x12, 0x1c, 0x0a, 0x18, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50,
	0x45, 0x52, 0x53, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x10, 0x18, 0x12, 0x1b,
	0x0a, 0x17, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x56, 0x45, 0x48,
	0x49, 0x43, 0x4c, 0x45, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x10, 0x19, 0x12, 0x1d, 0x0a, 0x19, 0x41,
	0x53, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x44, 0x55, 0x43, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x10, 0x1a, 0x12, 0x19, 0x0a, 0x15, 0x41, 0x53,
	0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x4c,
	0x4f, 0x41, 0x4e, 0x10, 0x1b, 0x12, 0x16, 0x0a, 0x12, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x47, 0x41, 0x44, 0x47, 0x45, 0x54, 0x53, 0x10, 0x1c, 0x12, 0x17, 0x0a,
	0x13, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x56, 0x45, 0x48, 0x49,
	0x43, 0x4c, 0x45, 0x53, 0x10, 0x1d, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x52, 0x59, 0x50, 0x54, 0x4f, 0x10, 0x1e, 0x12, 0x18, 0x0a,
	0x14, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x55, 0x52, 0x4e,
	0x49, 0x54, 0x55, 0x52, 0x45, 0x10, 0x1f, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x53, 0x53, 0x45, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x42, 0x4c,
	0x45, 0x53, 0x10, 0x20, 0x12, 0x18, 0x0a, 0x14, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x4a, 0x45, 0x57, 0x45, 0x4c, 0x4c, 0x45, 0x52, 0x59, 0x10, 0x21, 0x12, 0x15,
	0x0a, 0x11, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f, 0x54, 0x48,
	0x45, 0x52, 0x53, 0x10, 0x22, 0x2a, 0x99, 0x01, 0x0a, 0x14, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x74, 0x68, 0x44, 0x61, 0x74, 0x61, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x27,
	0x0a, 0x23, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f,
	0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x29, 0x0a, 0x25, 0x4e, 0x45, 0x54, 0x57, 0x4f,
	0x52, 0x54, 0x48, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x44, 0x41, 0x54, 0x41,
	0x10, 0x01, 0x12, 0x2d, 0x0a, 0x29, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x44,
	0x41, 0x54, 0x41, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x41,
	0x4e, 0x4b, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x10,
	0x02, 0x42, 0x68, 0x0a, 0x32, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74,
	0x68, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x5a, 0x32, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x74, 0x68, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_insights_networth_enums_enums_proto_rawDescOnce sync.Once
	file_api_insights_networth_enums_enums_proto_rawDescData = file_api_insights_networth_enums_enums_proto_rawDesc
)

func file_api_insights_networth_enums_enums_proto_rawDescGZIP() []byte {
	file_api_insights_networth_enums_enums_proto_rawDescOnce.Do(func() {
		file_api_insights_networth_enums_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_insights_networth_enums_enums_proto_rawDescData)
	})
	return file_api_insights_networth_enums_enums_proto_rawDescData
}

var file_api_insights_networth_enums_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_api_insights_networth_enums_enums_proto_goTypes = []interface{}{
	(NetWorthDashBoardType)(0), // 0: insights.networth.enums.NetWorthDashBoardType
	(AssetType)(0),             // 1: insights.networth.enums.AssetType
	(NetworthDataFileType)(0),  // 2: insights.networth.enums.NetworthDataFileType
}
var file_api_insights_networth_enums_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_insights_networth_enums_enums_proto_init() }
func file_api_insights_networth_enums_enums_proto_init() {
	if File_api_insights_networth_enums_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_insights_networth_enums_enums_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_insights_networth_enums_enums_proto_goTypes,
		DependencyIndexes: file_api_insights_networth_enums_enums_proto_depIdxs,
		EnumInfos:         file_api_insights_networth_enums_enums_proto_enumTypes,
	}.Build()
	File_api_insights_networth_enums_enums_proto = out.File
	file_api_insights_networth_enums_enums_proto_rawDesc = nil
	file_api_insights_networth_enums_enums_proto_goTypes = nil
	file_api_insights_networth_enums_enums_proto_depIdxs = nil
}
