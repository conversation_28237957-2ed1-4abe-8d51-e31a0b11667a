// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/preapprovedloan/loan_offer.proto

package preapprovedloan

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/preapprovedloan/enums"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.LoanProgramVersion(0)
)

// Validate checks the field values on LoanOffer with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LoanOffer) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoanOffer with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LoanOfferMultiError, or nil
// if none found.
func (m *LoanOffer) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanOffer) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ActorId

	// no validation rules for VendorOfferId

	// no validation rules for Vendor

	if all {
		switch v := interface{}(m.GetOfferConstraints()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanOfferValidationError{
					field:  "OfferConstraints",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanOfferValidationError{
					field:  "OfferConstraints",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOfferConstraints()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanOfferValidationError{
				field:  "OfferConstraints",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProcessingInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanOfferValidationError{
					field:  "ProcessingInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanOfferValidationError{
					field:  "ProcessingInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcessingInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanOfferValidationError{
				field:  "ProcessingInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetValidSince()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanOfferValidationError{
					field:  "ValidSince",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanOfferValidationError{
					field:  "ValidSince",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValidSince()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanOfferValidationError{
				field:  "ValidSince",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetValidTill()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanOfferValidationError{
					field:  "ValidTill",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanOfferValidationError{
					field:  "ValidTill",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValidTill()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanOfferValidationError{
				field:  "ValidTill",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeactivatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanOfferValidationError{
					field:  "DeactivatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanOfferValidationError{
					field:  "DeactivatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeactivatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanOfferValidationError{
				field:  "DeactivatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanOfferValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanOfferValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanOfferValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanOfferValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanOfferValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanOfferValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanOfferValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanOfferValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanOfferValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LoanOfferEligibilityCriteriaId

	// no validation rules for LoanProgram

	// no validation rules for LmsPartner

	// no validation rules for LmsPartnerProductId

	if all {
		switch v := interface{}(m.GetLastViewedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanOfferValidationError{
					field:  "LastViewedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanOfferValidationError{
					field:  "LastViewedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLastViewedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanOfferValidationError{
				field:  "LastViewedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsDiscountApplied

	if all {
		switch v := interface{}(m.GetOfferDisplayInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanOfferValidationError{
					field:  "OfferDisplayInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanOfferValidationError{
					field:  "OfferDisplayInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOfferDisplayInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanOfferValidationError{
				field:  "OfferDisplayInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LoanOfferType

	if len(errors) > 0 {
		return LoanOfferMultiError(errors)
	}

	return nil
}

// LoanOfferMultiError is an error wrapping multiple validation errors returned
// by LoanOffer.ValidateAll() if the designated constraints aren't met.
type LoanOfferMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanOfferMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanOfferMultiError) AllErrors() []error { return m }

// LoanOfferValidationError is the validation error returned by
// LoanOffer.Validate if the designated constraints aren't met.
type LoanOfferValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanOfferValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoanOfferValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoanOfferValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoanOfferValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoanOfferValidationError) ErrorName() string { return "LoanOfferValidationError" }

// Error satisfies the builtin error interface
func (e LoanOfferValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanOffer.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanOfferValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanOfferValidationError{}

// Validate checks the field values on RangeData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RangeData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RangeData with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RangeDataMultiError, or nil
// if none found.
func (m *RangeData) ValidateAll() error {
	return m.validate(true)
}

func (m *RangeData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Start

	// no validation rules for End

	switch v := m.Value.(type) {
	case *RangeData_Percentage:
		if v == nil {
			err := RangeDataValidationError{
				field:  "Value",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for Percentage
	case *RangeData_AbsoluteValue:
		if v == nil {
			err := RangeDataValidationError{
				field:  "Value",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAbsoluteValue()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RangeDataValidationError{
						field:  "AbsoluteValue",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RangeDataValidationError{
						field:  "AbsoluteValue",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAbsoluteValue()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RangeDataValidationError{
					field:  "AbsoluteValue",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.MinValue.(type) {
	case *RangeData_MinPercentage:
		if v == nil {
			err := RangeDataValidationError{
				field:  "MinValue",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for MinPercentage
	case *RangeData_MinAbsoluteValue:
		if v == nil {
			err := RangeDataValidationError{
				field:  "MinValue",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMinAbsoluteValue()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RangeDataValidationError{
						field:  "MinAbsoluteValue",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RangeDataValidationError{
						field:  "MinAbsoluteValue",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMinAbsoluteValue()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RangeDataValidationError{
					field:  "MinAbsoluteValue",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.MaxValue.(type) {
	case *RangeData_MaxPercentage:
		if v == nil {
			err := RangeDataValidationError{
				field:  "MaxValue",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for MaxPercentage
	case *RangeData_MaxAbsoluteValue:
		if v == nil {
			err := RangeDataValidationError{
				field:  "MaxValue",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMaxAbsoluteValue()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RangeDataValidationError{
						field:  "MaxAbsoluteValue",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RangeDataValidationError{
						field:  "MaxAbsoluteValue",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMaxAbsoluteValue()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RangeDataValidationError{
					field:  "MaxAbsoluteValue",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return RangeDataMultiError(errors)
	}

	return nil
}

// RangeDataMultiError is an error wrapping multiple validation errors returned
// by RangeData.ValidateAll() if the designated constraints aren't met.
type RangeDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RangeDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RangeDataMultiError) AllErrors() []error { return m }

// RangeDataValidationError is the validation error returned by
// RangeData.Validate if the designated constraints aren't met.
type RangeDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RangeDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RangeDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RangeDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RangeDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RangeDataValidationError) ErrorName() string { return "RangeDataValidationError" }

// Error satisfies the builtin error interface
func (e RangeDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRangeData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RangeDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RangeDataValidationError{}

// Validate checks the field values on OfferConstraints with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *OfferConstraints) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OfferConstraints with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OfferConstraintsMultiError, or nil if none found.
func (m *OfferConstraints) ValidateAll() error {
	return m.validate(true)
}

func (m *OfferConstraints) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetMaxLoanAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferConstraintsValidationError{
					field:  "MaxLoanAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferConstraintsValidationError{
					field:  "MaxLoanAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaxLoanAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferConstraintsValidationError{
				field:  "MaxLoanAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMaxEmiAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferConstraintsValidationError{
					field:  "MaxEmiAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferConstraintsValidationError{
					field:  "MaxEmiAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaxEmiAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferConstraintsValidationError{
				field:  "MaxEmiAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MaxTenureMonths

	if all {
		switch v := interface{}(m.GetMinLoanAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferConstraintsValidationError{
					field:  "MinLoanAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferConstraintsValidationError{
					field:  "MinLoanAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMinLoanAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferConstraintsValidationError{
				field:  "MinLoanAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MinTenureMonths

	if all {
		switch v := interface{}(m.GetMinLoanAmountStepSize()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferConstraintsValidationError{
					field:  "MinLoanAmountStepSize",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferConstraintsValidationError{
					field:  "MinLoanAmountStepSize",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMinLoanAmountStepSize()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferConstraintsValidationError{
				field:  "MinLoanAmountStepSize",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.AdditionalConstraints.(type) {
	case *OfferConstraints_FiftyfinLamfConstraintInfo:
		if v == nil {
			err := OfferConstraintsValidationError{
				field:  "AdditionalConstraints",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFiftyfinLamfConstraintInfo()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OfferConstraintsValidationError{
						field:  "FiftyfinLamfConstraintInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OfferConstraintsValidationError{
						field:  "FiftyfinLamfConstraintInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFiftyfinLamfConstraintInfo()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OfferConstraintsValidationError{
					field:  "FiftyfinLamfConstraintInfo",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *OfferConstraints_LendenConstraintInfo:
		if v == nil {
			err := OfferConstraintsValidationError{
				field:  "AdditionalConstraints",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetLendenConstraintInfo()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OfferConstraintsValidationError{
						field:  "LendenConstraintInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OfferConstraintsValidationError{
						field:  "LendenConstraintInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetLendenConstraintInfo()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OfferConstraintsValidationError{
					field:  "LendenConstraintInfo",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return OfferConstraintsMultiError(errors)
	}

	return nil
}

// OfferConstraintsMultiError is an error wrapping multiple validation errors
// returned by OfferConstraints.ValidateAll() if the designated constraints
// aren't met.
type OfferConstraintsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OfferConstraintsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OfferConstraintsMultiError) AllErrors() []error { return m }

// OfferConstraintsValidationError is the validation error returned by
// OfferConstraints.Validate if the designated constraints aren't met.
type OfferConstraintsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OfferConstraintsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OfferConstraintsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OfferConstraintsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OfferConstraintsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OfferConstraintsValidationError) ErrorName() string { return "OfferConstraintsValidationError" }

// Error satisfies the builtin error interface
func (e OfferConstraintsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOfferConstraints.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OfferConstraintsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OfferConstraintsValidationError{}

// Validate checks the field values on OfferProcessingInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OfferProcessingInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OfferProcessingInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OfferProcessingInfoMultiError, or nil if none found.
func (m *OfferProcessingInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *OfferProcessingInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Gst

	for idx, item := range m.GetInterestRate() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OfferProcessingInfoValidationError{
						field:  fmt.Sprintf("InterestRate[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OfferProcessingInfoValidationError{
						field:  fmt.Sprintf("InterestRate[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OfferProcessingInfoValidationError{
					field:  fmt.Sprintf("InterestRate[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetProcessingFee() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OfferProcessingInfoValidationError{
						field:  fmt.Sprintf("ProcessingFee[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OfferProcessingInfoValidationError{
						field:  fmt.Sprintf("ProcessingFee[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OfferProcessingInfoValidationError{
					field:  fmt.Sprintf("ProcessingFee[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ApplicationId

	// no validation rules for LoanProgramVersion

	for idx, item := range m.GetOfferDiscounts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OfferProcessingInfoValidationError{
						field:  fmt.Sprintf("OfferDiscounts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OfferProcessingInfoValidationError{
						field:  fmt.Sprintf("OfferDiscounts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OfferProcessingInfoValidationError{
					field:  fmt.Sprintf("OfferDiscounts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for RateOfInterest

	// no validation rules for InterestType

	// no validation rules for TenureType

	// no validation rules for InterestRateFrequency

	for idx, item := range m.GetApplicableTenures() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OfferProcessingInfoValidationError{
						field:  fmt.Sprintf("ApplicableTenures[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OfferProcessingInfoValidationError{
						field:  fmt.Sprintf("ApplicableTenures[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OfferProcessingInfoValidationError{
					field:  fmt.Sprintf("ApplicableTenures[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return OfferProcessingInfoMultiError(errors)
	}

	return nil
}

// OfferProcessingInfoMultiError is an error wrapping multiple validation
// errors returned by OfferProcessingInfo.ValidateAll() if the designated
// constraints aren't met.
type OfferProcessingInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OfferProcessingInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OfferProcessingInfoMultiError) AllErrors() []error { return m }

// OfferProcessingInfoValidationError is the validation error returned by
// OfferProcessingInfo.Validate if the designated constraints aren't met.
type OfferProcessingInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OfferProcessingInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OfferProcessingInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OfferProcessingInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OfferProcessingInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OfferProcessingInfoValidationError) ErrorName() string {
	return "OfferProcessingInfoValidationError"
}

// Error satisfies the builtin error interface
func (e OfferProcessingInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOfferProcessingInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OfferProcessingInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OfferProcessingInfoValidationError{}

// Validate checks the field values on FiftyFinLamfConstraintInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FiftyFinLamfConstraintInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FiftyFinLamfConstraintInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FiftyFinLamfConstraintInfoMultiError, or nil if none found.
func (m *FiftyFinLamfConstraintInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *FiftyFinLamfConstraintInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetMfPortfolioConstraint()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FiftyFinLamfConstraintInfoValidationError{
					field:  "MfPortfolioConstraint",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FiftyFinLamfConstraintInfoValidationError{
					field:  "MfPortfolioConstraint",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMfPortfolioConstraint()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FiftyFinLamfConstraintInfoValidationError{
				field:  "MfPortfolioConstraint",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Source

	if len(errors) > 0 {
		return FiftyFinLamfConstraintInfoMultiError(errors)
	}

	return nil
}

// FiftyFinLamfConstraintInfoMultiError is an error wrapping multiple
// validation errors returned by FiftyFinLamfConstraintInfo.ValidateAll() if
// the designated constraints aren't met.
type FiftyFinLamfConstraintInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FiftyFinLamfConstraintInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FiftyFinLamfConstraintInfoMultiError) AllErrors() []error { return m }

// FiftyFinLamfConstraintInfoValidationError is the validation error returned
// by FiftyFinLamfConstraintInfo.Validate if the designated constraints aren't met.
type FiftyFinLamfConstraintInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FiftyFinLamfConstraintInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FiftyFinLamfConstraintInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FiftyFinLamfConstraintInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FiftyFinLamfConstraintInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FiftyFinLamfConstraintInfoValidationError) ErrorName() string {
	return "FiftyFinLamfConstraintInfoValidationError"
}

// Error satisfies the builtin error interface
func (e FiftyFinLamfConstraintInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFiftyFinLamfConstraintInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FiftyFinLamfConstraintInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FiftyFinLamfConstraintInfoValidationError{}

// Validate checks the field values on LendenConstraintInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LendenConstraintInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LendenConstraintInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LendenConstraintInfoMultiError, or nil if none found.
func (m *LendenConstraintInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *LendenConstraintInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetLoanSlabs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LendenConstraintInfoValidationError{
						field:  fmt.Sprintf("LoanSlabs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LendenConstraintInfoValidationError{
						field:  fmt.Sprintf("LoanSlabs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LendenConstraintInfoValidationError{
					field:  fmt.Sprintf("LoanSlabs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetBankDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LendenConstraintInfoValidationError{
					field:  "BankDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LendenConstraintInfoValidationError{
					field:  "BankDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBankDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LendenConstraintInfoValidationError{
				field:  "BankDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LendenConstraintInfoMultiError(errors)
	}

	return nil
}

// LendenConstraintInfoMultiError is an error wrapping multiple validation
// errors returned by LendenConstraintInfo.ValidateAll() if the designated
// constraints aren't met.
type LendenConstraintInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LendenConstraintInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LendenConstraintInfoMultiError) AllErrors() []error { return m }

// LendenConstraintInfoValidationError is the validation error returned by
// LendenConstraintInfo.Validate if the designated constraints aren't met.
type LendenConstraintInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LendenConstraintInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LendenConstraintInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LendenConstraintInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LendenConstraintInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LendenConstraintInfoValidationError) ErrorName() string {
	return "LendenConstraintInfoValidationError"
}

// Error satisfies the builtin error interface
func (e LendenConstraintInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLendenConstraintInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LendenConstraintInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LendenConstraintInfoValidationError{}

// Validate checks the field values on AaAnalysisBankDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AaAnalysisBankDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AaAnalysisBankDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AaAnalysisBankDetailsMultiError, or nil if none found.
func (m *AaAnalysisBankDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *AaAnalysisBankDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountHolderName

	// no validation rules for AccountNumber

	// no validation rules for Ifsc

	// no validation rules for Type

	// no validation rules for BankName

	if len(errors) > 0 {
		return AaAnalysisBankDetailsMultiError(errors)
	}

	return nil
}

// AaAnalysisBankDetailsMultiError is an error wrapping multiple validation
// errors returned by AaAnalysisBankDetails.ValidateAll() if the designated
// constraints aren't met.
type AaAnalysisBankDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AaAnalysisBankDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AaAnalysisBankDetailsMultiError) AllErrors() []error { return m }

// AaAnalysisBankDetailsValidationError is the validation error returned by
// AaAnalysisBankDetails.Validate if the designated constraints aren't met.
type AaAnalysisBankDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AaAnalysisBankDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AaAnalysisBankDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AaAnalysisBankDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AaAnalysisBankDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AaAnalysisBankDetailsValidationError) ErrorName() string {
	return "AaAnalysisBankDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e AaAnalysisBankDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAaAnalysisBankDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AaAnalysisBankDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AaAnalysisBankDetailsValidationError{}

// Validate checks the field values on LendenLoanSlab with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LendenLoanSlab) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LendenLoanSlab with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LendenLoanSlabMultiError,
// or nil if none found.
func (m *LendenLoanSlab) ValidateAll() error {
	return m.validate(true)
}

func (m *LendenLoanSlab) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetMinAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LendenLoanSlabValidationError{
					field:  "MinAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LendenLoanSlabValidationError{
					field:  "MinAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMinAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LendenLoanSlabValidationError{
				field:  "MinAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMaxAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LendenLoanSlabValidationError{
					field:  "MaxAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LendenLoanSlabValidationError{
					field:  "MaxAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaxAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LendenLoanSlabValidationError{
				field:  "MaxAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTenureRange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LendenLoanSlabValidationError{
					field:  "TenureRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LendenLoanSlabValidationError{
					field:  "TenureRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTenureRange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LendenLoanSlabValidationError{
				field:  "TenureRange",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LendenLoanSlabMultiError(errors)
	}

	return nil
}

// LendenLoanSlabMultiError is an error wrapping multiple validation errors
// returned by LendenLoanSlab.ValidateAll() if the designated constraints
// aren't met.
type LendenLoanSlabMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LendenLoanSlabMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LendenLoanSlabMultiError) AllErrors() []error { return m }

// LendenLoanSlabValidationError is the validation error returned by
// LendenLoanSlab.Validate if the designated constraints aren't met.
type LendenLoanSlabValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LendenLoanSlabValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LendenLoanSlabValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LendenLoanSlabValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LendenLoanSlabValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LendenLoanSlabValidationError) ErrorName() string { return "LendenLoanSlabValidationError" }

// Error satisfies the builtin error interface
func (e LendenLoanSlabValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLendenLoanSlab.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LendenLoanSlabValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LendenLoanSlabValidationError{}

// Validate checks the field values on LendenLoanSlabTenureRange with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LendenLoanSlabTenureRange) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LendenLoanSlabTenureRange with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LendenLoanSlabTenureRangeMultiError, or nil if none found.
func (m *LendenLoanSlabTenureRange) ValidateAll() error {
	return m.validate(true)
}

func (m *LendenLoanSlabTenureRange) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MinTenureInMonths

	// no validation rules for MaxTenureInMonths

	if len(errors) > 0 {
		return LendenLoanSlabTenureRangeMultiError(errors)
	}

	return nil
}

// LendenLoanSlabTenureRangeMultiError is an error wrapping multiple validation
// errors returned by LendenLoanSlabTenureRange.ValidateAll() if the
// designated constraints aren't met.
type LendenLoanSlabTenureRangeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LendenLoanSlabTenureRangeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LendenLoanSlabTenureRangeMultiError) AllErrors() []error { return m }

// LendenLoanSlabTenureRangeValidationError is the validation error returned by
// LendenLoanSlabTenureRange.Validate if the designated constraints aren't met.
type LendenLoanSlabTenureRangeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LendenLoanSlabTenureRangeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LendenLoanSlabTenureRangeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LendenLoanSlabTenureRangeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LendenLoanSlabTenureRangeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LendenLoanSlabTenureRangeValidationError) ErrorName() string {
	return "LendenLoanSlabTenureRangeValidationError"
}

// Error satisfies the builtin error interface
func (e LendenLoanSlabTenureRangeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLendenLoanSlabTenureRange.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LendenLoanSlabTenureRangeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LendenLoanSlabTenureRangeValidationError{}

// Validate checks the field values on OfferDisplayInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *OfferDisplayInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OfferDisplayInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OfferDisplayInfoMultiError, or nil if none found.
func (m *OfferDisplayInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *OfferDisplayInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FundingProbability

	// no validation rules for ExpectedTimeToGetFunding

	// no validation rules for IsRecommended

	if len(errors) > 0 {
		return OfferDisplayInfoMultiError(errors)
	}

	return nil
}

// OfferDisplayInfoMultiError is an error wrapping multiple validation errors
// returned by OfferDisplayInfo.ValidateAll() if the designated constraints
// aren't met.
type OfferDisplayInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OfferDisplayInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OfferDisplayInfoMultiError) AllErrors() []error { return m }

// OfferDisplayInfoValidationError is the validation error returned by
// OfferDisplayInfo.Validate if the designated constraints aren't met.
type OfferDisplayInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OfferDisplayInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OfferDisplayInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OfferDisplayInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OfferDisplayInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OfferDisplayInfoValidationError) ErrorName() string { return "OfferDisplayInfoValidationError" }

// Error satisfies the builtin error interface
func (e OfferDisplayInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOfferDisplayInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OfferDisplayInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OfferDisplayInfoValidationError{}

// Validate checks the field values on OfferProcessingInfo_ApplicableTenure
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *OfferProcessingInfo_ApplicableTenure) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OfferProcessingInfo_ApplicableTenure
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// OfferProcessingInfo_ApplicableTenureMultiError, or nil if none found.
func (m *OfferProcessingInfo_ApplicableTenure) ValidateAll() error {
	return m.validate(true)
}

func (m *OfferProcessingInfo_ApplicableTenure) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MinValue

	// no validation rules for MaxValue

	if len(errors) > 0 {
		return OfferProcessingInfo_ApplicableTenureMultiError(errors)
	}

	return nil
}

// OfferProcessingInfo_ApplicableTenureMultiError is an error wrapping multiple
// validation errors returned by
// OfferProcessingInfo_ApplicableTenure.ValidateAll() if the designated
// constraints aren't met.
type OfferProcessingInfo_ApplicableTenureMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OfferProcessingInfo_ApplicableTenureMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OfferProcessingInfo_ApplicableTenureMultiError) AllErrors() []error { return m }

// OfferProcessingInfo_ApplicableTenureValidationError is the validation error
// returned by OfferProcessingInfo_ApplicableTenure.Validate if the designated
// constraints aren't met.
type OfferProcessingInfo_ApplicableTenureValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OfferProcessingInfo_ApplicableTenureValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OfferProcessingInfo_ApplicableTenureValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OfferProcessingInfo_ApplicableTenureValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OfferProcessingInfo_ApplicableTenureValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OfferProcessingInfo_ApplicableTenureValidationError) ErrorName() string {
	return "OfferProcessingInfo_ApplicableTenureValidationError"
}

// Error satisfies the builtin error interface
func (e OfferProcessingInfo_ApplicableTenureValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOfferProcessingInfo_ApplicableTenure.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OfferProcessingInfo_ApplicableTenureValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OfferProcessingInfo_ApplicableTenureValidationError{}

// Validate checks the field values on OfferProcessingInfo_OfferDiscount with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *OfferProcessingInfo_OfferDiscount) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OfferProcessingInfo_OfferDiscount
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// OfferProcessingInfo_OfferDiscountMultiError, or nil if none found.
func (m *OfferProcessingInfo_OfferDiscount) ValidateAll() error {
	return m.validate(true)
}

func (m *OfferProcessingInfo_OfferDiscount) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DiscountedParameter

	// no validation rules for DiscountValue

	if all {
		switch v := interface{}(m.GetDiscountValidSince()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferProcessingInfo_OfferDiscountValidationError{
					field:  "DiscountValidSince",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferProcessingInfo_OfferDiscountValidationError{
					field:  "DiscountValidSince",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDiscountValidSince()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferProcessingInfo_OfferDiscountValidationError{
				field:  "DiscountValidSince",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDiscountValidTill()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferProcessingInfo_OfferDiscountValidationError{
					field:  "DiscountValidTill",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferProcessingInfo_OfferDiscountValidationError{
					field:  "DiscountValidTill",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDiscountValidTill()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferProcessingInfo_OfferDiscountValidationError{
				field:  "DiscountValidTill",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return OfferProcessingInfo_OfferDiscountMultiError(errors)
	}

	return nil
}

// OfferProcessingInfo_OfferDiscountMultiError is an error wrapping multiple
// validation errors returned by
// OfferProcessingInfo_OfferDiscount.ValidateAll() if the designated
// constraints aren't met.
type OfferProcessingInfo_OfferDiscountMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OfferProcessingInfo_OfferDiscountMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OfferProcessingInfo_OfferDiscountMultiError) AllErrors() []error { return m }

// OfferProcessingInfo_OfferDiscountValidationError is the validation error
// returned by OfferProcessingInfo_OfferDiscount.Validate if the designated
// constraints aren't met.
type OfferProcessingInfo_OfferDiscountValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OfferProcessingInfo_OfferDiscountValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OfferProcessingInfo_OfferDiscountValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OfferProcessingInfo_OfferDiscountValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OfferProcessingInfo_OfferDiscountValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OfferProcessingInfo_OfferDiscountValidationError) ErrorName() string {
	return "OfferProcessingInfo_OfferDiscountValidationError"
}

// Error satisfies the builtin error interface
func (e OfferProcessingInfo_OfferDiscountValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOfferProcessingInfo_OfferDiscount.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OfferProcessingInfo_OfferDiscountValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OfferProcessingInfo_OfferDiscountValidationError{}
