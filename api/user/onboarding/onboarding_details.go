package onboarding

// Deprecated: do not use, use SetPreAccCreationAddMoneyMetadataSuccessReason instead
func (s *StageMetadata) SetPreAccCreationMetadataSkippedAfterSavingsAccCreationFlag() {
	if s == nil {
		return
	}

	if s.GetPreAccountCreationAddMoneyMetadata() == nil {
		s.PreAccountCreationAddMoneyMetadata = &PreAccountCreationAddMoneyMetadata{}
	}

	s.PreAccountCreationAddMoneyMetadata.SkippedAfterSavingsAccCreation = true
}

// Deprecated: do not use, use SetPreAccCreationAddMoneyMetadataSuccessReason instead
func (s *StageMetadata) SetPreAccCreationMetadataSkippedAfterSuccessfulAddMoney() {
	if s == nil {
		return
	}

	if s.GetPreAccountCreationAddMoneyMetadata() == nil {
		s.PreAccountCreationAddMoneyMetadata = &PreAccountCreationAddMoneyMetadata{}
	}

	s.PreAccountCreationAddMoneyMetadata.SkippedAfterSuccessfulAddMoney = true
}

func (s *StageMetadata) SetPreAccCreationAddMoneyMetadataSuccessReason(successReason string) {
	if s == nil {
		return
	}

	if successReason == "" {
		return
	}

	if s.GetPreAccountCreationAddMoneyMetadata() == nil {
		s.PreAccountCreationAddMoneyMetadata = &PreAccountCreationAddMoneyMetadata{}
	}

	s.PreAccountCreationAddMoneyMetadata.SuccessReason = successReason
}

func (s *StageMetadata) SetPreAccCreationAddMoneyMetadataSkipReason(skipReason string) {
	if s == nil {
		return
	}

	if skipReason == "" {
		return
	}
	if s.GetPreAccountCreationAddMoneyMetadata() == nil {
		s.PreAccountCreationAddMoneyMetadata = &PreAccountCreationAddMoneyMetadata{}
	}

	s.PreAccountCreationAddMoneyMetadata.SkipReason = skipReason
}

func (s *StageMetadata) SetAddMoneyMetadataSuccessReason(successReason string) {
	if s == nil {
		return
	}

	if successReason == "" {
		return
	}

	if s.GetAddMoneyMetadata() == nil {
		s.AddMoneyMetadata = &AddMoneyMetadata{}
	}

	s.AddMoneyMetadata.SuccessReason = successReason
}

func (s *StageMetadata) SetAddMoneyMetadataSkipReason(skipReason string) {
	if s == nil {
		return
	}

	if skipReason == "" {
		return
	}
	if s.GetAddMoneyMetadata() == nil {
		s.AddMoneyMetadata = &AddMoneyMetadata{}
	}

	s.AddMoneyMetadata.SkipReason = skipReason
}

func (s OnboardingIntent) IsWealthBuilderUserIntent() bool {
	return s == OnboardingIntent_ONBOARDING_INTENT_NET_WORTH || s == OnboardingIntent_ONBOARDING_INTENT_WEALTH_ANALYSER
}
