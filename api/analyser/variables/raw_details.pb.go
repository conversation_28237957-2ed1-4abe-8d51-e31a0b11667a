// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/analyser/variables/raw_details.proto

package variables

import (
	investment "github.com/epifi/gamma/api/analyser/investment"
	mutualfund "github.com/epifi/gamma/api/analyser/variables/mutualfund"
	networth "github.com/epifi/gamma/api/analyser/variables/networth"
	userdeclarations "github.com/epifi/gamma/api/analyser/variables/userdeclarations"
	networth1 "github.com/epifi/gamma/api/insights/networth"
	model "github.com/epifi/gamma/api/insights/user_declaration/model"
	mutualfund1 "github.com/epifi/gamma/api/investment/mutualfund"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RawDetailsName int32

const (
	RawDetailsName_RAW_DETAILS_NAME_UNSPECIFIED                      RawDetailsName = 0
	RawDetailsName_RAW_DETAILS_NAME_USER_DECLARED_MONTHLY_INCOME     RawDetailsName = 1
	RawDetailsName_RAW_DETAILS_NAME_MF_PORTFOLIO_HISTORY             RawDetailsName = 2
	RawDetailsName_RAW_DETAILS_NAME_USER_DECLARED_COMPANY_DETAILS    RawDetailsName = 3
	RawDetailsName_RAW_DETAILS_NAME_USER_DOB                         RawDetailsName = 4
	RawDetailsName_RAW_DETAILS_NAME_MF_PORTFOLIO_ANALYTICS           RawDetailsName = 5
	RawDetailsName_RAW_DETAILS_NAME_PORTFOLIO_SUMMARY                RawDetailsName = 6
	RawDetailsName_RAW_DETAILS_NAME_MF_LOW_TRACKING_ERROR_INDEX_FUND RawDetailsName = 7
	RawDetailsName_RAW_DETAILS_NAME_NETWORTH_DETAILS                 RawDetailsName = 8
	RawDetailsName_RAW_DETAILS_NAME_MF_ASSET_CATEGORY_DETAILS        RawDetailsName = 9
	RawDetailsName_RAW_DETAILS_NAME_MF_INVESTMENT_ACTIVITIES         RawDetailsName = 10
	RawDetailsName_RAW_DETAILS_NAME_ASSETS_DAY_CHANGE                RawDetailsName = 11
	// Fetches the weekly change in the mutual fund and indian stocks portfolio
	RawDetailsName_RAW_DETAILS_NAME_WEEKLY_CHANGE RawDetailsName = 12
)

// Enum value maps for RawDetailsName.
var (
	RawDetailsName_name = map[int32]string{
		0:  "RAW_DETAILS_NAME_UNSPECIFIED",
		1:  "RAW_DETAILS_NAME_USER_DECLARED_MONTHLY_INCOME",
		2:  "RAW_DETAILS_NAME_MF_PORTFOLIO_HISTORY",
		3:  "RAW_DETAILS_NAME_USER_DECLARED_COMPANY_DETAILS",
		4:  "RAW_DETAILS_NAME_USER_DOB",
		5:  "RAW_DETAILS_NAME_MF_PORTFOLIO_ANALYTICS",
		6:  "RAW_DETAILS_NAME_PORTFOLIO_SUMMARY",
		7:  "RAW_DETAILS_NAME_MF_LOW_TRACKING_ERROR_INDEX_FUND",
		8:  "RAW_DETAILS_NAME_NETWORTH_DETAILS",
		9:  "RAW_DETAILS_NAME_MF_ASSET_CATEGORY_DETAILS",
		10: "RAW_DETAILS_NAME_MF_INVESTMENT_ACTIVITIES",
		11: "RAW_DETAILS_NAME_ASSETS_DAY_CHANGE",
		12: "RAW_DETAILS_NAME_WEEKLY_CHANGE",
	}
	RawDetailsName_value = map[string]int32{
		"RAW_DETAILS_NAME_UNSPECIFIED":                      0,
		"RAW_DETAILS_NAME_USER_DECLARED_MONTHLY_INCOME":     1,
		"RAW_DETAILS_NAME_MF_PORTFOLIO_HISTORY":             2,
		"RAW_DETAILS_NAME_USER_DECLARED_COMPANY_DETAILS":    3,
		"RAW_DETAILS_NAME_USER_DOB":                         4,
		"RAW_DETAILS_NAME_MF_PORTFOLIO_ANALYTICS":           5,
		"RAW_DETAILS_NAME_PORTFOLIO_SUMMARY":                6,
		"RAW_DETAILS_NAME_MF_LOW_TRACKING_ERROR_INDEX_FUND": 7,
		"RAW_DETAILS_NAME_NETWORTH_DETAILS":                 8,
		"RAW_DETAILS_NAME_MF_ASSET_CATEGORY_DETAILS":        9,
		"RAW_DETAILS_NAME_MF_INVESTMENT_ACTIVITIES":         10,
		"RAW_DETAILS_NAME_ASSETS_DAY_CHANGE":                11,
		"RAW_DETAILS_NAME_WEEKLY_CHANGE":                    12,
	}
)

func (x RawDetailsName) Enum() *RawDetailsName {
	p := new(RawDetailsName)
	*p = x
	return p
}

func (x RawDetailsName) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RawDetailsName) Descriptor() protoreflect.EnumDescriptor {
	return file_api_analyser_variables_raw_details_proto_enumTypes[0].Descriptor()
}

func (RawDetailsName) Type() protoreflect.EnumType {
	return &file_api_analyser_variables_raw_details_proto_enumTypes[0]
}

func (x RawDetailsName) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RawDetailsName.Descriptor instead.
func (RawDetailsName) EnumDescriptor() ([]byte, []int) {
	return file_api_analyser_variables_raw_details_proto_rawDescGZIP(), []int{0}
}

type RawDetailsDataStatus int32

const (
	RawDetailsDataStatus_RAW_DETAILS_DATA_STATUS_UNSPECIFIED   RawDetailsDataStatus = 0
	RawDetailsDataStatus_RAW_DETAILS_DATA_STATUS_AVAILABLE     RawDetailsDataStatus = 1
	RawDetailsDataStatus_RAW_DETAILS_DATA_STATUS_NOT_AVAILABLE RawDetailsDataStatus = 2
)

// Enum value maps for RawDetailsDataStatus.
var (
	RawDetailsDataStatus_name = map[int32]string{
		0: "RAW_DETAILS_DATA_STATUS_UNSPECIFIED",
		1: "RAW_DETAILS_DATA_STATUS_AVAILABLE",
		2: "RAW_DETAILS_DATA_STATUS_NOT_AVAILABLE",
	}
	RawDetailsDataStatus_value = map[string]int32{
		"RAW_DETAILS_DATA_STATUS_UNSPECIFIED":   0,
		"RAW_DETAILS_DATA_STATUS_AVAILABLE":     1,
		"RAW_DETAILS_DATA_STATUS_NOT_AVAILABLE": 2,
	}
)

func (x RawDetailsDataStatus) Enum() *RawDetailsDataStatus {
	p := new(RawDetailsDataStatus)
	*p = x
	return p
}

func (x RawDetailsDataStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RawDetailsDataStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_analyser_variables_raw_details_proto_enumTypes[1].Descriptor()
}

func (RawDetailsDataStatus) Type() protoreflect.EnumType {
	return &file_api_analyser_variables_raw_details_proto_enumTypes[1]
}

func (x RawDetailsDataStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RawDetailsDataStatus.Descriptor instead.
func (RawDetailsDataStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_analyser_variables_raw_details_proto_rawDescGZIP(), []int{1}
}

// RawDetails contains any of the available raw details which help in building analysis variables
type RawDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RawDetailsName RawDetailsName `protobuf:"varint,1,opt,name=raw_details_name,json=rawDetailsName,proto3,enum=api.analyser.variables.RawDetailsName" json:"raw_details_name,omitempty"`
	// Types that are assignable to Variable:
	//
	//	*RawDetails_UserDeclaredMonthlyIncome
	//	*RawDetails_MfPortfolioHistory
	//	*RawDetails_UserDeclaredCompanyDetails
	//	*RawDetails_UserDob
	//	*RawDetails_MfPortfolioAnalytics
	//	*RawDetails_PortfolioSummary
	//	*RawDetails_MfLowTrackingErrorFundDetails
	//	*RawDetails_NetworthDetails
	//	*RawDetails_MfAssetCategoryDetails
	//	*RawDetails_MfInvestmentActivities
	//	*RawDetails_AssetsDayChange
	Variable             isRawDetails_Variable `protobuf_oneof:"variable"`
	RawDetailsDataStatus RawDetailsDataStatus  `protobuf:"varint,3,opt,name=raw_details_data_status,json=rawDetailsDataStatus,proto3,enum=api.analyser.variables.RawDetailsDataStatus" json:"raw_details_data_status,omitempty"`
}

func (x *RawDetails) Reset() {
	*x = RawDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analyser_variables_raw_details_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RawDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RawDetails) ProtoMessage() {}

func (x *RawDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_analyser_variables_raw_details_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RawDetails.ProtoReflect.Descriptor instead.
func (*RawDetails) Descriptor() ([]byte, []int) {
	return file_api_analyser_variables_raw_details_proto_rawDescGZIP(), []int{0}
}

func (x *RawDetails) GetRawDetailsName() RawDetailsName {
	if x != nil {
		return x.RawDetailsName
	}
	return RawDetailsName_RAW_DETAILS_NAME_UNSPECIFIED
}

func (m *RawDetails) GetVariable() isRawDetails_Variable {
	if m != nil {
		return m.Variable
	}
	return nil
}

func (x *RawDetails) GetUserDeclaredMonthlyIncome() *UserDeclaredMonthlyIncome {
	if x, ok := x.GetVariable().(*RawDetails_UserDeclaredMonthlyIncome); ok {
		return x.UserDeclaredMonthlyIncome
	}
	return nil
}

func (x *RawDetails) GetMfPortfolioHistory() *MfPortfolioHistory {
	if x, ok := x.GetVariable().(*RawDetails_MfPortfolioHistory); ok {
		return x.MfPortfolioHistory
	}
	return nil
}

func (x *RawDetails) GetUserDeclaredCompanyDetails() *UserDeclaredCompanyDetails {
	if x, ok := x.GetVariable().(*RawDetails_UserDeclaredCompanyDetails); ok {
		return x.UserDeclaredCompanyDetails
	}
	return nil
}

func (x *RawDetails) GetUserDob() *userdeclarations.UserDob {
	if x, ok := x.GetVariable().(*RawDetails_UserDob); ok {
		return x.UserDob
	}
	return nil
}

func (x *RawDetails) GetMfPortfolioAnalytics() *MfPortfolioAnalytics {
	if x, ok := x.GetVariable().(*RawDetails_MfPortfolioAnalytics); ok {
		return x.MfPortfolioAnalytics
	}
	return nil
}

func (x *RawDetails) GetPortfolioSummary() *networth.PortfolioSummary {
	if x, ok := x.GetVariable().(*RawDetails_PortfolioSummary); ok {
		return x.PortfolioSummary
	}
	return nil
}

func (x *RawDetails) GetMfLowTrackingErrorFundDetails() *MfLowTrackingErrorFundDetails {
	if x, ok := x.GetVariable().(*RawDetails_MfLowTrackingErrorFundDetails); ok {
		return x.MfLowTrackingErrorFundDetails
	}
	return nil
}

func (x *RawDetails) GetNetworthDetails() *NetworthDetails {
	if x, ok := x.GetVariable().(*RawDetails_NetworthDetails); ok {
		return x.NetworthDetails
	}
	return nil
}

func (x *RawDetails) GetMfAssetCategoryDetails() *mutualfund.MfAssetCategoryDetails {
	if x, ok := x.GetVariable().(*RawDetails_MfAssetCategoryDetails); ok {
		return x.MfAssetCategoryDetails
	}
	return nil
}

func (x *RawDetails) GetMfInvestmentActivities() *mutualfund.MfInvestmentActivities {
	if x, ok := x.GetVariable().(*RawDetails_MfInvestmentActivities); ok {
		return x.MfInvestmentActivities
	}
	return nil
}

func (x *RawDetails) GetAssetsDayChange() *AssetsDayChangeWrapper {
	if x, ok := x.GetVariable().(*RawDetails_AssetsDayChange); ok {
		return x.AssetsDayChange
	}
	return nil
}

func (x *RawDetails) GetRawDetailsDataStatus() RawDetailsDataStatus {
	if x != nil {
		return x.RawDetailsDataStatus
	}
	return RawDetailsDataStatus_RAW_DETAILS_DATA_STATUS_UNSPECIFIED
}

type isRawDetails_Variable interface {
	isRawDetails_Variable()
}

type RawDetails_UserDeclaredMonthlyIncome struct {
	// RAW_DETAILS_NAME_USER_DECLARED_MONTHLY_INCOME this will contain the raw details of the user declared monthly salary
	UserDeclaredMonthlyIncome *UserDeclaredMonthlyIncome `protobuf:"bytes,2,opt,name=user_declared_monthly_income,json=userDeclaredMonthlyIncome,proto3,oneof"`
}

type RawDetails_MfPortfolioHistory struct {
	// RAW_DETAILS_NAME_MF_PORTFOLIO_HISTORY this will contain the raw details of the mutual fund portfolio history
	MfPortfolioHistory *MfPortfolioHistory `protobuf:"bytes,4,opt,name=mf_portfolio_history,json=mfPortfolioHistory,proto3,oneof"`
}

type RawDetails_UserDeclaredCompanyDetails struct {
	// RAW_DETAILS_NAME_USER_DECLARED_COMPANY_DETAILS
	UserDeclaredCompanyDetails *UserDeclaredCompanyDetails `protobuf:"bytes,5,opt,name=user_declared_company_details,json=userDeclaredCompanyDetails,proto3,oneof"`
}

type RawDetails_UserDob struct {
	// RAW_DETAILS_NAME_USER_DOB
	UserDob *userdeclarations.UserDob `protobuf:"bytes,6,opt,name=user_dob,json=userDob,proto3,oneof"`
}

type RawDetails_MfPortfolioAnalytics struct {
	// RAW_DETAILS_MF_PORTFOLIO_ANALYTICS will contain over all mf portfolio and scheme level analytics
	MfPortfolioAnalytics *MfPortfolioAnalytics `protobuf:"bytes,7,opt,name=mf_portfolio_analytics,json=mfPortfolioAnalytics,proto3,oneof"`
}

type RawDetails_PortfolioSummary struct {
	// RAW_DETAILS_NAME_PORTFOLIO_SUMMARY will contain daily portfolio change summary
	PortfolioSummary *networth.PortfolioSummary `protobuf:"bytes,8,opt,name=portfolio_summary,json=portfolioSummary,proto3,oneof"`
}

type RawDetails_MfLowTrackingErrorFundDetails struct {
	// RAW_DETAILS_NAME_MF_LOW_TRACKING_ERROR_INDEX_FUND
	MfLowTrackingErrorFundDetails *MfLowTrackingErrorFundDetails `protobuf:"bytes,9,opt,name=mf_low_tracking_error_fund_details,json=mfLowTrackingErrorFundDetails,proto3,oneof"`
}

type RawDetails_NetworthDetails struct {
	// RAW_DETAILS_NAME_USER_CONNECTED_ASSETS_DETAILS
	NetworthDetails *NetworthDetails `protobuf:"bytes,10,opt,name=networth_details,json=networthDetails,proto3,oneof"`
}

type RawDetails_MfAssetCategoryDetails struct {
	// RAW_DETAILS_NAME_MF_ASSET_CATEGORY_DETAILS
	MfAssetCategoryDetails *mutualfund.MfAssetCategoryDetails `protobuf:"bytes,11,opt,name=mf_asset_category_details,json=mfAssetCategoryDetails,proto3,oneof"`
}

type RawDetails_MfInvestmentActivities struct {
	// RAW_DETAILS_NAME_MF_INVESTMENT_ACTIVITIES
	MfInvestmentActivities *mutualfund.MfInvestmentActivities `protobuf:"bytes,12,opt,name=mf_investment_activities,json=mfInvestmentActivities,proto3,oneof"`
}

type RawDetails_AssetsDayChange struct {
	// RAW_DETAILS_NAME_ASSETS_DAY_CHANGE
	AssetsDayChange *AssetsDayChangeWrapper `protobuf:"bytes,13,opt,name=assets_day_change,json=assetsDayChange,proto3,oneof"`
}

func (*RawDetails_UserDeclaredMonthlyIncome) isRawDetails_Variable() {}

func (*RawDetails_MfPortfolioHistory) isRawDetails_Variable() {}

func (*RawDetails_UserDeclaredCompanyDetails) isRawDetails_Variable() {}

func (*RawDetails_UserDob) isRawDetails_Variable() {}

func (*RawDetails_MfPortfolioAnalytics) isRawDetails_Variable() {}

func (*RawDetails_PortfolioSummary) isRawDetails_Variable() {}

func (*RawDetails_MfLowTrackingErrorFundDetails) isRawDetails_Variable() {}

func (*RawDetails_NetworthDetails) isRawDetails_Variable() {}

func (*RawDetails_MfAssetCategoryDetails) isRawDetails_Variable() {}

func (*RawDetails_MfInvestmentActivities) isRawDetails_Variable() {}

func (*RawDetails_AssetsDayChange) isRawDetails_Variable() {}

type AssetsDayChangeWrapper struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Asset response map with insights.networth.enums.AssetType.String() as key
	AssetResponseMap map[string]*networth1.AssetTypeDayChangeResponse `protobuf:"bytes,1,rep,name=asset_response_map,json=assetResponseMap,proto3" json:"asset_response_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// security metadata map for securities/assets in the day changes response
	// key is security_id(internal to us)
	SecurityMetadataMap map[string]*SecurityMetadata `protobuf:"bytes,2,rep,name=security_metadata_map,json=securityMetadataMap,proto3" json:"security_metadata_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *AssetsDayChangeWrapper) Reset() {
	*x = AssetsDayChangeWrapper{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analyser_variables_raw_details_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetsDayChangeWrapper) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetsDayChangeWrapper) ProtoMessage() {}

func (x *AssetsDayChangeWrapper) ProtoReflect() protoreflect.Message {
	mi := &file_api_analyser_variables_raw_details_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetsDayChangeWrapper.ProtoReflect.Descriptor instead.
func (*AssetsDayChangeWrapper) Descriptor() ([]byte, []int) {
	return file_api_analyser_variables_raw_details_proto_rawDescGZIP(), []int{1}
}

func (x *AssetsDayChangeWrapper) GetAssetResponseMap() map[string]*networth1.AssetTypeDayChangeResponse {
	if x != nil {
		return x.AssetResponseMap
	}
	return nil
}

func (x *AssetsDayChangeWrapper) GetSecurityMetadataMap() map[string]*SecurityMetadata {
	if x != nil {
		return x.SecurityMetadataMap
	}
	return nil
}

type SecurityMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LogoUrl      string `protobuf:"bytes,1,opt,name=logo_url,json=logoUrl,proto3" json:"logo_url,omitempty"`
	SecurityName string `protobuf:"bytes,2,opt,name=security_name,json=securityName,proto3" json:"security_name,omitempty"`
}

func (x *SecurityMetadata) Reset() {
	*x = SecurityMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analyser_variables_raw_details_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SecurityMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecurityMetadata) ProtoMessage() {}

func (x *SecurityMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_analyser_variables_raw_details_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecurityMetadata.ProtoReflect.Descriptor instead.
func (*SecurityMetadata) Descriptor() ([]byte, []int) {
	return file_api_analyser_variables_raw_details_proto_rawDescGZIP(), []int{2}
}

func (x *SecurityMetadata) GetLogoUrl() string {
	if x != nil {
		return x.LogoUrl
	}
	return ""
}

func (x *SecurityMetadata) GetSecurityName() string {
	if x != nil {
		return x.SecurityName
	}
	return ""
}

type UserDeclaredMonthlyIncome struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Declaration *model.UserDeclaration `protobuf:"bytes,1,opt,name=declaration,proto3" json:"declaration,omitempty"`
}

func (x *UserDeclaredMonthlyIncome) Reset() {
	*x = UserDeclaredMonthlyIncome{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analyser_variables_raw_details_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserDeclaredMonthlyIncome) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserDeclaredMonthlyIncome) ProtoMessage() {}

func (x *UserDeclaredMonthlyIncome) ProtoReflect() protoreflect.Message {
	mi := &file_api_analyser_variables_raw_details_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserDeclaredMonthlyIncome.ProtoReflect.Descriptor instead.
func (*UserDeclaredMonthlyIncome) Descriptor() ([]byte, []int) {
	return file_api_analyser_variables_raw_details_proto_rawDescGZIP(), []int{3}
}

func (x *UserDeclaredMonthlyIncome) GetDeclaration() *model.UserDeclaration {
	if x != nil {
		return x.Declaration
	}
	return nil
}

type MfPortfolioHistory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Details                  []*investment.MFPortfolioDetails `protobuf:"bytes,1,rep,name=details,proto3" json:"details,omitempty"`
	EnrichedPortfolioDetails *investment.PortfolioChanges     `protobuf:"bytes,2,opt,name=enriched_portfolio_details,json=enrichedPortfolioDetails,proto3" json:"enriched_portfolio_details,omitempty"`
}

func (x *MfPortfolioHistory) Reset() {
	*x = MfPortfolioHistory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analyser_variables_raw_details_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MfPortfolioHistory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MfPortfolioHistory) ProtoMessage() {}

func (x *MfPortfolioHistory) ProtoReflect() protoreflect.Message {
	mi := &file_api_analyser_variables_raw_details_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MfPortfolioHistory.ProtoReflect.Descriptor instead.
func (*MfPortfolioHistory) Descriptor() ([]byte, []int) {
	return file_api_analyser_variables_raw_details_proto_rawDescGZIP(), []int{4}
}

func (x *MfPortfolioHistory) GetDetails() []*investment.MFPortfolioDetails {
	if x != nil {
		return x.Details
	}
	return nil
}

func (x *MfPortfolioHistory) GetEnrichedPortfolioDetails() *investment.PortfolioChanges {
	if x != nil {
		return x.EnrichedPortfolioDetails
	}
	return nil
}

type UserDeclaredCompanyDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CompanyDetailsDeclaration *model.UserDeclaration `protobuf:"bytes,1,opt,name=company_details_declaration,json=companyDetailsDeclaration,proto3" json:"company_details_declaration,omitempty"`
}

func (x *UserDeclaredCompanyDetails) Reset() {
	*x = UserDeclaredCompanyDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analyser_variables_raw_details_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserDeclaredCompanyDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserDeclaredCompanyDetails) ProtoMessage() {}

func (x *UserDeclaredCompanyDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_analyser_variables_raw_details_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserDeclaredCompanyDetails.ProtoReflect.Descriptor instead.
func (*UserDeclaredCompanyDetails) Descriptor() ([]byte, []int) {
	return file_api_analyser_variables_raw_details_proto_rawDescGZIP(), []int{5}
}

func (x *UserDeclaredCompanyDetails) GetCompanyDetailsDeclaration() *model.UserDeclaration {
	if x != nil {
		return x.CompanyDetailsDeclaration
	}
	return nil
}

type MfSchemeAnalytics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MutualFundCatalogDetails  *mutualfund1.MutualFund                `protobuf:"bytes,1,opt,name=mutual_fund_catalog_details,json=mutualFundCatalogDetails,proto3" json:"mutual_fund_catalog_details,omitempty"`
	EnrichedMfSchemeAnalytics *investment.EnrichedMfSchemeAnalytics  `protobuf:"bytes,2,opt,name=enriched_mf_scheme_analytics,json=enrichedMfSchemeAnalytics,proto3" json:"enriched_mf_scheme_analytics,omitempty"`
	CategoryAvgDetails        *mutualfund1.MutualFundCategoryAverage `protobuf:"bytes,3,opt,name=category_avg_details,json=categoryAvgDetails,proto3" json:"category_avg_details,omitempty"`
}

func (x *MfSchemeAnalytics) Reset() {
	*x = MfSchemeAnalytics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analyser_variables_raw_details_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MfSchemeAnalytics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MfSchemeAnalytics) ProtoMessage() {}

func (x *MfSchemeAnalytics) ProtoReflect() protoreflect.Message {
	mi := &file_api_analyser_variables_raw_details_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MfSchemeAnalytics.ProtoReflect.Descriptor instead.
func (*MfSchemeAnalytics) Descriptor() ([]byte, []int) {
	return file_api_analyser_variables_raw_details_proto_rawDescGZIP(), []int{6}
}

func (x *MfSchemeAnalytics) GetMutualFundCatalogDetails() *mutualfund1.MutualFund {
	if x != nil {
		return x.MutualFundCatalogDetails
	}
	return nil
}

func (x *MfSchemeAnalytics) GetEnrichedMfSchemeAnalytics() *investment.EnrichedMfSchemeAnalytics {
	if x != nil {
		return x.EnrichedMfSchemeAnalytics
	}
	return nil
}

func (x *MfSchemeAnalytics) GetCategoryAvgDetails() *mutualfund1.MutualFundCategoryAverage {
	if x != nil {
		return x.CategoryAvgDetails
	}
	return nil
}

type MfPortfolioAnalytics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MfSchemeAnalytics          []*MfSchemeAnalytics                     `protobuf:"bytes,1,rep,name=mf_scheme_analytics,json=mfSchemeAnalytics,proto3" json:"mf_scheme_analytics,omitempty"`
	UnknownMfSchemeAnalytics   []*MfSchemeAnalytics                     `protobuf:"bytes,2,rep,name=unknown_mf_scheme_analytics,json=unknownMfSchemeAnalytics,proto3" json:"unknown_mf_scheme_analytics,omitempty"`
	EnrichedPortfolioAnalytics *investment.EnrichedMFPortfolioAnalytics `protobuf:"bytes,3,opt,name=enriched_portfolio_analytics,json=enrichedPortfolioAnalytics,proto3" json:"enriched_portfolio_analytics,omitempty"`
}

func (x *MfPortfolioAnalytics) Reset() {
	*x = MfPortfolioAnalytics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analyser_variables_raw_details_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MfPortfolioAnalytics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MfPortfolioAnalytics) ProtoMessage() {}

func (x *MfPortfolioAnalytics) ProtoReflect() protoreflect.Message {
	mi := &file_api_analyser_variables_raw_details_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MfPortfolioAnalytics.ProtoReflect.Descriptor instead.
func (*MfPortfolioAnalytics) Descriptor() ([]byte, []int) {
	return file_api_analyser_variables_raw_details_proto_rawDescGZIP(), []int{7}
}

func (x *MfPortfolioAnalytics) GetMfSchemeAnalytics() []*MfSchemeAnalytics {
	if x != nil {
		return x.MfSchemeAnalytics
	}
	return nil
}

func (x *MfPortfolioAnalytics) GetUnknownMfSchemeAnalytics() []*MfSchemeAnalytics {
	if x != nil {
		return x.UnknownMfSchemeAnalytics
	}
	return nil
}

func (x *MfPortfolioAnalytics) GetEnrichedPortfolioAnalytics() *investment.EnrichedMFPortfolioAnalytics {
	if x != nil {
		return x.EnrichedPortfolioAnalytics
	}
	return nil
}

type MfLowTrackingErrorFundDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LowTrackingErrorIndexFund *mutualfund1.MutualFund `protobuf:"bytes,1,opt,name=low_tracking_error_index_fund,json=lowTrackingErrorIndexFund,proto3" json:"low_tracking_error_index_fund,omitempty"`
}

func (x *MfLowTrackingErrorFundDetails) Reset() {
	*x = MfLowTrackingErrorFundDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analyser_variables_raw_details_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MfLowTrackingErrorFundDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MfLowTrackingErrorFundDetails) ProtoMessage() {}

func (x *MfLowTrackingErrorFundDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_analyser_variables_raw_details_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MfLowTrackingErrorFundDetails.ProtoReflect.Descriptor instead.
func (*MfLowTrackingErrorFundDetails) Descriptor() ([]byte, []int) {
	return file_api_analyser_variables_raw_details_proto_rawDescGZIP(), []int{8}
}

func (x *MfLowTrackingErrorFundDetails) GetLowTrackingErrorIndexFund() *mutualfund1.MutualFund {
	if x != nil {
		return x.LowTrackingErrorIndexFund
	}
	return nil
}

type NetworthDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetValues []*networth1.AssetValue `protobuf:"bytes,1,rep,name=asset_values,json=assetValues,proto3" json:"asset_values,omitempty"`
}

func (x *NetworthDetails) Reset() {
	*x = NetworthDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analyser_variables_raw_details_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NetworthDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetworthDetails) ProtoMessage() {}

func (x *NetworthDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_analyser_variables_raw_details_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetworthDetails.ProtoReflect.Descriptor instead.
func (*NetworthDetails) Descriptor() ([]byte, []int) {
	return file_api_analyser_variables_raw_details_proto_rawDescGZIP(), []int{9}
}

func (x *NetworthDetails) GetAssetValues() []*networth1.AssetValue {
	if x != nil {
		return x.AssetValues
	}
	return nil
}

var File_api_analyser_variables_raw_details_proto protoreflect.FileDescriptor

var file_api_analyser_variables_raw_details_proto_rawDesc = []byte{
	0x0a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2f, 0x76,
	0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2f, 0x72, 0x61, 0x77, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x16, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c,
	0x65, 0x73, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72,
	0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3e, 0x61, 0x70, 0x69, 0x2f, 0x61,
	0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65,
	0x73, 0x2f, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2f, 0x69, 0x6e, 0x76,
	0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x69, 0x73, 0x74, 0x65,
	0x6e, 0x63, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x37, 0x61, 0x70, 0x69, 0x2f, 0x61,
	0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65,
	0x73, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2f, 0x70, 0x6f, 0x72, 0x74, 0x66,
	0x6f, 0x6c, 0x69, 0x6f, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x40, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72,
	0x2f, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x64,
	0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3a, 0x61, 0x70, 0x69, 0x2f, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x63,
	0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64,
	0x2f, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x5f, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xdd, 0x0a, 0x0a, 0x0a, 0x52, 0x61, 0x77, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x50, 0x0a, 0x10, 0x72, 0x61, 0x77, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x61, 0x72, 0x69, 0x61,
	0x62, 0x6c, 0x65, 0x73, 0x2e, 0x52, 0x61, 0x77, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x4e,
	0x61, 0x6d, 0x65, 0x52, 0x0e, 0x72, 0x61, 0x77, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x74, 0x0a, 0x1c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x63, 0x6c,
	0x61, 0x72, 0x65, 0x64, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x5f, 0x69, 0x6e, 0x63,
	0x6f, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c,
	0x65, 0x73, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x65, 0x64, 0x4d,
	0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x48, 0x00, 0x52, 0x19,
	0x75, 0x73, 0x65, 0x72, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x65, 0x64, 0x4d, 0x6f, 0x6e, 0x74,
	0x68, 0x6c, 0x79, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x12, 0x5e, 0x0a, 0x14, 0x6d, 0x66, 0x5f,
	0x70, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x5f, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72,
	0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e,
	0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73,
	0x2e, 0x4d, 0x66, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x48, 0x69, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x48, 0x00, 0x52, 0x12, 0x6d, 0x66, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c,
	0x69, 0x6f, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x77, 0x0a, 0x1d, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e,
	0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65,
	0x63, 0x6c, 0x61, 0x72, 0x65, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x1a, 0x75, 0x73, 0x65, 0x72, 0x44, 0x65, 0x63, 0x6c,
	0x61, 0x72, 0x65, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x4d, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x64, 0x6f, 0x62, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79,
	0x73, 0x65, 0x72, 0x2e, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x44, 0x6f, 0x62, 0x48, 0x00, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x44, 0x6f,
	0x62, 0x12, 0x64, 0x0a, 0x16, 0x6d, 0x66, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69,
	0x6f, 0x5f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72,
	0x2e, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2e, 0x4d, 0x66, 0x50, 0x6f, 0x72,
	0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x48,
	0x00, 0x52, 0x14, 0x6d, 0x66, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x41, 0x6e,
	0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x12, 0x60, 0x0a, 0x11, 0x70, 0x6f, 0x72, 0x74, 0x66,
	0x6f, 0x6c, 0x69, 0x6f, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65,
	0x72, 0x2e, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x74, 0x68, 0x2e, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x53, 0x75,
	0x6d, 0x6d, 0x61, 0x72, 0x79, 0x48, 0x00, 0x52, 0x10, 0x70, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c,
	0x69, 0x6f, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x82, 0x01, 0x0a, 0x22, 0x6d, 0x66,
	0x5f, 0x6c, 0x6f, 0x77, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x5f, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61,
	0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2e,
	0x4d, 0x66, 0x4c, 0x6f, 0x77, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x46, 0x75, 0x6e, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52,
	0x1d, 0x6d, 0x66, 0x4c, 0x6f, 0x77, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x46, 0x75, 0x6e, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x54,
	0x0a, 0x10, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65,
	0x73, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x48, 0x00, 0x52, 0x0f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x76, 0x0a, 0x19, 0x6d, 0x66, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e,
	0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73,
	0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x4d, 0x66, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x48, 0x00, 0x52, 0x16, 0x6d, 0x66, 0x41, 0x73, 0x73, 0x65, 0x74, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x75, 0x0a, 0x18,
	0x6d, 0x66, 0x5f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x61,
	0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75,
	0x6e, 0x64, 0x2e, 0x4d, 0x66, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x48, 0x00, 0x52, 0x16, 0x6d, 0x66, 0x49,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x69, 0x65, 0x73, 0x12, 0x5c, 0x0a, 0x11, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x5f, 0x64, 0x61,
	0x79, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x61,
	0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x44, 0x61,
	0x79, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x48, 0x00,
	0x52, 0x0f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x44, 0x61, 0x79, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x12, 0x63, 0x0a, 0x17, 0x72, 0x61, 0x77, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65,
	0x72, 0x2e, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2e, 0x52, 0x61, 0x77, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x44, 0x61, 0x74, 0x61, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x14, 0x72, 0x61, 0x77, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x44, 0x61, 0x74, 0x61,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0a, 0x0a, 0x08, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62,
	0x6c, 0x65, 0x22, 0xef, 0x03, 0x0a, 0x16, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x44, 0x61, 0x79,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x12, 0x72, 0x0a,
	0x12, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x70, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c,
	0x65, 0x73, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x44, 0x61, 0x79, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x10, 0x61, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4d, 0x61,
	0x70, 0x12, 0x7b, 0x0a, 0x15, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x6d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x47, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e,
	0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73,
	0x44, 0x61, 0x79, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72,
	0x2e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x13, 0x73, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x79, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x4d, 0x61, 0x70, 0x1a, 0x72,
	0x0a, 0x15, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4d,
	0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x43, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x44, 0x61, 0x79, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x1a, 0x70, 0x0a, 0x18, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x3e, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x76,
	0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0x52, 0x0a, 0x10, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79,
	0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x6c, 0x6f, 0x67, 0x6f,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6c, 0x6f, 0x67, 0x6f,
	0x55, 0x72, 0x6c, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x73, 0x0a, 0x19, 0x55, 0x73, 0x65, 0x72,
	0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x65, 0x64, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x49,
	0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x12, 0x56, 0x0a, 0x0b, 0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x64,
	0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x0b, 0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xbc, 0x01,
	0x0a, 0x12, 0x4d, 0x66, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x48, 0x69, 0x73,
	0x74, 0x6f, 0x72, 0x79, 0x12, 0x41, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72,
	0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x46, 0x50, 0x6f,
	0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x07,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x63, 0x0a, 0x1a, 0x65, 0x6e, 0x72, 0x69, 0x63,
	0x68, 0x65, 0x64, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x6e,
	0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x73, 0x52, 0x18, 0x65, 0x6e, 0x72, 0x69, 0x63, 0x68, 0x65, 0x64, 0x50, 0x6f, 0x72, 0x74,
	0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x92, 0x01, 0x0a,
	0x1a, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x65, 0x64, 0x43, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x74, 0x0a, 0x1b, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x5f, 0x64,
	0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x34, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x63, 0x6c, 0x61,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x19, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x22, 0xd2, 0x02, 0x0a, 0x11, 0x4d, 0x66, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x41, 0x6e,
	0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x12, 0x64, 0x0a, 0x1b, 0x6d, 0x75, 0x74, 0x75, 0x61,
	0x6c, 0x5f, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75,
	0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x4d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x46,
	0x75, 0x6e, 0x64, 0x52, 0x18, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x46, 0x75, 0x6e, 0x64, 0x43,
	0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x6f, 0x0a,
	0x1c, 0x65, 0x6e, 0x72, 0x69, 0x63, 0x68, 0x65, 0x64, 0x5f, 0x6d, 0x66, 0x5f, 0x73, 0x63, 0x68,
	0x65, 0x6d, 0x65, 0x5f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x69,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x45, 0x6e, 0x72, 0x69, 0x63, 0x68,
	0x65, 0x64, 0x4d, 0x66, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74,
	0x69, 0x63, 0x73, 0x52, 0x19, 0x65, 0x6e, 0x72, 0x69, 0x63, 0x68, 0x65, 0x64, 0x4d, 0x66, 0x53,
	0x63, 0x68, 0x65, 0x6d, 0x65, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x12, 0x66,
	0x0a, 0x14, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x61, 0x76, 0x67, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75,
	0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x4d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x46,
	0x75, 0x6e, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x41, 0x76, 0x65, 0x72, 0x61,
	0x67, 0x65, 0x52, 0x12, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x41, 0x76, 0x67, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xd0, 0x02, 0x0a, 0x14, 0x4d, 0x66, 0x50, 0x6f, 0x72,
	0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x12,
	0x59, 0x0a, 0x13, 0x6d, 0x66, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x61, 0x6e, 0x61,
	0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x61, 0x72, 0x69,
	0x61, 0x62, 0x6c, 0x65, 0x73, 0x2e, 0x4d, 0x66, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x41, 0x6e,
	0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x52, 0x11, 0x6d, 0x66, 0x53, 0x63, 0x68, 0x65, 0x6d,
	0x65, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x12, 0x68, 0x0a, 0x1b, 0x75, 0x6e,
	0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x5f, 0x6d, 0x66, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f,
	0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x76,
	0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2e, 0x4d, 0x66, 0x53, 0x63, 0x68, 0x65, 0x6d,
	0x65, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x52, 0x18, 0x75, 0x6e, 0x6b, 0x6e,
	0x6f, 0x77, 0x6e, 0x4d, 0x66, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x41, 0x6e, 0x61, 0x6c, 0x79,
	0x74, 0x69, 0x63, 0x73, 0x12, 0x73, 0x0a, 0x1c, 0x65, 0x6e, 0x72, 0x69, 0x63, 0x68, 0x65, 0x64,
	0x5f, 0x70, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x5f, 0x61, 0x6e, 0x61, 0x6c, 0x79,
	0x74, 0x69, 0x63, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x61, 0x6e, 0x61,
	0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x45, 0x6e, 0x72, 0x69, 0x63, 0x68, 0x65, 0x64, 0x4d, 0x46, 0x50, 0x6f, 0x72, 0x74, 0x66,
	0x6f, 0x6c, 0x69, 0x6f, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x52, 0x1a, 0x65,
	0x6e, 0x72, 0x69, 0x63, 0x68, 0x65, 0x64, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f,
	0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x22, 0x88, 0x01, 0x0a, 0x1d, 0x4d, 0x66,
	0x4c, 0x6f, 0x77, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x46, 0x75, 0x6e, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x67, 0x0a, 0x1d, 0x6c,
	0x6f, 0x77, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f, 0x66, 0x75, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x4d,
	0x75, 0x74, 0x75, 0x61, 0x6c, 0x46, 0x75, 0x6e, 0x64, 0x52, 0x19, 0x6c, 0x6f, 0x77, 0x54, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x49, 0x6e, 0x64, 0x65, 0x78,
	0x46, 0x75, 0x6e, 0x64, 0x22, 0x53, 0x0a, 0x0f, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x40, 0x0a, 0x0c, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74,
	0x68, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0b, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x2a, 0xc1, 0x04, 0x0a, 0x0e, 0x52, 0x61,
	0x77, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x1c,
	0x52, 0x41, 0x57, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x4e, 0x41, 0x4d, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x31,
	0x0a, 0x2d, 0x52, 0x41, 0x57, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x4e, 0x41,
	0x4d, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x43, 0x4c, 0x41, 0x52, 0x45, 0x44,
	0x5f, 0x4d, 0x4f, 0x4e, 0x54, 0x48, 0x4c, 0x59, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x45, 0x10,
	0x01, 0x12, 0x29, 0x0a, 0x25, 0x52, 0x41, 0x57, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53,
	0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4d, 0x46, 0x5f, 0x50, 0x4f, 0x52, 0x54, 0x46, 0x4f, 0x4c,
	0x49, 0x4f, 0x5f, 0x48, 0x49, 0x53, 0x54, 0x4f, 0x52, 0x59, 0x10, 0x02, 0x12, 0x32, 0x0a, 0x2e,
	0x52, 0x41, 0x57, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x4e, 0x41, 0x4d, 0x45,
	0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x43, 0x4c, 0x41, 0x52, 0x45, 0x44, 0x5f, 0x43,
	0x4f, 0x4d, 0x50, 0x41, 0x4e, 0x59, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x03,
	0x12, 0x1d, 0x0a, 0x19, 0x52, 0x41, 0x57, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f,
	0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x44, 0x4f, 0x42, 0x10, 0x04, 0x12,
	0x2b, 0x0a, 0x27, 0x52, 0x41, 0x57, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x4e,
	0x41, 0x4d, 0x45, 0x5f, 0x4d, 0x46, 0x5f, 0x50, 0x4f, 0x52, 0x54, 0x46, 0x4f, 0x4c, 0x49, 0x4f,
	0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x54, 0x49, 0x43, 0x53, 0x10, 0x05, 0x12, 0x26, 0x0a, 0x22,
	0x52, 0x41, 0x57, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x4e, 0x41, 0x4d, 0x45,
	0x5f, 0x50, 0x4f, 0x52, 0x54, 0x46, 0x4f, 0x4c, 0x49, 0x4f, 0x5f, 0x53, 0x55, 0x4d, 0x4d, 0x41,
	0x52, 0x59, 0x10, 0x06, 0x12, 0x35, 0x0a, 0x31, 0x52, 0x41, 0x57, 0x5f, 0x44, 0x45, 0x54, 0x41,
	0x49, 0x4c, 0x53, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4d, 0x46, 0x5f, 0x4c, 0x4f, 0x57, 0x5f,
	0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x49,
	0x4e, 0x44, 0x45, 0x58, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x10, 0x07, 0x12, 0x25, 0x0a, 0x21, 0x52,
	0x41, 0x57, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f,
	0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53,
	0x10, 0x08, 0x12, 0x2e, 0x0a, 0x2a, 0x52, 0x41, 0x57, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c,
	0x53, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4d, 0x46, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f,
	0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53,
	0x10, 0x09, 0x12, 0x2d, 0x0a, 0x29, 0x52, 0x41, 0x57, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c,
	0x53, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4d, 0x46, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54,
	0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x49, 0x45, 0x53, 0x10,
	0x0a, 0x12, 0x26, 0x0a, 0x22, 0x52, 0x41, 0x57, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53,
	0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x53, 0x5f, 0x44, 0x41, 0x59,
	0x5f, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x10, 0x0b, 0x12, 0x22, 0x0a, 0x1e, 0x52, 0x41, 0x57,
	0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x57, 0x45,
	0x45, 0x4b, 0x4c, 0x59, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x10, 0x0c, 0x2a, 0x91, 0x01,
	0x0a, 0x14, 0x52, 0x61, 0x77, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x44, 0x61, 0x74, 0x61,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x27, 0x0a, 0x23, 0x52, 0x41, 0x57, 0x5f, 0x44, 0x45,
	0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x25, 0x0a, 0x21, 0x52, 0x41, 0x57, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x44,
	0x41, 0x54, 0x41, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x56, 0x41, 0x49, 0x4c,
	0x41, 0x42, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x29, 0x0a, 0x25, 0x52, 0x41, 0x57, 0x5f, 0x44, 0x45,
	0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41, 0x42, 0x4c, 0x45, 0x10,
	0x02, 0x42, 0x5e, 0x0a, 0x2d, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c,
	0x65, 0x73, 0x5a, 0x2d, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61,
	0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65,
	0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_analyser_variables_raw_details_proto_rawDescOnce sync.Once
	file_api_analyser_variables_raw_details_proto_rawDescData = file_api_analyser_variables_raw_details_proto_rawDesc
)

func file_api_analyser_variables_raw_details_proto_rawDescGZIP() []byte {
	file_api_analyser_variables_raw_details_proto_rawDescOnce.Do(func() {
		file_api_analyser_variables_raw_details_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_analyser_variables_raw_details_proto_rawDescData)
	})
	return file_api_analyser_variables_raw_details_proto_rawDescData
}

var file_api_analyser_variables_raw_details_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_analyser_variables_raw_details_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_api_analyser_variables_raw_details_proto_goTypes = []interface{}{
	(RawDetailsName)(0),                             // 0: api.analyser.variables.RawDetailsName
	(RawDetailsDataStatus)(0),                       // 1: api.analyser.variables.RawDetailsDataStatus
	(*RawDetails)(nil),                              // 2: api.analyser.variables.RawDetails
	(*AssetsDayChangeWrapper)(nil),                  // 3: api.analyser.variables.AssetsDayChangeWrapper
	(*SecurityMetadata)(nil),                        // 4: api.analyser.variables.SecurityMetadata
	(*UserDeclaredMonthlyIncome)(nil),               // 5: api.analyser.variables.UserDeclaredMonthlyIncome
	(*MfPortfolioHistory)(nil),                      // 6: api.analyser.variables.MfPortfolioHistory
	(*UserDeclaredCompanyDetails)(nil),              // 7: api.analyser.variables.UserDeclaredCompanyDetails
	(*MfSchemeAnalytics)(nil),                       // 8: api.analyser.variables.MfSchemeAnalytics
	(*MfPortfolioAnalytics)(nil),                    // 9: api.analyser.variables.MfPortfolioAnalytics
	(*MfLowTrackingErrorFundDetails)(nil),           // 10: api.analyser.variables.MfLowTrackingErrorFundDetails
	(*NetworthDetails)(nil),                         // 11: api.analyser.variables.NetworthDetails
	nil,                                             // 12: api.analyser.variables.AssetsDayChangeWrapper.AssetResponseMapEntry
	nil,                                             // 13: api.analyser.variables.AssetsDayChangeWrapper.SecurityMetadataMapEntry
	(*userdeclarations.UserDob)(nil),                // 14: api.analyser.variables.userdeclarations.UserDob
	(*networth.PortfolioSummary)(nil),               // 15: api.analyser.variables.networth.PortfolioSummary
	(*mutualfund.MfAssetCategoryDetails)(nil),       // 16: api.analyser.variables.mutualfund.MfAssetCategoryDetails
	(*mutualfund.MfInvestmentActivities)(nil),       // 17: api.analyser.variables.mutualfund.MfInvestmentActivities
	(*model.UserDeclaration)(nil),                   // 18: api.insights.user_declaration.model.UserDeclaration
	(*investment.MFPortfolioDetails)(nil),           // 19: analyser.investment.MFPortfolioDetails
	(*investment.PortfolioChanges)(nil),             // 20: analyser.investment.PortfolioChanges
	(*mutualfund1.MutualFund)(nil),                  // 21: api.investment.mutualfund.MutualFund
	(*investment.EnrichedMfSchemeAnalytics)(nil),    // 22: analyser.investment.EnrichedMfSchemeAnalytics
	(*mutualfund1.MutualFundCategoryAverage)(nil),   // 23: api.investment.mutualfund.MutualFundCategoryAverage
	(*investment.EnrichedMFPortfolioAnalytics)(nil), // 24: analyser.investment.EnrichedMFPortfolioAnalytics
	(*networth1.AssetValue)(nil),                    // 25: insights.networth.AssetValue
	(*networth1.AssetTypeDayChangeResponse)(nil),    // 26: insights.networth.AssetTypeDayChangeResponse
}
var file_api_analyser_variables_raw_details_proto_depIdxs = []int32{
	0,  // 0: api.analyser.variables.RawDetails.raw_details_name:type_name -> api.analyser.variables.RawDetailsName
	5,  // 1: api.analyser.variables.RawDetails.user_declared_monthly_income:type_name -> api.analyser.variables.UserDeclaredMonthlyIncome
	6,  // 2: api.analyser.variables.RawDetails.mf_portfolio_history:type_name -> api.analyser.variables.MfPortfolioHistory
	7,  // 3: api.analyser.variables.RawDetails.user_declared_company_details:type_name -> api.analyser.variables.UserDeclaredCompanyDetails
	14, // 4: api.analyser.variables.RawDetails.user_dob:type_name -> api.analyser.variables.userdeclarations.UserDob
	9,  // 5: api.analyser.variables.RawDetails.mf_portfolio_analytics:type_name -> api.analyser.variables.MfPortfolioAnalytics
	15, // 6: api.analyser.variables.RawDetails.portfolio_summary:type_name -> api.analyser.variables.networth.PortfolioSummary
	10, // 7: api.analyser.variables.RawDetails.mf_low_tracking_error_fund_details:type_name -> api.analyser.variables.MfLowTrackingErrorFundDetails
	11, // 8: api.analyser.variables.RawDetails.networth_details:type_name -> api.analyser.variables.NetworthDetails
	16, // 9: api.analyser.variables.RawDetails.mf_asset_category_details:type_name -> api.analyser.variables.mutualfund.MfAssetCategoryDetails
	17, // 10: api.analyser.variables.RawDetails.mf_investment_activities:type_name -> api.analyser.variables.mutualfund.MfInvestmentActivities
	3,  // 11: api.analyser.variables.RawDetails.assets_day_change:type_name -> api.analyser.variables.AssetsDayChangeWrapper
	1,  // 12: api.analyser.variables.RawDetails.raw_details_data_status:type_name -> api.analyser.variables.RawDetailsDataStatus
	12, // 13: api.analyser.variables.AssetsDayChangeWrapper.asset_response_map:type_name -> api.analyser.variables.AssetsDayChangeWrapper.AssetResponseMapEntry
	13, // 14: api.analyser.variables.AssetsDayChangeWrapper.security_metadata_map:type_name -> api.analyser.variables.AssetsDayChangeWrapper.SecurityMetadataMapEntry
	18, // 15: api.analyser.variables.UserDeclaredMonthlyIncome.declaration:type_name -> api.insights.user_declaration.model.UserDeclaration
	19, // 16: api.analyser.variables.MfPortfolioHistory.details:type_name -> analyser.investment.MFPortfolioDetails
	20, // 17: api.analyser.variables.MfPortfolioHistory.enriched_portfolio_details:type_name -> analyser.investment.PortfolioChanges
	18, // 18: api.analyser.variables.UserDeclaredCompanyDetails.company_details_declaration:type_name -> api.insights.user_declaration.model.UserDeclaration
	21, // 19: api.analyser.variables.MfSchemeAnalytics.mutual_fund_catalog_details:type_name -> api.investment.mutualfund.MutualFund
	22, // 20: api.analyser.variables.MfSchemeAnalytics.enriched_mf_scheme_analytics:type_name -> analyser.investment.EnrichedMfSchemeAnalytics
	23, // 21: api.analyser.variables.MfSchemeAnalytics.category_avg_details:type_name -> api.investment.mutualfund.MutualFundCategoryAverage
	8,  // 22: api.analyser.variables.MfPortfolioAnalytics.mf_scheme_analytics:type_name -> api.analyser.variables.MfSchemeAnalytics
	8,  // 23: api.analyser.variables.MfPortfolioAnalytics.unknown_mf_scheme_analytics:type_name -> api.analyser.variables.MfSchemeAnalytics
	24, // 24: api.analyser.variables.MfPortfolioAnalytics.enriched_portfolio_analytics:type_name -> analyser.investment.EnrichedMFPortfolioAnalytics
	21, // 25: api.analyser.variables.MfLowTrackingErrorFundDetails.low_tracking_error_index_fund:type_name -> api.investment.mutualfund.MutualFund
	25, // 26: api.analyser.variables.NetworthDetails.asset_values:type_name -> insights.networth.AssetValue
	26, // 27: api.analyser.variables.AssetsDayChangeWrapper.AssetResponseMapEntry.value:type_name -> insights.networth.AssetTypeDayChangeResponse
	4,  // 28: api.analyser.variables.AssetsDayChangeWrapper.SecurityMetadataMapEntry.value:type_name -> api.analyser.variables.SecurityMetadata
	29, // [29:29] is the sub-list for method output_type
	29, // [29:29] is the sub-list for method input_type
	29, // [29:29] is the sub-list for extension type_name
	29, // [29:29] is the sub-list for extension extendee
	0,  // [0:29] is the sub-list for field type_name
}

func init() { file_api_analyser_variables_raw_details_proto_init() }
func file_api_analyser_variables_raw_details_proto_init() {
	if File_api_analyser_variables_raw_details_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_analyser_variables_raw_details_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RawDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analyser_variables_raw_details_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetsDayChangeWrapper); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analyser_variables_raw_details_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SecurityMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analyser_variables_raw_details_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserDeclaredMonthlyIncome); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analyser_variables_raw_details_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MfPortfolioHistory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analyser_variables_raw_details_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserDeclaredCompanyDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analyser_variables_raw_details_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MfSchemeAnalytics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analyser_variables_raw_details_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MfPortfolioAnalytics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analyser_variables_raw_details_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MfLowTrackingErrorFundDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analyser_variables_raw_details_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NetworthDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_analyser_variables_raw_details_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*RawDetails_UserDeclaredMonthlyIncome)(nil),
		(*RawDetails_MfPortfolioHistory)(nil),
		(*RawDetails_UserDeclaredCompanyDetails)(nil),
		(*RawDetails_UserDob)(nil),
		(*RawDetails_MfPortfolioAnalytics)(nil),
		(*RawDetails_PortfolioSummary)(nil),
		(*RawDetails_MfLowTrackingErrorFundDetails)(nil),
		(*RawDetails_NetworthDetails)(nil),
		(*RawDetails_MfAssetCategoryDetails)(nil),
		(*RawDetails_MfInvestmentActivities)(nil),
		(*RawDetails_AssetsDayChange)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_analyser_variables_raw_details_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_analyser_variables_raw_details_proto_goTypes,
		DependencyIndexes: file_api_analyser_variables_raw_details_proto_depIdxs,
		EnumInfos:         file_api_analyser_variables_raw_details_proto_enumTypes,
		MessageInfos:      file_api_analyser_variables_raw_details_proto_msgTypes,
	}.Build()
	File_api_analyser_variables_raw_details_proto = out.File
	file_api_analyser_variables_raw_details_proto_rawDesc = nil
	file_api_analyser_variables_raw_details_proto_goTypes = nil
	file_api_analyser_variables_raw_details_proto_depIdxs = nil
}
