// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/analyser/variables/analysis_variables.proto

package variables

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on AnalysisVariable with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AnalysisVariable) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AnalysisVariable with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AnalysisVariableMultiError, or nil if none found.
func (m *AnalysisVariable) ValidateAll() error {
	return m.validate(true)
}

func (m *AnalysisVariable) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AnalysisVariableName

	// no validation rules for AnalysisVariableState

	switch v := m.Variable.(type) {
	case *AnalysisVariable_MfInvestmentActivities:
		if v == nil {
			err := AnalysisVariableValidationError{
				field:  "Variable",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMfInvestmentActivities()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AnalysisVariableValidationError{
						field:  "MfInvestmentActivities",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AnalysisVariableValidationError{
						field:  "MfInvestmentActivities",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMfInvestmentActivities()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AnalysisVariableValidationError{
					field:  "MfInvestmentActivities",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *AnalysisVariable_MfMarketCapDetails:
		if v == nil {
			err := AnalysisVariableValidationError{
				field:  "Variable",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMfMarketCapDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AnalysisVariableValidationError{
						field:  "MfMarketCapDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AnalysisVariableValidationError{
						field:  "MfMarketCapDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMfMarketCapDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AnalysisVariableValidationError{
					field:  "MfMarketCapDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *AnalysisVariable_MfMonthlyInvestmentStatistics:
		if v == nil {
			err := AnalysisVariableValidationError{
				field:  "Variable",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMfMonthlyInvestmentStatistics()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AnalysisVariableValidationError{
						field:  "MfMonthlyInvestmentStatistics",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AnalysisVariableValidationError{
						field:  "MfMonthlyInvestmentStatistics",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMfMonthlyInvestmentStatistics()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AnalysisVariableValidationError{
					field:  "MfMonthlyInvestmentStatistics",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *AnalysisVariable_MfPortfolioPerformanceDetails:
		if v == nil {
			err := AnalysisVariableValidationError{
				field:  "Variable",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMfPortfolioPerformanceDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AnalysisVariableValidationError{
						field:  "MfPortfolioPerformanceDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AnalysisVariableValidationError{
						field:  "MfPortfolioPerformanceDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMfPortfolioPerformanceDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AnalysisVariableValidationError{
					field:  "MfPortfolioPerformanceDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *AnalysisVariable_MfAssetCategoryDetails:
		if v == nil {
			err := AnalysisVariableValidationError{
				field:  "Variable",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMfAssetCategoryDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AnalysisVariableValidationError{
						field:  "MfAssetCategoryDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AnalysisVariableValidationError{
						field:  "MfAssetCategoryDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMfAssetCategoryDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AnalysisVariableValidationError{
					field:  "MfAssetCategoryDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *AnalysisVariable_MfSecretsSchemeAnalytics:
		if v == nil {
			err := AnalysisVariableValidationError{
				field:  "Variable",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMfSecretsSchemeAnalytics()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AnalysisVariableValidationError{
						field:  "MfSecretsSchemeAnalytics",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AnalysisVariableValidationError{
						field:  "MfSecretsSchemeAnalytics",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMfSecretsSchemeAnalytics()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AnalysisVariableValidationError{
					field:  "MfSecretsSchemeAnalytics",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *AnalysisVariable_MfSectorDetails:
		if v == nil {
			err := AnalysisVariableValidationError{
				field:  "Variable",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMfSectorDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AnalysisVariableValidationError{
						field:  "MfSectorDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AnalysisVariableValidationError{
						field:  "MfSectorDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMfSectorDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AnalysisVariableValidationError{
					field:  "MfSectorDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *AnalysisVariable_MfStocksDetails:
		if v == nil {
			err := AnalysisVariableValidationError{
				field:  "Variable",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMfStocksDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AnalysisVariableValidationError{
						field:  "MfStocksDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AnalysisVariableValidationError{
						field:  "MfStocksDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMfStocksDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AnalysisVariableValidationError{
					field:  "MfStocksDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *AnalysisVariable_UserDeclarationMonthlyIncome:
		if v == nil {
			err := AnalysisVariableValidationError{
				field:  "Variable",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUserDeclarationMonthlyIncome()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AnalysisVariableValidationError{
						field:  "UserDeclarationMonthlyIncome",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AnalysisVariableValidationError{
						field:  "UserDeclarationMonthlyIncome",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUserDeclarationMonthlyIncome()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AnalysisVariableValidationError{
					field:  "UserDeclarationMonthlyIncome",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *AnalysisVariable_UserDeclarationCompanyDetails:
		if v == nil {
			err := AnalysisVariableValidationError{
				field:  "Variable",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUserDeclarationCompanyDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AnalysisVariableValidationError{
						field:  "UserDeclarationCompanyDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AnalysisVariableValidationError{
						field:  "UserDeclarationCompanyDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUserDeclarationCompanyDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AnalysisVariableValidationError{
					field:  "UserDeclarationCompanyDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *AnalysisVariable_UserDob:
		if v == nil {
			err := AnalysisVariableValidationError{
				field:  "Variable",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUserDob()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AnalysisVariableValidationError{
						field:  "UserDob",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AnalysisVariableValidationError{
						field:  "UserDob",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUserDob()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AnalysisVariableValidationError{
					field:  "UserDob",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *AnalysisVariable_PortfolioSummary:
		if v == nil {
			err := AnalysisVariableValidationError{
				field:  "Variable",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPortfolioSummary()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AnalysisVariableValidationError{
						field:  "PortfolioSummary",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AnalysisVariableValidationError{
						field:  "PortfolioSummary",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPortfolioSummary()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AnalysisVariableValidationError{
					field:  "PortfolioSummary",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *AnalysisVariable_NetworthDetails:
		if v == nil {
			err := AnalysisVariableValidationError{
				field:  "Variable",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNetworthDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AnalysisVariableValidationError{
						field:  "NetworthDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AnalysisVariableValidationError{
						field:  "NetworthDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNetworthDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AnalysisVariableValidationError{
					field:  "NetworthDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *AnalysisVariable_IndianStocksDistribution:
		if v == nil {
			err := AnalysisVariableValidationError{
				field:  "Variable",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetIndianStocksDistribution()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AnalysisVariableValidationError{
						field:  "IndianStocksDistribution",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AnalysisVariableValidationError{
						field:  "IndianStocksDistribution",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetIndianStocksDistribution()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AnalysisVariableValidationError{
					field:  "IndianStocksDistribution",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *AnalysisVariable_NpsDistribution:
		if v == nil {
			err := AnalysisVariableValidationError{
				field:  "Variable",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNpsDistribution()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AnalysisVariableValidationError{
						field:  "NpsDistribution",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AnalysisVariableValidationError{
						field:  "NpsDistribution",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNpsDistribution()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AnalysisVariableValidationError{
					field:  "NpsDistribution",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *AnalysisVariable_MfWeeklyDistribution:
		if v == nil {
			err := AnalysisVariableValidationError{
				field:  "Variable",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMfWeeklyDistribution()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AnalysisVariableValidationError{
						field:  "MfWeeklyDistribution",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AnalysisVariableValidationError{
						field:  "MfWeeklyDistribution",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMfWeeklyDistribution()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AnalysisVariableValidationError{
					field:  "MfWeeklyDistribution",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *AnalysisVariable_GoldDistribution:
		if v == nil {
			err := AnalysisVariableValidationError{
				field:  "Variable",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetGoldDistribution()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AnalysisVariableValidationError{
						field:  "GoldDistribution",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AnalysisVariableValidationError{
						field:  "GoldDistribution",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetGoldDistribution()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AnalysisVariableValidationError{
					field:  "GoldDistribution",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *AnalysisVariable_IndianStocksWeeklyDistribution:
		if v == nil {
			err := AnalysisVariableValidationError{
				field:  "Variable",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetIndianStocksWeeklyDistribution()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AnalysisVariableValidationError{
						field:  "IndianStocksWeeklyDistribution",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AnalysisVariableValidationError{
						field:  "IndianStocksWeeklyDistribution",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetIndianStocksWeeklyDistribution()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AnalysisVariableValidationError{
					field:  "IndianStocksWeeklyDistribution",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return AnalysisVariableMultiError(errors)
	}

	return nil
}

// AnalysisVariableMultiError is an error wrapping multiple validation errors
// returned by AnalysisVariable.ValidateAll() if the designated constraints
// aren't met.
type AnalysisVariableMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AnalysisVariableMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AnalysisVariableMultiError) AllErrors() []error { return m }

// AnalysisVariableValidationError is the validation error returned by
// AnalysisVariable.Validate if the designated constraints aren't met.
type AnalysisVariableValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AnalysisVariableValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AnalysisVariableValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AnalysisVariableValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AnalysisVariableValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AnalysisVariableValidationError) ErrorName() string { return "AnalysisVariableValidationError" }

// Error satisfies the builtin error interface
func (e AnalysisVariableValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAnalysisVariable.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AnalysisVariableValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AnalysisVariableValidationError{}

// Validate checks the field values on AssetTypeDayChangeResponseWrapper with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *AssetTypeDayChangeResponseWrapper) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AssetTypeDayChangeResponseWrapper
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// AssetTypeDayChangeResponseWrapperMultiError, or nil if none found.
func (m *AssetTypeDayChangeResponseWrapper) ValidateAll() error {
	return m.validate(true)
}

func (m *AssetTypeDayChangeResponseWrapper) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDayChangeResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AssetTypeDayChangeResponseWrapperValidationError{
					field:  "DayChangeResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AssetTypeDayChangeResponseWrapperValidationError{
					field:  "DayChangeResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDayChangeResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AssetTypeDayChangeResponseWrapperValidationError{
				field:  "DayChangeResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetSecurityMetadataMap()))
		i := 0
		for key := range m.GetSecurityMetadataMap() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetSecurityMetadataMap()[key]
			_ = val

			// no validation rules for SecurityMetadataMap[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, AssetTypeDayChangeResponseWrapperValidationError{
							field:  fmt.Sprintf("SecurityMetadataMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, AssetTypeDayChangeResponseWrapperValidationError{
							field:  fmt.Sprintf("SecurityMetadataMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return AssetTypeDayChangeResponseWrapperValidationError{
						field:  fmt.Sprintf("SecurityMetadataMap[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return AssetTypeDayChangeResponseWrapperMultiError(errors)
	}

	return nil
}

// AssetTypeDayChangeResponseWrapperMultiError is an error wrapping multiple
// validation errors returned by
// AssetTypeDayChangeResponseWrapper.ValidateAll() if the designated
// constraints aren't met.
type AssetTypeDayChangeResponseWrapperMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AssetTypeDayChangeResponseWrapperMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AssetTypeDayChangeResponseWrapperMultiError) AllErrors() []error { return m }

// AssetTypeDayChangeResponseWrapperValidationError is the validation error
// returned by AssetTypeDayChangeResponseWrapper.Validate if the designated
// constraints aren't met.
type AssetTypeDayChangeResponseWrapperValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AssetTypeDayChangeResponseWrapperValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AssetTypeDayChangeResponseWrapperValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AssetTypeDayChangeResponseWrapperValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AssetTypeDayChangeResponseWrapperValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AssetTypeDayChangeResponseWrapperValidationError) ErrorName() string {
	return "AssetTypeDayChangeResponseWrapperValidationError"
}

// Error satisfies the builtin error interface
func (e AssetTypeDayChangeResponseWrapperValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAssetTypeDayChangeResponseWrapper.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AssetTypeDayChangeResponseWrapperValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AssetTypeDayChangeResponseWrapperValidationError{}
