// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/analyser/variables/analysis_variables.proto

package variables

import (
	mutualfund "github.com/epifi/gamma/api/analyser/variables/mutualfund"
	networth "github.com/epifi/gamma/api/analyser/variables/networth"
	userdeclarations "github.com/epifi/gamma/api/analyser/variables/userdeclarations"
	networth1 "github.com/epifi/gamma/api/insights/networth"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AnalysisVariableName int32

const (
	AnalysisVariableName_ANALYSIS_VARIABLE_NAME_UNSPECIFIED                               AnalysisVariableName = 0
	AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_INVESTMENT_ACTIVITIES                  AnalysisVariableName = 1
	AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_MARKET_CAP_DETAILS                     AnalysisVariableName = 2
	AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_MONTHLY_INVESTMENT_STATISTICS          AnalysisVariableName = 3
	AnalysisVariableName_ANALYSIS_VARIABLE_NAME_PORTFOLIO_PERFORMANCE                     AnalysisVariableName = 4
	AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_ASSET_CATEGORY_DETAILS                 AnalysisVariableName = 5
	AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS                       AnalysisVariableName = 6
	AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SECTOR_DETAILS                         AnalysisVariableName = 7
	AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_STOCKS_DETAILS                         AnalysisVariableName = 8
	AnalysisVariableName_ANALYSIS_VARIABLE_NAME_USER_DECLARED_MONTHLY_INCOME              AnalysisVariableName = 9
	AnalysisVariableName_ANALYSIS_VARIABLE_NAME_USER_DECLARED_COMPANY_DETAILS             AnalysisVariableName = 10
	AnalysisVariableName_ANALYSIS_VARIABLE_NAME_USER_DOB                                  AnalysisVariableName = 11
	AnalysisVariableName_ANALYSIS_VARIABLE_NAME_PORTFOLIO_SUMMARY                         AnalysisVariableName = 12
	AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_DAILY_MARKET_COMPARISON_WITH_PORTFOLIO AnalysisVariableName = 13
	AnalysisVariableName_ANALYSIS_VARIABLE_NAME_NETWORTH_DETAILS                          AnalysisVariableName = 14
	AnalysisVariableName_ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_ASSETS_DISTRIBUTION         AnalysisVariableName = 15
	AnalysisVariableName_ANALYSIS_VARIABLE_NAME_NPS_ASSETS_DISTRIBUTION                   AnalysisVariableName = 16
	AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_WEEKLY_DISTRIBUTION                    AnalysisVariableName = 17
	AnalysisVariableName_ANALYSIS_VARIABLE_NAME_GOLD_ASSETS_DISTRIBUTION                  AnalysisVariableName = 18
	// Analysis variable for Indian stocks weekly distribution
	AnalysisVariableName_ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_WEEKLY_DISTRIBUTION AnalysisVariableName = 19
)

// Enum value maps for AnalysisVariableName.
var (
	AnalysisVariableName_name = map[int32]string{
		0:  "ANALYSIS_VARIABLE_NAME_UNSPECIFIED",
		1:  "ANALYSIS_VARIABLE_NAME_MF_INVESTMENT_ACTIVITIES",
		2:  "ANALYSIS_VARIABLE_NAME_MF_MARKET_CAP_DETAILS",
		3:  "ANALYSIS_VARIABLE_NAME_MF_MONTHLY_INVESTMENT_STATISTICS",
		4:  "ANALYSIS_VARIABLE_NAME_PORTFOLIO_PERFORMANCE",
		5:  "ANALYSIS_VARIABLE_NAME_MF_ASSET_CATEGORY_DETAILS",
		6:  "ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS",
		7:  "ANALYSIS_VARIABLE_NAME_MF_SECTOR_DETAILS",
		8:  "ANALYSIS_VARIABLE_NAME_MF_STOCKS_DETAILS",
		9:  "ANALYSIS_VARIABLE_NAME_USER_DECLARED_MONTHLY_INCOME",
		10: "ANALYSIS_VARIABLE_NAME_USER_DECLARED_COMPANY_DETAILS",
		11: "ANALYSIS_VARIABLE_NAME_USER_DOB",
		12: "ANALYSIS_VARIABLE_NAME_PORTFOLIO_SUMMARY",
		13: "ANALYSIS_VARIABLE_NAME_MF_DAILY_MARKET_COMPARISON_WITH_PORTFOLIO",
		14: "ANALYSIS_VARIABLE_NAME_NETWORTH_DETAILS",
		15: "ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_ASSETS_DISTRIBUTION",
		16: "ANALYSIS_VARIABLE_NAME_NPS_ASSETS_DISTRIBUTION",
		17: "ANALYSIS_VARIABLE_NAME_MF_WEEKLY_DISTRIBUTION",
		18: "ANALYSIS_VARIABLE_NAME_GOLD_ASSETS_DISTRIBUTION",
		19: "ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_WEEKLY_DISTRIBUTION",
	}
	AnalysisVariableName_value = map[string]int32{
		"ANALYSIS_VARIABLE_NAME_UNSPECIFIED":                               0,
		"ANALYSIS_VARIABLE_NAME_MF_INVESTMENT_ACTIVITIES":                  1,
		"ANALYSIS_VARIABLE_NAME_MF_MARKET_CAP_DETAILS":                     2,
		"ANALYSIS_VARIABLE_NAME_MF_MONTHLY_INVESTMENT_STATISTICS":          3,
		"ANALYSIS_VARIABLE_NAME_PORTFOLIO_PERFORMANCE":                     4,
		"ANALYSIS_VARIABLE_NAME_MF_ASSET_CATEGORY_DETAILS":                 5,
		"ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS":                       6,
		"ANALYSIS_VARIABLE_NAME_MF_SECTOR_DETAILS":                         7,
		"ANALYSIS_VARIABLE_NAME_MF_STOCKS_DETAILS":                         8,
		"ANALYSIS_VARIABLE_NAME_USER_DECLARED_MONTHLY_INCOME":              9,
		"ANALYSIS_VARIABLE_NAME_USER_DECLARED_COMPANY_DETAILS":             10,
		"ANALYSIS_VARIABLE_NAME_USER_DOB":                                  11,
		"ANALYSIS_VARIABLE_NAME_PORTFOLIO_SUMMARY":                         12,
		"ANALYSIS_VARIABLE_NAME_MF_DAILY_MARKET_COMPARISON_WITH_PORTFOLIO": 13,
		"ANALYSIS_VARIABLE_NAME_NETWORTH_DETAILS":                          14,
		"ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_ASSETS_DISTRIBUTION":         15,
		"ANALYSIS_VARIABLE_NAME_NPS_ASSETS_DISTRIBUTION":                   16,
		"ANALYSIS_VARIABLE_NAME_MF_WEEKLY_DISTRIBUTION":                    17,
		"ANALYSIS_VARIABLE_NAME_GOLD_ASSETS_DISTRIBUTION":                  18,
		"ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_WEEKLY_DISTRIBUTION":         19,
	}
)

func (x AnalysisVariableName) Enum() *AnalysisVariableName {
	p := new(AnalysisVariableName)
	*p = x
	return p
}

func (x AnalysisVariableName) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AnalysisVariableName) Descriptor() protoreflect.EnumDescriptor {
	return file_api_analyser_variables_analysis_variables_proto_enumTypes[0].Descriptor()
}

func (AnalysisVariableName) Type() protoreflect.EnumType {
	return &file_api_analyser_variables_analysis_variables_proto_enumTypes[0]
}

func (x AnalysisVariableName) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AnalysisVariableName.Descriptor instead.
func (AnalysisVariableName) EnumDescriptor() ([]byte, []int) {
	return file_api_analyser_variables_analysis_variables_proto_rawDescGZIP(), []int{0}
}

type AnalysisVariableState int32

const (
	AnalysisVariableState_ANALYSIS_VARIABLE_STATE_UNSPECIFIED  AnalysisVariableState = 0
	AnalysisVariableState_ANALYSIS_VARIABLE_STATE_AVAILABLE    AnalysisVariableState = 1
	AnalysisVariableState_ANALYSIS_VARIABLE_STATE_DATA_MISSING AnalysisVariableState = 2
)

// Enum value maps for AnalysisVariableState.
var (
	AnalysisVariableState_name = map[int32]string{
		0: "ANALYSIS_VARIABLE_STATE_UNSPECIFIED",
		1: "ANALYSIS_VARIABLE_STATE_AVAILABLE",
		2: "ANALYSIS_VARIABLE_STATE_DATA_MISSING",
	}
	AnalysisVariableState_value = map[string]int32{
		"ANALYSIS_VARIABLE_STATE_UNSPECIFIED":  0,
		"ANALYSIS_VARIABLE_STATE_AVAILABLE":    1,
		"ANALYSIS_VARIABLE_STATE_DATA_MISSING": 2,
	}
)

func (x AnalysisVariableState) Enum() *AnalysisVariableState {
	p := new(AnalysisVariableState)
	*p = x
	return p
}

func (x AnalysisVariableState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AnalysisVariableState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_analyser_variables_analysis_variables_proto_enumTypes[1].Descriptor()
}

func (AnalysisVariableState) Type() protoreflect.EnumType {
	return &file_api_analyser_variables_analysis_variables_proto_enumTypes[1]
}

func (x AnalysisVariableState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AnalysisVariableState.Descriptor instead.
func (AnalysisVariableState) EnumDescriptor() ([]byte, []int) {
	return file_api_analyser_variables_analysis_variables_proto_rawDescGZIP(), []int{1}
}

// AnalysisVariable contains any of the available analysis variables
type AnalysisVariable struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AnalysisVariableName AnalysisVariableName `protobuf:"varint,1,opt,name=analysis_variable_name,json=analysisVariableName,proto3,enum=api.analyser.variables.AnalysisVariableName" json:"analysis_variable_name,omitempty"`
	// Types that are assignable to Variable:
	//
	//	*AnalysisVariable_MfInvestmentActivities
	//	*AnalysisVariable_MfMarketCapDetails
	//	*AnalysisVariable_MfMonthlyInvestmentStatistics
	//	*AnalysisVariable_MfPortfolioPerformanceDetails
	//	*AnalysisVariable_MfAssetCategoryDetails
	//	*AnalysisVariable_MfSecretsSchemeAnalytics
	//	*AnalysisVariable_MfSectorDetails
	//	*AnalysisVariable_MfStocksDetails
	//	*AnalysisVariable_UserDeclarationMonthlyIncome
	//	*AnalysisVariable_UserDeclarationCompanyDetails
	//	*AnalysisVariable_UserDob
	//	*AnalysisVariable_PortfolioSummary
	//	*AnalysisVariable_NetworthDetails
	//	*AnalysisVariable_IndianStocksDistribution
	//	*AnalysisVariable_NpsDistribution
	//	*AnalysisVariable_MfWeeklyDistribution
	//	*AnalysisVariable_GoldDistribution
	//	*AnalysisVariable_IndianStocksWeeklyDistribution
	Variable isAnalysisVariable_Variable `protobuf_oneof:"variable"`
	// raw details which doesnt exist with epifi system so telling the client to handling this accordingly
	UnavailableRawDetailsName []RawDetailsName      `protobuf:"varint,11,rep,packed,name=unavailable_raw_details_name,json=unavailableRawDetailsName,proto3,enum=api.analyser.variables.RawDetailsName" json:"unavailable_raw_details_name,omitempty"`
	AnalysisVariableState     AnalysisVariableState `protobuf:"varint,12,opt,name=analysis_variable_state,json=analysisVariableState,proto3,enum=api.analyser.variables.AnalysisVariableState" json:"analysis_variable_state,omitempty"`
}

func (x *AnalysisVariable) Reset() {
	*x = AnalysisVariable{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analyser_variables_analysis_variables_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnalysisVariable) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalysisVariable) ProtoMessage() {}

func (x *AnalysisVariable) ProtoReflect() protoreflect.Message {
	mi := &file_api_analyser_variables_analysis_variables_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalysisVariable.ProtoReflect.Descriptor instead.
func (*AnalysisVariable) Descriptor() ([]byte, []int) {
	return file_api_analyser_variables_analysis_variables_proto_rawDescGZIP(), []int{0}
}

func (x *AnalysisVariable) GetAnalysisVariableName() AnalysisVariableName {
	if x != nil {
		return x.AnalysisVariableName
	}
	return AnalysisVariableName_ANALYSIS_VARIABLE_NAME_UNSPECIFIED
}

func (m *AnalysisVariable) GetVariable() isAnalysisVariable_Variable {
	if m != nil {
		return m.Variable
	}
	return nil
}

func (x *AnalysisVariable) GetMfInvestmentActivities() *mutualfund.MfInvestmentActivities {
	if x, ok := x.GetVariable().(*AnalysisVariable_MfInvestmentActivities); ok {
		return x.MfInvestmentActivities
	}
	return nil
}

func (x *AnalysisVariable) GetMfMarketCapDetails() *mutualfund.MfMarketCapDetails {
	if x, ok := x.GetVariable().(*AnalysisVariable_MfMarketCapDetails); ok {
		return x.MfMarketCapDetails
	}
	return nil
}

func (x *AnalysisVariable) GetMfMonthlyInvestmentStatistics() *mutualfund.MfMonthlyInvestmentStatistics {
	if x, ok := x.GetVariable().(*AnalysisVariable_MfMonthlyInvestmentStatistics); ok {
		return x.MfMonthlyInvestmentStatistics
	}
	return nil
}

func (x *AnalysisVariable) GetMfPortfolioPerformanceDetails() *mutualfund.MfPortfolioPerformanceDetails {
	if x, ok := x.GetVariable().(*AnalysisVariable_MfPortfolioPerformanceDetails); ok {
		return x.MfPortfolioPerformanceDetails
	}
	return nil
}

func (x *AnalysisVariable) GetMfAssetCategoryDetails() *mutualfund.MfAssetCategoryDetails {
	if x, ok := x.GetVariable().(*AnalysisVariable_MfAssetCategoryDetails); ok {
		return x.MfAssetCategoryDetails
	}
	return nil
}

func (x *AnalysisVariable) GetMfSecretsSchemeAnalytics() *mutualfund.MfSchemeAnalytics {
	if x, ok := x.GetVariable().(*AnalysisVariable_MfSecretsSchemeAnalytics); ok {
		return x.MfSecretsSchemeAnalytics
	}
	return nil
}

func (x *AnalysisVariable) GetMfSectorDetails() *mutualfund.MfSectorDetails {
	if x, ok := x.GetVariable().(*AnalysisVariable_MfSectorDetails); ok {
		return x.MfSectorDetails
	}
	return nil
}

func (x *AnalysisVariable) GetMfStocksDetails() *mutualfund.MfStocksDetails {
	if x, ok := x.GetVariable().(*AnalysisVariable_MfStocksDetails); ok {
		return x.MfStocksDetails
	}
	return nil
}

func (x *AnalysisVariable) GetUserDeclarationMonthlyIncome() *userdeclarations.MonthlyIncome {
	if x, ok := x.GetVariable().(*AnalysisVariable_UserDeclarationMonthlyIncome); ok {
		return x.UserDeclarationMonthlyIncome
	}
	return nil
}

func (x *AnalysisVariable) GetUserDeclarationCompanyDetails() *userdeclarations.CompanyDetails {
	if x, ok := x.GetVariable().(*AnalysisVariable_UserDeclarationCompanyDetails); ok {
		return x.UserDeclarationCompanyDetails
	}
	return nil
}

func (x *AnalysisVariable) GetUserDob() *userdeclarations.UserDob {
	if x, ok := x.GetVariable().(*AnalysisVariable_UserDob); ok {
		return x.UserDob
	}
	return nil
}

func (x *AnalysisVariable) GetPortfolioSummary() *networth.PortfolioSummary {
	if x, ok := x.GetVariable().(*AnalysisVariable_PortfolioSummary); ok {
		return x.PortfolioSummary
	}
	return nil
}

func (x *AnalysisVariable) GetNetworthDetails() *networth.NetworthDetails {
	if x, ok := x.GetVariable().(*AnalysisVariable_NetworthDetails); ok {
		return x.NetworthDetails
	}
	return nil
}

func (x *AnalysisVariable) GetIndianStocksDistribution() *AssetTypeDayChangeResponseWrapper {
	if x, ok := x.GetVariable().(*AnalysisVariable_IndianStocksDistribution); ok {
		return x.IndianStocksDistribution
	}
	return nil
}

func (x *AnalysisVariable) GetNpsDistribution() *AssetTypeDayChangeResponseWrapper {
	if x, ok := x.GetVariable().(*AnalysisVariable_NpsDistribution); ok {
		return x.NpsDistribution
	}
	return nil
}

func (x *AnalysisVariable) GetMfWeeklyDistribution() *AssetTypeDayChangeResponseWrapper {
	if x, ok := x.GetVariable().(*AnalysisVariable_MfWeeklyDistribution); ok {
		return x.MfWeeklyDistribution
	}
	return nil
}

func (x *AnalysisVariable) GetGoldDistribution() *AssetTypeDayChangeResponseWrapper {
	if x, ok := x.GetVariable().(*AnalysisVariable_GoldDistribution); ok {
		return x.GoldDistribution
	}
	return nil
}

func (x *AnalysisVariable) GetIndianStocksWeeklyDistribution() *AssetTypeDayChangeResponseWrapper {
	if x, ok := x.GetVariable().(*AnalysisVariable_IndianStocksWeeklyDistribution); ok {
		return x.IndianStocksWeeklyDistribution
	}
	return nil
}

func (x *AnalysisVariable) GetUnavailableRawDetailsName() []RawDetailsName {
	if x != nil {
		return x.UnavailableRawDetailsName
	}
	return nil
}

func (x *AnalysisVariable) GetAnalysisVariableState() AnalysisVariableState {
	if x != nil {
		return x.AnalysisVariableState
	}
	return AnalysisVariableState_ANALYSIS_VARIABLE_STATE_UNSPECIFIED
}

type isAnalysisVariable_Variable interface {
	isAnalysisVariable_Variable()
}

type AnalysisVariable_MfInvestmentActivities struct {
	// ANALYSIS_VARIABLE_NAME_MF_INVESTMENT_ACTIVITIES
	MfInvestmentActivities *mutualfund.MfInvestmentActivities `protobuf:"bytes,2,opt,name=mf_investment_activities,json=mfInvestmentActivities,proto3,oneof"`
}

type AnalysisVariable_MfMarketCapDetails struct {
	// ANALYSIS_VARIABLE_NAME_MF_MARKET_CAP_DETAILS
	MfMarketCapDetails *mutualfund.MfMarketCapDetails `protobuf:"bytes,3,opt,name=mf_market_cap_details,json=mfMarketCapDetails,proto3,oneof"`
}

type AnalysisVariable_MfMonthlyInvestmentStatistics struct {
	// ANALYSIS_VARIABLE_NAME_MF_MONTHLY_INVESTMENT_STATISTICS
	MfMonthlyInvestmentStatistics *mutualfund.MfMonthlyInvestmentStatistics `protobuf:"bytes,4,opt,name=mf_monthly_investment_statistics,json=mfMonthlyInvestmentStatistics,proto3,oneof"`
}

type AnalysisVariable_MfPortfolioPerformanceDetails struct {
	// ANALYSIS_VARIABLE_NAME_PORTFOLIO_PERFORMANCE, ANALYSIS_VARIABLE_NAME_MF_DAILY_MARKET_COMPARISON_WITH_PORTFOLIO
	// we will be using the same variable object for both the enums, diff is
	// ANALYSIS_VARIABLE_NAME_PORTFOLIO_PERFORMANCE -> percentage change for overall portfolio till date,
	// ANALYSIS_VARIABLE_NAME_MF_DAILY_MARKET_COMPARISON_WITH_PORTFOLIO -> daily percentage change for portfolio compared to market daily
	MfPortfolioPerformanceDetails *mutualfund.MfPortfolioPerformanceDetails `protobuf:"bytes,5,opt,name=mf_portfolio_performance_details,json=mfPortfolioPerformanceDetails,proto3,oneof"`
}

type AnalysisVariable_MfAssetCategoryDetails struct {
	// ANALYSIS_VARIABLE_NAME_MF_ASSET_CATEGORY_DETAILS
	MfAssetCategoryDetails *mutualfund.MfAssetCategoryDetails `protobuf:"bytes,6,opt,name=mf_asset_category_details,json=mfAssetCategoryDetails,proto3,oneof"`
}

type AnalysisVariable_MfSecretsSchemeAnalytics struct {
	// ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS
	MfSecretsSchemeAnalytics *mutualfund.MfSchemeAnalytics `protobuf:"bytes,7,opt,name=mf_secrets_scheme_analytics,json=mfSecretsSchemeAnalytics,proto3,oneof"`
}

type AnalysisVariable_MfSectorDetails struct {
	// ANALYSIS_VARIABLE_NAME_MF_SECTOR_DETAILS
	MfSectorDetails *mutualfund.MfSectorDetails `protobuf:"bytes,8,opt,name=mf_sector_details,json=mfSectorDetails,proto3,oneof"`
}

type AnalysisVariable_MfStocksDetails struct {
	// ANALYSIS_VARIABLE_NAME_MF_STOCKS_DETAILS
	MfStocksDetails *mutualfund.MfStocksDetails `protobuf:"bytes,9,opt,name=mf_stocks_details,json=mfStocksDetails,proto3,oneof"`
}

type AnalysisVariable_UserDeclarationMonthlyIncome struct {
	// ANALYSIS_VARIABLE_NAME_USER_DECLARED_MONTHLY_SALARY
	UserDeclarationMonthlyIncome *userdeclarations.MonthlyIncome `protobuf:"bytes,10,opt,name=user_declaration_monthly_income,json=userDeclarationMonthlyIncome,proto3,oneof"`
}

type AnalysisVariable_UserDeclarationCompanyDetails struct {
	// ANALYSIS_VARIABLE_NAME_USER_DECLARED_COMPANY_DETAILS
	UserDeclarationCompanyDetails *userdeclarations.CompanyDetails `protobuf:"bytes,13,opt,name=user_declaration_company_details,json=userDeclarationCompanyDetails,proto3,oneof"`
}

type AnalysisVariable_UserDob struct {
	// ANALYSIS_VARIABLE_NAME_USER_DOB
	UserDob *userdeclarations.UserDob `protobuf:"bytes,14,opt,name=user_dob,json=userDob,proto3,oneof"`
}

type AnalysisVariable_PortfolioSummary struct {
	// ANALYSIS_VARIABLE_NAME_PORTFOLIO_SUMMARY
	PortfolioSummary *networth.PortfolioSummary `protobuf:"bytes,15,opt,name=portfolio_summary,json=portfolioSummary,proto3,oneof"`
}

type AnalysisVariable_NetworthDetails struct {
	// ANALYSIS_VARIABLE_NAME_NETWORTH_DETAILS
	NetworthDetails *networth.NetworthDetails `protobuf:"bytes,16,opt,name=networth_details,json=networthDetails,proto3,oneof"`
}

type AnalysisVariable_IndianStocksDistribution struct {
	// ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_ASSETS_DISTRIBUTION
	IndianStocksDistribution *AssetTypeDayChangeResponseWrapper `protobuf:"bytes,17,opt,name=indian_stocks_distribution,json=indianStocksDistribution,proto3,oneof"`
}

type AnalysisVariable_NpsDistribution struct {
	// ANALYSIS_VARIABLE_NAME_NPS_ASSETS_DISTRIBUTION
	NpsDistribution *AssetTypeDayChangeResponseWrapper `protobuf:"bytes,18,opt,name=nps_distribution,json=npsDistribution,proto3,oneof"`
}

type AnalysisVariable_MfWeeklyDistribution struct {
	// ANALYSIS_VARIABLE_NAME_MF_WEEKLY_DISTRIBUTION
	MfWeeklyDistribution *AssetTypeDayChangeResponseWrapper `protobuf:"bytes,19,opt,name=mf_weekly_distribution,json=mfWeeklyDistribution,proto3,oneof"`
}

type AnalysisVariable_GoldDistribution struct {
	// ANALYSIS_VARIABLE_NAME_GOLD_ASSETS_DISTRIBUTION
	GoldDistribution *AssetTypeDayChangeResponseWrapper `protobuf:"bytes,20,opt,name=gold_distribution,json=goldDistribution,proto3,oneof"`
}

type AnalysisVariable_IndianStocksWeeklyDistribution struct {
	// ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_WEEKLY_DISTRIBUTION
	IndianStocksWeeklyDistribution *AssetTypeDayChangeResponseWrapper `protobuf:"bytes,21,opt,name=indian_stocks_weekly_distribution,json=indianStocksWeeklyDistribution,proto3,oneof"`
}

func (*AnalysisVariable_MfInvestmentActivities) isAnalysisVariable_Variable() {}

func (*AnalysisVariable_MfMarketCapDetails) isAnalysisVariable_Variable() {}

func (*AnalysisVariable_MfMonthlyInvestmentStatistics) isAnalysisVariable_Variable() {}

func (*AnalysisVariable_MfPortfolioPerformanceDetails) isAnalysisVariable_Variable() {}

func (*AnalysisVariable_MfAssetCategoryDetails) isAnalysisVariable_Variable() {}

func (*AnalysisVariable_MfSecretsSchemeAnalytics) isAnalysisVariable_Variable() {}

func (*AnalysisVariable_MfSectorDetails) isAnalysisVariable_Variable() {}

func (*AnalysisVariable_MfStocksDetails) isAnalysisVariable_Variable() {}

func (*AnalysisVariable_UserDeclarationMonthlyIncome) isAnalysisVariable_Variable() {}

func (*AnalysisVariable_UserDeclarationCompanyDetails) isAnalysisVariable_Variable() {}

func (*AnalysisVariable_UserDob) isAnalysisVariable_Variable() {}

func (*AnalysisVariable_PortfolioSummary) isAnalysisVariable_Variable() {}

func (*AnalysisVariable_NetworthDetails) isAnalysisVariable_Variable() {}

func (*AnalysisVariable_IndianStocksDistribution) isAnalysisVariable_Variable() {}

func (*AnalysisVariable_NpsDistribution) isAnalysisVariable_Variable() {}

func (*AnalysisVariable_MfWeeklyDistribution) isAnalysisVariable_Variable() {}

func (*AnalysisVariable_GoldDistribution) isAnalysisVariable_Variable() {}

func (*AnalysisVariable_IndianStocksWeeklyDistribution) isAnalysisVariable_Variable() {}

type AssetTypeDayChangeResponseWrapper struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DayChangeResponse *networth1.AssetTypeDayChangeResponse `protobuf:"bytes,1,opt,name=day_change_response,json=dayChangeResponse,proto3" json:"day_change_response,omitempty"`
	// security metadata map for securities/assets in the day changes response
	// key is security_id(internal to us)
	SecurityMetadataMap map[string]*SecurityMetadata `protobuf:"bytes,2,rep,name=security_metadata_map,json=securityMetadataMap,proto3" json:"security_metadata_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *AssetTypeDayChangeResponseWrapper) Reset() {
	*x = AssetTypeDayChangeResponseWrapper{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analyser_variables_analysis_variables_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetTypeDayChangeResponseWrapper) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetTypeDayChangeResponseWrapper) ProtoMessage() {}

func (x *AssetTypeDayChangeResponseWrapper) ProtoReflect() protoreflect.Message {
	mi := &file_api_analyser_variables_analysis_variables_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetTypeDayChangeResponseWrapper.ProtoReflect.Descriptor instead.
func (*AssetTypeDayChangeResponseWrapper) Descriptor() ([]byte, []int) {
	return file_api_analyser_variables_analysis_variables_proto_rawDescGZIP(), []int{1}
}

func (x *AssetTypeDayChangeResponseWrapper) GetDayChangeResponse() *networth1.AssetTypeDayChangeResponse {
	if x != nil {
		return x.DayChangeResponse
	}
	return nil
}

func (x *AssetTypeDayChangeResponseWrapper) GetSecurityMetadataMap() map[string]*SecurityMetadata {
	if x != nil {
		return x.SecurityMetadataMap
	}
	return nil
}

var File_api_analyser_variables_analysis_variables_proto protoreflect.FileDescriptor

var file_api_analyser_variables_analysis_variables_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2f, 0x76,
	0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69,
	0x73, 0x5f, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x16, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e,
	0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x1a, 0x3e, 0x61, 0x70, 0x69, 0x2f, 0x61,
	0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65,
	0x73, 0x2f, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2f, 0x69, 0x6e, 0x76,
	0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x69, 0x73, 0x74, 0x65,
	0x6e, 0x63, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x37, 0x61, 0x70, 0x69, 0x2f, 0x61,
	0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65,
	0x73, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2f, 0x70, 0x6f, 0x72, 0x74, 0x66,
	0x6f, 0x6c, 0x69, 0x6f, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72,
	0x2f, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2f, 0x72, 0x61, 0x77, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3c, 0x61, 0x70,
	0x69, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x72, 0x69, 0x61,
	0x62, 0x6c, 0x65, 0x73, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3b, 0x61, 0x70, 0x69, 0x2f,
	0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c,
	0x65, 0x73, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x69, 0x6e, 0x63, 0x6f, 0x6d,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x40, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x61,
	0x6c, 0x79, 0x73, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2f,
	0x75, 0x73, 0x65, 0x72, 0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x65, 0x64, 0x5f, 0x64,
	0x61, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe3,
	0x12, 0x0a, 0x10, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x56, 0x61, 0x72, 0x69, 0x61,
	0x62, 0x6c, 0x65, 0x12, 0x62, 0x0a, 0x16, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x5f,
	0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73,
	0x65, 0x72, 0x2e, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2e, 0x41, 0x6e, 0x61,
	0x6c, 0x79, 0x73, 0x69, 0x73, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x52, 0x14, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x56, 0x61, 0x72, 0x69, 0x61,
	0x62, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x75, 0x0a, 0x18, 0x6d, 0x66, 0x5f, 0x69, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x69, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c,
	0x65, 0x73, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x4d, 0x66,
	0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x69, 0x65, 0x73, 0x48, 0x00, 0x52, 0x16, 0x6d, 0x66, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x12, 0x6a,
	0x0a, 0x15, 0x6d, 0x66, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x5f, 0x63, 0x61, 0x70, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x61, 0x72,
	0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e,
	0x64, 0x2e, 0x4d, 0x66, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x43, 0x61, 0x70, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x12, 0x6d, 0x66, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74,
	0x43, 0x61, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x8b, 0x01, 0x0a, 0x20, 0x6d,
	0x66, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x5f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c,
	0x79, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2e, 0x6d,
	0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x4d, 0x66, 0x4d, 0x6f, 0x6e, 0x74,
	0x68, 0x6c, 0x79, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x48, 0x00, 0x52, 0x1d, 0x6d, 0x66, 0x4d, 0x6f, 0x6e,
	0x74, 0x68, 0x6c, 0x79, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x12, 0x8b, 0x01, 0x0a, 0x20, 0x6d, 0x66, 0x5f,
	0x70, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x5f, 0x70, 0x65, 0x72, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73,
	0x65, 0x72, 0x2e, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2e, 0x6d, 0x75, 0x74,
	0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x4d, 0x66, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f,
	0x6c, 0x69, 0x6f, 0x50, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x1d, 0x6d, 0x66, 0x50, 0x6f, 0x72, 0x74, 0x66,
	0x6f, 0x6c, 0x69, 0x6f, 0x50, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x76, 0x0a, 0x19, 0x6d, 0x66, 0x5f, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c,
	0x65, 0x73, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x4d, 0x66,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x16, 0x6d, 0x66, 0x41, 0x73, 0x73, 0x65, 0x74, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x75,
	0x0a, 0x1b, 0x6d, 0x66, 0x5f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x5f, 0x73, 0x63, 0x68,
	0x65, 0x6d, 0x65, 0x5f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73,
	0x65, 0x72, 0x2e, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2e, 0x6d, 0x75, 0x74,
	0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x4d, 0x66, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65,
	0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x48, 0x00, 0x52, 0x18, 0x6d, 0x66, 0x53,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x41, 0x6e, 0x61, 0x6c,
	0x79, 0x74, 0x69, 0x63, 0x73, 0x12, 0x60, 0x0a, 0x11, 0x6d, 0x66, 0x5f, 0x73, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e,
	0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c,
	0x66, 0x75, 0x6e, 0x64, 0x2e, 0x4d, 0x66, 0x53, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x0f, 0x6d, 0x66, 0x53, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x60, 0x0a, 0x11, 0x6d, 0x66, 0x5f, 0x73, 0x74,
	0x6f, 0x63, 0x6b, 0x73, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65,
	0x72, 0x2e, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2e, 0x6d, 0x75, 0x74, 0x75,
	0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x4d, 0x66, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x0f, 0x6d, 0x66, 0x53, 0x74, 0x6f, 0x63,
	0x6b, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x7f, 0x0a, 0x1f, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f,
	0x6e, 0x74, 0x68, 0x6c, 0x79, 0x5f, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65,
	0x72, 0x2e, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x4d, 0x6f, 0x6e,
	0x74, 0x68, 0x6c, 0x79, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x48, 0x00, 0x52, 0x1c, 0x75, 0x73,
	0x65, 0x72, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x6e,
	0x74, 0x68, 0x6c, 0x79, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x12, 0x82, 0x01, 0x0a, 0x20, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c,
	0x79, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00,
	0x52, 0x1d, 0x75, 0x73, 0x65, 0x72, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x4d, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x64, 0x6f, 0x62, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x30, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72,
	0x2e, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x64,
	0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x44, 0x6f, 0x62, 0x48, 0x00, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x44, 0x6f, 0x62, 0x12, 0x60,
	0x0a, 0x11, 0x70, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x5f, 0x73, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c,
	0x65, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x50, 0x6f, 0x72, 0x74,
	0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x48, 0x00, 0x52, 0x10,
	0x70, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79,
	0x12, 0x5d, 0x0a, 0x10, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62,
	0x6c, 0x65, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x4e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x74, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x0f,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x79, 0x0a, 0x1a, 0x69, 0x6e, 0x64, 0x69, 0x61, 0x6e, 0x5f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73,
	0x5f, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73,
	0x65, 0x72, 0x2e, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2e, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x44, 0x61, 0x79, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x48, 0x00,
	0x52, 0x18, 0x69, 0x6e, 0x64, 0x69, 0x61, 0x6e, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x44, 0x69,
	0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x66, 0x0a, 0x10, 0x6e, 0x70,
	0x73, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79,
	0x73, 0x65, 0x72, 0x2e, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2e, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x44, 0x61, 0x79, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x48,
	0x00, 0x52, 0x0f, 0x6e, 0x70, 0x73, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x71, 0x0a, 0x16, 0x6d, 0x66, 0x5f, 0x77, 0x65, 0x65, 0x6b, 0x6c, 0x79, 0x5f,
	0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x13, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x39, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65,
	0x72, 0x2e, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2e, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x44, 0x61, 0x79, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x48, 0x00, 0x52,
	0x14, 0x6d, 0x66, 0x57, 0x65, 0x65, 0x6b, 0x6c, 0x79, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x68, 0x0a, 0x11, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x64, 0x69,
	0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x39, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e,
	0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x44, 0x61, 0x79, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x48, 0x00, 0x52, 0x10, 0x67,
	0x6f, 0x6c, 0x64, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x86, 0x01, 0x0a, 0x21, 0x69, 0x6e, 0x64, 0x69, 0x61, 0x6e, 0x5f, 0x73, 0x74, 0x6f, 0x63, 0x6b,
	0x73, 0x5f, 0x77, 0x65, 0x65, 0x6b, 0x6c, 0x79, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x61, 0x72, 0x69, 0x61,
	0x62, 0x6c, 0x65, 0x73, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x44, 0x61,
	0x79, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x57,
	0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x48, 0x00, 0x52, 0x1e, 0x69, 0x6e, 0x64, 0x69, 0x61, 0x6e,
	0x53, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x57, 0x65, 0x65, 0x6b, 0x6c, 0x79, 0x44, 0x69, 0x73, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x67, 0x0a, 0x1c, 0x75, 0x6e, 0x61, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x72, 0x61, 0x77, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x26,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x61,
	0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2e, 0x52, 0x61, 0x77, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x19, 0x75, 0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x6c, 0x65, 0x52, 0x61, 0x77, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x65, 0x0a, 0x17, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x5f, 0x76, 0x61,
	0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65,
	0x72, 0x2e, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2e, 0x41, 0x6e, 0x61, 0x6c,
	0x79, 0x73, 0x69, 0x73, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x52, 0x15, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x56, 0x61, 0x72, 0x69, 0x61,
	0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x42, 0x0a, 0x0a, 0x08, 0x76, 0x61, 0x72, 0x69,
	0x61, 0x62, 0x6c, 0x65, 0x22, 0xfd, 0x02, 0x0a, 0x21, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x44, 0x61, 0x79, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x12, 0x5d, 0x0a, 0x13, 0x64, 0x61,
	0x79, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x44, 0x61, 0x79, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x11, 0x64, 0x61, 0x79, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x86, 0x01, 0x0a, 0x15, 0x73, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x6d, 0x61, 0x70, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x52, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c,
	0x65, 0x73, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x44, 0x61, 0x79, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x57, 0x72, 0x61,
	0x70, 0x70, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x13, 0x73,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x4d,
	0x61, 0x70, 0x1a, 0x70, 0x0a, 0x18, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x3e, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x76,
	0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x2a, 0xa7, 0x08, 0x0a, 0x14, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69,
	0x73, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a,
	0x22, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x49, 0x53, 0x5f, 0x56, 0x41, 0x52, 0x49, 0x41, 0x42,
	0x4c, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x33, 0x0a, 0x2f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x49,
	0x53, 0x5f, 0x56, 0x41, 0x52, 0x49, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f,
	0x4d, 0x46, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x43,
	0x54, 0x49, 0x56, 0x49, 0x54, 0x49, 0x45, 0x53, 0x10, 0x01, 0x12, 0x30, 0x0a, 0x2c, 0x41, 0x4e,
	0x41, 0x4c, 0x59, 0x53, 0x49, 0x53, 0x5f, 0x56, 0x41, 0x52, 0x49, 0x41, 0x42, 0x4c, 0x45, 0x5f,
	0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4d, 0x46, 0x5f, 0x4d, 0x41, 0x52, 0x4b, 0x45, 0x54, 0x5f, 0x43,
	0x41, 0x50, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x02, 0x12, 0x3b, 0x0a, 0x37,
	0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x49, 0x53, 0x5f, 0x56, 0x41, 0x52, 0x49, 0x41, 0x42, 0x4c,
	0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4d, 0x46, 0x5f, 0x4d, 0x4f, 0x4e, 0x54, 0x48, 0x4c,
	0x59, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x49, 0x53, 0x54, 0x49, 0x43, 0x53, 0x10, 0x03, 0x12, 0x30, 0x0a, 0x2c, 0x41, 0x4e, 0x41,
	0x4c, 0x59, 0x53, 0x49, 0x53, 0x5f, 0x56, 0x41, 0x52, 0x49, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x4e,
	0x41, 0x4d, 0x45, 0x5f, 0x50, 0x4f, 0x52, 0x54, 0x46, 0x4f, 0x4c, 0x49, 0x4f, 0x5f, 0x50, 0x45,
	0x52, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x04, 0x12, 0x34, 0x0a, 0x30, 0x41,
	0x4e, 0x41, 0x4c, 0x59, 0x53, 0x49, 0x53, 0x5f, 0x56, 0x41, 0x52, 0x49, 0x41, 0x42, 0x4c, 0x45,
	0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4d, 0x46, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x43,
	0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10,
	0x05, 0x12, 0x2e, 0x0a, 0x2a, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x49, 0x53, 0x5f, 0x56, 0x41,
	0x52, 0x49, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4d, 0x46, 0x5f, 0x53,
	0x43, 0x48, 0x45, 0x4d, 0x45, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x54, 0x49, 0x43, 0x53, 0x10,
	0x06, 0x12, 0x2c, 0x0a, 0x28, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x49, 0x53, 0x5f, 0x56, 0x41,
	0x52, 0x49, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4d, 0x46, 0x5f, 0x53,
	0x45, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x07, 0x12,
	0x2c, 0x0a, 0x28, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x49, 0x53, 0x5f, 0x56, 0x41, 0x52, 0x49,
	0x41, 0x42, 0x4c, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4d, 0x46, 0x5f, 0x53, 0x54, 0x4f,
	0x43, 0x4b, 0x53, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x08, 0x12, 0x37, 0x0a,
	0x33, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x49, 0x53, 0x5f, 0x56, 0x41, 0x52, 0x49, 0x41, 0x42,
	0x4c, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x43,
	0x4c, 0x41, 0x52, 0x45, 0x44, 0x5f, 0x4d, 0x4f, 0x4e, 0x54, 0x48, 0x4c, 0x59, 0x5f, 0x49, 0x4e,
	0x43, 0x4f, 0x4d, 0x45, 0x10, 0x09, 0x12, 0x38, 0x0a, 0x34, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53,
	0x49, 0x53, 0x5f, 0x56, 0x41, 0x52, 0x49, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45,
	0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x43, 0x4c, 0x41, 0x52, 0x45, 0x44, 0x5f, 0x43,
	0x4f, 0x4d, 0x50, 0x41, 0x4e, 0x59, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x0a,
	0x12, 0x23, 0x0a, 0x1f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x49, 0x53, 0x5f, 0x56, 0x41, 0x52,
	0x49, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f,
	0x44, 0x4f, 0x42, 0x10, 0x0b, 0x12, 0x2c, 0x0a, 0x28, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x49,
	0x53, 0x5f, 0x56, 0x41, 0x52, 0x49, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f,
	0x50, 0x4f, 0x52, 0x54, 0x46, 0x4f, 0x4c, 0x49, 0x4f, 0x5f, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52,
	0x59, 0x10, 0x0c, 0x12, 0x44, 0x0a, 0x40, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x49, 0x53, 0x5f,
	0x56, 0x41, 0x52, 0x49, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4d, 0x46,
	0x5f, 0x44, 0x41, 0x49, 0x4c, 0x59, 0x5f, 0x4d, 0x41, 0x52, 0x4b, 0x45, 0x54, 0x5f, 0x43, 0x4f,
	0x4d, 0x50, 0x41, 0x52, 0x49, 0x53, 0x4f, 0x4e, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f, 0x50, 0x4f,
	0x52, 0x54, 0x46, 0x4f, 0x4c, 0x49, 0x4f, 0x10, 0x0d, 0x12, 0x2b, 0x0a, 0x27, 0x41, 0x4e, 0x41,
	0x4c, 0x59, 0x53, 0x49, 0x53, 0x5f, 0x56, 0x41, 0x52, 0x49, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x4e,
	0x41, 0x4d, 0x45, 0x5f, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x44, 0x45, 0x54,
	0x41, 0x49, 0x4c, 0x53, 0x10, 0x0e, 0x12, 0x3c, 0x0a, 0x38, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53,
	0x49, 0x53, 0x5f, 0x56, 0x41, 0x52, 0x49, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45,
	0x5f, 0x49, 0x4e, 0x44, 0x49, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x41,
	0x53, 0x53, 0x45, 0x54, 0x53, 0x5f, 0x44, 0x49, 0x53, 0x54, 0x52, 0x49, 0x42, 0x55, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0x0f, 0x12, 0x32, 0x0a, 0x2e, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x49, 0x53,
	0x5f, 0x56, 0x41, 0x52, 0x49, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4e,
	0x50, 0x53, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x53, 0x5f, 0x44, 0x49, 0x53, 0x54, 0x52, 0x49,
	0x42, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x10, 0x12, 0x31, 0x0a, 0x2d, 0x41, 0x4e, 0x41, 0x4c,
	0x59, 0x53, 0x49, 0x53, 0x5f, 0x56, 0x41, 0x52, 0x49, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x4e, 0x41,
	0x4d, 0x45, 0x5f, 0x4d, 0x46, 0x5f, 0x57, 0x45, 0x45, 0x4b, 0x4c, 0x59, 0x5f, 0x44, 0x49, 0x53,
	0x54, 0x52, 0x49, 0x42, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x11, 0x12, 0x33, 0x0a, 0x2f, 0x41,
	0x4e, 0x41, 0x4c, 0x59, 0x53, 0x49, 0x53, 0x5f, 0x56, 0x41, 0x52, 0x49, 0x41, 0x42, 0x4c, 0x45,
	0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x47, 0x4f, 0x4c, 0x44, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54,
	0x53, 0x5f, 0x44, 0x49, 0x53, 0x54, 0x52, 0x49, 0x42, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x12,
	0x12, 0x3c, 0x0a, 0x38, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x49, 0x53, 0x5f, 0x56, 0x41, 0x52,
	0x49, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x49, 0x4e, 0x44, 0x49, 0x41,
	0x4e, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x57, 0x45, 0x45, 0x4b, 0x4c, 0x59, 0x5f,
	0x44, 0x49, 0x53, 0x54, 0x52, 0x49, 0x42, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x13, 0x2a, 0x91,
	0x01, 0x0a, 0x15, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x56, 0x61, 0x72, 0x69, 0x61,
	0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x27, 0x0a, 0x23, 0x41, 0x4e, 0x41, 0x4c,
	0x59, 0x53, 0x49, 0x53, 0x5f, 0x56, 0x41, 0x52, 0x49, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x25, 0x0a, 0x21, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x49, 0x53, 0x5f, 0x56, 0x41,
	0x52, 0x49, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x41, 0x56, 0x41,
	0x49, 0x4c, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x28, 0x0a, 0x24, 0x41, 0x4e, 0x41, 0x4c,
	0x59, 0x53, 0x49, 0x53, 0x5f, 0x56, 0x41, 0x52, 0x49, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x45, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4e, 0x47,
	0x10, 0x02, 0x42, 0x5e, 0x0a, 0x2d, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62,
	0x6c, 0x65, 0x73, 0x5a, 0x2d, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c,
	0x65, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_analyser_variables_analysis_variables_proto_rawDescOnce sync.Once
	file_api_analyser_variables_analysis_variables_proto_rawDescData = file_api_analyser_variables_analysis_variables_proto_rawDesc
)

func file_api_analyser_variables_analysis_variables_proto_rawDescGZIP() []byte {
	file_api_analyser_variables_analysis_variables_proto_rawDescOnce.Do(func() {
		file_api_analyser_variables_analysis_variables_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_analyser_variables_analysis_variables_proto_rawDescData)
	})
	return file_api_analyser_variables_analysis_variables_proto_rawDescData
}

var file_api_analyser_variables_analysis_variables_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_analyser_variables_analysis_variables_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_analyser_variables_analysis_variables_proto_goTypes = []interface{}{
	(AnalysisVariableName)(0),                 // 0: api.analyser.variables.AnalysisVariableName
	(AnalysisVariableState)(0),                // 1: api.analyser.variables.AnalysisVariableState
	(*AnalysisVariable)(nil),                  // 2: api.analyser.variables.AnalysisVariable
	(*AssetTypeDayChangeResponseWrapper)(nil), // 3: api.analyser.variables.AssetTypeDayChangeResponseWrapper
	nil, // 4: api.analyser.variables.AssetTypeDayChangeResponseWrapper.SecurityMetadataMapEntry
	(*mutualfund.MfInvestmentActivities)(nil),        // 5: api.analyser.variables.mutualfund.MfInvestmentActivities
	(*mutualfund.MfMarketCapDetails)(nil),            // 6: api.analyser.variables.mutualfund.MfMarketCapDetails
	(*mutualfund.MfMonthlyInvestmentStatistics)(nil), // 7: api.analyser.variables.mutualfund.MfMonthlyInvestmentStatistics
	(*mutualfund.MfPortfolioPerformanceDetails)(nil), // 8: api.analyser.variables.mutualfund.MfPortfolioPerformanceDetails
	(*mutualfund.MfAssetCategoryDetails)(nil),        // 9: api.analyser.variables.mutualfund.MfAssetCategoryDetails
	(*mutualfund.MfSchemeAnalytics)(nil),             // 10: api.analyser.variables.mutualfund.MfSchemeAnalytics
	(*mutualfund.MfSectorDetails)(nil),               // 11: api.analyser.variables.mutualfund.MfSectorDetails
	(*mutualfund.MfStocksDetails)(nil),               // 12: api.analyser.variables.mutualfund.MfStocksDetails
	(*userdeclarations.MonthlyIncome)(nil),           // 13: api.analyser.variables.userdeclarations.MonthlyIncome
	(*userdeclarations.CompanyDetails)(nil),          // 14: api.analyser.variables.userdeclarations.CompanyDetails
	(*userdeclarations.UserDob)(nil),                 // 15: api.analyser.variables.userdeclarations.UserDob
	(*networth.PortfolioSummary)(nil),                // 16: api.analyser.variables.networth.PortfolioSummary
	(*networth.NetworthDetails)(nil),                 // 17: api.analyser.variables.networth.NetworthDetails
	(RawDetailsName)(0),                              // 18: api.analyser.variables.RawDetailsName
	(*networth1.AssetTypeDayChangeResponse)(nil),     // 19: insights.networth.AssetTypeDayChangeResponse
	(*SecurityMetadata)(nil),                         // 20: api.analyser.variables.SecurityMetadata
}
var file_api_analyser_variables_analysis_variables_proto_depIdxs = []int32{
	0,  // 0: api.analyser.variables.AnalysisVariable.analysis_variable_name:type_name -> api.analyser.variables.AnalysisVariableName
	5,  // 1: api.analyser.variables.AnalysisVariable.mf_investment_activities:type_name -> api.analyser.variables.mutualfund.MfInvestmentActivities
	6,  // 2: api.analyser.variables.AnalysisVariable.mf_market_cap_details:type_name -> api.analyser.variables.mutualfund.MfMarketCapDetails
	7,  // 3: api.analyser.variables.AnalysisVariable.mf_monthly_investment_statistics:type_name -> api.analyser.variables.mutualfund.MfMonthlyInvestmentStatistics
	8,  // 4: api.analyser.variables.AnalysisVariable.mf_portfolio_performance_details:type_name -> api.analyser.variables.mutualfund.MfPortfolioPerformanceDetails
	9,  // 5: api.analyser.variables.AnalysisVariable.mf_asset_category_details:type_name -> api.analyser.variables.mutualfund.MfAssetCategoryDetails
	10, // 6: api.analyser.variables.AnalysisVariable.mf_secrets_scheme_analytics:type_name -> api.analyser.variables.mutualfund.MfSchemeAnalytics
	11, // 7: api.analyser.variables.AnalysisVariable.mf_sector_details:type_name -> api.analyser.variables.mutualfund.MfSectorDetails
	12, // 8: api.analyser.variables.AnalysisVariable.mf_stocks_details:type_name -> api.analyser.variables.mutualfund.MfStocksDetails
	13, // 9: api.analyser.variables.AnalysisVariable.user_declaration_monthly_income:type_name -> api.analyser.variables.userdeclarations.MonthlyIncome
	14, // 10: api.analyser.variables.AnalysisVariable.user_declaration_company_details:type_name -> api.analyser.variables.userdeclarations.CompanyDetails
	15, // 11: api.analyser.variables.AnalysisVariable.user_dob:type_name -> api.analyser.variables.userdeclarations.UserDob
	16, // 12: api.analyser.variables.AnalysisVariable.portfolio_summary:type_name -> api.analyser.variables.networth.PortfolioSummary
	17, // 13: api.analyser.variables.AnalysisVariable.networth_details:type_name -> api.analyser.variables.networth.NetworthDetails
	3,  // 14: api.analyser.variables.AnalysisVariable.indian_stocks_distribution:type_name -> api.analyser.variables.AssetTypeDayChangeResponseWrapper
	3,  // 15: api.analyser.variables.AnalysisVariable.nps_distribution:type_name -> api.analyser.variables.AssetTypeDayChangeResponseWrapper
	3,  // 16: api.analyser.variables.AnalysisVariable.mf_weekly_distribution:type_name -> api.analyser.variables.AssetTypeDayChangeResponseWrapper
	3,  // 17: api.analyser.variables.AnalysisVariable.gold_distribution:type_name -> api.analyser.variables.AssetTypeDayChangeResponseWrapper
	3,  // 18: api.analyser.variables.AnalysisVariable.indian_stocks_weekly_distribution:type_name -> api.analyser.variables.AssetTypeDayChangeResponseWrapper
	18, // 19: api.analyser.variables.AnalysisVariable.unavailable_raw_details_name:type_name -> api.analyser.variables.RawDetailsName
	1,  // 20: api.analyser.variables.AnalysisVariable.analysis_variable_state:type_name -> api.analyser.variables.AnalysisVariableState
	19, // 21: api.analyser.variables.AssetTypeDayChangeResponseWrapper.day_change_response:type_name -> insights.networth.AssetTypeDayChangeResponse
	4,  // 22: api.analyser.variables.AssetTypeDayChangeResponseWrapper.security_metadata_map:type_name -> api.analyser.variables.AssetTypeDayChangeResponseWrapper.SecurityMetadataMapEntry
	20, // 23: api.analyser.variables.AssetTypeDayChangeResponseWrapper.SecurityMetadataMapEntry.value:type_name -> api.analyser.variables.SecurityMetadata
	24, // [24:24] is the sub-list for method output_type
	24, // [24:24] is the sub-list for method input_type
	24, // [24:24] is the sub-list for extension type_name
	24, // [24:24] is the sub-list for extension extendee
	0,  // [0:24] is the sub-list for field type_name
}

func init() { file_api_analyser_variables_analysis_variables_proto_init() }
func file_api_analyser_variables_analysis_variables_proto_init() {
	if File_api_analyser_variables_analysis_variables_proto != nil {
		return
	}
	file_api_analyser_variables_raw_details_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_analyser_variables_analysis_variables_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnalysisVariable); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analyser_variables_analysis_variables_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetTypeDayChangeResponseWrapper); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_analyser_variables_analysis_variables_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*AnalysisVariable_MfInvestmentActivities)(nil),
		(*AnalysisVariable_MfMarketCapDetails)(nil),
		(*AnalysisVariable_MfMonthlyInvestmentStatistics)(nil),
		(*AnalysisVariable_MfPortfolioPerformanceDetails)(nil),
		(*AnalysisVariable_MfAssetCategoryDetails)(nil),
		(*AnalysisVariable_MfSecretsSchemeAnalytics)(nil),
		(*AnalysisVariable_MfSectorDetails)(nil),
		(*AnalysisVariable_MfStocksDetails)(nil),
		(*AnalysisVariable_UserDeclarationMonthlyIncome)(nil),
		(*AnalysisVariable_UserDeclarationCompanyDetails)(nil),
		(*AnalysisVariable_UserDob)(nil),
		(*AnalysisVariable_PortfolioSummary)(nil),
		(*AnalysisVariable_NetworthDetails)(nil),
		(*AnalysisVariable_IndianStocksDistribution)(nil),
		(*AnalysisVariable_NpsDistribution)(nil),
		(*AnalysisVariable_MfWeeklyDistribution)(nil),
		(*AnalysisVariable_GoldDistribution)(nil),
		(*AnalysisVariable_IndianStocksWeeklyDistribution)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_analyser_variables_analysis_variables_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_analyser_variables_analysis_variables_proto_goTypes,
		DependencyIndexes: file_api_analyser_variables_analysis_variables_proto_depIdxs,
		EnumInfos:         file_api_analyser_variables_analysis_variables_proto_enumTypes,
		MessageInfos:      file_api_analyser_variables_analysis_variables_proto_msgTypes,
	}.Build()
	File_api_analyser_variables_analysis_variables_proto = out.File
	file_api_analyser_variables_analysis_variables_proto_rawDesc = nil
	file_api_analyser_variables_analysis_variables_proto_goTypes = nil
	file_api_analyser_variables_analysis_variables_proto_depIdxs = nil
}
