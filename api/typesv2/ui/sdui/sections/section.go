package sections

// NewSection creates and returns a new Section instance.
func NewSection() *Section {
	return &Section{}
}

// WithDepthWiseListSection sets the content to a depth-wise list section and returns the instance for method chaining.
func (x *Section) WithDepthWiseListSection(section *DepthWiseListSection) *Section {
	if x == nil {
		return x
	}
	x.Content = &Section_DepthWiseListSection{
		DepthWiseListSection: section,
	}
	return x
}

// WithHorizontalListSection sets the content to a horizontal list section and returns the instance for method chaining.
func (x *Section) WithHorizontalListSection(section *HorizontalListSection) *Section {
	if x == nil {
		return x
	}
	x.Content = &Section_HorizontalListSection{
		HorizontalListSection: section,
	}
	return x
}

// WithVerticalListSection sets the content to a vertical list section and returns the instance for method chaining.
func (x *Section) WithVerticalListSection(section *VerticalListSection) *Section {
	if x == nil {
		return x
	}
	x.Content = &Section_VerticalListSection{
		VerticalListSection: section,
	}
	return x
}

// WithGridListSection sets the content to a grid list section and returns the instance for method chaining.
func (x *Section) WithGridListSection(section *GridListSection) *Section {
	if x == nil {
		return x
	}
	x.Content = &Section_GridListSection{
		GridListSection: section,
	}
	return x
}

// WithExpandableSection sets the content to an expandable section and returns the instance for method chaining.
func (x *Section) WithExpandableSection(section *ExpandableSection) *Section {
	if x == nil {
		return x
	}
	x.Content = &Section_ExpandableSection{
		ExpandableSection: section,
	}
	return x
}
