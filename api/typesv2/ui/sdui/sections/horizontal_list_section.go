package sections

import (
	"github.com/epifi/gamma/api/typesv2/ui/sdui/behaviors"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/properties"
)

// NewHorizontalListSection creates and returns a new HorizontalListSection instance.
func NewHorizontalListSection() *HorizontalListSection {
	return &HorizontalListSection{}
}

// WithScrollable sets whether the section is scrollable and returns the instance for method chaining.
func (x *HorizontalListSection) WithScrollable(isScrollable bool) *HorizontalListSection {
	if x == nil {
		return x
	}
	x.IsScrollable = isScrollable
	return x
}

// WithComponents adds components to the section and returns the instance for method chaining.
func (x *HorizontalListSection) WithComponents(components ...*components.Component) *HorizontalListSection {
	if x == nil {
		return x
	}
	for _, component := range components {
		if component != nil {
			x.Components = append(x.Components, component)
		}
	}
	return x
}

// WithVisualProperties adds visual properties to the section and returns the instance for method chaining.
func (x *HorizontalListSection) WithVisualProperties(visualProperties ...*properties.VisualProperty) *HorizontalListSection {
	if x == nil {
		return x
	}
	for _, visualProperty := range visualProperties {
		if visualProperty != nil {
			x.VisualProperties = append(x.VisualProperties, visualProperty)
		}
	}
	return x
}

// WithVerticalAlignment sets the vertical alignment and returns the instance for method chaining.
func (x *HorizontalListSection) WithVerticalAlignment(alignment HorizontalListSection_VerticalAlignment) *HorizontalListSection {
	if x == nil {
		return x
	}
	x.VerticalAlignment = alignment
	return x
}

// WithInteractionBehaviors adds interaction behaviors to the section and returns the instance for method chaining.
func (x *HorizontalListSection) WithInteractionBehaviors(behaviors ...*behaviors.InteractionBehavior) *HorizontalListSection {
	if x == nil {
		return x
	}
	for _, behavior := range behaviors {
		if behavior != nil {
			x.InteractionBehaviors = append(x.InteractionBehaviors, behavior)
		}
	}
	return x
}

// WithHorizontalArrangement sets the horizontal arrangement and returns the instance for method chaining.
func (x *HorizontalListSection) WithHorizontalArrangement(arrangement HorizontalListSection_HorizontalArrangement) *HorizontalListSection {
	if x == nil {
		return x
	}
	x.HorizontalArrangement = arrangement
	return x
}

// WithListElementOverlapProps sets the list element overlap properties and returns the instance for method chaining.
func (x *HorizontalListSection) WithListElementOverlapProps(props *properties.ListElementOverlapProps) *HorizontalListSection {
	if x == nil || props == nil {
		return x
	}
	x.ListElementOverlapProps = props
	return x
}
