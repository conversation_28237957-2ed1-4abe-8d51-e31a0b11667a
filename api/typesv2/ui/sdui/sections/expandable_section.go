package sections

import (
	"github.com/epifi/gamma/api/typesv2/ui/sdui/behaviors"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/properties"
)

// NewExpandableSection creates and returns a new ExpandableSection instance.
func NewExpandableSection() *ExpandableSection {
	return &ExpandableSection{}
}

// WithHeader sets the header for the expandable section and returns the instance for method chaining.
func (x *ExpandableSection) WithHeader(header *ExpandableSection_Header) *ExpandableSection {
	if x == nil {
		return x
	}
	x.Header = header
	return x
}

// WithExpandableContent sets the expandable content and returns the instance for method chaining.
func (x *ExpandableSection) WithExpandableContent(content *components.Component) *ExpandableSection {
	if x == nil {
		return x
	}
	x.ExpandableContent = content
	return x
}

// WithVisualProperties adds visual properties to the section and returns the instance for method chaining.
func (x *ExpandableSection) WithVisualProperties(visualProperties ...*properties.VisualProperty) *ExpandableSection {
	if x == nil {
		return x
	}
	for _, visualProperty := range visualProperties {
		if visualProperty != nil {
			x.VisualProperties = append(x.VisualProperties, visualProperty)
		}
	}
	return x
}

// WithExpanded sets the expanded state and returns the instance for method chaining.
func (x *ExpandableSection) WithExpanded(isExpanded bool) *ExpandableSection {
	if x == nil {
		return x
	}
	x.IsExpanded = isExpanded
	return x
}

// WithInteractionBehaviors adds interaction behaviors to the section and returns the instance for method chaining.
func (x *ExpandableSection) WithInteractionBehaviors(behaviors ...*behaviors.InteractionBehavior) *ExpandableSection {
	if x == nil {
		return x
	}
	for _, behavior := range behaviors {
		if behavior != nil {
			x.InteractionBehaviors = append(x.InteractionBehaviors, behavior)
		}
	}
	return x
}
