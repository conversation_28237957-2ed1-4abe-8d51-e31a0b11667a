package sections

import (
	"github.com/epifi/gamma/api/typesv2/ui/sdui/behaviors"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/properties"
)

// NewVerticalListSection creates and returns a new VerticalListSection instance.
func NewVerticalListSection() *VerticalListSection {
	return &VerticalListSection{}
}

// WithScrollable sets whether the section is scrollable and returns the instance for method chaining.
func (x *VerticalListSection) WithScrollable(isScrollable bool) *VerticalListSection {
	if x == nil {
		return x
	}
	x.IsScrollable = isScrollable
	return x
}

// WithComponents adds components to the section and returns the instance for method chaining.
func (x *VerticalListSection) WithComponents(components ...*components.Component) *VerticalListSection {
	if x == nil {
		return x
	}
	for _, component := range components {
		if component != nil {
			x.Components = append(x.Components, component)
		}
	}
	return x
}

// WithVisualProperties adds visual properties to the section and returns the instance for method chaining.
func (x *VerticalListSection) WithVisualProperties(visualProperties ...*properties.VisualProperty) *VerticalListSection {
	if x == nil {
		return x
	}
	for _, visualProperty := range visualProperties {
		if visualProperty != nil {
			x.VisualProperties = append(x.VisualProperties, visualProperty)
		}
	}
	return x
}

// WithHorizontalAlignment sets the horizontal alignment and returns the instance for method chaining.
func (x *VerticalListSection) WithHorizontalAlignment(alignment VerticalListSection_HorizontalAlignment) *VerticalListSection {
	if x == nil {
		return x
	}
	x.HorizontalAlignment = alignment
	return x
}

// WithInteractionBehaviors adds interaction behaviors to the section and returns the instance for method chaining.
func (x *VerticalListSection) WithInteractionBehaviors(behaviors ...*behaviors.InteractionBehavior) *VerticalListSection {
	if x == nil {
		return x
	}
	for _, behavior := range behaviors {
		if behavior != nil {
			x.InteractionBehaviors = append(x.InteractionBehaviors, behavior)
		}
	}
	return x
}

// WithLoadBehavior sets the load behavior and returns the instance for method chaining.
func (x *VerticalListSection) WithLoadBehavior(behavior *behaviors.LifecycleBehavior) *VerticalListSection {
	if x == nil {
		return x
	}
	x.LoadBehavior = behavior
	return x
}

// WithVerticalArrangement sets the vertical arrangement and returns the instance for method chaining.
func (x *VerticalListSection) WithVerticalArrangement(arrangement VerticalListSection_VerticalArrangement) *VerticalListSection {
	if x == nil {
		return x
	}
	x.VerticalArrangement = arrangement
	return x
}

// WithListElementOverlapProps sets the list element overlap properties and returns the instance for method chaining.
func (x *VerticalListSection) WithListElementOverlapProps(props *properties.ListElementOverlapProps) *VerticalListSection {
	if x == nil || props == nil {
		return x
	}
	x.ListElementOverlapProps = props
	return x
}
