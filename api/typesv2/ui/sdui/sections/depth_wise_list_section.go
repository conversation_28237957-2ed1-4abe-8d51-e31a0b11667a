package sections

import (
	"github.com/epifi/gamma/api/typesv2/ui/sdui/behaviors"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/properties"
)

// NewDepthWiseListSection creates and returns a new DepthWiseListSection instance.
func NewDepthWiseListSection() *DepthWiseListSection {
	return &DepthWiseListSection{}
}

// WithScrollable sets whether the section is scrollable and returns the instance for method chaining.
func (x *DepthWiseListSection) WithScrollable(isScrollable bool) *DepthWiseListSection {
	if x == nil {
		return x
	}
	x.IsScrollable = isScrollable
	return x
}

// WithComponents adds components to the section and returns the instance for method chaining.
func (x *DepthWiseListSection) WithComponents(components ...*components.Component) *DepthWiseListSection {
	if x == nil {
		return x
	}
	for _, component := range components {
		if component != nil {
			x.Components = append(x.Components, component)
		}
	}
	return x
}

// WithVisualProperties adds visual properties to the section and returns the instance for method chaining.
func (x *DepthWiseListSection) WithVisualProperties(visualProperties ...*properties.VisualProperty) *DepthWiseListSection {
	if x == nil {
		return x
	}
	for _, visualProperty := range visualProperties {
		if visualProperty != nil {
			x.VisualProperties = append(x.VisualProperties, visualProperty)
		}
	}
	return x
}

// WithAlignment sets the depth-wise alignment and returns the instance for method chaining.
func (x *DepthWiseListSection) WithAlignment(alignment DepthWiseListSection_DepthWiseAlignment) *DepthWiseListSection {
	if x == nil {
		return x
	}
	x.Alignment = alignment
	return x
}

// WithInteractionBehaviors adds interaction behaviors to the section and returns the instance for method chaining.
func (x *DepthWiseListSection) WithInteractionBehaviors(behaviors ...*behaviors.InteractionBehavior) *DepthWiseListSection {
	if x == nil {
		return x
	}
	for _, behavior := range behaviors {
		if behavior != nil {
			x.InteractionBehaviors = append(x.InteractionBehaviors, behavior)
		}
	}
	return x
}
