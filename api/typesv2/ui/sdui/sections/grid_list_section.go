package sections

import (
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/properties"
)

// NewGridListSection creates and returns a new GridListSection instance.
func NewGridListSection() *GridListSection {
	return &GridListSection{}
}

// WithComponents adds components to the section and returns the instance for method chaining.
func (x *GridListSection) WithComponents(components ...*components.Component) *GridListSection {
	if x == nil {
		return x
	}
	for _, component := range components {
		if component != nil {
			x.Components = append(x.Components, component)
		}
	}
	return x
}

// WithVisualProperties adds visual properties to the section and returns the instance for method chaining.
func (x *GridListSection) WithVisualProperties(visualProperties ...*properties.VisualProperty) *GridListSection {
	if x == nil {
		return x
	}
	for _, visualProperty := range visualProperties {
		if visualProperty != nil {
			x.VisualProperties = append(x.VisualProperties, visualProperty)
		}
	}
	return x
}

// WithAdaptiveCellSize sets the grid to use adaptive cell sizing and returns the instance for method chaining.
func (x *GridListSection) WithAdaptiveCellSize(size int32) *GridListSection {
	if x == nil {
		return x
	}
	x.GridCells = &GridListSection_Adaptive{
		Adaptive: &GridListSection_CellsAdaptive{
			Size: size,
		},
	}
	return x
}

// WithFixedCellCount sets the grid to use a fixed cell count and returns the instance for method chaining.
func (x *GridListSection) WithFixedCellCount(count int32) *GridListSection {
	if x == nil {
		return x
	}
	x.GridCells = &GridListSection_Fixed{
		Fixed: &GridListSection_CellsFixedCount{
			Count: count,
		},
	}
	return x
}

// WithOrientation sets the grid orientation and returns the instance for method chaining.
func (x *GridListSection) WithOrientation(orientation GridListSection_Orientation) *GridListSection {
	if x == nil {
		return x
	}
	x.Orientation = orientation
	return x
}
