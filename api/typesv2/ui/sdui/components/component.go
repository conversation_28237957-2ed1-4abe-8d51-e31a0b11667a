package components

import (
	"google.golang.org/protobuf/types/known/anypb"

	"github.com/epifi/gamma/api/typesv2/ui/sdui/analytics"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/behaviors"
)

// NewComponent creates and returns a new Component instance.
func NewComponent() *Component {
	return &Component{}
}

// WithContent sets the component content and returns the instance for method chaining.
func (x *Component) WithContent(content *anypb.Any) *Component {
	if x == nil {
		return x
	}
	x.Content = content
	return x
}

// WithLoadBehavior sets the load behavior and returns the instance for method chaining.
func (x *Component) WithLoadBehavior(behavior *behaviors.LifecycleBehavior) *Component {
	if x == nil {
		return x
	}
	x.LoadBehavior = behavior
	return x
}

// WithLoadBehaviorAnalyticsEvent sets the load behavior with analytics event and returns the instance for method chaining.
func (x *Component) WithLoadBehaviorAnalyticsEvent(analyticsEvent *analytics.AnalyticsEvent) *Component {
	if x == nil {
		return x
	}
	x.LoadBehavior = &behaviors.LifecycleBehavior{
		AnalyticsEvent: analyticsEvent,
	}
	return x
}

// WithVisibleBehavior sets the visible behavior and returns the instance for method chaining.
func (x *Component) WithVisibleBehavior(behavior *behaviors.LifecycleBehavior) *Component {
	if x == nil {
		return x
	}
	x.VisibleBehavior = behavior
	return x
}

// WithVisibleBehaviorAnalyticsEvent sets the visible behavior with analytics event and returns the instance for method chaining.
func (x *Component) WithVisibleBehaviorAnalyticsEvent(analyticsEvent *analytics.AnalyticsEvent) *Component {
	if x == nil {
		return x
	}
	x.VisibleBehavior = &behaviors.LifecycleBehavior{
		AnalyticsEvent: analyticsEvent,
	}
	return x
}

// WithInteractionBehaviors adds interaction behaviors to the component and returns the instance for method chaining.
func (x *Component) WithInteractionBehaviors(behaviors ...*behaviors.InteractionBehavior) *Component {
	if x == nil {
		return x
	}
	for _, behavior := range behaviors {
		if behavior != nil {
			x.InteractionBehaviors = append(x.InteractionBehaviors, behavior)
		}
	}
	return x
}
