package behaviors

import (
	anyPb "google.golang.org/protobuf/types/known/anypb"

	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"

	"github.com/epifi/gamma/api/typesv2/ui/sdui/analytics"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/properties"
)

// NewInteractionBehavior creates and returns a new InteractionBehavior instance.
func NewInteractionBehavior() *InteractionBehavior {
	return &InteractionBehavior{}
}

// WithOnClickBehavior sets the click behavior with the specified action and returns the instance for method chaining.
func (x *InteractionBehavior) WithOnClickBehavior(action *anyPb.Any) *InteractionBehavior {
	if x == nil {
		return x
	}
	x.Behavior = &InteractionBehavior_OnClickBehavior{
		OnClickBehavior: &OnClickBehavior{
			Action: action,
		},
	}
	return x
}

// WithOnLongPressBehavior sets the long press behavior with the specified action and returns the instance for method chaining.
func (x *InteractionBehavior) WithOnLongPressBehavior(action *anyPb.Any) *InteractionBehavior {
	if x == nil {
		return x
	}
	x.Behavior = &InteractionBehavior_OnLongPressBehavior{
		OnLongPressBehavior: &OnLongPressBehavior{
			Action: action,
		},
	}
	return x
}

// WithExpandBehavior sets the expand behavior with the specified action and returns the instance for method chaining.
func (x *InteractionBehavior) WithExpandBehavior(action *anyPb.Any) *InteractionBehavior {
	if x == nil {
		return x
	}
	x.Behavior = &InteractionBehavior_ExpandBehavior{
		ExpandBehavior: &OnExpandBehaviour{
			Action: action,
		},
	}
	return x
}

// WithCollapseBehavior sets the collapse behavior with the specified action and returns the instance for method chaining.
func (x *InteractionBehavior) WithCollapseBehavior(action *anyPb.Any) *InteractionBehavior {
	if x == nil {
		return x
	}
	x.Behavior = &InteractionBehavior_CollapseBehavior{
		CollapseBehavior: &OnCollapseBehaviour{
			Action: action,
		},
	}
	return x
}

// WithTooltipBehavior sets the tooltip behavior and returns the instance for method chaining.
func (x *InteractionBehavior) WithTooltipBehavior(tooltipBehavior *TooltipBehavior) *InteractionBehavior {
	if x == nil {
		return x
	}
	x.Behavior = &InteractionBehavior_TooltipBehavior{
		TooltipBehavior: tooltipBehavior,
	}
	return x
}

// WithAnalyticsEvent sets the analytics event and returns the instance for method chaining.
func (x *InteractionBehavior) WithAnalyticsEvent(analyticsEvent *analytics.AnalyticsEvent) *InteractionBehavior {
	if x == nil {
		return x
	}
	x.AnalyticsEvent = analyticsEvent
	return x
}

// NewTooltipBehavior creates and returns a new TooltipBehavior instance.
func NewTooltipBehavior() *TooltipBehavior {
	return &TooltipBehavior{}
}

// WithTooltipText sets the tooltip text and returns the instance for method chaining.
func (x *TooltipBehavior) WithTooltipText(text *common.Text) *TooltipBehavior {
	if x == nil {
		return x
	}
	x.TooltipText = text
	return x
}

// WithTooltipPositionAndOffsetPercentage sets the tooltip position and offset percentage and returns the instance for method chaining.
func (x *TooltipBehavior) WithTooltipPositionAndOffsetPercentage(offsetPercent int32, position TooltipBehavior_TooltipPosition) *TooltipBehavior {
	if x == nil {
		return x
	}
	x.Position = position
	x.TooltipMidOffsetPercent = offsetPercent
	return x
}

// WithBackgroundColour sets the background color and returns the instance for method chaining.
func (x *TooltipBehavior) WithBackgroundColour(bgColor *widget.BackgroundColour) *TooltipBehavior {
	if x == nil {
		return x
	}
	x.BackgroundColour = bgColor
	return x
}

// WithSize sets the size and returns the instance for method chaining.
func (x *TooltipBehavior) WithSize(size *properties.Size) *TooltipBehavior {
	if x == nil {
		return x
	}
	x.Size = size
	return x
}
