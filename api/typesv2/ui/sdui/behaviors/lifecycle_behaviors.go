package behaviors

import "github.com/epifi/gamma/api/typesv2/ui/sdui/analytics"

// NewLifecycleBehavior creates and returns a new LifecycleBehavior instance.
func NewLifecycleBehavior() *LifecycleBehavior {
	return &LifecycleBehavior{}
}

// WithAnalyticsEvent sets the analytics event and returns the instance for method chaining.
func (x *LifecycleBehavior) WithAnalyticsEvent(analyticsEvent *analytics.AnalyticsEvent) *LifecycleBehavior {
	if x == nil {
		return x
	}
	x.AnalyticsEvent = analyticsEvent
	return x
}
