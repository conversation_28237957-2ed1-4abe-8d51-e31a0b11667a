package properties

import (
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
)

// NewVisualProperty creates and returns a new VisualProperty instance.
func NewVisualProperty() *VisualProperty {
	return &VisualProperty{}
}

// WithVisualPropertyBorder sets the border property and returns the instance for method chaining.
func (x *VisualProperty) WithVisualPropertyBorder(border *BorderProperty) *VisualProperty {
	if x == nil {
		return x
	}
	x.Properties = &VisualProperty_Border{
		Border: border,
	}
	return x
}

// WithVisualPropertyPadding sets the padding property with specified values and returns the instance for method chaining.
func (x *VisualProperty) WithVisualPropertyPadding(left, top, right, bottom int32) *VisualProperty {
	if x == nil {
		return x
	}
	x.Properties = &VisualProperty_Padding{
		Padding: &PaddingProperty{
			Left:   left,
			Top:    top,
			Right:  right,
			Bottom: bottom,
		},
	}
	return x
}

// WithVisualPropertyCorner sets the corner radius property with specified values and returns the instance for method chaining.
func (x *VisualProperty) WithVisualPropertyCorner(topLeft, topRight, bottomLeft, bottomRight int32) *VisualProperty {
	if x == nil {
		return x
	}
	x.Properties = &VisualProperty_Corner{
		Corner: &CornerProperty{
			TopLeftCornerRadius:  topLeft,
			TopRightCornerRadius: topRight,
			BottomLeftCorner:     bottomLeft,
			BottomRightCorner:    bottomRight,
		},
	}
	return x
}

// WithVisualPropertyPosition sets the position property with specified coordinates and returns the instance for method chaining.
func (x *VisualProperty) WithVisualPropertyPosition(xAxis, yAxis int32) *VisualProperty {
	if x == nil {
		return x
	}
	x.Properties = &VisualProperty_Position{
		Position: &Position{
			XAxis: xAxis,
			YAxis: yAxis,
		},
	}
	return x
}

// WithVisualPropertyShadow sets the shadow property with specified parameters and returns the instance for method chaining.
func (x *VisualProperty) WithVisualPropertyShadow(height, blur, opacity int32, color *widget.BackgroundColour) *VisualProperty {
	if x == nil {
		return x
	}
	x.Properties = &VisualProperty_Shadow{
		Shadow: widget.GetShadow(height, blur, opacity, color),
	}
	return x
}

// WithVisualPropertyBgColor sets the background color property and returns the instance for method chaining.
func (x *VisualProperty) WithVisualPropertyBgColor(color *widget.BackgroundColour) *VisualProperty {
	if x == nil {
		return x
	}
	x.Properties = &VisualProperty_BgColor{
		BgColor: color,
	}
	return x
}

// WithVisualPropertyBlockBgColor sets the background color using a block color string and returns the instance for method chaining.
func (x *VisualProperty) WithVisualPropertyBlockBgColor(color string) *VisualProperty {
	if x == nil {
		return x
	}
	x.Properties = &VisualProperty_BgColor{
		BgColor: widget.GetBlockBackgroundColour(color),
	}
	return x
}

// WithVisualPropertySize sets the size property with specified dimensions and returns the instance for method chaining.
func (x *VisualProperty) WithVisualPropertySize(width, height *Size_Dimension) *VisualProperty {
	if x == nil {
		return x
	}
	x.Properties = &VisualProperty_Size{
		Size: &Size{
			Width:  width,
			Height: height,
		},
	}
	return x
}

// WithVisualPropertyContainer sets the container property and returns the instance for method chaining.
func (x *VisualProperty) WithVisualPropertyContainer(containerProperty *ContainerProperty) *VisualProperty {
	if x == nil {
		return x
	}
	x.Properties = &VisualProperty_ContainerProperty{
		ContainerProperty: containerProperty,
	}
	return x
}

// GetSizeDimension creates and returns a Size_Dimension with the specified type and values.
func GetSizeDimension(sizeType Size_Dimension_Type, exactValue int32, weightValue float32) *Size_Dimension {
	return &Size_Dimension{
		Type:       sizeType,
		ExactValue: exactValue,
		Weight: &Weight{
			Value: weightValue,
		},
	}
}

// NewListElementOverlapProps creates and returns a new ListElementOverlapProps instance.
func NewListElementOverlapProps() *ListElementOverlapProps {
	return &ListElementOverlapProps{}
}

// WithOverlapDevicePixels sets the overlap device pixels and returns the instance for method chaining.
func (x *ListElementOverlapProps) WithOverlapDevicePixels(pixels int32) *ListElementOverlapProps {
	if x == nil {
		return x
	}
	x.OverlapDevicePixels = pixels
	return x
}

// WithOverlapPaddingPixels sets the overlap padding pixels and returns the instance for method chaining.
func (x *ListElementOverlapProps) WithOverlapPaddingPixels(pixels int32) *ListElementOverlapProps {
	if x == nil {
		return x
	}
	x.OverlapPaddingPixels = pixels
	return x
}

// WithPaddingBgColor sets the padding background color and returns the instance for method chaining.
func (x *ListElementOverlapProps) WithPaddingBgColor(color *widget.BackgroundColour) *ListElementOverlapProps {
	if x == nil {
		return x
	}
	x.PaddingBgColor = color
	return x
}

// WithOverlapCornerRadiusPixels sets the overlap corner radius pixels and returns the instance for method chaining.
func (x *ListElementOverlapProps) WithOverlapCornerRadiusPixels(pixels int32) *ListElementOverlapProps {
	if x == nil {
		return x
	}
	x.OverlapCornerRadiusPixels = pixels
	return x
}
