package analytics

// NewAnalyticsEvent creates and returns a new AnalyticsEvent instance.
func NewAnalyticsEvent() *AnalyticsEvent {
	return &AnalyticsEvent{}
}

// WithEventName sets the event name and returns the instance for method chaining.
func (x *AnalyticsEvent) WithEventName(eventName string) *AnalyticsEvent {
	if x == nil {
		return x
	}
	x.EventName = eventName
	return x
}

// WithEventAndProperties sets the event name and properties and returns the instance for method chaining.
func (x *AnalyticsEvent) WithEventAndProperties(eventName string, properties map[string]string) *AnalyticsEvent {
	if x == nil {
		return x
	}
	x.EventName = eventName
	x.Properties = properties
	return x
}
