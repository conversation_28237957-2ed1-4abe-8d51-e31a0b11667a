// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/investment/mutualfund/external/mutual_fund_external_request.proto

package external

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on MFHoldingsImportRequestTracker with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MFHoldingsImportRequestTracker) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MFHoldingsImportRequestTracker with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// MFHoldingsImportRequestTrackerMultiError, or nil if none found.
func (m *MFHoldingsImportRequestTracker) ValidateAll() error {
	return m.validate(true)
}

func (m *MFHoldingsImportRequestTracker) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ActorId

	// no validation rules for ConsentId

	// no validation rules for TransactionId

	// no validation rules for ClientReferenceNumber

	// no validation rules for State

	// no validation rules for FailureReason

	// no validation rules for Vendor

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MFHoldingsImportRequestTrackerValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MFHoldingsImportRequestTrackerValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MFHoldingsImportRequestTrackerValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MFHoldingsImportRequestTrackerValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MFHoldingsImportRequestTrackerValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MFHoldingsImportRequestTrackerValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MFHoldingsImportRequestTrackerValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MFHoldingsImportRequestTrackerValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MFHoldingsImportRequestTrackerValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetVendorData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MFHoldingsImportRequestTrackerValidationError{
					field:  "VendorData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MFHoldingsImportRequestTrackerValidationError{
					field:  "VendorData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVendorData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MFHoldingsImportRequestTrackerValidationError{
				field:  "VendorData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ExternalId

	if all {
		switch v := interface{}(m.GetMetaData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MFHoldingsImportRequestTrackerValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MFHoldingsImportRequestTrackerValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetaData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MFHoldingsImportRequestTrackerValidationError{
				field:  "MetaData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Provenance

	// no validation rules for FlowType

	if len(errors) > 0 {
		return MFHoldingsImportRequestTrackerMultiError(errors)
	}

	return nil
}

// MFHoldingsImportRequestTrackerMultiError is an error wrapping multiple
// validation errors returned by MFHoldingsImportRequestTracker.ValidateAll()
// if the designated constraints aren't met.
type MFHoldingsImportRequestTrackerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MFHoldingsImportRequestTrackerMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MFHoldingsImportRequestTrackerMultiError) AllErrors() []error { return m }

// MFHoldingsImportRequestTrackerValidationError is the validation error
// returned by MFHoldingsImportRequestTracker.Validate if the designated
// constraints aren't met.
type MFHoldingsImportRequestTrackerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MFHoldingsImportRequestTrackerValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MFHoldingsImportRequestTrackerValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MFHoldingsImportRequestTrackerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MFHoldingsImportRequestTrackerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MFHoldingsImportRequestTrackerValidationError) ErrorName() string {
	return "MFHoldingsImportRequestTrackerValidationError"
}

// Error satisfies the builtin error interface
func (e MFHoldingsImportRequestTrackerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMFHoldingsImportRequestTracker.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MFHoldingsImportRequestTrackerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MFHoldingsImportRequestTrackerValidationError{}

// Validate checks the field values on VendorData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *VendorData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VendorData with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in VendorDataMultiError, or
// nil if none found.
func (m *VendorData) ValidateAll() error {
	return m.validate(true)
}

func (m *VendorData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetInitiateHoldingsImportResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VendorDataValidationError{
					field:  "InitiateHoldingsImportResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VendorDataValidationError{
					field:  "InitiateHoldingsImportResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInitiateHoldingsImportResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VendorDataValidationError{
				field:  "InitiateHoldingsImportResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VendorDataMultiError(errors)
	}

	return nil
}

// VendorDataMultiError is an error wrapping multiple validation errors
// returned by VendorData.ValidateAll() if the designated constraints aren't met.
type VendorDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VendorDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VendorDataMultiError) AllErrors() []error { return m }

// VendorDataValidationError is the validation error returned by
// VendorData.Validate if the designated constraints aren't met.
type VendorDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VendorDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VendorDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VendorDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VendorDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VendorDataValidationError) ErrorName() string { return "VendorDataValidationError" }

// Error satisfies the builtin error interface
func (e VendorDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVendorData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VendorDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VendorDataValidationError{}

// Validate checks the field values on InitiateHoldingsImportResponseVendorData
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *InitiateHoldingsImportResponseVendorData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// InitiateHoldingsImportResponseVendorData with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// InitiateHoldingsImportResponseVendorDataMultiError, or nil if none found.
func (m *InitiateHoldingsImportResponseVendorData) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateHoldingsImportResponseVendorData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OtpReference

	// no validation rules for RequestId

	if len(errors) > 0 {
		return InitiateHoldingsImportResponseVendorDataMultiError(errors)
	}

	return nil
}

// InitiateHoldingsImportResponseVendorDataMultiError is an error wrapping
// multiple validation errors returned by
// InitiateHoldingsImportResponseVendorData.ValidateAll() if the designated
// constraints aren't met.
type InitiateHoldingsImportResponseVendorDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateHoldingsImportResponseVendorDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateHoldingsImportResponseVendorDataMultiError) AllErrors() []error { return m }

// InitiateHoldingsImportResponseVendorDataValidationError is the validation
// error returned by InitiateHoldingsImportResponseVendorData.Validate if the
// designated constraints aren't met.
type InitiateHoldingsImportResponseVendorDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateHoldingsImportResponseVendorDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateHoldingsImportResponseVendorDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateHoldingsImportResponseVendorDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateHoldingsImportResponseVendorDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateHoldingsImportResponseVendorDataValidationError) ErrorName() string {
	return "InitiateHoldingsImportResponseVendorDataValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateHoldingsImportResponseVendorDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateHoldingsImportResponseVendorData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateHoldingsImportResponseVendorDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateHoldingsImportResponseVendorDataValidationError{}

// Validate checks the field values on MFHoldingsImportRequestMetaData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MFHoldingsImportRequestMetaData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MFHoldingsImportRequestMetaData with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// MFHoldingsImportRequestMetaDataMultiError, or nil if none found.
func (m *MFHoldingsImportRequestMetaData) ValidateAll() error {
	return m.validate(true)
}

func (m *MFHoldingsImportRequestMetaData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetExitDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MFHoldingsImportRequestMetaDataValidationError{
					field:  "ExitDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MFHoldingsImportRequestMetaDataValidationError{
					field:  "ExitDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExitDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MFHoldingsImportRequestMetaDataValidationError{
				field:  "ExitDeeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OtpVerificationAttempts

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MFHoldingsImportRequestMetaDataValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MFHoldingsImportRequestMetaDataValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MFHoldingsImportRequestMetaDataValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetOtpRequests()))
		i := 0
		for key := range m.GetOtpRequests() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetOtpRequests()[key]
			_ = val

			// no validation rules for OtpRequests[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, MFHoldingsImportRequestMetaDataValidationError{
							field:  fmt.Sprintf("OtpRequests[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, MFHoldingsImportRequestMetaDataValidationError{
							field:  fmt.Sprintf("OtpRequests[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return MFHoldingsImportRequestMetaDataValidationError{
						field:  fmt.Sprintf("OtpRequests[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return MFHoldingsImportRequestMetaDataMultiError(errors)
	}

	return nil
}

// MFHoldingsImportRequestMetaDataMultiError is an error wrapping multiple
// validation errors returned by MFHoldingsImportRequestMetaData.ValidateAll()
// if the designated constraints aren't met.
type MFHoldingsImportRequestMetaDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MFHoldingsImportRequestMetaDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MFHoldingsImportRequestMetaDataMultiError) AllErrors() []error { return m }

// MFHoldingsImportRequestMetaDataValidationError is the validation error
// returned by MFHoldingsImportRequestMetaData.Validate if the designated
// constraints aren't met.
type MFHoldingsImportRequestMetaDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MFHoldingsImportRequestMetaDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MFHoldingsImportRequestMetaDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MFHoldingsImportRequestMetaDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MFHoldingsImportRequestMetaDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MFHoldingsImportRequestMetaDataValidationError) ErrorName() string {
	return "MFHoldingsImportRequestMetaDataValidationError"
}

// Error satisfies the builtin error interface
func (e MFHoldingsImportRequestMetaDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMFHoldingsImportRequestMetaData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MFHoldingsImportRequestMetaDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MFHoldingsImportRequestMetaDataValidationError{}

// Validate checks the field values on OtpMedium with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *OtpMedium) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OtpMedium with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in OtpMediumMultiError, or nil
// if none found.
func (m *OtpMedium) ValidateAll() error {
	return m.validate(true)
}

func (m *OtpMedium) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OtpMediumType

	switch v := m.Identifier.(type) {
	case *OtpMedium_PhoneNumber:
		if v == nil {
			err := OtpMediumValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPhoneNumber()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OtpMediumValidationError{
						field:  "PhoneNumber",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OtpMediumValidationError{
						field:  "PhoneNumber",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OtpMediumValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *OtpMedium_Email:
		if v == nil {
			err := OtpMediumValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if err := m._validateEmail(m.GetEmail()); err != nil {
			err = OtpMediumValidationError{
				field:  "Email",
				reason: "value must be a valid email address",
				cause:  err,
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return OtpMediumMultiError(errors)
	}

	return nil
}

func (m *OtpMedium) _validateHostname(host string) error {
	s := strings.ToLower(strings.TrimSuffix(host, "."))

	if len(host) > 253 {
		return errors.New("hostname cannot exceed 253 characters")
	}

	for _, part := range strings.Split(s, ".") {
		if l := len(part); l == 0 || l > 63 {
			return errors.New("hostname part must be non-empty and cannot exceed 63 characters")
		}

		if part[0] == '-' {
			return errors.New("hostname parts cannot begin with hyphens")
		}

		if part[len(part)-1] == '-' {
			return errors.New("hostname parts cannot end with hyphens")
		}

		for _, r := range part {
			if (r < 'a' || r > 'z') && (r < '0' || r > '9') && r != '-' {
				return fmt.Errorf("hostname parts can only contain alphanumeric characters or hyphens, got %q", string(r))
			}
		}
	}

	return nil
}

func (m *OtpMedium) _validateEmail(addr string) error {
	a, err := mail.ParseAddress(addr)
	if err != nil {
		return err
	}
	addr = a.Address

	if len(addr) > 254 {
		return errors.New("email addresses cannot exceed 254 characters")
	}

	parts := strings.SplitN(addr, "@", 2)

	if len(parts[0]) > 64 {
		return errors.New("email address local phrase cannot exceed 64 characters")
	}

	return m._validateHostname(parts[1])
}

// OtpMediumMultiError is an error wrapping multiple validation errors returned
// by OtpMedium.ValidateAll() if the designated constraints aren't met.
type OtpMediumMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OtpMediumMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OtpMediumMultiError) AllErrors() []error { return m }

// OtpMediumValidationError is the validation error returned by
// OtpMedium.Validate if the designated constraints aren't met.
type OtpMediumValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OtpMediumValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OtpMediumValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OtpMediumValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OtpMediumValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OtpMediumValidationError) ErrorName() string { return "OtpMediumValidationError" }

// Error satisfies the builtin error interface
func (e OtpMediumValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOtpMedium.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OtpMediumValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OtpMediumValidationError{}

// Validate checks the field values on OtpRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *OtpRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OtpRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in OtpRequestMultiError, or
// nil if none found.
func (m *OtpRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *OtpRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ClientReferenceNumber

	// no validation rules for OtpReference

	// no validation rules for RequestId

	if all {
		switch v := interface{}(m.GetOtpMedium()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OtpRequestValidationError{
					field:  "OtpMedium",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OtpRequestValidationError{
					field:  "OtpMedium",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOtpMedium()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OtpRequestValidationError{
				field:  "OtpMedium",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for State

	// no validation rules for FailureReason

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OtpRequestValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OtpRequestValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OtpRequestValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OtpRequestValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OtpRequestValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OtpRequestValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return OtpRequestMultiError(errors)
	}

	return nil
}

// OtpRequestMultiError is an error wrapping multiple validation errors
// returned by OtpRequest.ValidateAll() if the designated constraints aren't met.
type OtpRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OtpRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OtpRequestMultiError) AllErrors() []error { return m }

// OtpRequestValidationError is the validation error returned by
// OtpRequest.Validate if the designated constraints aren't met.
type OtpRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OtpRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OtpRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OtpRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OtpRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OtpRequestValidationError) ErrorName() string { return "OtpRequestValidationError" }

// Error satisfies the builtin error interface
func (e OtpRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOtpRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OtpRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OtpRequestValidationError{}

// Validate checks the field values on MfExternalNFTRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MfExternalNFTRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MfExternalNFTRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MfExternalNFTRequestMultiError, or nil if none found.
func (m *MfExternalNFTRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *MfExternalNFTRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ActorId

	// no validation rules for ExternalId

	// no validation rules for ClientReferenceId

	// no validation rules for Type

	// no validation rules for Status

	// no validation rules for SubStatus

	// no validation rules for Provenance

	// no validation rules for Vendor

	if all {
		switch v := interface{}(m.GetDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MfExternalNFTRequestValidationError{
					field:  "Details",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MfExternalNFTRequestValidationError{
					field:  "Details",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MfExternalNFTRequestValidationError{
				field:  "Details",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MfExternalNFTRequestValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MfExternalNFTRequestValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MfExternalNFTRequestValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MfExternalNFTRequestValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MfExternalNFTRequestValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MfExternalNFTRequestValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MfExternalNFTRequestValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MfExternalNFTRequestValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MfExternalNFTRequestValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MfExternalNFTRequestMultiError(errors)
	}

	return nil
}

// MfExternalNFTRequestMultiError is an error wrapping multiple validation
// errors returned by MfExternalNFTRequest.ValidateAll() if the designated
// constraints aren't met.
type MfExternalNFTRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MfExternalNFTRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MfExternalNFTRequestMultiError) AllErrors() []error { return m }

// MfExternalNFTRequestValidationError is the validation error returned by
// MfExternalNFTRequest.Validate if the designated constraints aren't met.
type MfExternalNFTRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MfExternalNFTRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MfExternalNFTRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MfExternalNFTRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MfExternalNFTRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MfExternalNFTRequestValidationError) ErrorName() string {
	return "MfExternalNFTRequestValidationError"
}

// Error satisfies the builtin error interface
func (e MfExternalNFTRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMfExternalNFTRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MfExternalNFTRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MfExternalNFTRequestValidationError{}

// Validate checks the field values on NFTDetails with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *NFTDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NFTDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in NFTDetailsMultiError, or
// nil if none found.
func (m *NFTDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *NFTDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NFTDetailsValidationError{
					field:  "RequestData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NFTDetailsValidationError{
					field:  "RequestData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NFTDetailsValidationError{
				field:  "RequestData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetVendorData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NFTDetailsValidationError{
					field:  "VendorData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NFTDetailsValidationError{
					field:  "VendorData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVendorData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NFTDetailsValidationError{
				field:  "VendorData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAuthDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NFTDetailsValidationError{
					field:  "AuthDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NFTDetailsValidationError{
					field:  "AuthDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuthDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NFTDetailsValidationError{
				field:  "AuthDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return NFTDetailsMultiError(errors)
	}

	return nil
}

// NFTDetailsMultiError is an error wrapping multiple validation errors
// returned by NFTDetails.ValidateAll() if the designated constraints aren't met.
type NFTDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NFTDetailsMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NFTDetailsMultiError) AllErrors() []error { return m }

// NFTDetailsValidationError is the validation error returned by
// NFTDetails.Validate if the designated constraints aren't met.
type NFTDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NFTDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NFTDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NFTDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NFTDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NFTDetailsValidationError) ErrorName() string { return "NFTDetailsValidationError" }

// Error satisfies the builtin error interface
func (e NFTDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNFTDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NFTDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NFTDetailsValidationError{}

// Validate checks the field values on NFTRequestData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *NFTRequestData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NFTRequestData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in NFTRequestDataMultiError,
// or nil if none found.
func (m *NFTRequestData) ValidateAll() error {
	return m.validate(true)
}

func (m *NFTRequestData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Details.(type) {
	case *NFTRequestData_EmailUpdateDetails:
		if v == nil {
			err := NFTRequestDataValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetEmailUpdateDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NFTRequestDataValidationError{
						field:  "EmailUpdateDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NFTRequestDataValidationError{
						field:  "EmailUpdateDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetEmailUpdateDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NFTRequestDataValidationError{
					field:  "EmailUpdateDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *NFTRequestData_MobileUpdateDetails:
		if v == nil {
			err := NFTRequestDataValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMobileUpdateDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NFTRequestDataValidationError{
						field:  "MobileUpdateDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NFTRequestDataValidationError{
						field:  "MobileUpdateDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMobileUpdateDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NFTRequestDataValidationError{
					field:  "MobileUpdateDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return NFTRequestDataMultiError(errors)
	}

	return nil
}

// NFTRequestDataMultiError is an error wrapping multiple validation errors
// returned by NFTRequestData.ValidateAll() if the designated constraints
// aren't met.
type NFTRequestDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NFTRequestDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NFTRequestDataMultiError) AllErrors() []error { return m }

// NFTRequestDataValidationError is the validation error returned by
// NFTRequestData.Validate if the designated constraints aren't met.
type NFTRequestDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NFTRequestDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NFTRequestDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NFTRequestDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NFTRequestDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NFTRequestDataValidationError) ErrorName() string { return "NFTRequestDataValidationError" }

// Error satisfies the builtin error interface
func (e NFTRequestDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNFTRequestData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NFTRequestDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NFTRequestDataValidationError{}

// Validate checks the field values on NFTVendorData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *NFTVendorData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NFTVendorData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in NFTVendorDataMultiError, or
// nil if none found.
func (m *NFTVendorData) ValidateAll() error {
	return m.validate(true)
}

func (m *NFTVendorData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RequestId

	if all {
		switch v := interface{}(m.GetAuthOtpInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NFTVendorDataValidationError{
					field:  "AuthOtpInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NFTVendorDataValidationError{
					field:  "AuthOtpInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuthOtpInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NFTVendorDataValidationError{
				field:  "AuthOtpInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.Details.(type) {
	case *NFTVendorData_EmailUpdateVendorData:
		if v == nil {
			err := NFTVendorDataValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetEmailUpdateVendorData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NFTVendorDataValidationError{
						field:  "EmailUpdateVendorData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NFTVendorDataValidationError{
						field:  "EmailUpdateVendorData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetEmailUpdateVendorData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NFTVendorDataValidationError{
					field:  "EmailUpdateVendorData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *NFTVendorData_MobileUpdateVendorData:
		if v == nil {
			err := NFTVendorDataValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMobileUpdateVendorData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NFTVendorDataValidationError{
						field:  "MobileUpdateVendorData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NFTVendorDataValidationError{
						field:  "MobileUpdateVendorData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMobileUpdateVendorData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NFTVendorDataValidationError{
					field:  "MobileUpdateVendorData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return NFTVendorDataMultiError(errors)
	}

	return nil
}

// NFTVendorDataMultiError is an error wrapping multiple validation errors
// returned by NFTVendorData.ValidateAll() if the designated constraints
// aren't met.
type NFTVendorDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NFTVendorDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NFTVendorDataMultiError) AllErrors() []error { return m }

// NFTVendorDataValidationError is the validation error returned by
// NFTVendorData.Validate if the designated constraints aren't met.
type NFTVendorDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NFTVendorDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NFTVendorDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NFTVendorDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NFTVendorDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NFTVendorDataValidationError) ErrorName() string { return "NFTVendorDataValidationError" }

// Error satisfies the builtin error interface
func (e NFTVendorDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNFTVendorData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NFTVendorDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NFTVendorDataValidationError{}

// Validate checks the field values on EmailUpdateRequestDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EmailUpdateRequestDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EmailUpdateRequestDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EmailUpdateRequestDetailsMultiError, or nil if none found.
func (m *EmailUpdateRequestDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *EmailUpdateRequestDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetUpdateDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, EmailUpdateRequestDetailsValidationError{
						field:  fmt.Sprintf("UpdateDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, EmailUpdateRequestDetailsValidationError{
						field:  fmt.Sprintf("UpdateDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return EmailUpdateRequestDetailsValidationError{
					field:  fmt.Sprintf("UpdateDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return EmailUpdateRequestDetailsMultiError(errors)
	}

	return nil
}

// EmailUpdateRequestDetailsMultiError is an error wrapping multiple validation
// errors returned by EmailUpdateRequestDetails.ValidateAll() if the
// designated constraints aren't met.
type EmailUpdateRequestDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EmailUpdateRequestDetailsMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EmailUpdateRequestDetailsMultiError) AllErrors() []error { return m }

// EmailUpdateRequestDetailsValidationError is the validation error returned by
// EmailUpdateRequestDetails.Validate if the designated constraints aren't met.
type EmailUpdateRequestDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EmailUpdateRequestDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EmailUpdateRequestDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EmailUpdateRequestDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EmailUpdateRequestDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EmailUpdateRequestDetailsValidationError) ErrorName() string {
	return "EmailUpdateRequestDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e EmailUpdateRequestDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEmailUpdateRequestDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EmailUpdateRequestDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EmailUpdateRequestDetailsValidationError{}

// Validate checks the field values on MobileUpdateRequestDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MobileUpdateRequestDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MobileUpdateRequestDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MobileUpdateRequestDetailsMultiError, or nil if none found.
func (m *MobileUpdateRequestDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *MobileUpdateRequestDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetUpdateDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MobileUpdateRequestDetailsValidationError{
						field:  fmt.Sprintf("UpdateDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MobileUpdateRequestDetailsValidationError{
						field:  fmt.Sprintf("UpdateDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MobileUpdateRequestDetailsValidationError{
					field:  fmt.Sprintf("UpdateDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return MobileUpdateRequestDetailsMultiError(errors)
	}

	return nil
}

// MobileUpdateRequestDetailsMultiError is an error wrapping multiple
// validation errors returned by MobileUpdateRequestDetails.ValidateAll() if
// the designated constraints aren't met.
type MobileUpdateRequestDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MobileUpdateRequestDetailsMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MobileUpdateRequestDetailsMultiError) AllErrors() []error { return m }

// MobileUpdateRequestDetailsValidationError is the validation error returned
// by MobileUpdateRequestDetails.Validate if the designated constraints aren't met.
type MobileUpdateRequestDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MobileUpdateRequestDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MobileUpdateRequestDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MobileUpdateRequestDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MobileUpdateRequestDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MobileUpdateRequestDetailsValidationError) ErrorName() string {
	return "MobileUpdateRequestDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e MobileUpdateRequestDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMobileUpdateRequestDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MobileUpdateRequestDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MobileUpdateRequestDetailsValidationError{}

// Validate checks the field values on FolioEmailUpdateDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FolioEmailUpdateDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FolioEmailUpdateDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FolioEmailUpdateDetailsMultiError, or nil if none found.
func (m *FolioEmailUpdateDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *FolioEmailUpdateDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FolioNumber

	// no validation rules for NewEmail

	// no validation rules for AmcCode

	// no validation rules for Relationship

	if len(errors) > 0 {
		return FolioEmailUpdateDetailsMultiError(errors)
	}

	return nil
}

// FolioEmailUpdateDetailsMultiError is an error wrapping multiple validation
// errors returned by FolioEmailUpdateDetails.ValidateAll() if the designated
// constraints aren't met.
type FolioEmailUpdateDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FolioEmailUpdateDetailsMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FolioEmailUpdateDetailsMultiError) AllErrors() []error { return m }

// FolioEmailUpdateDetailsValidationError is the validation error returned by
// FolioEmailUpdateDetails.Validate if the designated constraints aren't met.
type FolioEmailUpdateDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FolioEmailUpdateDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FolioEmailUpdateDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FolioEmailUpdateDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FolioEmailUpdateDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FolioEmailUpdateDetailsValidationError) ErrorName() string {
	return "FolioEmailUpdateDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e FolioEmailUpdateDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFolioEmailUpdateDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FolioEmailUpdateDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FolioEmailUpdateDetailsValidationError{}

// Validate checks the field values on FolioMobileUpdateDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FolioMobileUpdateDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FolioMobileUpdateDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FolioMobileUpdateDetailsMultiError, or nil if none found.
func (m *FolioMobileUpdateDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *FolioMobileUpdateDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FolioNumber

	if all {
		switch v := interface{}(m.GetNewMobile()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FolioMobileUpdateDetailsValidationError{
					field:  "NewMobile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FolioMobileUpdateDetailsValidationError{
					field:  "NewMobile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNewMobile()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FolioMobileUpdateDetailsValidationError{
				field:  "NewMobile",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AmcCode

	// no validation rules for Relationship

	if len(errors) > 0 {
		return FolioMobileUpdateDetailsMultiError(errors)
	}

	return nil
}

// FolioMobileUpdateDetailsMultiError is an error wrapping multiple validation
// errors returned by FolioMobileUpdateDetails.ValidateAll() if the designated
// constraints aren't met.
type FolioMobileUpdateDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FolioMobileUpdateDetailsMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FolioMobileUpdateDetailsMultiError) AllErrors() []error { return m }

// FolioMobileUpdateDetailsValidationError is the validation error returned by
// FolioMobileUpdateDetails.Validate if the designated constraints aren't met.
type FolioMobileUpdateDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FolioMobileUpdateDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FolioMobileUpdateDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FolioMobileUpdateDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FolioMobileUpdateDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FolioMobileUpdateDetailsValidationError) ErrorName() string {
	return "FolioMobileUpdateDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e FolioMobileUpdateDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFolioMobileUpdateDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FolioMobileUpdateDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FolioMobileUpdateDetailsValidationError{}

// Validate checks the field values on EmailUpdateVendorData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EmailUpdateVendorData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EmailUpdateVendorData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EmailUpdateVendorDataMultiError, or nil if none found.
func (m *EmailUpdateVendorData) ValidateAll() error {
	return m.validate(true)
}

func (m *EmailUpdateVendorData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetNewEmailVerification()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EmailUpdateVendorDataValidationError{
					field:  "NewEmailVerification",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EmailUpdateVendorDataValidationError{
					field:  "NewEmailVerification",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNewEmailVerification()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EmailUpdateVendorDataValidationError{
				field:  "NewEmailVerification",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return EmailUpdateVendorDataMultiError(errors)
	}

	return nil
}

// EmailUpdateVendorDataMultiError is an error wrapping multiple validation
// errors returned by EmailUpdateVendorData.ValidateAll() if the designated
// constraints aren't met.
type EmailUpdateVendorDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EmailUpdateVendorDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EmailUpdateVendorDataMultiError) AllErrors() []error { return m }

// EmailUpdateVendorDataValidationError is the validation error returned by
// EmailUpdateVendorData.Validate if the designated constraints aren't met.
type EmailUpdateVendorDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EmailUpdateVendorDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EmailUpdateVendorDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EmailUpdateVendorDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EmailUpdateVendorDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EmailUpdateVendorDataValidationError) ErrorName() string {
	return "EmailUpdateVendorDataValidationError"
}

// Error satisfies the builtin error interface
func (e EmailUpdateVendorDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEmailUpdateVendorData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EmailUpdateVendorDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EmailUpdateVendorDataValidationError{}

// Validate checks the field values on MobileUpdateVendorData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MobileUpdateVendorData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MobileUpdateVendorData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MobileUpdateVendorDataMultiError, or nil if none found.
func (m *MobileUpdateVendorData) ValidateAll() error {
	return m.validate(true)
}

func (m *MobileUpdateVendorData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetNewMobileVerification()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MobileUpdateVendorDataValidationError{
					field:  "NewMobileVerification",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MobileUpdateVendorDataValidationError{
					field:  "NewMobileVerification",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNewMobileVerification()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MobileUpdateVendorDataValidationError{
				field:  "NewMobileVerification",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MobileUpdateVendorDataMultiError(errors)
	}

	return nil
}

// MobileUpdateVendorDataMultiError is an error wrapping multiple validation
// errors returned by MobileUpdateVendorData.ValidateAll() if the designated
// constraints aren't met.
type MobileUpdateVendorDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MobileUpdateVendorDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MobileUpdateVendorDataMultiError) AllErrors() []error { return m }

// MobileUpdateVendorDataValidationError is the validation error returned by
// MobileUpdateVendorData.Validate if the designated constraints aren't met.
type MobileUpdateVendorDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MobileUpdateVendorDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MobileUpdateVendorDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MobileUpdateVendorDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MobileUpdateVendorDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MobileUpdateVendorDataValidationError) ErrorName() string {
	return "MobileUpdateVendorDataValidationError"
}

// Error satisfies the builtin error interface
func (e MobileUpdateVendorDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMobileUpdateVendorData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MobileUpdateVendorDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MobileUpdateVendorDataValidationError{}

// Validate checks the field values on OtpInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *OtpInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OtpInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in OtpInfoMultiError, or nil if none found.
func (m *OtpInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *OtpInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OtpReferenceNumber

	// no validation rules for RequestId

	// no validation rules for UserSubjectReference

	// no validation rules for Status

	if all {
		switch v := interface{}(m.GetOtpMedium()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OtpInfoValidationError{
					field:  "OtpMedium",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OtpInfoValidationError{
					field:  "OtpMedium",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOtpMedium()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OtpInfoValidationError{
				field:  "OtpMedium",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OtpInfoValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OtpInfoValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OtpInfoValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OtpInfoValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OtpInfoValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OtpInfoValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return OtpInfoMultiError(errors)
	}

	return nil
}

// OtpInfoMultiError is an error wrapping multiple validation errors returned
// by OtpInfo.ValidateAll() if the designated constraints aren't met.
type OtpInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OtpInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OtpInfoMultiError) AllErrors() []error { return m }

// OtpInfoValidationError is the validation error returned by OtpInfo.Validate
// if the designated constraints aren't met.
type OtpInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OtpInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OtpInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OtpInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OtpInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OtpInfoValidationError) ErrorName() string { return "OtpInfoValidationError" }

// Error satisfies the builtin error interface
func (e OtpInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOtpInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OtpInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OtpInfoValidationError{}
