// Code generated by MockGen. DO NOT EDIT.
// Source: api/tiering/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	sherlock_banners "github.com/epifi/gamma/api/cx/sherlock_banners"
	dynamic_elements "github.com/epifi/gamma/api/dynamic_elements"
	tiering "github.com/epifi/gamma/api/tiering"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockTieringClient is a mock of TieringClient interface.
type MockTieringClient struct {
	ctrl     *gomock.Controller
	recorder *MockTieringClientMockRecorder
}

// MockTieringClientMockRecorder is the mock recorder for MockTieringClient.
type MockTieringClientMockRecorder struct {
	mock *MockTieringClient
}

// NewMockTieringClient creates a new mock instance.
func NewMockTieringClient(ctrl *gomock.Controller) *MockTieringClient {
	mock := &MockTieringClient{ctrl: ctrl}
	mock.recorder = &MockTieringClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTieringClient) EXPECT() *MockTieringClientMockRecorder {
	return m.recorder
}

// CheckIfActorIsEligibleForCashbackReward mocks base method.
func (m *MockTieringClient) CheckIfActorIsEligibleForCashbackReward(ctx context.Context, in *tiering.CheckIfActorIsEligibleForCashbackRewardRequest, opts ...grpc.CallOption) (*tiering.CheckIfActorIsEligibleForCashbackRewardResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckIfActorIsEligibleForCashbackReward", varargs...)
	ret0, _ := ret[0].(*tiering.CheckIfActorIsEligibleForCashbackRewardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckIfActorIsEligibleForCashbackReward indicates an expected call of CheckIfActorIsEligibleForCashbackReward.
func (mr *MockTieringClientMockRecorder) CheckIfActorIsEligibleForCashbackReward(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIfActorIsEligibleForCashbackReward", reflect.TypeOf((*MockTieringClient)(nil).CheckIfActorIsEligibleForCashbackReward), varargs...)
}

// DynamicElementCallback mocks base method.
func (m *MockTieringClient) DynamicElementCallback(ctx context.Context, in *dynamic_elements.DynamicElementCallbackRequest, opts ...grpc.CallOption) (*dynamic_elements.DynamicElementCallbackResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DynamicElementCallback", varargs...)
	ret0, _ := ret[0].(*dynamic_elements.DynamicElementCallbackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DynamicElementCallback indicates an expected call of DynamicElementCallback.
func (mr *MockTieringClientMockRecorder) DynamicElementCallback(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DynamicElementCallback", reflect.TypeOf((*MockTieringClient)(nil).DynamicElementCallback), varargs...)
}

// EvaluateTierForActor mocks base method.
func (m *MockTieringClient) EvaluateTierForActor(ctx context.Context, in *tiering.EvaluateTierForActorRequest, opts ...grpc.CallOption) (*tiering.EvaluateTierForActorResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "EvaluateTierForActor", varargs...)
	ret0, _ := ret[0].(*tiering.EvaluateTierForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EvaluateTierForActor indicates an expected call of EvaluateTierForActor.
func (mr *MockTieringClientMockRecorder) EvaluateTierForActor(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EvaluateTierForActor", reflect.TypeOf((*MockTieringClient)(nil).EvaluateTierForActor), varargs...)
}

// FetchDynamicElements mocks base method.
func (m *MockTieringClient) FetchDynamicElements(ctx context.Context, in *dynamic_elements.FetchDynamicElementsRequest, opts ...grpc.CallOption) (*dynamic_elements.FetchDynamicElementsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchDynamicElements", varargs...)
	ret0, _ := ret[0].(*dynamic_elements.FetchDynamicElementsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchDynamicElements indicates an expected call of FetchDynamicElements.
func (mr *MockTieringClientMockRecorder) FetchDynamicElements(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchDynamicElements", reflect.TypeOf((*MockTieringClient)(nil).FetchDynamicElements), varargs...)
}

// FetchSherlockBanners mocks base method.
func (m *MockTieringClient) FetchSherlockBanners(ctx context.Context, in *sherlock_banners.FetchSherlockBannersRequest, opts ...grpc.CallOption) (*sherlock_banners.FetchSherlockBannersResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchSherlockBanners", varargs...)
	ret0, _ := ret[0].(*sherlock_banners.FetchSherlockBannersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchSherlockBanners indicates an expected call of FetchSherlockBanners.
func (mr *MockTieringClientMockRecorder) FetchSherlockBanners(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchSherlockBanners", reflect.TypeOf((*MockTieringClient)(nil).FetchSherlockBanners), varargs...)
}

// GetAMBInfo mocks base method.
func (m *MockTieringClient) GetAMBInfo(ctx context.Context, in *tiering.GetAMBInfoRequest, opts ...grpc.CallOption) (*tiering.GetAMBInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAMBInfo", varargs...)
	ret0, _ := ret[0].(*tiering.GetAMBInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAMBInfo indicates an expected call of GetAMBInfo.
func (mr *MockTieringClientMockRecorder) GetAMBInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAMBInfo", reflect.TypeOf((*MockTieringClient)(nil).GetAMBInfo), varargs...)
}

// GetActorDistinctTiers mocks base method.
func (m *MockTieringClient) GetActorDistinctTiers(ctx context.Context, in *tiering.GetActorDistinctTiersRequest, opts ...grpc.CallOption) (*tiering.GetActorDistinctTiersResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetActorDistinctTiers", varargs...)
	ret0, _ := ret[0].(*tiering.GetActorDistinctTiersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActorDistinctTiers indicates an expected call of GetActorDistinctTiers.
func (mr *MockTieringClientMockRecorder) GetActorDistinctTiers(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActorDistinctTiers", reflect.TypeOf((*MockTieringClient)(nil).GetActorDistinctTiers), varargs...)
}

// GetActorScreenInteractionDetails mocks base method.
func (m *MockTieringClient) GetActorScreenInteractionDetails(ctx context.Context, in *tiering.GetActorScreenInteractionDetailsRequest, opts ...grpc.CallOption) (*tiering.GetActorScreenInteractionDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetActorScreenInteractionDetails", varargs...)
	ret0, _ := ret[0].(*tiering.GetActorScreenInteractionDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActorScreenInteractionDetails indicates an expected call of GetActorScreenInteractionDetails.
func (mr *MockTieringClientMockRecorder) GetActorScreenInteractionDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActorScreenInteractionDetails", reflect.TypeOf((*MockTieringClient)(nil).GetActorScreenInteractionDetails), varargs...)
}

// GetConfigParams mocks base method.
func (m *MockTieringClient) GetConfigParams(ctx context.Context, in *tiering.GetConfigParamsRequest, opts ...grpc.CallOption) (*tiering.GetConfigParamsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetConfigParams", varargs...)
	ret0, _ := ret[0].(*tiering.GetConfigParamsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConfigParams indicates an expected call of GetConfigParams.
func (mr *MockTieringClientMockRecorder) GetConfigParams(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConfigParams", reflect.TypeOf((*MockTieringClient)(nil).GetConfigParams), varargs...)
}

// GetCriteriaForActor mocks base method.
func (m *MockTieringClient) GetCriteriaForActor(ctx context.Context, in *tiering.GetCriteriaForActorRequest, opts ...grpc.CallOption) (*tiering.GetCriteriaForActorResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCriteriaForActor", varargs...)
	ret0, _ := ret[0].(*tiering.GetCriteriaForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCriteriaForActor indicates an expected call of GetCriteriaForActor.
func (mr *MockTieringClientMockRecorder) GetCriteriaForActor(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCriteriaForActor", reflect.TypeOf((*MockTieringClient)(nil).GetCriteriaForActor), varargs...)
}

// GetCurrentTierForActor mocks base method.
func (m *MockTieringClient) GetCurrentTierForActor(ctx context.Context, in *tiering.GetCurrentTierForActorRequest, opts ...grpc.CallOption) (*tiering.GetCurrentTierForActorResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCurrentTierForActor", varargs...)
	ret0, _ := ret[0].(*tiering.GetCurrentTierForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCurrentTierForActor indicates an expected call of GetCurrentTierForActor.
func (mr *MockTieringClientMockRecorder) GetCurrentTierForActor(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCurrentTierForActor", reflect.TypeOf((*MockTieringClient)(nil).GetCurrentTierForActor), varargs...)
}

// GetDetailsForCx mocks base method.
func (m *MockTieringClient) GetDetailsForCx(ctx context.Context, in *tiering.GetDetailsForCxRequest, opts ...grpc.CallOption) (*tiering.GetDetailsForCxResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetDetailsForCx", varargs...)
	ret0, _ := ret[0].(*tiering.GetDetailsForCxResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDetailsForCx indicates an expected call of GetDetailsForCx.
func (mr *MockTieringClientMockRecorder) GetDetailsForCx(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDetailsForCx", reflect.TypeOf((*MockTieringClient)(nil).GetDetailsForCx), varargs...)
}

// GetTierAtTime mocks base method.
func (m *MockTieringClient) GetTierAtTime(ctx context.Context, in *tiering.GetTierAtTimeRequest, opts ...grpc.CallOption) (*tiering.GetTierAtTimeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTierAtTime", varargs...)
	ret0, _ := ret[0].(*tiering.GetTierAtTimeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTierAtTime indicates an expected call of GetTierAtTime.
func (mr *MockTieringClientMockRecorder) GetTierAtTime(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTierAtTime", reflect.TypeOf((*MockTieringClient)(nil).GetTierAtTime), varargs...)
}

// GetTierTimeRangesForActor mocks base method.
func (m *MockTieringClient) GetTierTimeRangesForActor(ctx context.Context, in *tiering.GetTierTimeRangesForActorRequest, opts ...grpc.CallOption) (*tiering.GetTierTimeRangesForActorResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTierTimeRangesForActor", varargs...)
	ret0, _ := ret[0].(*tiering.GetTierTimeRangesForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTierTimeRangesForActor indicates an expected call of GetTierTimeRangesForActor.
func (mr *MockTieringClientMockRecorder) GetTierTimeRangesForActor(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTierTimeRangesForActor", reflect.TypeOf((*MockTieringClient)(nil).GetTierTimeRangesForActor), varargs...)
}

// GetTieringPitch mocks base method.
func (m *MockTieringClient) GetTieringPitch(ctx context.Context, in *tiering.GetTieringPitchRequest, opts ...grpc.CallOption) (*tiering.GetTieringPitchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTieringPitch", varargs...)
	ret0, _ := ret[0].(*tiering.GetTieringPitchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTieringPitch indicates an expected call of GetTieringPitch.
func (mr *MockTieringClientMockRecorder) GetTieringPitch(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTieringPitch", reflect.TypeOf((*MockTieringClient)(nil).GetTieringPitch), varargs...)
}

// GetTieringPitchV2 mocks base method.
func (m *MockTieringClient) GetTieringPitchV2(ctx context.Context, in *tiering.GetTieringPitchV2Request, opts ...grpc.CallOption) (*tiering.GetTieringPitchV2Response, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTieringPitchV2", varargs...)
	ret0, _ := ret[0].(*tiering.GetTieringPitchV2Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTieringPitchV2 indicates an expected call of GetTieringPitchV2.
func (mr *MockTieringClientMockRecorder) GetTieringPitchV2(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTieringPitchV2", reflect.TypeOf((*MockTieringClient)(nil).GetTieringPitchV2), varargs...)
}

// IsActorEligibleForMovement mocks base method.
func (m *MockTieringClient) IsActorEligibleForMovement(ctx context.Context, in *tiering.IsActorEligibleForMovementRequest, opts ...grpc.CallOption) (*tiering.IsActorEligibleForMovementResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IsActorEligibleForMovement", varargs...)
	ret0, _ := ret[0].(*tiering.IsActorEligibleForMovementResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsActorEligibleForMovement indicates an expected call of IsActorEligibleForMovement.
func (mr *MockTieringClientMockRecorder) IsActorEligibleForMovement(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsActorEligibleForMovement", reflect.TypeOf((*MockTieringClient)(nil).IsActorEligibleForMovement), varargs...)
}

// IsTieringEnabledForActor mocks base method.
func (m *MockTieringClient) IsTieringEnabledForActor(ctx context.Context, in *tiering.IsTieringEnabledForActorRequest, opts ...grpc.CallOption) (*tiering.IsTieringEnabledForActorResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IsTieringEnabledForActor", varargs...)
	ret0, _ := ret[0].(*tiering.IsTieringEnabledForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsTieringEnabledForActor indicates an expected call of IsTieringEnabledForActor.
func (mr *MockTieringClientMockRecorder) IsTieringEnabledForActor(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsTieringEnabledForActor", reflect.TypeOf((*MockTieringClient)(nil).IsTieringEnabledForActor), varargs...)
}

// IsUserEligibleForRewards mocks base method.
func (m *MockTieringClient) IsUserEligibleForRewards(ctx context.Context, in *tiering.IsUserEligibleForRewardsRequest, opts ...grpc.CallOption) (*tiering.IsUserEligibleForRewardsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IsUserEligibleForRewards", varargs...)
	ret0, _ := ret[0].(*tiering.IsUserEligibleForRewardsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsUserEligibleForRewards indicates an expected call of IsUserEligibleForRewards.
func (mr *MockTieringClientMockRecorder) IsUserEligibleForRewards(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsUserEligibleForRewards", reflect.TypeOf((*MockTieringClient)(nil).IsUserEligibleForRewards), varargs...)
}

// IsUserInGracePeriod mocks base method.
func (m *MockTieringClient) IsUserInGracePeriod(ctx context.Context, in *tiering.IsUserInGracePeriodRequest, opts ...grpc.CallOption) (*tiering.IsUserInGracePeriodResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IsUserInGracePeriod", varargs...)
	ret0, _ := ret[0].(*tiering.IsUserInGracePeriodResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsUserInGracePeriod indicates an expected call of IsUserInGracePeriod.
func (mr *MockTieringClientMockRecorder) IsUserInGracePeriod(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsUserInGracePeriod", reflect.TypeOf((*MockTieringClient)(nil).IsUserInGracePeriod), varargs...)
}

// OverrideCoolOffPeriod mocks base method.
func (m *MockTieringClient) OverrideCoolOffPeriod(ctx context.Context, in *tiering.OverrideCoolOffPeriodRequest, opts ...grpc.CallOption) (*tiering.OverrideCoolOffPeriodResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "OverrideCoolOffPeriod", varargs...)
	ret0, _ := ret[0].(*tiering.OverrideCoolOffPeriodResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OverrideCoolOffPeriod indicates an expected call of OverrideCoolOffPeriod.
func (mr *MockTieringClientMockRecorder) OverrideCoolOffPeriod(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OverrideCoolOffPeriod", reflect.TypeOf((*MockTieringClient)(nil).OverrideCoolOffPeriod), varargs...)
}

// OverrideGracePeriod mocks base method.
func (m *MockTieringClient) OverrideGracePeriod(ctx context.Context, in *tiering.OverrideGracePeriodRequest, opts ...grpc.CallOption) (*tiering.OverrideGracePeriodResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "OverrideGracePeriod", varargs...)
	ret0, _ := ret[0].(*tiering.OverrideGracePeriodResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OverrideGracePeriod indicates an expected call of OverrideGracePeriod.
func (mr *MockTieringClientMockRecorder) OverrideGracePeriod(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OverrideGracePeriod", reflect.TypeOf((*MockTieringClient)(nil).OverrideGracePeriod), varargs...)
}

// RecordComponentShownToActor mocks base method.
func (m *MockTieringClient) RecordComponentShownToActor(ctx context.Context, in *tiering.RecordComponentShownToActorRequest, opts ...grpc.CallOption) (*tiering.RecordComponentShownToActorResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RecordComponentShownToActor", varargs...)
	ret0, _ := ret[0].(*tiering.RecordComponentShownToActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecordComponentShownToActor indicates an expected call of RecordComponentShownToActor.
func (mr *MockTieringClientMockRecorder) RecordComponentShownToActor(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordComponentShownToActor", reflect.TypeOf((*MockTieringClient)(nil).RecordComponentShownToActor), varargs...)
}

// ShowComponentToActor mocks base method.
func (m *MockTieringClient) ShowComponentToActor(ctx context.Context, in *tiering.ShowComponentToActorRequest, opts ...grpc.CallOption) (*tiering.ShowComponentToActorResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ShowComponentToActor", varargs...)
	ret0, _ := ret[0].(*tiering.ShowComponentToActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ShowComponentToActor indicates an expected call of ShowComponentToActor.
func (mr *MockTieringClientMockRecorder) ShowComponentToActor(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ShowComponentToActor", reflect.TypeOf((*MockTieringClient)(nil).ShowComponentToActor), varargs...)
}

// Upgrade mocks base method.
func (m *MockTieringClient) Upgrade(ctx context.Context, in *tiering.UpgradeRequest, opts ...grpc.CallOption) (*tiering.UpgradeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Upgrade", varargs...)
	ret0, _ := ret[0].(*tiering.UpgradeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Upgrade indicates an expected call of Upgrade.
func (mr *MockTieringClientMockRecorder) Upgrade(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Upgrade", reflect.TypeOf((*MockTieringClient)(nil).Upgrade), varargs...)
}

// MockTieringServer is a mock of TieringServer interface.
type MockTieringServer struct {
	ctrl     *gomock.Controller
	recorder *MockTieringServerMockRecorder
}

// MockTieringServerMockRecorder is the mock recorder for MockTieringServer.
type MockTieringServerMockRecorder struct {
	mock *MockTieringServer
}

// NewMockTieringServer creates a new mock instance.
func NewMockTieringServer(ctrl *gomock.Controller) *MockTieringServer {
	mock := &MockTieringServer{ctrl: ctrl}
	mock.recorder = &MockTieringServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTieringServer) EXPECT() *MockTieringServerMockRecorder {
	return m.recorder
}

// CheckIfActorIsEligibleForCashbackReward mocks base method.
func (m *MockTieringServer) CheckIfActorIsEligibleForCashbackReward(arg0 context.Context, arg1 *tiering.CheckIfActorIsEligibleForCashbackRewardRequest) (*tiering.CheckIfActorIsEligibleForCashbackRewardResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckIfActorIsEligibleForCashbackReward", arg0, arg1)
	ret0, _ := ret[0].(*tiering.CheckIfActorIsEligibleForCashbackRewardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckIfActorIsEligibleForCashbackReward indicates an expected call of CheckIfActorIsEligibleForCashbackReward.
func (mr *MockTieringServerMockRecorder) CheckIfActorIsEligibleForCashbackReward(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIfActorIsEligibleForCashbackReward", reflect.TypeOf((*MockTieringServer)(nil).CheckIfActorIsEligibleForCashbackReward), arg0, arg1)
}

// DynamicElementCallback mocks base method.
func (m *MockTieringServer) DynamicElementCallback(arg0 context.Context, arg1 *dynamic_elements.DynamicElementCallbackRequest) (*dynamic_elements.DynamicElementCallbackResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DynamicElementCallback", arg0, arg1)
	ret0, _ := ret[0].(*dynamic_elements.DynamicElementCallbackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DynamicElementCallback indicates an expected call of DynamicElementCallback.
func (mr *MockTieringServerMockRecorder) DynamicElementCallback(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DynamicElementCallback", reflect.TypeOf((*MockTieringServer)(nil).DynamicElementCallback), arg0, arg1)
}

// EvaluateTierForActor mocks base method.
func (m *MockTieringServer) EvaluateTierForActor(arg0 context.Context, arg1 *tiering.EvaluateTierForActorRequest) (*tiering.EvaluateTierForActorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EvaluateTierForActor", arg0, arg1)
	ret0, _ := ret[0].(*tiering.EvaluateTierForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EvaluateTierForActor indicates an expected call of EvaluateTierForActor.
func (mr *MockTieringServerMockRecorder) EvaluateTierForActor(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EvaluateTierForActor", reflect.TypeOf((*MockTieringServer)(nil).EvaluateTierForActor), arg0, arg1)
}

// FetchDynamicElements mocks base method.
func (m *MockTieringServer) FetchDynamicElements(arg0 context.Context, arg1 *dynamic_elements.FetchDynamicElementsRequest) (*dynamic_elements.FetchDynamicElementsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchDynamicElements", arg0, arg1)
	ret0, _ := ret[0].(*dynamic_elements.FetchDynamicElementsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchDynamicElements indicates an expected call of FetchDynamicElements.
func (mr *MockTieringServerMockRecorder) FetchDynamicElements(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchDynamicElements", reflect.TypeOf((*MockTieringServer)(nil).FetchDynamicElements), arg0, arg1)
}

// FetchSherlockBanners mocks base method.
func (m *MockTieringServer) FetchSherlockBanners(arg0 context.Context, arg1 *sherlock_banners.FetchSherlockBannersRequest) (*sherlock_banners.FetchSherlockBannersResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchSherlockBanners", arg0, arg1)
	ret0, _ := ret[0].(*sherlock_banners.FetchSherlockBannersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchSherlockBanners indicates an expected call of FetchSherlockBanners.
func (mr *MockTieringServerMockRecorder) FetchSherlockBanners(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchSherlockBanners", reflect.TypeOf((*MockTieringServer)(nil).FetchSherlockBanners), arg0, arg1)
}

// GetAMBInfo mocks base method.
func (m *MockTieringServer) GetAMBInfo(arg0 context.Context, arg1 *tiering.GetAMBInfoRequest) (*tiering.GetAMBInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAMBInfo", arg0, arg1)
	ret0, _ := ret[0].(*tiering.GetAMBInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAMBInfo indicates an expected call of GetAMBInfo.
func (mr *MockTieringServerMockRecorder) GetAMBInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAMBInfo", reflect.TypeOf((*MockTieringServer)(nil).GetAMBInfo), arg0, arg1)
}

// GetActorDistinctTiers mocks base method.
func (m *MockTieringServer) GetActorDistinctTiers(arg0 context.Context, arg1 *tiering.GetActorDistinctTiersRequest) (*tiering.GetActorDistinctTiersResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActorDistinctTiers", arg0, arg1)
	ret0, _ := ret[0].(*tiering.GetActorDistinctTiersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActorDistinctTiers indicates an expected call of GetActorDistinctTiers.
func (mr *MockTieringServerMockRecorder) GetActorDistinctTiers(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActorDistinctTiers", reflect.TypeOf((*MockTieringServer)(nil).GetActorDistinctTiers), arg0, arg1)
}

// GetActorScreenInteractionDetails mocks base method.
func (m *MockTieringServer) GetActorScreenInteractionDetails(arg0 context.Context, arg1 *tiering.GetActorScreenInteractionDetailsRequest) (*tiering.GetActorScreenInteractionDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActorScreenInteractionDetails", arg0, arg1)
	ret0, _ := ret[0].(*tiering.GetActorScreenInteractionDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActorScreenInteractionDetails indicates an expected call of GetActorScreenInteractionDetails.
func (mr *MockTieringServerMockRecorder) GetActorScreenInteractionDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActorScreenInteractionDetails", reflect.TypeOf((*MockTieringServer)(nil).GetActorScreenInteractionDetails), arg0, arg1)
}

// GetConfigParams mocks base method.
func (m *MockTieringServer) GetConfigParams(arg0 context.Context, arg1 *tiering.GetConfigParamsRequest) (*tiering.GetConfigParamsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConfigParams", arg0, arg1)
	ret0, _ := ret[0].(*tiering.GetConfigParamsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConfigParams indicates an expected call of GetConfigParams.
func (mr *MockTieringServerMockRecorder) GetConfigParams(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConfigParams", reflect.TypeOf((*MockTieringServer)(nil).GetConfigParams), arg0, arg1)
}

// GetCriteriaForActor mocks base method.
func (m *MockTieringServer) GetCriteriaForActor(arg0 context.Context, arg1 *tiering.GetCriteriaForActorRequest) (*tiering.GetCriteriaForActorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCriteriaForActor", arg0, arg1)
	ret0, _ := ret[0].(*tiering.GetCriteriaForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCriteriaForActor indicates an expected call of GetCriteriaForActor.
func (mr *MockTieringServerMockRecorder) GetCriteriaForActor(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCriteriaForActor", reflect.TypeOf((*MockTieringServer)(nil).GetCriteriaForActor), arg0, arg1)
}

// GetCurrentTierForActor mocks base method.
func (m *MockTieringServer) GetCurrentTierForActor(arg0 context.Context, arg1 *tiering.GetCurrentTierForActorRequest) (*tiering.GetCurrentTierForActorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCurrentTierForActor", arg0, arg1)
	ret0, _ := ret[0].(*tiering.GetCurrentTierForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCurrentTierForActor indicates an expected call of GetCurrentTierForActor.
func (mr *MockTieringServerMockRecorder) GetCurrentTierForActor(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCurrentTierForActor", reflect.TypeOf((*MockTieringServer)(nil).GetCurrentTierForActor), arg0, arg1)
}

// GetDetailsForCx mocks base method.
func (m *MockTieringServer) GetDetailsForCx(arg0 context.Context, arg1 *tiering.GetDetailsForCxRequest) (*tiering.GetDetailsForCxResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDetailsForCx", arg0, arg1)
	ret0, _ := ret[0].(*tiering.GetDetailsForCxResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDetailsForCx indicates an expected call of GetDetailsForCx.
func (mr *MockTieringServerMockRecorder) GetDetailsForCx(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDetailsForCx", reflect.TypeOf((*MockTieringServer)(nil).GetDetailsForCx), arg0, arg1)
}

// GetTierAtTime mocks base method.
func (m *MockTieringServer) GetTierAtTime(arg0 context.Context, arg1 *tiering.GetTierAtTimeRequest) (*tiering.GetTierAtTimeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTierAtTime", arg0, arg1)
	ret0, _ := ret[0].(*tiering.GetTierAtTimeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTierAtTime indicates an expected call of GetTierAtTime.
func (mr *MockTieringServerMockRecorder) GetTierAtTime(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTierAtTime", reflect.TypeOf((*MockTieringServer)(nil).GetTierAtTime), arg0, arg1)
}

// GetTierTimeRangesForActor mocks base method.
func (m *MockTieringServer) GetTierTimeRangesForActor(arg0 context.Context, arg1 *tiering.GetTierTimeRangesForActorRequest) (*tiering.GetTierTimeRangesForActorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTierTimeRangesForActor", arg0, arg1)
	ret0, _ := ret[0].(*tiering.GetTierTimeRangesForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTierTimeRangesForActor indicates an expected call of GetTierTimeRangesForActor.
func (mr *MockTieringServerMockRecorder) GetTierTimeRangesForActor(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTierTimeRangesForActor", reflect.TypeOf((*MockTieringServer)(nil).GetTierTimeRangesForActor), arg0, arg1)
}

// GetTieringPitch mocks base method.
func (m *MockTieringServer) GetTieringPitch(arg0 context.Context, arg1 *tiering.GetTieringPitchRequest) (*tiering.GetTieringPitchResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTieringPitch", arg0, arg1)
	ret0, _ := ret[0].(*tiering.GetTieringPitchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTieringPitch indicates an expected call of GetTieringPitch.
func (mr *MockTieringServerMockRecorder) GetTieringPitch(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTieringPitch", reflect.TypeOf((*MockTieringServer)(nil).GetTieringPitch), arg0, arg1)
}

// GetTieringPitchV2 mocks base method.
func (m *MockTieringServer) GetTieringPitchV2(arg0 context.Context, arg1 *tiering.GetTieringPitchV2Request) (*tiering.GetTieringPitchV2Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTieringPitchV2", arg0, arg1)
	ret0, _ := ret[0].(*tiering.GetTieringPitchV2Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTieringPitchV2 indicates an expected call of GetTieringPitchV2.
func (mr *MockTieringServerMockRecorder) GetTieringPitchV2(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTieringPitchV2", reflect.TypeOf((*MockTieringServer)(nil).GetTieringPitchV2), arg0, arg1)
}

// IsActorEligibleForMovement mocks base method.
func (m *MockTieringServer) IsActorEligibleForMovement(arg0 context.Context, arg1 *tiering.IsActorEligibleForMovementRequest) (*tiering.IsActorEligibleForMovementResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsActorEligibleForMovement", arg0, arg1)
	ret0, _ := ret[0].(*tiering.IsActorEligibleForMovementResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsActorEligibleForMovement indicates an expected call of IsActorEligibleForMovement.
func (mr *MockTieringServerMockRecorder) IsActorEligibleForMovement(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsActorEligibleForMovement", reflect.TypeOf((*MockTieringServer)(nil).IsActorEligibleForMovement), arg0, arg1)
}

// IsTieringEnabledForActor mocks base method.
func (m *MockTieringServer) IsTieringEnabledForActor(arg0 context.Context, arg1 *tiering.IsTieringEnabledForActorRequest) (*tiering.IsTieringEnabledForActorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsTieringEnabledForActor", arg0, arg1)
	ret0, _ := ret[0].(*tiering.IsTieringEnabledForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsTieringEnabledForActor indicates an expected call of IsTieringEnabledForActor.
func (mr *MockTieringServerMockRecorder) IsTieringEnabledForActor(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsTieringEnabledForActor", reflect.TypeOf((*MockTieringServer)(nil).IsTieringEnabledForActor), arg0, arg1)
}

// IsUserEligibleForRewards mocks base method.
func (m *MockTieringServer) IsUserEligibleForRewards(arg0 context.Context, arg1 *tiering.IsUserEligibleForRewardsRequest) (*tiering.IsUserEligibleForRewardsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsUserEligibleForRewards", arg0, arg1)
	ret0, _ := ret[0].(*tiering.IsUserEligibleForRewardsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsUserEligibleForRewards indicates an expected call of IsUserEligibleForRewards.
func (mr *MockTieringServerMockRecorder) IsUserEligibleForRewards(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsUserEligibleForRewards", reflect.TypeOf((*MockTieringServer)(nil).IsUserEligibleForRewards), arg0, arg1)
}

// IsUserInGracePeriod mocks base method.
func (m *MockTieringServer) IsUserInGracePeriod(arg0 context.Context, arg1 *tiering.IsUserInGracePeriodRequest) (*tiering.IsUserInGracePeriodResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsUserInGracePeriod", arg0, arg1)
	ret0, _ := ret[0].(*tiering.IsUserInGracePeriodResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsUserInGracePeriod indicates an expected call of IsUserInGracePeriod.
func (mr *MockTieringServerMockRecorder) IsUserInGracePeriod(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsUserInGracePeriod", reflect.TypeOf((*MockTieringServer)(nil).IsUserInGracePeriod), arg0, arg1)
}

// OverrideCoolOffPeriod mocks base method.
func (m *MockTieringServer) OverrideCoolOffPeriod(arg0 context.Context, arg1 *tiering.OverrideCoolOffPeriodRequest) (*tiering.OverrideCoolOffPeriodResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OverrideCoolOffPeriod", arg0, arg1)
	ret0, _ := ret[0].(*tiering.OverrideCoolOffPeriodResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OverrideCoolOffPeriod indicates an expected call of OverrideCoolOffPeriod.
func (mr *MockTieringServerMockRecorder) OverrideCoolOffPeriod(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OverrideCoolOffPeriod", reflect.TypeOf((*MockTieringServer)(nil).OverrideCoolOffPeriod), arg0, arg1)
}

// OverrideGracePeriod mocks base method.
func (m *MockTieringServer) OverrideGracePeriod(arg0 context.Context, arg1 *tiering.OverrideGracePeriodRequest) (*tiering.OverrideGracePeriodResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OverrideGracePeriod", arg0, arg1)
	ret0, _ := ret[0].(*tiering.OverrideGracePeriodResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OverrideGracePeriod indicates an expected call of OverrideGracePeriod.
func (mr *MockTieringServerMockRecorder) OverrideGracePeriod(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OverrideGracePeriod", reflect.TypeOf((*MockTieringServer)(nil).OverrideGracePeriod), arg0, arg1)
}

// RecordComponentShownToActor mocks base method.
func (m *MockTieringServer) RecordComponentShownToActor(arg0 context.Context, arg1 *tiering.RecordComponentShownToActorRequest) (*tiering.RecordComponentShownToActorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordComponentShownToActor", arg0, arg1)
	ret0, _ := ret[0].(*tiering.RecordComponentShownToActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecordComponentShownToActor indicates an expected call of RecordComponentShownToActor.
func (mr *MockTieringServerMockRecorder) RecordComponentShownToActor(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordComponentShownToActor", reflect.TypeOf((*MockTieringServer)(nil).RecordComponentShownToActor), arg0, arg1)
}

// ShowComponentToActor mocks base method.
func (m *MockTieringServer) ShowComponentToActor(arg0 context.Context, arg1 *tiering.ShowComponentToActorRequest) (*tiering.ShowComponentToActorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ShowComponentToActor", arg0, arg1)
	ret0, _ := ret[0].(*tiering.ShowComponentToActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ShowComponentToActor indicates an expected call of ShowComponentToActor.
func (mr *MockTieringServerMockRecorder) ShowComponentToActor(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ShowComponentToActor", reflect.TypeOf((*MockTieringServer)(nil).ShowComponentToActor), arg0, arg1)
}

// Upgrade mocks base method.
func (m *MockTieringServer) Upgrade(arg0 context.Context, arg1 *tiering.UpgradeRequest) (*tiering.UpgradeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Upgrade", arg0, arg1)
	ret0, _ := ret[0].(*tiering.UpgradeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Upgrade indicates an expected call of Upgrade.
func (mr *MockTieringServerMockRecorder) Upgrade(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Upgrade", reflect.TypeOf((*MockTieringServer)(nil).Upgrade), arg0, arg1)
}

// MockUnsafeTieringServer is a mock of UnsafeTieringServer interface.
type MockUnsafeTieringServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeTieringServerMockRecorder
}

// MockUnsafeTieringServerMockRecorder is the mock recorder for MockUnsafeTieringServer.
type MockUnsafeTieringServerMockRecorder struct {
	mock *MockUnsafeTieringServer
}

// NewMockUnsafeTieringServer creates a new mock instance.
func NewMockUnsafeTieringServer(ctrl *gomock.Controller) *MockUnsafeTieringServer {
	mock := &MockUnsafeTieringServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeTieringServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeTieringServer) EXPECT() *MockUnsafeTieringServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedTieringServer mocks base method.
func (m *MockUnsafeTieringServer) mustEmbedUnimplementedTieringServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedTieringServer")
}

// mustEmbedUnimplementedTieringServer indicates an expected call of mustEmbedUnimplementedTieringServer.
func (mr *MockUnsafeTieringServerMockRecorder) mustEmbedUnimplementedTieringServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedTieringServer", reflect.TypeOf((*MockUnsafeTieringServer)(nil).mustEmbedUnimplementedTieringServer))
}
