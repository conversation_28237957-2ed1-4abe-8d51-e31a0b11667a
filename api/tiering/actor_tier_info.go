package tiering

import "time"

// IsTrialActive checks if the trial is currently active based on the start and end times.
// update api/tiering/external/external.go if this function is modified.
func (x *TrialDetails) IsTrialActive() bool {
	if x == nil {
		return false
	}

	if x.GetTrialStartTime() == nil || x.GetTrialEndTime() == nil {
		return false
	}

	return x.GetTrialStartTime().AsTime().Before(time.Now()) && x.GetTrialEndTime().AsTime().After(time.Now())
}
