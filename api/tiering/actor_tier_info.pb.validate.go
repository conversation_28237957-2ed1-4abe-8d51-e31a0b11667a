// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/tiering/internal/actor_tier_info.proto

package tiering

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/tiering/enums"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.Tier(0)
)

// Validate checks the field values on ActorTierInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ActorTierInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActorTierInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ActorTierInfoMultiError, or
// nil if none found.
func (m *ActorTierInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ActorTierInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for Tier

	// no validation rules for MovementReferenceId

	// no validation rules for CriteriaReferenceId

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActorTierInfoValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActorTierInfoValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActorTierInfoValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActorTierInfoValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActorTierInfoValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActorTierInfoValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorBaseTier

	if len(errors) > 0 {
		return ActorTierInfoMultiError(errors)
	}

	return nil
}

// ActorTierInfoMultiError is an error wrapping multiple validation errors
// returned by ActorTierInfo.ValidateAll() if the designated constraints
// aren't met.
type ActorTierInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActorTierInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActorTierInfoMultiError) AllErrors() []error { return m }

// ActorTierInfoValidationError is the validation error returned by
// ActorTierInfo.Validate if the designated constraints aren't met.
type ActorTierInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActorTierInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActorTierInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActorTierInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActorTierInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActorTierInfoValidationError) ErrorName() string { return "ActorTierInfoValidationError" }

// Error satisfies the builtin error interface
func (e ActorTierInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActorTierInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActorTierInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActorTierInfoValidationError{}
