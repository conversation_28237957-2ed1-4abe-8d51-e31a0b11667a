package enums

import (
	"errors"
)

func GetMovementTypeFromStartAndEndTiers(fromTier, toTier Tier) (TierMovementType, error) {
	if fromTier == Tier_TIER_UNSPECIFIED || toTier == Tier_TIER_UNSPECIFIED {
		return TierMovementType_TIER_MOVEMENT_TYPE_UNSPECIFIED, nil
	}

	if fromTier == toTier {
		return TierMovementType_TIER_MOVEMENT_TYPE_UNSPECIFIED, errors.New("start and end tiers are same")
	}
	// Get movement type
	if fromTier.Number() < toTier.Number() {
		return TierMovementType_TIER_MOVEMENT_TYPE_UPGRADE, nil
	}

	return TierMovementType_TIER_MOVEMENT_TYPE_DOWNGRADE, nil
}
