// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/acquisition/crossattach/user_attribute.proto

package crossattach

import (
	networth "github.com/epifi/gamma/api/insights/networth"
	lendability "github.com/epifi/gamma/api/preapprovedloan/lendability"
	onboarding "github.com/epifi/gamma/api/user/onboarding"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Enum to identify type of user attribute
// Each type defined below is mapped to a value which is used for cross attach
type UserAttribute int32

const (
	UserAttribute_USER_ATTRIBUTE_UNSPECIFIED            UserAttribute = 0
	UserAttribute_USER_ATTRIBUTE_SCREENER_CHECK_RESULT  UserAttribute = 1
	UserAttribute_USER_ATTRIBUTE_LOAN_AFFINITY_CATEGORY UserAttribute = 2
	// Probability of default category
	UserAttribute_USER_ATTRIBUTE_PD_CATEGORY                          UserAttribute = 3
	UserAttribute_USER_ATTRIBUTE_HOME_LANDED_TIMESTAMP                UserAttribute = 4
	UserAttribute_USER_ATTRIBUTE_LATEST_ASSET_CONNECTED_TIMESTAMP     UserAttribute = 5
	UserAttribute_USER_ATTRIBUTE_NET_WORTH_ASSET_VALUES_LIST          UserAttribute = 7
	UserAttribute_USER_ATTRIBUTE_ONBOARDING_FEATURE_DETAILS           UserAttribute = 8
	UserAttribute_USER_ATTRIBUTE_ONBOARDING_FI_LITE_DETAILS           UserAttribute = 9
	UserAttribute_USER_ATTRIBUTE_ONBOARDING_INTENT_SELECTION_METADATA UserAttribute = 10
)

// Enum value maps for UserAttribute.
var (
	UserAttribute_name = map[int32]string{
		0:  "USER_ATTRIBUTE_UNSPECIFIED",
		1:  "USER_ATTRIBUTE_SCREENER_CHECK_RESULT",
		2:  "USER_ATTRIBUTE_LOAN_AFFINITY_CATEGORY",
		3:  "USER_ATTRIBUTE_PD_CATEGORY",
		4:  "USER_ATTRIBUTE_HOME_LANDED_TIMESTAMP",
		5:  "USER_ATTRIBUTE_LATEST_ASSET_CONNECTED_TIMESTAMP",
		7:  "USER_ATTRIBUTE_NET_WORTH_ASSET_VALUES_LIST",
		8:  "USER_ATTRIBUTE_ONBOARDING_FEATURE_DETAILS",
		9:  "USER_ATTRIBUTE_ONBOARDING_FI_LITE_DETAILS",
		10: "USER_ATTRIBUTE_ONBOARDING_INTENT_SELECTION_METADATA",
	}
	UserAttribute_value = map[string]int32{
		"USER_ATTRIBUTE_UNSPECIFIED":                          0,
		"USER_ATTRIBUTE_SCREENER_CHECK_RESULT":                1,
		"USER_ATTRIBUTE_LOAN_AFFINITY_CATEGORY":               2,
		"USER_ATTRIBUTE_PD_CATEGORY":                          3,
		"USER_ATTRIBUTE_HOME_LANDED_TIMESTAMP":                4,
		"USER_ATTRIBUTE_LATEST_ASSET_CONNECTED_TIMESTAMP":     5,
		"USER_ATTRIBUTE_NET_WORTH_ASSET_VALUES_LIST":          7,
		"USER_ATTRIBUTE_ONBOARDING_FEATURE_DETAILS":           8,
		"USER_ATTRIBUTE_ONBOARDING_FI_LITE_DETAILS":           9,
		"USER_ATTRIBUTE_ONBOARDING_INTENT_SELECTION_METADATA": 10,
	}
)

func (x UserAttribute) Enum() *UserAttribute {
	p := new(UserAttribute)
	*p = x
	return p
}

func (x UserAttribute) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserAttribute) Descriptor() protoreflect.EnumDescriptor {
	return file_api_acquisition_crossattach_user_attribute_proto_enumTypes[0].Descriptor()
}

func (UserAttribute) Type() protoreflect.EnumType {
	return &file_api_acquisition_crossattach_user_attribute_proto_enumTypes[0]
}

func (x UserAttribute) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserAttribute.Descriptor instead.
func (UserAttribute) EnumDescriptor() ([]byte, []int) {
	return file_api_acquisition_crossattach_user_attribute_proto_rawDescGZIP(), []int{0}
}

// UserAttributeValue is used to store value of user attribute. Each value maps to one user attribute type.
type UserAttributeValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Value:
	//
	//	*UserAttributeValue_ScreenerCheckResult
	//	*UserAttributeValue_LoanAffinityCategory
	//	*UserAttributeValue_PdCategory
	//	*UserAttributeValue_HomeLandedTimestamp
	//	*UserAttributeValue_LatestAssetConnectedTimestamp
	//	*UserAttributeValue_WealthBuilderConnected
	//	*UserAttributeValue_NetWorthAssetValueList_
	//	*UserAttributeValue_OnbFeatureDetails
	//	*UserAttributeValue_OnbFiLiteDetails
	//	*UserAttributeValue_OnbIntentSelectionMetadata
	Value isUserAttributeValue_Value `protobuf_oneof:"value"`
}

func (x *UserAttributeValue) Reset() {
	*x = UserAttributeValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_acquisition_crossattach_user_attribute_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserAttributeValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserAttributeValue) ProtoMessage() {}

func (x *UserAttributeValue) ProtoReflect() protoreflect.Message {
	mi := &file_api_acquisition_crossattach_user_attribute_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserAttributeValue.ProtoReflect.Descriptor instead.
func (*UserAttributeValue) Descriptor() ([]byte, []int) {
	return file_api_acquisition_crossattach_user_attribute_proto_rawDescGZIP(), []int{0}
}

func (m *UserAttributeValue) GetValue() isUserAttributeValue_Value {
	if m != nil {
		return m.Value
	}
	return nil
}

func (x *UserAttributeValue) GetScreenerCheckResult() bool {
	if x, ok := x.GetValue().(*UserAttributeValue_ScreenerCheckResult); ok {
		return x.ScreenerCheckResult
	}
	return false
}

func (x *UserAttributeValue) GetLoanAffinityCategory() lendability.LoanAffinityCategory {
	if x, ok := x.GetValue().(*UserAttributeValue_LoanAffinityCategory); ok {
		return x.LoanAffinityCategory
	}
	return lendability.LoanAffinityCategory(0)
}

func (x *UserAttributeValue) GetPdCategory() lendability.PDCategory {
	if x, ok := x.GetValue().(*UserAttributeValue_PdCategory); ok {
		return x.PdCategory
	}
	return lendability.PDCategory(0)
}

func (x *UserAttributeValue) GetHomeLandedTimestamp() *timestamppb.Timestamp {
	if x, ok := x.GetValue().(*UserAttributeValue_HomeLandedTimestamp); ok {
		return x.HomeLandedTimestamp
	}
	return nil
}

func (x *UserAttributeValue) GetLatestAssetConnectedTimestamp() *timestamppb.Timestamp {
	if x, ok := x.GetValue().(*UserAttributeValue_LatestAssetConnectedTimestamp); ok {
		return x.LatestAssetConnectedTimestamp
	}
	return nil
}

func (x *UserAttributeValue) GetWealthBuilderConnected() bool {
	if x, ok := x.GetValue().(*UserAttributeValue_WealthBuilderConnected); ok {
		return x.WealthBuilderConnected
	}
	return false
}

func (x *UserAttributeValue) GetNetWorthAssetValueList() *UserAttributeValue_NetWorthAssetValueList {
	if x, ok := x.GetValue().(*UserAttributeValue_NetWorthAssetValueList_); ok {
		return x.NetWorthAssetValueList
	}
	return nil
}

func (x *UserAttributeValue) GetOnbFeatureDetails() *onboarding.FeatureDetails {
	if x, ok := x.GetValue().(*UserAttributeValue_OnbFeatureDetails); ok {
		return x.OnbFeatureDetails
	}
	return nil
}

func (x *UserAttributeValue) GetOnbFiLiteDetails() *onboarding.FiLiteDetails {
	if x, ok := x.GetValue().(*UserAttributeValue_OnbFiLiteDetails); ok {
		return x.OnbFiLiteDetails
	}
	return nil
}

func (x *UserAttributeValue) GetOnbIntentSelectionMetadata() *onboarding.IntentSelectionMetadata {
	if x, ok := x.GetValue().(*UserAttributeValue_OnbIntentSelectionMetadata); ok {
		return x.OnbIntentSelectionMetadata
	}
	return nil
}

type isUserAttributeValue_Value interface {
	isUserAttributeValue_Value()
}

type UserAttributeValue_ScreenerCheckResult struct {
	// Maps to USER_ATTRIBUTE_SCREENER_STATUS
	ScreenerCheckResult bool `protobuf:"varint,1,opt,name=screener_check_result,json=screenerCheckResult,proto3,oneof"`
}

type UserAttributeValue_LoanAffinityCategory struct {
	// Maps to USER_ATTRIBUTE_LOAN_AFFINITY_CATEGORY
	LoanAffinityCategory lendability.LoanAffinityCategory `protobuf:"varint,2,opt,name=loan_affinity_category,json=loanAffinityCategory,proto3,enum=lendability.LoanAffinityCategory,oneof"`
}

type UserAttributeValue_PdCategory struct {
	// Maps to USER_ATTRIBUTE_PD_CATEGORY
	PdCategory lendability.PDCategory `protobuf:"varint,3,opt,name=pd_category,json=pdCategory,proto3,enum=lendability.PDCategory,oneof"`
}

type UserAttributeValue_HomeLandedTimestamp struct {
	// Maps to USER_ATTRIBUTE_HOME_LANDED_TIMESTAMP
	HomeLandedTimestamp *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=home_landed_timestamp,json=homeLandedTimestamp,proto3,oneof"`
}

type UserAttributeValue_LatestAssetConnectedTimestamp struct {
	// Maps to USER_ATTRIBUTE_LATEST_ASSET_CONNECTED_TIMESTAMP
	LatestAssetConnectedTimestamp *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=latest_asset_connected_timestamp,json=latestAssetConnectedTimestamp,proto3,oneof"`
}

type UserAttributeValue_WealthBuilderConnected struct {
	// Maps to USER_ATTRIBUTE_WEALTH_BUILDER_CONNECTED
	WealthBuilderConnected bool `protobuf:"varint,6,opt,name=wealth_builder_connected,json=wealthBuilderConnected,proto3,oneof"`
}

type UserAttributeValue_NetWorthAssetValueList_ struct {
	// Maps to USER_ATTRIBUTE_NET_WORTH_ASSET_VALUES_LIST
	NetWorthAssetValueList *UserAttributeValue_NetWorthAssetValueList `protobuf:"bytes,7,opt,name=net_worth_asset_value_list,json=netWorthAssetValueList,proto3,oneof"`
}

type UserAttributeValue_OnbFeatureDetails struct {
	// Maps to USER_ATTRIBUTE_ONBOARDING_FEATURE_DETAILS
	OnbFeatureDetails *onboarding.FeatureDetails `protobuf:"bytes,8,opt,name=onb_feature_details,json=onbFeatureDetails,proto3,oneof"`
}

type UserAttributeValue_OnbFiLiteDetails struct {
	// Maps to USER_ATTRIBUTE_ONBOARDING_FI_LITE_DETAILS
	OnbFiLiteDetails *onboarding.FiLiteDetails `protobuf:"bytes,9,opt,name=onb_fi_lite_details,json=onbFiLiteDetails,proto3,oneof"`
}

type UserAttributeValue_OnbIntentSelectionMetadata struct {
	// Maps to USER_ATTRIBUTE_ONBOARDING_INTENT_DETAILS
	OnbIntentSelectionMetadata *onboarding.IntentSelectionMetadata `protobuf:"bytes,10,opt,name=onb_intent_selection_metadata,json=onbIntentSelectionMetadata,proto3,oneof"`
}

func (*UserAttributeValue_ScreenerCheckResult) isUserAttributeValue_Value() {}

func (*UserAttributeValue_LoanAffinityCategory) isUserAttributeValue_Value() {}

func (*UserAttributeValue_PdCategory) isUserAttributeValue_Value() {}

func (*UserAttributeValue_HomeLandedTimestamp) isUserAttributeValue_Value() {}

func (*UserAttributeValue_LatestAssetConnectedTimestamp) isUserAttributeValue_Value() {}

func (*UserAttributeValue_WealthBuilderConnected) isUserAttributeValue_Value() {}

func (*UserAttributeValue_NetWorthAssetValueList_) isUserAttributeValue_Value() {}

func (*UserAttributeValue_OnbFeatureDetails) isUserAttributeValue_Value() {}

func (*UserAttributeValue_OnbFiLiteDetails) isUserAttributeValue_Value() {}

func (*UserAttributeValue_OnbIntentSelectionMetadata) isUserAttributeValue_Value() {}

type UserAttributeValue_NetWorthAssetValueList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NetWorthAssetValues []*networth.AssetValue `protobuf:"bytes,1,rep,name=net_worth_asset_values,json=netWorthAssetValues,proto3" json:"net_worth_asset_values,omitempty"`
}

func (x *UserAttributeValue_NetWorthAssetValueList) Reset() {
	*x = UserAttributeValue_NetWorthAssetValueList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_acquisition_crossattach_user_attribute_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserAttributeValue_NetWorthAssetValueList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserAttributeValue_NetWorthAssetValueList) ProtoMessage() {}

func (x *UserAttributeValue_NetWorthAssetValueList) ProtoReflect() protoreflect.Message {
	mi := &file_api_acquisition_crossattach_user_attribute_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserAttributeValue_NetWorthAssetValueList.ProtoReflect.Descriptor instead.
func (*UserAttributeValue_NetWorthAssetValueList) Descriptor() ([]byte, []int) {
	return file_api_acquisition_crossattach_user_attribute_proto_rawDescGZIP(), []int{0, 0}
}

func (x *UserAttributeValue_NetWorthAssetValueList) GetNetWorthAssetValues() []*networth.AssetValue {
	if x != nil {
		return x.NetWorthAssetValues
	}
	return nil
}

var File_api_acquisition_crossattach_user_attribute_proto protoreflect.FileDescriptor

var file_api_acquisition_crossattach_user_attribute_proto_rawDesc = []byte{
	0x0a, 0x30, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x63, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x2f, 0x63, 0x72, 0x6f, 0x73, 0x73, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x2f, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x17, 0x61, 0x63, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x63, 0x72, 0x6f, 0x73, 0x73, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x1a, 0x23, 0x61, 0x70, 0x69,
	0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x74, 0x68, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x2a, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x35, 0x61, 0x70,
	0x69, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe3, 0x07, 0x0a, 0x12, 0x55, 0x73, 0x65, 0x72, 0x41, 0x74, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x34, 0x0a, 0x15, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x13, 0x73, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x59, 0x0a, 0x16, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x66, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x79, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x21, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x2e,
	0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x66, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x79, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x48, 0x00, 0x52, 0x14, 0x6c, 0x6f, 0x61, 0x6e, 0x41, 0x66, 0x66, 0x69,
	0x6e, 0x69, 0x74, 0x79, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x3a, 0x0a, 0x0b,
	0x70, 0x64, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x17, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x2e,
	0x50, 0x44, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x48, 0x00, 0x52, 0x0a, 0x70, 0x64,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x50, 0x0a, 0x15, 0x68, 0x6f, 0x6d, 0x65,
	0x5f, 0x6c, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x48, 0x00, 0x52, 0x13, 0x68, 0x6f, 0x6d, 0x65, 0x4c, 0x61, 0x6e, 0x64, 0x65,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x65, 0x0a, 0x20, 0x6c, 0x61,
	0x74, 0x65, 0x73, 0x74, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x48, 0x00, 0x52, 0x1d, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x43,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x12, 0x3a, 0x0a, 0x18, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x5f, 0x62, 0x75, 0x69, 0x6c,
	0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x16, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x42, 0x75, 0x69,
	0x6c, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x12, 0x80, 0x01,
	0x0a, 0x1a, 0x6e, 0x65, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x5f, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x42, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x63, 0x72, 0x6f, 0x73, 0x73, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x2e,
	0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x41, 0x73, 0x73, 0x65, 0x74, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x00, 0x52, 0x16, 0x6e, 0x65, 0x74, 0x57, 0x6f, 0x72,
	0x74, 0x68, 0x41, 0x73, 0x73, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x51, 0x0a, 0x13, 0x6f, 0x6e, 0x62, 0x5f, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00,
	0x52, 0x11, 0x6f, 0x6e, 0x62, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x4f, 0x0a, 0x13, 0x6f, 0x6e, 0x62, 0x5f, 0x66, 0x69, 0x5f, 0x6c, 0x69,
	0x74, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x2e, 0x46, 0x69, 0x4c, 0x69, 0x74, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x48, 0x00, 0x52, 0x10, 0x6f, 0x6e, 0x62, 0x46, 0x69, 0x4c, 0x69, 0x74, 0x65, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x6d, 0x0a, 0x1d, 0x6f, 0x6e, 0x62, 0x5f, 0x69, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x49, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x1a, 0x6f, 0x6e, 0x62, 0x49, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x1a, 0x6c, 0x0a, 0x16, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x52, 0x0a,
	0x16, 0x6e, 0x65, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74,
	0x68, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x13, 0x6e, 0x65,
	0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x41, 0x73, 0x73, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x73, 0x42, 0x07, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x2a, 0xca, 0x03, 0x0a, 0x0d, 0x55,
	0x73, 0x65, 0x72, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x12, 0x1e, 0x0a, 0x1a,
	0x55, 0x53, 0x45, 0x52, 0x5f, 0x41, 0x54, 0x54, 0x52, 0x49, 0x42, 0x55, 0x54, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x28, 0x0a, 0x24,
	0x55, 0x53, 0x45, 0x52, 0x5f, 0x41, 0x54, 0x54, 0x52, 0x49, 0x42, 0x55, 0x54, 0x45, 0x5f, 0x53,
	0x43, 0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x52, 0x45,
	0x53, 0x55, 0x4c, 0x54, 0x10, 0x01, 0x12, 0x29, 0x0a, 0x25, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x41,
	0x54, 0x54, 0x52, 0x49, 0x42, 0x55, 0x54, 0x45, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x46,
	0x46, 0x49, 0x4e, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x10,
	0x02, 0x12, 0x1e, 0x0a, 0x1a, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x41, 0x54, 0x54, 0x52, 0x49, 0x42,
	0x55, 0x54, 0x45, 0x5f, 0x50, 0x44, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x10,
	0x03, 0x12, 0x28, 0x0a, 0x24, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x41, 0x54, 0x54, 0x52, 0x49, 0x42,
	0x55, 0x54, 0x45, 0x5f, 0x48, 0x4f, 0x4d, 0x45, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x45, 0x44, 0x5f,
	0x54, 0x49, 0x4d, 0x45, 0x53, 0x54, 0x41, 0x4d, 0x50, 0x10, 0x04, 0x12, 0x33, 0x0a, 0x2f, 0x55,
	0x53, 0x45, 0x52, 0x5f, 0x41, 0x54, 0x54, 0x52, 0x49, 0x42, 0x55, 0x54, 0x45, 0x5f, 0x4c, 0x41,
	0x54, 0x45, 0x53, 0x54, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x4e, 0x45,
	0x43, 0x54, 0x45, 0x44, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x53, 0x54, 0x41, 0x4d, 0x50, 0x10, 0x05,
	0x12, 0x2e, 0x0a, 0x2a, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x41, 0x54, 0x54, 0x52, 0x49, 0x42, 0x55,
	0x54, 0x45, 0x5f, 0x4e, 0x45, 0x54, 0x5f, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x41, 0x53, 0x53,
	0x45, 0x54, 0x5f, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x53, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x10, 0x07,
	0x12, 0x2d, 0x0a, 0x29, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x41, 0x54, 0x54, 0x52, 0x49, 0x42, 0x55,
	0x54, 0x45, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x45,
	0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x08, 0x12,
	0x2d, 0x0a, 0x29, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x41, 0x54, 0x54, 0x52, 0x49, 0x42, 0x55, 0x54,
	0x45, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x49, 0x5f,
	0x4c, 0x49, 0x54, 0x45, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x09, 0x12, 0x37,
	0x0a, 0x33, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x41, 0x54, 0x54, 0x52, 0x49, 0x42, 0x55, 0x54, 0x45,
	0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x49, 0x4e, 0x54, 0x45,
	0x4e, 0x54, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4d, 0x45, 0x54,
	0x41, 0x44, 0x41, 0x54, 0x41, 0x10, 0x0a, 0x42, 0x68, 0x0a, 0x32, 0x63, 0x6f, 0x6d, 0x2e, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x63, 0x72, 0x6f, 0x73, 0x73, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x5a, 0x32, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x63, 0x71, 0x75, 0x69, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x63, 0x72, 0x6f, 0x73, 0x73, 0x61, 0x74, 0x74, 0x61, 0x63,
	0x68, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_acquisition_crossattach_user_attribute_proto_rawDescOnce sync.Once
	file_api_acquisition_crossattach_user_attribute_proto_rawDescData = file_api_acquisition_crossattach_user_attribute_proto_rawDesc
)

func file_api_acquisition_crossattach_user_attribute_proto_rawDescGZIP() []byte {
	file_api_acquisition_crossattach_user_attribute_proto_rawDescOnce.Do(func() {
		file_api_acquisition_crossattach_user_attribute_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_acquisition_crossattach_user_attribute_proto_rawDescData)
	})
	return file_api_acquisition_crossattach_user_attribute_proto_rawDescData
}

var file_api_acquisition_crossattach_user_attribute_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_acquisition_crossattach_user_attribute_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_acquisition_crossattach_user_attribute_proto_goTypes = []interface{}{
	(UserAttribute)(0),                                // 0: acquisition.crossattach.UserAttribute
	(*UserAttributeValue)(nil),                        // 1: acquisition.crossattach.UserAttributeValue
	(*UserAttributeValue_NetWorthAssetValueList)(nil), // 2: acquisition.crossattach.UserAttributeValue.NetWorthAssetValueList
	(lendability.LoanAffinityCategory)(0),             // 3: lendability.LoanAffinityCategory
	(lendability.PDCategory)(0),                       // 4: lendability.PDCategory
	(*timestamppb.Timestamp)(nil),                     // 5: google.protobuf.Timestamp
	(*onboarding.FeatureDetails)(nil),                 // 6: user.onboarding.FeatureDetails
	(*onboarding.FiLiteDetails)(nil),                  // 7: user.onboarding.FiLiteDetails
	(*onboarding.IntentSelectionMetadata)(nil),        // 8: user.onboarding.IntentSelectionMetadata
	(*networth.AssetValue)(nil),                       // 9: insights.networth.AssetValue
}
var file_api_acquisition_crossattach_user_attribute_proto_depIdxs = []int32{
	3, // 0: acquisition.crossattach.UserAttributeValue.loan_affinity_category:type_name -> lendability.LoanAffinityCategory
	4, // 1: acquisition.crossattach.UserAttributeValue.pd_category:type_name -> lendability.PDCategory
	5, // 2: acquisition.crossattach.UserAttributeValue.home_landed_timestamp:type_name -> google.protobuf.Timestamp
	5, // 3: acquisition.crossattach.UserAttributeValue.latest_asset_connected_timestamp:type_name -> google.protobuf.Timestamp
	2, // 4: acquisition.crossattach.UserAttributeValue.net_worth_asset_value_list:type_name -> acquisition.crossattach.UserAttributeValue.NetWorthAssetValueList
	6, // 5: acquisition.crossattach.UserAttributeValue.onb_feature_details:type_name -> user.onboarding.FeatureDetails
	7, // 6: acquisition.crossattach.UserAttributeValue.onb_fi_lite_details:type_name -> user.onboarding.FiLiteDetails
	8, // 7: acquisition.crossattach.UserAttributeValue.onb_intent_selection_metadata:type_name -> user.onboarding.IntentSelectionMetadata
	9, // 8: acquisition.crossattach.UserAttributeValue.NetWorthAssetValueList.net_worth_asset_values:type_name -> insights.networth.AssetValue
	9, // [9:9] is the sub-list for method output_type
	9, // [9:9] is the sub-list for method input_type
	9, // [9:9] is the sub-list for extension type_name
	9, // [9:9] is the sub-list for extension extendee
	0, // [0:9] is the sub-list for field type_name
}

func init() { file_api_acquisition_crossattach_user_attribute_proto_init() }
func file_api_acquisition_crossattach_user_attribute_proto_init() {
	if File_api_acquisition_crossattach_user_attribute_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_acquisition_crossattach_user_attribute_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserAttributeValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_acquisition_crossattach_user_attribute_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserAttributeValue_NetWorthAssetValueList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_acquisition_crossattach_user_attribute_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*UserAttributeValue_ScreenerCheckResult)(nil),
		(*UserAttributeValue_LoanAffinityCategory)(nil),
		(*UserAttributeValue_PdCategory)(nil),
		(*UserAttributeValue_HomeLandedTimestamp)(nil),
		(*UserAttributeValue_LatestAssetConnectedTimestamp)(nil),
		(*UserAttributeValue_WealthBuilderConnected)(nil),
		(*UserAttributeValue_NetWorthAssetValueList_)(nil),
		(*UserAttributeValue_OnbFeatureDetails)(nil),
		(*UserAttributeValue_OnbFiLiteDetails)(nil),
		(*UserAttributeValue_OnbIntentSelectionMetadata)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_acquisition_crossattach_user_attribute_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_acquisition_crossattach_user_attribute_proto_goTypes,
		DependencyIndexes: file_api_acquisition_crossattach_user_attribute_proto_depIdxs,
		EnumInfos:         file_api_acquisition_crossattach_user_attribute_proto_enumTypes,
		MessageInfos:      file_api_acquisition_crossattach_user_attribute_proto_msgTypes,
	}.Build()
	File_api_acquisition_crossattach_user_attribute_proto = out.File
	file_api_acquisition_crossattach_user_attribute_proto_rawDesc = nil
	file_api_acquisition_crossattach_user_attribute_proto_goTypes = nil
	file_api_acquisition_crossattach_user_attribute_proto_depIdxs = nil
}
