UPDATE
    rewards
SET
    reward_options = replace(replace(reward_options, 'https://epifi-icons.pointz.in/casper/catalog/REWARDS/Fi-points.png', 'https://epifi-icons.pointz.in/rewards/reward-details/fi-points-2d-with-bg.png'), '#00B899', ''),
	updated_at = now()
WHERE
    offer_id = 'ae4b3f0b-6585-416b-89f4-554cba604e0b';

UPDATE
    rewards
SET
    chosen_reward = replace(replace(chosen_reward, 'https://epifi-icons.pointz.in/casper/catalog/REWARDS/Fi-points.png', 'https://epifi-icons.pointz.in/rewards/reward-details/fi-points-2d-with-bg.png'), '#00B899', ''),
	updated_at = now()
WHERE
    offer_id = 'ae4b3f0b-6585-416b-89f4-554cba604e0b'
    AND status = 'PROCESSED';


UPDATE
	rewards
SET
	reward_options = replace(reward_options, '#00B899', ''),
	updated_at = now()
WHERE
	offer_id in ('615b7ca3-6f62-4ca4-82f1-72f77f5b13c4','ac83c260-3796-4b13-bd5b-5c7aa4cb966a','772d4dce-8ce3-4766-9855-8ceefb36f33e','ab0db87c-7d0c-48da-bf4f-0f6690f1d79b','89336879-62e8-46f5-a9e1-69a0a03866d6','39efd260-c3cd-420b-86bd-89f40c3c6479');

UPDATE
	rewards
SET
	chosen_reward = replace(chosen_reward, '#00B899', ''),
	updated_at = now()
WHERE
	offer_id in ('615b7ca3-6f62-4ca4-82f1-72f77f5b13c4','ac83c260-3796-4b13-bd5b-5c7aa4cb966a','772d4dce-8ce3-4766-9855-8ceefb36f33e','ab0db87c-7d0c-48da-bf4f-0f6690f1d79b','89336879-62e8-46f5-a9e1-69a0a03866d6','39efd260-c3cd-420b-86bd-89f40c3c6479')
  AND status = 'PROCESSED';
