-- update reward offer with correct display config in reward meta
UPDATE reward_offers
SET reward_meta = '{"rewardAggregates":{"rewardUnitsCapMonthlyUserAggregate":{"unitsCaps":[{"rewardType":"FI_COINS","units":1000}]}},"rewardLockTimeConfig":{"relativeTimeInMinutes":0},"defaultDecideTimeInSecs":45,"probability":1,"rewardConfigOptions":[{"rewardType":"FI_COINS","displayConfig":{"beforeClaimTextExpression":"Fi-Points","afterClaimTextExpression":"Fi-Points","icon":"https://epifi-icons.pointz.in/rewards/reward-details/fi-points-2d-with-bg.png"},"rewardProcessingTimeConfig":{"relativeTimeInMinutes":0},"expressionRangeProbabilityConfig":{"expression":"TXN_AMOUNT","rangeProbabilityUnits":[{"unitPercentageStart":10,"unitPercentageEnd":10,"percentage":100}],"upperLimit":50,"lowerLimit":1},"fiCoinsRewardConfig":{"isFiPoints":"TRUE"}}],"rewardNotificationConfig":{"rewardProcessingSuccessfulNotificationConfig":{"notifications":[{"notificationType":"SYSTEM_TRAY"},{"notificationType":"IN_APP"}]}},"isImplicitLockingDisabled":true,"rewardExpiryTimeConfig":{"relativeTimeInMinutes":43200}}',
	updated_at  = now()
WHERE id = 'ae4b3f0b-6585-416b-89f4-554cba604e0b';

UPDATE reward_offers
SET reward_meta = replace(reward_meta, '#00B899', ''),
	updated_at  = now()
WHERE id in ('615b7ca3-6f62-4ca4-82f1-72f77f5b13c4','ac83c260-3796-4b13-bd5b-5c7aa4cb966a','772d4dce-8ce3-4766-9855-8ceefb36f33e','ab0db87c-7d0c-48da-bf4f-0f6690f1d79b','89336879-62e8-46f5-a9e1-69a0a03866d6','39efd260-c3cd-420b-86bd-89f40c3c6479');
