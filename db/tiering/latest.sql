CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;
COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';
CREATE TABLE public.actor_screen_interactions (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    actor_id character varying NOT NULL,
    screen character varying DEFAULT 'TIERING_SCREEN_UNSPECIFIED'::character varying NOT NULL,
    status character varying DEFAULT 'ACTOR_SCREEN_INTERACTION_STATUS_UNSPECIFIED'::character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON TABLE public.actor_screen_interactions IS 'stores information on users visiting frontend screens';
COMMENT ON COLUMN public.actor_screen_interactions.actor_id IS 'ID of the user';
COMMENT ON COLUMN public.actor_screen_interactions.screen IS 'Name of the visited screen';
COMMENT ON COLUMN public.actor_screen_interactions.status IS 'Visit status of the screen (e.g., VISITED, NOT_VISITED)';
COMMENT ON COLUMN public.actor_screen_interactions.created_at IS 'Timestamp of the first visit';
CREATE TABLE public.actor_tier_infos (
    actor_id character varying NOT NULL,
    tier character varying NOT NULL,
    movement_reference_id uuid,
    criteria_reference_id uuid,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    actor_base_tier character varying,
    trial_details jsonb DEFAULT '{}'::jsonb
);
COMMENT ON TABLE public.actor_tier_infos IS 'master table to store current tier of the user, this will serve as a source of truth of the users tier';
COMMENT ON COLUMN public.actor_tier_infos.tier IS '{"proto_type":"tiering.enums.Tier", "comment":"tier name"}';
COMMENT ON COLUMN public.actor_tier_infos.actor_base_tier IS 'users are assigned base tier as either regular (5,000 minimum balance) or Standard (no minimum balance)';
COMMENT ON COLUMN public.actor_tier_infos.trial_details IS 'details of the trial period for a user, such as start date, end date, and tier opted by user for trial';
CREATE TABLE public.eligible_tier_movements (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    criteria_reference_id uuid NOT NULL,
    from_tier character varying NOT NULL,
    to_tier character varying NOT NULL,
    actor_id character varying NOT NULL,
    movement_type character varying NOT NULL,
    movement_timestamp timestamp with time zone NOT NULL,
    status character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    details jsonb NOT NULL,
    criteria_option_type character varying DEFAULT 'CRITERIA_OPTION_TYPE_UNSPECIFIED'::character varying NOT NULL,
    evaluator_meta jsonb DEFAULT '{}'::jsonb
);
COMMENT ON TABLE public.eligible_tier_movements IS 'store users eligible tier movement due to QC evaluation changes';
COMMENT ON COLUMN public.eligible_tier_movements.from_tier IS '{"proto_type":"tiering.enums.Tier", "comment":"from tier"}';
COMMENT ON COLUMN public.eligible_tier_movements.to_tier IS '{"proto_type":"tiering.enums.Tier", "comment":"to tier"}';
COMMENT ON COLUMN public.eligible_tier_movements.movement_type IS '{"proto_type":"tiering.enums.TierMovementType", "comment":"movement type"}';
COMMENT ON COLUMN public.eligible_tier_movements.status IS '{"proto_type":"tiering.enums.EligibleTierMovementStatus", "comment":"eligible tier movement status"}';
COMMENT ON COLUMN public.eligible_tier_movements.criteria_option_type IS 'tracks criteria option type changes';
COMMENT ON COLUMN public.eligible_tier_movements.evaluator_meta IS 'meta data from tiering evaluator, such as list of criteria option types user has satisfied for a given tier';
CREATE TABLE public.schema_migrations (
    version bigint NOT NULL,
    dirty boolean NOT NULL
);
CREATE TABLE public.tier_criteria (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    criteria_name character varying NOT NULL,
    details jsonb NOT NULL,
    status character varying NOT NULL,
    executed_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);
COMMENT ON TABLE public.tier_criteria IS 'stores scheme related information which is the set of qualifying criteria';
COMMENT ON COLUMN public.tier_criteria.criteria_name IS '{"proto_type":"tiering.enums.CriteriaName", "comment":"tier criteria name"}';
COMMENT ON COLUMN public.tier_criteria.details IS 'details of a tier criteria';
COMMENT ON COLUMN public.tier_criteria.status IS '{"proto_type":"tiering.enums.TierCriteriaStatus", "comment":"status of tier criteria"}';
COMMENT ON COLUMN public.tier_criteria.executed_at IS 'timestamp at which criteria got first triggered';
CREATE TABLE public.tier_movement_histories (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    eligibility_movement_reference_id uuid NOT NULL,
    criteria_reference_id uuid NOT NULL,
    from_tier character varying NOT NULL,
    to_tier character varying NOT NULL,
    actor_id character varying NOT NULL,
    movement_type character varying NOT NULL,
    provenance character varying NOT NULL,
    reason jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON TABLE public.tier_movement_histories IS 'capture tier change of a user';
COMMENT ON COLUMN public.tier_movement_histories.from_tier IS '{"proto_type":"tiering.enums.Tier", "comment":"from tier"}';
COMMENT ON COLUMN public.tier_movement_histories.to_tier IS '{"proto_type":"tiering.enums.Tier", "comment":"to tier"}';
COMMENT ON COLUMN public.tier_movement_histories.movement_type IS '{"proto_type":"tiering.enums.TierMovementType", "comment":"movement type"}';
COMMENT ON COLUMN public.tier_movement_histories.provenance IS '{"proto_type":"tiering.enums.Provenance", "comment":"provenance of upgrade or downgrade"}';
CREATE TABLE public.tiering_abusers (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    actor_id character varying NOT NULL,
    month timestamp with time zone NOT NULL,
    tier_info jsonb NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON TABLE public.tiering_abusers IS 'stores information about users who have abused tiering criteria in specific months';
COMMENT ON COLUMN public.tiering_abusers.actor_id IS 'ID of the user who abused tiering criteria';
COMMENT ON COLUMN public.tiering_abusers.month IS 'month and year when the abuse occurred, stored as timestamp for easy extraction';
COMMENT ON COLUMN public.tiering_abusers.tier_info IS 'JSONB containing list of tier and tier_criteria information for the abuse incident';
COMMENT ON COLUMN public.tiering_abusers.created_at IS 'timestamp when the record was created';
COMMENT ON COLUMN public.tiering_abusers.updated_at IS 'timestamp when the record was last updated';
COMMENT ON COLUMN public.tiering_abusers.deleted_at IS 'timestamp when the record was soft deleted, NULL for active records';
ALTER TABLE ONLY public.actor_screen_interactions
    ADD CONSTRAINT actor_screen_interactions_pkey PRIMARY KEY (actor_id, screen);
ALTER TABLE ONLY public.actor_tier_infos
    ADD CONSTRAINT actor_tier_infos_pkey PRIMARY KEY (actor_id);
ALTER TABLE ONLY public.eligible_tier_movements
    ADD CONSTRAINT eligible_tier_movements_pkey PRIMARY KEY (actor_id, id);
ALTER TABLE ONLY public.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);
ALTER TABLE ONLY public.tier_criteria
    ADD CONSTRAINT tier_criteria_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.tier_movement_histories
    ADD CONSTRAINT tier_movement_histories_pkey PRIMARY KEY (actor_id, id);
ALTER TABLE ONLY public.tiering_abusers
    ADD CONSTRAINT tiering_abusers_pkey PRIMARY KEY (id);
CREATE INDEX actor_screen_interactions_actor_id_idx ON public.actor_screen_interactions USING btree (actor_id);
CREATE INDEX actor_screen_interactions_updated_at_idx ON public.actor_screen_interactions USING btree (updated_at DESC);
CREATE INDEX actor_tier_infos_updated_at_idx ON public.actor_tier_infos USING btree (updated_at DESC);
CREATE UNIQUE INDEX eligible_tier_movements_id_idx ON public.eligible_tier_movements USING btree (id);
CREATE INDEX eligible_tier_movements_updated_at_idx ON public.eligible_tier_movements USING btree (updated_at DESC);
CREATE INDEX etm_actor_id_movement_type_created_at_idx ON public.eligible_tier_movements USING btree (actor_id, movement_type, created_at DESC);
CREATE INDEX etm_actor_id_status_created_at_idx ON public.eligible_tier_movements USING btree (actor_id, status, created_at DESC);
CREATE UNIQUE INDEX tier_criteria_criteria_name_idx ON public.tier_criteria USING btree (criteria_name);
CREATE INDEX tier_criteria_updated_at_idx ON public.tier_criteria USING btree (updated_at DESC);
CREATE INDEX tier_movement_histories_eligibility_movement_reference_id_idx ON public.tier_movement_histories USING btree (eligibility_movement_reference_id);
CREATE UNIQUE INDEX tier_movement_histories_id_idx ON public.tier_movement_histories USING btree (id);
CREATE INDEX tier_movement_histories_updated_at_idx ON public.tier_movement_histories USING btree (updated_at DESC);
CREATE UNIQUE INDEX tiering_abusers_actor_month_uniq_idx ON public.tiering_abusers USING btree (actor_id, month) WHERE (deleted_at IS NULL);
CREATE INDEX tiering_abusers_month_idx ON public.tiering_abusers USING btree (month) WHERE (deleted_at IS NULL);
CREATE INDEX tiering_abusers_updated_at_idx ON public.tiering_abusers USING btree (updated_at DESC);
CREATE INDEX tmh_actor_id_from_tier_movement_type_created_at_idx ON public.tier_movement_histories USING btree (actor_id, from_tier, movement_type, created_at DESC);
CREATE INDEX tmh_actor_id_movement_type_created_at_idx ON public.tier_movement_histories USING btree (actor_id, movement_type, created_at DESC);
CREATE INDEX tmh_actor_id_to_tier_created_at_idx ON public.tier_movement_histories USING btree (actor_id, to_tier, created_at DESC);
CREATE INDEX tmh_eligibility_movement_reference_id_idx ON public.tier_movement_histories USING btree (eligibility_movement_reference_id);
ALTER TABLE ONLY public.actor_tier_infos
    ADD CONSTRAINT fk_ati_criteria_reference_id_ref_tier_criteria_id FOREIGN KEY (criteria_reference_id) REFERENCES public.tier_criteria(id);
ALTER TABLE ONLY public.actor_tier_infos
    ADD CONSTRAINT fk_ati_movement_reference_id_ref_tmh_id FOREIGN KEY (movement_reference_id) REFERENCES public.tier_movement_histories(id);
ALTER TABLE ONLY public.eligible_tier_movements
    ADD CONSTRAINT fk_etm_criteria_reference_id_ref_tier_criteria_id FOREIGN KEY (criteria_reference_id) REFERENCES public.tier_criteria(id);
ALTER TABLE ONLY public.tier_movement_histories
    ADD CONSTRAINT fk_tmh_criteria_reference_id_ref_tier_criteria_id FOREIGN KEY (criteria_reference_id) REFERENCES public.tier_criteria(id);
ALTER TABLE ONLY public.tier_movement_histories
    ADD CONSTRAINT fk_tmh_eligibility_movement_reference_id_ref_etm_id FOREIGN KEY (eligibility_movement_reference_id) REFERENCES public.eligible_tier_movements(id);
