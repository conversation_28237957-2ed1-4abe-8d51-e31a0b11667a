-- tier_criteria
INSERT INTO tier_criteria
(id, criteria_name, details, status, executed_at, created_at, updated_at)
VALUES
	('57f83510-407f-4a9a-ada3-998a032a54dd','CRITERIA_NAME_C_ONE','{"movementDetailsList":[{"tierName":"TIER_TEN", "options":[{}]}, {"tierName":"TIER_ONE_HUNDRED", "options":[{"actions":[{"actionDetails":{"balance":{"minBalance":{"currencyCode":"INR", "units":"10000"}}}}, {"actionDetails":{"kyc":{"kycLevel":"FULL_KYC"}}}]}]}, {"tierName":"TIER_ONE_THOUSAND", "options":[{"actions":[{"actionDetails":{"balance":{"minBalance":{"currencyCode":"INR", "units":"50000"}}}}, {"actionDetails":{"kyc":{"kycLevel":"FULL_KYC"}}}]}]}, {"tierName":"TIER_TWO_THOUSAND", "options":[{"actions":[{"actionDetails":{"kyc":{"kycLevel":"FULL_KYC"}}}, {"actionDetails":{"salary":{"minSalary":{"currencyCode":"INR", "units":"20000"}}}}]}]}]}','TIER_CRITERIA_STATUS_ACTIVE','1970-01-01 00:00:00.000000 +00:00','2022-11-23 05:37:56.184776 +00:00','2022-11-23 05:37:56.184776 +00:00');

-- eligible_tier_movements
INSERT INTO eligible_tier_movements
(id, criteria_reference_id, from_tier, to_tier, actor_id, movement_type, movement_timestamp, status, created_at, updated_at, deleted_at, details, evaluator_meta)
VALUES
	('57f83510-407f-4a9a-ada3-998a032a54de','57f83510-407f-4a9a-ada3-998a032a54dd','TIER_TEN','TIER_ONE_HUNDRED','actor-1','TIER_MOVEMENT_TYPE_UPGRADE','2022-11-23 05:38:56.184776 +00:00','ELIGIBLE_TIER_MOVEMENT_STATUS_ELIGIBLE','2022-11-23 05:38:56.184776 +00:00','2022-11-23 05:38:56.184776 +00:00',null,'{}', '{}');
INSERT INTO eligible_tier_movements
(id, criteria_reference_id, from_tier, to_tier, actor_id, movement_type, movement_timestamp, status, created_at, updated_at, deleted_at, details, evaluator_meta)
VALUES
	('57f83510-407f-4a9a-ada3-998a032a55ef','57f83510-407f-4a9a-ada3-998a032a54dd','TIER_ONE_HUNDRED','TIER_TEN','actor-1','TIER_MOVEMENT_TYPE_DOWNGRADE','2022-11-23 05:38:56.184776 +00:00','ELIGIBLE_TIER_MOVEMENT_STATUS_DONE','2021-11-23 05:38:56.184776 +00:00','2021-11-23 05:38:56.184776 +00:00',null,'{}', '{}');

-- tier_movement_histories
INSERT INTO tier_movement_histories
(id, eligibility_movement_reference_id, criteria_reference_id, from_tier, to_tier, actor_id, movement_type, provenance, reason, created_at, updated_at)
VALUES
	('57f83510-407f-4a9a-ada3-998a032a56df','57f83510-407f-4a9a-ada3-998a032a54de','57f83510-407f-4a9a-ada3-998a032a54dd','TIER_TEN','TIER_ONE_HUNDRED','actor-1','TIER_MOVEMENT_TYPE_UPGRADE','PROVENANCE_AUTOMATIC','{}','2021-11-23 05:39:56.184776 +00:00','2021-11-23 05:39:56.184776 +00:00');

INSERT INTO tier_movement_histories
(id, eligibility_movement_reference_id, criteria_reference_id, from_tier, to_tier, actor_id, movement_type, provenance, reason, created_at, updated_at)
VALUES
	('57f83510-407f-4a9a-ada3-998a032a54df','57f83510-407f-4a9a-ada3-998a032a54de','57f83510-407f-4a9a-ada3-998a032a54dd','TIER_TEN','TIER_ONE_HUNDRED','actor-1','TIER_MOVEMENT_TYPE_UPGRADE','PROVENANCE_AUTOMATIC','{}','2022-11-23 05:39:56.184776 +00:00','2022-11-23 05:39:56.184776 +00:00');

INSERT INTO tier_movement_histories
(id, eligibility_movement_reference_id, criteria_reference_id, from_tier, to_tier, actor_id, movement_type, provenance, reason, created_at, updated_at)
VALUES
	('51d8052a-137d-481c-926a-94f3fadec30d','57f83510-407f-4a9a-ada3-998a032a54de','57f83510-407f-4a9a-ada3-998a032a54dd','TIER_ONE_HUNDRED','TIER_ONE_THOUSAND','actor-1','TIER_MOVEMENT_TYPE_UPGRADE','PROVENANCE_AUTOMATIC','{}','2022-11-23 05:39:56.184776 +00:00','2022-11-23 05:39:56.184776 +00:00');

INSERT INTO tier_movement_histories
(id, eligibility_movement_reference_id, criteria_reference_id, from_tier, to_tier, actor_id, movement_type, provenance, reason, created_at, updated_at)
VALUES
	('57f83510-407f-4a9a-ada3-998a032a56d1','57f83510-407f-4a9a-ada3-998a032a54de','57f83510-407f-4a9a-ada3-998a032a54dd','TIER_UNSPECIFIED','TIER_ONE_HUNDRED','actor-3','TIER_MOVEMENT_TYPE_UPGRADE','PROVENANCE_AUTOMATIC','{}','2021-11-01 05:39:56.184776 +00:00','2021-11-01 05:39:56.184776 +00:00');

INSERT INTO tier_movement_histories
(id, eligibility_movement_reference_id, criteria_reference_id, from_tier, to_tier, actor_id, movement_type, provenance, reason, created_at, updated_at)
VALUES
	('57f83510-407f-4a9a-ada3-998a032a56d2','57f83510-407f-4a9a-ada3-998a032a54de','57f83510-407f-4a9a-ada3-998a032a54dd','TIER_ONE_HUNDRED','TIER_ONE_THOUSAND','actor-3','TIER_MOVEMENT_TYPE_UPGRADE','PROVENANCE_AUTOMATIC','{}','2021-11-05 05:39:56.184776 +00:00','2021-11-05 05:39:56.184776 +00:00');

INSERT INTO tier_movement_histories
(id, eligibility_movement_reference_id, criteria_reference_id, from_tier, to_tier, actor_id, movement_type, provenance, reason, created_at, updated_at)
VALUES
	('57f83510-407f-4a9a-ada3-998a032a56d3','57f83510-407f-4a9a-ada3-998a032a54de','57f83510-407f-4a9a-ada3-998a032a54dd','TIER_ONE_THOUSAND','TIER_TEN','actor-3','TIER_MOVEMENT_TYPE_UPGRADE','PROVENANCE_AUTOMATIC','{}','2021-11-10 05:39:56.184776 +00:00','2021-11-10 05:39:56.184776 +00:00');

INSERT INTO tier_movement_histories
(id, eligibility_movement_reference_id, criteria_reference_id, from_tier, to_tier, actor_id, movement_type, provenance, reason, created_at, updated_at)
VALUES
	('57f83510-407f-4a9a-ada3-998a032a56d4','57f83510-407f-4a9a-ada3-998a032a54de','57f83510-407f-4a9a-ada3-998a032a54dd','TIER_TEN','TIER_ONE_HUNDRED','actor-3','TIER_MOVEMENT_TYPE_UPGRADE','PROVENANCE_AUTOMATIC','{}','2021-11-15 05:39:56.184776 +00:00','2021-11-15 05:39:56.184776 +00:00');

INSERT INTO tier_movement_histories
(id, eligibility_movement_reference_id, criteria_reference_id, from_tier, to_tier, actor_id, movement_type, provenance, reason, created_at, updated_at)
VALUES
	('57f83510-407f-4a9a-ada3-998a032a56d5','57f83510-407f-4a9a-ada3-998a032a54de','57f83510-407f-4a9a-ada3-998a032a54dd','TIER_ONE_HUNDRED','TIER_ONE_THOUSAND','actor-3','TIER_MOVEMENT_TYPE_UPGRADE','PROVENANCE_AUTOMATIC','{}','2021-11-20 05:39:56.184776 +00:00','2021-11-20 05:39:56.184776 +00:00');

INSERT INTO tier_movement_histories
(id, eligibility_movement_reference_id, criteria_reference_id, from_tier, to_tier, actor_id, movement_type, provenance, reason, created_at, updated_at)
VALUES
	('57f83510-407f-4a9a-ada3-998a032a56d6','57f83510-407f-4a9a-ada3-998a032a54de','57f83510-407f-4a9a-ada3-998a032a54dd','TIER_UNSPECIFIED','TIER_ONE_THOUSAND','actor-4','TIER_MOVEMENT_TYPE_UPGRADE','PROVENANCE_AUTOMATIC','{}','2023-11-20 05:39:56.184776 +00:00','2023-11-20 05:39:56.184776 +00:00');

-- dummy tier criteria
INSERT INTO tier_criteria
(id, criteria_name, details, status, executed_at, created_at, updated_at)
VALUES
	('00000000-0000-0000-0000-000000000000','CRITERIA_NAME_UNSPECIFIED','{}', 'TIER_CRITERIA_STATUS_UNSPECIFIED','1970-01-01 00:00:00.000000 +00:00','2022-11-23 05:37:56.184776 +00:00','2022-11-23 05:37:56.184776 +00:00');
-- dummy eligible tier movement
INSERT INTO eligible_tier_movements
(id, criteria_reference_id, from_tier, to_tier, actor_id, movement_type, movement_timestamp, status, created_at, updated_at, deleted_at, details, evaluator_meta)
VALUES
	('00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','TIER_UNSPECIFIED','TIER_UNSPECIFIED','actor-0','TIER_MOVEMENT_TYPE_UNSPECIFIED','2022-11-23 05:38:56.184776 +00:00','ELIGIBLE_TIER_MOVEMENT_STATUS_UNSPECIFIED','2022-11-23 05:38:56.184776 +00:00','2022-11-23 05:38:56.184776 +00:00',null,'{}', '{}');
-- dummy tier movement history
INSERT INTO tier_movement_histories
(id, eligibility_movement_reference_id, criteria_reference_id, from_tier, to_tier, actor_id, movement_type, provenance, reason, created_at, updated_at)
VALUES
	('00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','TIER_UNSPECIFIED','TIER_UNSPECIFIED','actor-0','TIER_MOVEMENT_TYPE_UPGRADE','PROVENANCE_AUTOMATIC','{}','2021-11-20 05:39:56.184776 +00:00','2021-11-20 05:39:56.184776 +00:00');

-- actor_tier_infos
INSERT INTO actor_tier_infos
(actor_id, tier, movement_reference_id, criteria_reference_id, actor_base_tier, trial_details, created_at, updated_at)
VALUES
	('actor-1','TIER_ONE_HUNDRED','57f83510-407f-4a9a-ada3-998a032a54df','57f83510-407f-4a9a-ada3-998a032a54dd', 'TIER_TEN', '{"tier":"TIER_ONE_THOUSAND","optedAt":"2024-12-31T18:30:00Z","trialStartTime":"2024-12-31T18:30:00Z","trialEndTime":"2025-01-24T18:30:00Z"}', '2022-11-23 05:40:56.184776 +00:00','2022-11-23 05:40:56.184776 +00:00');

-- tiering_abusers
INSERT INTO tiering_abusers
(id, actor_id, month, tier_info, created_at, updated_at, deleted_at)
VALUES
	('11111111-1111-1111-1111-111111111111','actor-1','2024-01-15 05:00:00.000000 +00:00','{"entries": [{"tier": "TIER_TWO_THOUSAND", "tierCriteria": "MONTHLY_BALANCE_CRITERIA"}]}','2024-01-15 05:00:00.000000 +00:00','2024-01-15 05:00:00.000000 +00:00',null);

INSERT INTO tiering_abusers
(id, actor_id, month, tier_info, created_at, updated_at, deleted_at)
VALUES
	('22222222-2222-2222-2222-222222222222','actor-2','2024-01-31 20:45:00.000000 +00:00','{"entries": [{"tier": "TIER_ONE_THOUSAND_FIVE_HUNDRED", "tierCriteria": "TRANSACTION_VOLUME_CRITERIA"}, {"tier": "TIER_ONE_THOUSAND", "tierCriteria": "KYC_CRITERIA"}]}','2024-01-31 20:45:00.000000 +00:00','2024-01-31 20:45:00.000000 +00:00',null);

INSERT INTO tiering_abusers
(id, actor_id, month, tier_info, created_at, updated_at, deleted_at)
VALUES
	('33333333-3333-3333-3333-333333333333','actor-1','2024-02-29 23:45:00.000000 +00:00','{"entries": [{"tier": "TIER_ONE_THOUSAND_TWO_FIFTY", "tierCriteria": "KYC_CRITERIA"}]}','2024-02-29 23:45:00.000000 +00:00','2024-02-29 23:45:00.000000 +00:00',null);

INSERT INTO tiering_abusers
(id, actor_id, month, tier_info, created_at, updated_at, deleted_at)
VALUES
	('44444444-4444-4444-4444-444444444444','actor-3','2024-01-01 00:00:00.000000 +00:00','{"entries": [{"tier": "TIER_ONE_THOUSAND_FIVE_HUNDRED", "tierCriteria": "MONTHLY_BALANCE_CRITERIA"}]}','2024-01-01 06:00:00.000000 +00:00','2024-01-01 06:00:00.000000 +00:00','2024-01-15 12:00:00.000000 +00:00');
