package networth

import (
	"context"
	"encoding/json"
	"fmt"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/colors"
	moneyPb "github.com/epifi/be-common/pkg/money"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/assetandanalysis"
	"github.com/epifi/gamma/api/typesv2/ui"
	networthCommon "github.com/epifi/gamma/frontend/insights/networth/common"
	magicimport "github.com/epifi/gamma/frontend/insights/networth/magic_import"
	"github.com/epifi/gamma/pkg/deeplinkv3"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/epifi/gamma/frontend/insights/networth/manual_forms/formbuilder"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"

	feError "github.com/epifi/gamma/api/frontend/errors"
	"github.com/epifi/gamma/api/frontend/header"
	"github.com/epifi/gamma/api/frontend/insights/networth"
	"github.com/epifi/gamma/api/typesv2"
	formErrors "github.com/epifi/gamma/frontend/insights/networth/manual_forms/errors"
)

const (
	companyNotSupportedText          = "New company addition is not supported yet"
	investmentDateAfterMaturityDate  = "Maturity date before investment date is not allowed"
	investmentDateAfterCurrentDate   = "Future investment date is not allowed"
	firstDateOfIssueAfterCurrentDate = "First date of issue after current date is not allowed"
)

func (s *Service) SubmitManualForm(ctx context.Context, req *networth.SubmitManualFormRequest) (*networth.SubmitManualFormResponse, error) {
	err := s.submitManualForm(ctx, req.GetReq().GetAuth().GetActorId(), req.GetFormIdentifier(), req.GetFormData())
	if err != nil {
		logger.Error(ctx, "failed to submit manual form", zap.Any("form identifier", req.GetFormIdentifier()), zap.Error(err))
		return generateSubmitManualFormResponseFromErr(err)
	}
	return genSubmitManualFormResponseWithStatus(rpc.StatusOk()), nil
}

func (s *Service) SubmitManualForms(ctx context.Context, req *networth.SubmitManualFormsRequest) (*networth.SubmitManualFormsResponse, error) {
	networthAdded := moneyPb.ZeroINR().GetPb()
	numberOfAssets := 0
	for _, form := range req.GetFormSubmissionData() {
		err := s.submitManualForm(ctx, req.GetReq().GetAuth().GetActorId(), form.GetFormIdentifier(), form.GetFormData())
		if err != nil {
			logger.Error(ctx, "failed to submit manual forms", zap.Any("form identifier", form.GetFormIdentifier()), zap.Error(err))
		} else {
			numberOfAssets++
			assetValue := moneyPb.ZeroINR().GetPb()
			for _, inputData := range form.GetFormData() {
				inputValue := inputData.GetInputValueFromSingleOption()
				if inputData.GetFieldName() == networth.NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_CURRENT_VALUE.String() {
					assetValue = moneyPb.AmountINR(inputValue.GetInt64Data().GetData().GetValue()).GetPb()
					break
				}
			}
			logger.Debug(ctx, "adding asset value to assetImportFlow magic import", zap.Any("assetValue", assetValue))

			networthAdded, err = moneyPb.Sum(networthAdded, assetValue)
			if err != nil {
				logger.Error(ctx, "failed to sum networth", zap.Error(err))
			}
		}
	}
	magicImportAssetImportFlowOutput := &magicimport.MagicImportAssetImportFlow{
		NumberOfAssets: numberOfAssets,
		NetworthAdded:  networthAdded,
	}
	payload, err := json.Marshal(magicImportAssetImportFlowOutput)
	if err != nil {
		logger.Error(ctx, "failed to marshal magic import asset import flow output", zap.Error(err))
		return &networth.SubmitManualFormsResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternal(),
			},
		}, nil
	}
	screenOpt := deeplinkv3.GetScreenOptionV2WithoutError(&assetandanalysis.AssetImportStatusPolingScreenOptions{
		FlowType: assetandanalysis.AssetImportStatusPolingScreenOptions_FLOW_TYPE_MAGIC_IMPORT.String(),
		// todo : remove this once IOS is force updated >= 656
		AssetType:               assetandanalysis.AssetImportStatusPolingScreenOptions_FLOW_TYPE_MAGIC_IMPORT.String(),
		Payload:                 payload,
		BackgroundColor:         colors.ColorDarkBase,
		BackgroundLottieUrl:     "https://epifi-icons.pointz.in/networth/asset_update_lottie.json",
		CurrentNetworth:         typesv2.GetFromBeMoney(moneyPb.ZeroINR().GetPb()),
		ImportStatusDetails:     s.getAssetImportStatusDetails(),
		ImportInProgressDetails: s.getAssetImportInProgressDetails(),
		ImportFailureDetails:    s.getAssetImportFailureDetails(),
		PollingDetails: &assetandanalysis.AssetImportStatusPolingScreenOptions_PollingData{
			PollingInterval:    4,
			MaxPollingDuration: 12,
		},
	})
	assetImportFlowStatusDl := &deeplinkPb.Deeplink{
		Screen:          deeplinkPb.Screen_ASSET_IMPORT_STATUS_POLLING_SCREEN,
		ScreenOptionsV2: screenOpt,
	}
	return &networth.SubmitManualFormsResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		ExitDeeplink: assetImportFlowStatusDl,
	}, nil
}

func (s *Service) getAssetImportStatusDetails() *assetandanalysis.AssetImportStatusDetails {
	return &assetandanalysis.AssetImportStatusDetails{
		Title: commontypes.GetTextFromStringFontColourFontStyle("Fetching your details", colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_HEADLINE_L),
		FooterDetails: &assetandanalysis.AssetImportStatusDetails_FooterDetails{
			Content:    commontypes.GetTextFromStringFontColourFontStyle(networthCommon.AssetImportFooterText, colors.ColorMonochromeAsh, commontypes.FontStyle_SUBTITLE_S).WithAlignment(commontypes.Text_ALIGNMENT_LEFT),
			RightImage: commontypes.GetVisualElementFromUrlHeightAndWidth(networthCommon.PeerUserIcon, 48, 58),
		},
		LottieDetails: &assetandanalysis.DataFetchLottieDetails{
			StartFrame: networthCommon.InProgressStartFrame,
			EndFrame:   networthCommon.InProgressEndFrame,
			ShouldLoop: true,
		},
	}
}

func (s *Service) getAssetImportInProgressDetails() *assetandanalysis.AssetImportTerminalInProgressDetails {
	return &assetandanalysis.AssetImportTerminalInProgressDetails{
		Title: commontypes.GetTextFromStringFontColourFontStyle("Running some numbers", colors.ColorSnow, commontypes.FontStyle_HEADLINE_L),
		AssetsUpdate: &ui.VerticalKeyValuePair{
			Title:                        ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Updating Net Worth", colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_HEADLINE_M)),
			Value:                        ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Analyzing", "#D2AC3D", commontypes.FontStyle_SUBTITLE_XS)).WithContainerPadding(4, 8, 4, 8).WithContainerCornerRadius(12).WithContainerBackgroundColor("#FFFCEB"),
			VerticalPaddingBtwTitleValue: 12,
		},
		ExitCta: &deeplinkPb.Cta{
			Text:         "Continue",
			Type:         deeplinkPb.Cta_CONTINUE,
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
		},
		LottieDetails: &assetandanalysis.DataFetchLottieDetails{
			StartFrame: networthCommon.UpdatingStartFrame,
			EndFrame:   networthCommon.UpdatingEndFrame,
		},
	}
}

func (s *Service) getAssetImportFailureDetails() *assetandanalysis.AssetImportTerminalFailureDetails {
	return &assetandanalysis.AssetImportTerminalFailureDetails{
		ErrorTitle:   commontypes.GetTextFromStringFontColourFontStyle(networthCommon.AssetImportFailureTitle, colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_HEADLINE_L),
		ErrorMessage: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(networthCommon.AssetImportFailureMsg, colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_HEADLINE_M)),
		ErrorImage:   commontypes.GetVisualElementFromUrlHeightAndWidth(networthCommon.RedAlertIcon, 88, 88),
		Ctas: []*deeplinkPb.Cta{
			{
				Text:         "Skip",
				Type:         deeplinkPb.Cta_CONTINUE,
				DisplayTheme: deeplinkPb.Cta_PRIMARY,
			},
		},
		LottieDetails: &assetandanalysis.DataFetchLottieDetails{
			StartFrame: networthCommon.FailedStateStartFrame,
			EndFrame:   networthCommon.FailedStateEndFrame,
		},
	}
}

func (s *Service) submitManualForm(ctx context.Context, actorId string, formIdentifier *typesv2.ManualAssetFormIdentifier, formData []*networth.NetWorthManualInputData) error {
	emptyForm, err := s.formProcessor.BuildEmptyForm(ctx, &formbuilder.BuildFormRequest{
		ActorId:        actorId,
		FormIdentifier: formIdentifier,
	})
	if err != nil {
		return fmt.Errorf("failure in build empty form : %w", err)
	}

	formInputComponents, err := s.validateAndSetInputDataInForm(ctx, formData, emptyForm)
	if err != nil {
		return fmt.Errorf("failure in validate and set input data : %w", err)
	}
	err = s.formProcessor.SubmitForm(ctx, &formbuilder.SubmitFormRequest{
		ActorId:         actorId,
		FormIdentifier:  formIdentifier,
		InputComponents: formInputComponents,
		FormData:        formData,
	})
	if err != nil {
		return fmt.Errorf("failure in submit form : %w", err)
	}
	return nil
}

func (s *Service) validateAndSetInputDataInForm(ctx context.Context, formData []*networth.NetWorthManualInputData, form *networth.NetWorthManualForm) ([]*networth.NetWorthManualFormInputComponent, error) {
	allInputComponents := form.GetAllInputComponents()
	// note that validate input data also sets the input data into formInputComponents
	validateErr := s.formInputValidator.ValidateInputData(ctx, formData, allInputComponents)
	if validateErr != nil {
		return nil, errors.Wrap(epifierrors.ErrInvalidArgument, validateErr.Error())
	}
	return allInputComponents, nil
}

func generateSubmitManualFormResponseFromErr(err error) (*networth.SubmitManualFormResponse, error) {
	switch {
	case errors.Is(err, epifierrors.ErrInvalidArgument):
		return genSubmitManualFormResponseWithStatus(rpc.StatusInvalidArgument()), nil
	case errors.Is(err, formErrors.CustomCompanyNotAllowedError):
		return genSubmitManualFormResponseWithBottomSheet(rpc.StatusInvalidArgument(), companyNotSupportedText), nil
	case errors.Is(err, formErrors.InvestmentDateAfterMaturityDateError):
		return genSubmitManualFormResponseWithBottomSheet(rpc.StatusInvalidArgument(), investmentDateAfterMaturityDate), nil
	case errors.Is(err, formErrors.InvestmentDateAfterCurrentDateError):
		return genSubmitManualFormResponseWithBottomSheet(rpc.StatusInvalidArgument(), investmentDateAfterCurrentDate), nil
	case errors.Is(err, formErrors.FirstDateOfIssueAfterCurrentDateError):
		return genSubmitManualFormResponseWithBottomSheet(rpc.StatusInvalidArgument(), firstDateOfIssueAfterCurrentDate), nil
	default:
		return genSubmitManualFormResponseWithStatus(rpc.StatusInternal()), nil
	}
}

func genSubmitManualFormResponseWithStatus(status *rpc.Status) *networth.SubmitManualFormResponse {
	return &networth.SubmitManualFormResponse{
		RespHeader: &header.ResponseHeader{
			Status: status,
		},
	}
}

func genSubmitManualFormResponseWithBottomSheet(status *rpc.Status, errorText string) *networth.SubmitManualFormResponse {
	return &networth.SubmitManualFormResponse{
		RespHeader: &header.ResponseHeader{
			Status: status,
			ErrorView: &feError.ErrorView{
				Type: feError.ErrorViewType_BOTTOM_SHEET,
				Options: &feError.ErrorView_BottomSheetErrorView{
					BottomSheetErrorView: &feError.BottomSheetErrorView{
						Title: errorText,
						Ctas: []*feError.CTA{
							{
								Type:         feError.CTA_DONE,
								Text:         "Ok, got it",
								DisplayTheme: feError.CTA_SECONDARY,
							},
						},
					},
				},
			},
		},
	}
}
