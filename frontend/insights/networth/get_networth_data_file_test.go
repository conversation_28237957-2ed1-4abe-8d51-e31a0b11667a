package networth

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common/file"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/frontend/header"
	networthFePb "github.com/epifi/gamma/api/frontend/insights/networth"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	"github.com/epifi/gamma/api/insights/networth/enums"
)

func TestService_GetNetworthDataFile(t *testing.T) {
	logger.Init(cfg.TestEnv)
	type args struct {
		ctx context.Context
		req *networthFePb.GetNetworthDataFileRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(f *fields)
		want       *networthFePb.GetNetworthDataFileResponse
		wantErr    bool
	}{
		{
			name: "empty request payload, fetch networth data, success",
			args: args{
				ctx: context.Background(),
				req: &networthFePb.GetNetworthDataFileRequest{
					Req: &header.RequestHeader{Auth: &header.AuthHeader{ActorId: "actor-id"}},
				},
			},
			setupMocks: func(f *fields) {
				f.networthClient.EXPECT().GetNetworthDataFile(gomock.Any(), gomock.Any()).Return(&networthPb.GetNetworthDataFileResponse{
					Status: rpc.StatusOk(),
					NetworthFile: &file.File{
						FileName: "Fi_summary.txt",
						Type:     file.FileType_FILE_TYPE_TXT,
					},
				}, nil)
			},
			want: &networthFePb.GetNetworthDataFileResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				NetworthFile: &file.File{
					FileName: "Fi_summary.txt",
					Type:     file.FileType_FILE_TYPE_TXT,
				},
			},
		},
		{
			name: "request payload for networth data, success",
			args: args{
				ctx: context.Background(),
				req: &networthFePb.GetNetworthDataFileRequest{
					Req:        &header.RequestHeader{Auth: &header.AuthHeader{ActorId: "actor-id"}},
					ReqPayload: "{\"networthDataFilePurpose\":\"NETWORTH_DATA_FILE_PURPOSE_EXPORT\",\"networthDataFileType\":\"NETWORTH_DATA_FILE_TYPE_NETWORTH_DATA\"}",
				},
			},
			setupMocks: func(f *fields) {
				f.networthClient.EXPECT().GetNetworthDataFile(gomock.Any(), &networthPb.GetNetworthDataFileRequest{
					ActorId:               "actor-id",
					NetWorthDataFileTypes: []enums.NetworthDataFileType{enums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_NETWORTH_DATA},
				}).Return(&networthPb.GetNetworthDataFileResponse{
					Status: rpc.StatusOk(),
					NetworthFile: &file.File{
						FileName: "Fi_summary.txt",
						Type:     file.FileType_FILE_TYPE_TXT,
					},
				}, nil)
			},
			want: &networthFePb.GetNetworthDataFileResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				NetworthFile: &file.File{
					FileName: "Fi_summary.txt",
					Type:     file.FileType_FILE_TYPE_TXT,
				},
			},
		},
		{
			name: "request payload for bank details, success",
			args: args{
				ctx: context.Background(),
				req: &networthFePb.GetNetworthDataFileRequest{
					Req:        &header.RequestHeader{Auth: &header.AuthHeader{ActorId: "actor-id"}},
					ReqPayload: "{\"networthDataFilePurpose\":\"NETWORTH_DATA_FILE_PURPOSE_EXPORT\",\"networthDataFileType\":\"NETWORTH_DATA_FILE_TYPE_BANK_DETAILS_DATA\"}",
				},
			},
			setupMocks: func(f *fields) {
				f.networthClient.EXPECT().GetNetworthDataFile(gomock.Any(), &networthPb.GetNetworthDataFileRequest{
					ActorId:               "actor-id",
					NetWorthDataFileTypes: []enums.NetworthDataFileType{enums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_BANK_DETAILS_DATA},
				}).Return(&networthPb.GetNetworthDataFileResponse{
					Status: rpc.StatusOk(),
					NetworthFile: &file.File{
						FileName: "Fi_summary.txt",
						Type:     file.FileType_FILE_TYPE_TXT,
					},
				}, nil)
			},
			want: &networthFePb.GetNetworthDataFileResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				NetworthFile: &file.File{
					FileName: "Fi_summary.txt",
					Type:     file.FileType_FILE_TYPE_TXT,
				},
			},
		},
		{
			name: "failed to unmarshal request payload",
			args: args{
				ctx: context.Background(),
				req: &networthFePb.GetNetworthDataFileRequest{
					ReqPayload: "invalid_request_payload",
				},
			},
			setupMocks: func(f *fields) {},
			want: &networthFePb.GetNetworthDataFileResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusInvalidArgument(),
				},
			},
		},
		{
			name: "GetNetworthDataFile BE api failed, internal error",
			args: args{
				ctx: context.Background(),
				req: &networthFePb.GetNetworthDataFileRequest{
					Req:        &header.RequestHeader{Auth: &header.AuthHeader{ActorId: "actor-id"}},
					ReqPayload: "{\"networthDataFilePurpose\":\"NETWORTH_DATA_FILE_PURPOSE_EXPORT\",\"networthDataFileType\":\"NETWORTH_DATA_FILE_TYPE_BANK_DETAILS_DATA\"}",
				},
			},
			setupMocks: func(f *fields) {
				f.networthClient.EXPECT().GetNetworthDataFile(gomock.Any(), &networthPb.GetNetworthDataFileRequest{
					ActorId:               "actor-id",
					NetWorthDataFileTypes: []enums.NetworthDataFileType{enums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_BANK_DETAILS_DATA},
				}).Return(&networthPb.GetNetworthDataFileResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want: &networthFePb.GetNetworthDataFileResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusInternal(),
				},
			},
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			f := initFields(ctrl)
			tt.setupMocks(f)
			s := NewService(dynConf, f.networthConfig, f.networthClient, f.consentClient, f.sectionGenerator, f.dataFetcher, f.visualisationGeneratorFactory,
				f.formBuilderFactory, f.formInputValidator, f.assetDashboardGeneratorFactory, f.time, f.onbClient, f.connectedAccountClient, f.deeplinkBuilder,
				f.epfClient, f.mfExternalOrdersClient, f.creditReportClient, f.mockReleaseEvaluator, nil, nil, nil, nil, nil, nil, nil, f.mockVariableGeneratorClient, f.mockBroker)
			f.mockBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).AnyTimes()
			got, err := s.GetNetworthDataFile(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetNetworthDataFile() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&file.File{}, "base64_data"),
			}
			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("GetNetworthDataFile(), diff: %v", diff)
			}
		})
	}
}
