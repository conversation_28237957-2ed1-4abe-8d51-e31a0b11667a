package credit_report

import (
	"time"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/gamma/api/frontend/deeplink"

	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/google/wire"
	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"

	"github.com/epifi/gamma/analyser/creditscore/params_fetcher"
	creditReportPb "github.com/epifi/gamma/api/creditreportv2"
	secretsFePb "github.com/epifi/gamma/api/frontend/insights/secrets"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/secrets"
	creditReportVgPb "github.com/epifi/gamma/api/vendorgateway/credit_report"
	"github.com/epifi/gamma/frontend/analyser/debt/credit_score"
	"github.com/epifi/gamma/frontend/analyser/debt/credit_score/lens"
	"github.com/epifi/gamma/frontend/analyser/debt/credit_score/store"
	analyserErrors "github.com/epifi/gamma/frontend/analyser/errors"
	"github.com/epifi/gamma/frontend/analyser/insights/loans"
	"github.com/epifi/gamma/frontend/config/genconf"
	secretStore "github.com/epifi/gamma/frontend/insights/secrets/credit_report/store"
	crUi "github.com/epifi/gamma/frontend/insights/secrets/credit_report/ui"
	"github.com/epifi/gamma/frontend/insights/secrets/dataproviders"
	secretErrors "github.com/epifi/gamma/frontend/insights/secrets/errors"
	"github.com/epifi/gamma/frontend/insights/secrets/secret_builder"
	"github.com/epifi/gamma/pkg/feature/release"
)

// Skipping wire bind as we donot want to create new wrapper interfaces for each builder
var CreditReportSecretsBuilderWireSet = wire.NewSet(
	dataproviders.SecretDataProvidersWireSet,
	NewCreditReportSecretBuilder,
	crUi.NewCreditReportUiBuilder,
	credit_score.WireCreditReportFetcherSet,
	params_fetcher.CreditScoreParamsFetcherWireset)

const (
	lightningIconUrl = "https://epifi-icons.pointz.in/networth/thundercloud.png"
)

type CreditReportSecretBuilder struct {
	config                  *genconf.Config
	creditReportFetcher     *credit_score.CreditReportFetcher
	creditReportUIBuilder   *crUi.CreditReportUiBuilder
	creditScoreParamFetcher params_fetcher.CreditScoreParamsFetcher
	releaseEvaluator        release.IEvaluator
	lendingInsights         loans.LendingInsights
	creditReportClient      creditReportPb.CreditReportManagerClient
	timeImpl                datetime.Time
}

func NewCreditReportSecretBuilder(config *genconf.Config, creditReportUIBuilder *crUi.CreditReportUiBuilder,
	creditReportFetcher *credit_score.CreditReportFetcher, creditScoreParamFetcher params_fetcher.CreditScoreParamsFetcher,
	releaseEvaluator release.IEvaluator, lendingInsights loans.LendingInsights,
	creditReportClient creditReportPb.CreditReportManagerClient, timeImpl datetime.Time) *CreditReportSecretBuilder {
	return &CreditReportSecretBuilder{
		config:                  config,
		creditReportFetcher:     creditReportFetcher,
		creditReportUIBuilder:   creditReportUIBuilder,
		creditScoreParamFetcher: creditScoreParamFetcher,
		releaseEvaluator:        releaseEvaluator,
		lendingInsights:         lendingInsights,
		creditReportClient:      creditReportClient,
		timeImpl:                timeImpl,
	}
}

func (csp *CreditReportSecretBuilder) BuildSecretSummary(ctx context.Context, req *secret_builder.BuildSecretSummaryRequest) (*secret_builder.BuildSecretSummaryResponse, error) {
	creditReports, err := csp.creditReportFetcher.RefreshAndGet(ctx, req.ActorId)
	if err != nil && !errors.Is(err, analyserErrors.NoDataFoundErr) {
		return nil, fmt.Errorf("failed to fetch credit reports: %w", err)
	}
	// check if latest report is null, then return no report found error state
	var latestCreditReport *creditReportVgPb.CreditReportData
	if len(creditReports) > 0 {
		latestCreditReport = creditReports[0].GetCreditReportData()
	}
	if errors.Is(err, analyserErrors.NoDataFoundErr) || latestCreditReport == nil || latestCreditReport.GetScore().GetBureauScore() == "" {
		summary, summaryErr := csp.creditReportUIBuilder.BuildZeroStateSummary(ctx, req.SecretConfig)
		if summaryErr != nil {
			return nil, fmt.Errorf("failed to build credit report zero state summary: %w", summaryErr)
		}
		return &secret_builder.BuildSecretSummaryResponse{Resp: summary}, nil
	}
	summary, err := csp.creditReportUIBuilder.BuildSummary(ctx, req.SecretConfig, initialiseCreditStore(creditReports, csp.creditScoreParamFetcher))
	if err != nil {
		return nil, fmt.Errorf("failed to build credit report summary: %w", err)
	}
	return &secret_builder.BuildSecretSummaryResponse{Resp: summary}, nil
}

func (csp *CreditReportSecretBuilder) BuildSecretAnalyser(ctx context.Context, req *secret_builder.BuildSecretAnalyserRequest) (*secret_builder.BuildSecretAnalyserResponse, error) {
	creditReports, err := csp.creditReportFetcher.RefreshAndGet(ctx, req.ActorId)
	if err != nil && !errors.Is(err, analyserErrors.NoDataFoundErr) {
		return nil, fmt.Errorf("failed to fetch credit reports: %w", err)
	}
	// check if latest report is null, then return no report found error state
	var latestCreditReport *creditReportVgPb.CreditReportData
	if len(creditReports) > 0 {
		latestCreditReport = creditReports[0].GetCreditReportData()
	}
	if errors.Is(err, analyserErrors.NoDataFoundErr) || latestCreditReport == nil || latestCreditReport.GetScore().GetBureauScore() == "" {
		// Determine the appropriate provenance for the credit report request
		// By default, use MONEY_SECRETS provenance for backward compatibility
		// If a specific provenance is provided in the request (e.g., WEB_CREDIT_REPORT_ANALYSER),
		// use that instead. This allows different flows (mobile app, web) to be distinguished
		// in the credit report service.
		var provenance creditReportPb.Provenance
		switch req.Provenance {
		case secrets.Provenance_PROVENANCE_WEB_CREDIT_REPORT_ANALYSER:
			provenance = creditReportPb.Provenance_PROVENANCE_WEB_CREDIT_REPORT_ANALYSER
		default:
			provenance = creditReportPb.Provenance_PROVENANCE_MONEY_SECRETS
		}

		// handle the case where no credit report is found
		zeroStateDl, zeroStateErr := csp.handleNoReportFound(ctx, req.ActorId)
		if errors.Is(zeroStateErr, secretErrors.NoDataFoundToBuildSecret) && provenance == creditReportPb.Provenance_PROVENANCE_MONEY_SECRETS {
			return &secret_builder.BuildSecretAnalyserResponse{
				Resp: &secretsFePb.SecretAnalyserResponse{
					Response: &secretsFePb.SecretAnalyserResponse_RedirectDeeplink{
						RedirectDeeplink: zeroStateDl,
					},
				},
			}, secretErrors.NoDataToBuildSecret
		}

		creditReportRes, creditReportErr := csp.creditReportClient.StartDownloadProcess(ctx, &creditReportPb.StartDownloadProcessRequest{
			ActorId:          req.ActorId,
			RequestId:        uuid.New().String(),
			Provenance:       provenance,
			Vendor:           commonvgpb.Vendor_EXPERIAN,
			RedirectDeeplink: secrets.GetSecretAnalyserScreenDeeplink(req.SecretConfig.GetName()),
			AllowWithoutPan:  true,
		})
		if rpcErr := epifigrpc.RPCError(creditReportRes, creditReportErr); rpcErr != nil {
			return nil, fmt.Errorf("failed to initiate credit report download process: %w", rpcErr)
		}
		return &secret_builder.BuildSecretAnalyserResponse{
			Resp: &secretsFePb.SecretAnalyserResponse{Response: &secretsFePb.SecretAnalyserResponse_RedirectDeeplink{RedirectDeeplink: creditReportRes.GetNextAction()}}}, secretErrors.NoDataToBuildSecret
	}
	creditLensProcessor := initialiseCreditScoreLensProcessor(csp.config, store.NewCreditScoreSummaryStore(creditReports), csp.creditScoreParamFetcher,
		csp.releaseEvaluator, csp.lendingInsights)
	secretAnalyser, err := csp.creditReportUIBuilder.BuildSecretAnalyser(ctx, req, creditLensProcessor, initialiseCreditStore(creditReports, csp.creditScoreParamFetcher))
	if err != nil {
		return nil, fmt.Errorf("failed to build credit score secret analyser: %w", err)
	}
	return &secret_builder.BuildSecretAnalyserResponse{
		Resp: &secretsFePb.SecretAnalyserResponse{Response: &secretsFePb.SecretAnalyserResponse_SecretAnalyser{SecretAnalyser: secretAnalyser}}}, nil
}

func initialiseCreditScoreLensProcessor(genConf *genconf.Config,
	store *store.CreditScoreSummaryStore, creditScoreParamsFetcher params_fetcher.CreditScoreParamsFetcher,
	releaseEvaluator release.IEvaluator, lendingInsights loans.LendingInsights) *lens.CreditScoreSummaryLensProcessor {
	return lens.NewCreditScoreSummaryLensProcessor(genConf, store, creditScoreParamsFetcher, datetime.NewDefaultTime(), releaseEvaluator, lendingInsights)
}

func initialiseCreditStore(creditReports []*creditReportPb.CreditReportDownloadDetails, creditScoreParamFetcher params_fetcher.CreditScoreParamsFetcher) *secretStore.CreditScoreSecretStore {
	return secretStore.NewCreditScoreSecretStore(creditReports, creditScoreParamFetcher)
}

func (csp *CreditReportSecretBuilder) handleNoReportFound(ctx context.Context, actorId string) (*deeplink.Deeplink, error) {
	lastAttemptRes, err := csp.creditReportClient.GetLatestDownloadProcessDetails(ctx, &creditReportPb.GetLatestDownloadProcessDetailsRequest{
		ActorId: actorId,
		Vendor:  commonvgpb.Vendor_EXPERIAN,
	})
	if rpcErr := epifigrpc.RPCError(lastAttemptRes, err); rpcErr != nil && !lastAttemptRes.GetStatus().IsRecordNotFound() {
		return nil, fmt.Errorf("error while fetching last attempt details : %w", rpcErr)
	}
	currTime := csp.timeImpl.Now()

	// will redirect to INITIATE_CREDIT_REPORT_DOWNLOAD_FOR_ANALYSER screen if:
	// 1. this is user's first attempt to download report.
	// 2. last download attempt was a failure (other than report not found)
	// 3. some time has passed since last download attempt in which report wasn't found.
	if csp.isFirstAttempt(lastAttemptRes) || csp.transientFailureInLastAttempt(lastAttemptRes) ||
		csp.timePassedSinceReportNotFoundInLastAttempt(currTime, lastAttemptRes) {
		return nil, secretErrors.NoDataToBuildSecret
	}
	return csp.getNoRecordFoundZeroState(), secretErrors.NoDataFoundToBuildSecret
}

func (csp *CreditReportSecretBuilder) getNoRecordFoundZeroState() *deeplink.Deeplink {
	errorState := &deeplink.Deeplink{
		Screen: deeplink.Screen_DETAILED_ERROR_VIEW_SCREEN,
		ScreenOptions: &deeplink.Deeplink_DetailedErrorViewScreenOptions{
			DetailedErrorViewScreenOptions: &deeplink.DetailedErrorViewScreenOptions{
				TitleText:    commontypes.GetTextFromHtmlStringFontColourFontStyle("No credit score found", "#313234", commontypes.FontStyle_HEADLINE_L),
				SubtitleText: commontypes.GetTextFromHtmlStringFontColourFontStyle("This is probably because you do not have any loans or credit cards", "#929599", commontypes.FontStyle_BODY_S),
				Image: commontypes.GetVisualElementImageFromUrl(lightningIconUrl).WithProperties(&commontypes.VisualElementProperties{
					Height: 150,
					Width:  150,
				}).WithImageType(commontypes.ImageType_PNG),
				Ctas: []*deeplink.Cta{
					{
						Type:         deeplink.Cta_CUSTOM,
						Text:         "Ok, got it",
						DisplayTheme: deeplink.Cta_PRIMARY,
						Deeplink: &deeplink.Deeplink{
							Screen: deeplink.Screen_HOME,
						},
					},
				},
			},
		},
	}
	return errorState
}

func (csp *CreditReportSecretBuilder) isFirstAttempt(lastAttemptRes *creditReportPb.GetLatestDownloadProcessDetailsResponse) bool {
	return lastAttemptRes.GetStatus().IsRecordNotFound()
}

func (csp *CreditReportSecretBuilder) transientFailureInLastAttempt(lastAttemptRes *creditReportPb.GetLatestDownloadProcessDetailsResponse) bool {
	return lastAttemptRes.GetProcessSubStatus() != creditReportPb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_REPORT_NOT_FOUND
}

func (csp *CreditReportSecretBuilder) timePassedSinceReportNotFoundInLastAttempt(currTime time.Time, lastAttemptRes *creditReportPb.GetLatestDownloadProcessDetailsResponse) bool {
	return lastAttemptRes.GetProcessSubStatus() == creditReportPb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_REPORT_NOT_FOUND &&
		(currTime.Sub(lastAttemptRes.GetCreatedAt().AsTime()) > csp.config.MoneySecretsConfig().CreditScoreSecretsConfig().CoolOffDurationForRnfInMinutes())
}
