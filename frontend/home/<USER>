package home

import (
	"context"
	json2 "encoding/json"
	"fmt"
	"strings"
	"time"

	types "github.com/epifi/gamma/api/typesv2"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"

	"github.com/epifi/gamma/pkg/accrual"

	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/crypto"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	pb "github.com/epifi/gamma/api/frontend/home"
	productPb "github.com/epifi/gamma/api/product"
	segmentPb "github.com/epifi/gamma/api/segment"
	homeTypesPb "github.com/epifi/gamma/api/typesv2/home"
	"github.com/epifi/gamma/api/typesv2/ui"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/onboarding"

	"github.com/epifi/gamma/frontend/config"
	homeUtils "github.com/epifi/gamma/frontend/home/<USER>"
	"github.com/epifi/gamma/frontend/pkg/common"
	"github.com/epifi/gamma/frontend/pkg/featureflags"
	homePkg "github.com/epifi/gamma/frontend/pkg/home"
)

var (
	shortcutOptionsListForRandomization = [...]homeTypesPb.IconType{
		homeTypesPb.IconType_SHORTCUT_CREDIT_SCORE_ANALYSER,
		homeTypesPb.IconType_SHORTCUT_CARD_OFFERS,
		homeTypesPb.IconType_SHORTCUT_WAYS_TO_EARN,
		homeTypesPb.IconType_SHORTCUT_SD,
		homeTypesPb.IconType_SHORTCUT_BANK_TRANSFER,
		homeTypesPb.IconType_SHORTCUT_CONNECTED_ACCOUNT,
	}

	// softIntentCategoryToOrderedShortcutsMapForNewSAUsers is a map that contains the default shortcuts for new SA users
	// currently new SA user covers UserTypeFiSAWithD0To7 and UserTypeFiSAWithD8To14
	// this can be moved to config or we can have a shortcut evaluater if such use cases increases
	softIntentCategoryToOrderedShortcutsMapForNewSAUsers = map[onboarding.OnboardingSoftIntentCategory][]int32{
		onboarding.OnboardingSoftIntentCategory_ONBOARDING_SOFT_INTENT_CATEGORY_EVERYDAY_NEEDS: {
			int32(homeTypesPb.IconType_SHORTCUT_PAY_TO_UPI),
			int32(homeTypesPb.IconType_SHORTCUT_DEBIT_CARD),
			int32(homeTypesPb.IconType_SHORTCUT_AA_SALARY_PLAN),
			int32(homeTypesPb.IconType_SHORTCUT_CREDIT_SCORE_ANALYSER),
		},
		onboarding.OnboardingSoftIntentCategory_ONBOARDING_SOFT_INTENT_CATEGORY_GLOBAL_SPENDING: {
			int32(homeTypesPb.IconType_SHORTCUT_PAY_TO_UPI),
			int32(homeTypesPb.IconType_SHORTCUT_DEBIT_CARD),
			int32(homeTypesPb.IconType_SHORTCUT_AA_SALARY_PLAN),
			int32(homeTypesPb.IconType_SHORTCUT_CREDIT_SCORE_ANALYSER),
		},
		onboarding.OnboardingSoftIntentCategory_ONBOARDING_SOFT_INTENT_CATEGORY_INVEST_MONEY: {
			int32(homeTypesPb.IconType_SHORTCUT_PAY_TO_UPI),
			int32(homeTypesPb.IconType_SHORTCUT_DEBIT_CARD),
			int32(homeTypesPb.IconType_SHORTCUT_NETWORTH),
			int32(homeTypesPb.IconType_SHORTCUT_CREDIT_SCORE_ANALYSER),
		},
		onboarding.OnboardingSoftIntentCategory_ONBOARDING_SOFT_INTENT_CATEGORY_INSTANT_LOANS: {
			int32(homeTypesPb.IconType_SHORTCUT_PAY_TO_UPI),
			int32(homeTypesPb.IconType_SHORTCUT_DEBIT_CARD),
			int32(homeTypesPb.IconType_SHORTCUT_SECURED_LOANS),
			int32(homeTypesPb.IconType_SHORTCUT_CREDIT_SCORE_ANALYSER),
		},
	}
)

type CommonUserDetails struct {
	UserType      homePkg.UserType
	OnbDetailsRes *onboarding.GetDetailsResponse
}

func (s *Service) GetHomeShortcuts(ctx context.Context, req *pb.GetHomeShortcutsRequest) (*pb.GetHomeShortcutsResponse, error) {
	deviceDetails := s.genconf.HomeRevampParams().HomeShortcutParams().PlatformVersionDetails()
	if !isDevicePlatformVersionValidForHomeShortcuts(ctx, deviceDetails) {
		return &pb.GetHomeShortcutsResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusRecordNotFoundWithDebugMsg("App version not valid for home shortcuts")},
		}, nil
	}
	actorId := req.GetReq().GetAuth().GetActorId()
	appPlatform := req.GetReq().GetAuth().GetDevice().GetPlatform()
	appVersionCode := req.GetReq().GetAppVersionCode()
	appName := req.GetReq().GetAppName()

	commonUserDetails, err := s.getCommonUserDetails(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error in get common user details", zap.Error(err))
		return &pb.GetHomeShortcutsResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("error in fetching user type for home shortcuts")},
		}, nil
	}

	if featureflags.IsFeatureHomeDesignEnhancementsEnabled(ctx, &featureflags.IsFeatureHomeDesignEnhancementsEnabledRequest{
		ActorId: actorId,
		ExternalDeps: &common.ExternalDependencies{
			Evaluator:            s.evaluator,
			OnboardingClient:     s.onboardingClient,
			QuestSdkClient:       s.questSdkClient,
			UserAttributeFetcher: s.userAttributeFetcher,
			NetWorthClient:       s.networthClient,
		},
	}) {
		return s.getHomeShortcutsV2(ctx, actorId, appPlatform, appVersionCode, appName, true, commonUserDetails)
	}

	homeIcons, areDefaultShortcutIconsPresent, err := s.getHomeShortcutIcons(ctx, actorId, appPlatform, appVersionCode, appName, false, commonUserDetails)
	if err != nil {
		return &pb.GetHomeShortcutsResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("error in fetching home shortcuts")},
		}, nil
	}

	shouldShowExploreFiShortcut := s.shouldShowExploreFiShortcut(ctx, commonUserDetails.UserType)
	if shouldShowExploreFiShortcut {
		constructedExploreFiIcon := getExploreFiIcon()
		homeIcons = append([]*pb.Icon{constructedExploreFiIcon}, homeIcons...)
	}
	if len(homeIcons) == 0 {
		return &pb.GetHomeShortcutsResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
			ZeroState:  getZeroStateIconForHomeShortcuts(),
		}, nil
	}

	shouldShowAddMoreShortcuts := s.shouldShowAddOrEditShortcut(commonUserDetails.UserType)
	if shouldShowAddMoreShortcuts {
		extraIcon := getExtraIconInHomeShortcutIcons(homeUtils.ShortcutsWidgetEditIcon, EditShortcutsText)
		if (shouldShowExploreFiShortcut && len(homeIcons) == 1) || areDefaultShortcutIconsPresent {
			extraIcon = getExtraIconInHomeShortcutIcons(homeUtils.ShortcutsWidgetPlusIcon, AddShortcutsText)
		}
		homeIcons = append(homeIcons, extraIcon)
	}

	var title *commontypes.Text
	if s.genconf.HomeRevampParams().HomeShortcutParams().Title(ctx) != "" {
		title = &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: s.genconf.HomeRevampParams().HomeShortcutParams().Title(ctx),
			},
			FontColor: "#333333",
			FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_M},
		}
	}

	return &pb.GetHomeShortcutsResponse{
		Title:                title,
		RespHeader:           &header.ResponseHeader{Status: rpc.StatusOk()},
		ShortcutIcons:        homeIcons,
		MaximumNoOfShortcuts: s.genconf.HomeRevampParams().HomeShortcutParams().MaximumNoOfShortcuts(),
	}, nil
}

func (s *Service) getHomeShortcutsV2(ctx context.Context, actorId string, appPlatform commontypes.Platform, appVersionCode uint32, appName commontypes.AppName, isFeatureHomeDesignEnhancementsEnabled bool, commonUserDetails *CommonUserDetails) (*pb.GetHomeShortcutsResponse, error) {
	homeIcons, _, err := s.getHomeShortcutIcons(ctx, actorId, appPlatform, appVersionCode, appName, isFeatureHomeDesignEnhancementsEnabled, commonUserDetails)
	if err != nil {
		logger.Error(ctx, "error while fetching home shortcuts", zap.Error(err))
		return &pb.GetHomeShortcutsResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("error in fetching home shortcuts")},
		}, nil
	}

	maximumNoOfShortcuts := 8
	if len(homeIcons) < 5 {
		maximumNoOfShortcuts = 4
	}

	// Log the shortcut icons being shown to the user
	shortcutIconIds := make([]string, len(homeIcons))
	for i, icon := range homeIcons {
		shortcutIconIds[i] = icon.GetId()
	}
	logger.InfoForActor(ctx, s.genconf.ActorsWhitelistedForLogs(), "getHomeShortcutsV2", zap.Int("maximumNoOfShortcuts", maximumNoOfShortcuts), zap.Int("homeIconsLength", len(homeIcons)), zap.Strings("shortcutIconIds", shortcutIconIds))

	return &pb.GetHomeShortcutsResponse{
		Title: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: homeUtils.QuickActionsShortcutTitle,
			},
			FontColor: "#38393B",
			FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_DISPLAY_M},
		},
		RespHeader:    &header.ResponseHeader{Status: rpc.StatusOk()},
		ShortcutIcons: homeIcons,
		SeeAllCta: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle("Explore All", "#FFFFFF", commontypes.FontStyle_SUBTITLE_XS),
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				CornerRadius:  24,
				Height:        28,
				Width:         100,
				LeftPadding:   8,
				RightPadding:  2,
				TopPadding:    4,
				BottomPadding: 4,
				BackgroundColour: widget.GetLinearGradientBackgroundColour(0, []*widget.ColorStop{
					{
						Color:          "#00B899",
						StopPercentage: 0,
					},
					{
						Color:          "#006D5B",
						StopPercentage: 100,
					},
				}),
			},
			RightVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/chevron_right_white.png", 20, 20),
			LeftVisualElement:  commontypes.GetVisualElementFromUrlHeightAndWidth(homeUtils.ExploreIconsForHomeShortcuts, 20, 20),
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_HOME_EXPLORE,
			},
		},
		MaximumNoOfShortcuts: uint32(maximumNoOfShortcuts),
	}, nil
}

func (s *Service) GetHomeShortcutOptions(ctx context.Context, req *pb.GetHomeShortcutOptionsRequest) (*pb.GetHomeShortcutOptionsResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()
	appPlatform := req.GetReq().GetAuth().GetDevice().GetPlatform()
	appVersionCode := req.GetReq().GetAppVersionCode()
	appName := req.GetReq().GetAppName()

	isFeatureHomeDesignEnhancementsEnabled := featureflags.IsFeatureHomeDesignEnhancementsEnabled(ctx, &featureflags.IsFeatureHomeDesignEnhancementsEnabledRequest{
		ActorId: actorId,
		ExternalDeps: &common.ExternalDependencies{
			Evaluator:            s.evaluator,
			OnboardingClient:     s.onboardingClient,
			QuestSdkClient:       s.questSdkClient,
			UserAttributeFetcher: s.userAttributeFetcher,
			NetWorthClient:       s.networthClient,
		},
	})
	homeIcons, _, err := s.getHomeShortcutIcons(ctx, actorId, appPlatform, appVersionCode, appName, isFeatureHomeDesignEnhancementsEnabled, nil)
	if err != nil {
		return &pb.GetHomeShortcutOptionsResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("error in fetching home shortcuts")},
		}, nil
	}

	_, exploreSection := s.getHomeExploreSections(ctx, actorId, appPlatform, appVersionCode, isFeatureHomeDesignEnhancementsEnabled)
	response := pb.GetHomeShortcutOptionsResponse{
		RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
		ChosenShortcutsSection: &pb.GetHomeShortcutOptionsResponse_ChosenShortcutsSection{
			ChosenShortcutIcons:  homeIcons,
			MaximumNoOfShortcuts: s.genconf.HomeRevampParams().HomeShortcutParams().MaximumNoOfShortcuts(),
		},
		AvailableShortcutsSection: &pb.GetHomeShortcutOptionsResponse_AvailableShortcutsSection{
			AvailableShortcutsRows: make([]*pb.GetHomeShortcutOptionsResponse_AvailableShortcutsSection_AvailableShortcutsRow, len(exploreSection)),
		},
		BottomCtasChosenShortcutsSectionStateToCtaListMap: getBottomCtasChosenShortcutsSectionStateToCtaListMap(),
		BackBtnCtaChosenShortcutsSectionStateToListMap:    getBackBtnCtaChosenShortcutsSectionStateToListMap(),
	}
	for idx, section := range exploreSection {
		response.AvailableShortcutsSection.AvailableShortcutsRows[idx] = &pb.GetHomeShortcutOptionsResponse_AvailableShortcutsSection_AvailableShortcutsRow{
			Title: section.GetTitle(),
			Icon:  section.GetIcon(),
		}
	}
	return &response, nil
}

func (s *Service) getHomeShortcutIcons(ctx context.Context, actorId string, appPlatform commontypes.Platform, appVersionCode uint32, appName commontypes.AppName, isFeatureHomeDesignEnhancementsEnabled bool, commonUserDetails *CommonUserDetails) ([]*pb.Icon, bool, error) {
	userPreferencesRequest := userPb.GetUserPreferencesRequest{
		ActorId:         actorId,
		PreferenceTypes: []userPb.PreferenceType{userPb.PreferenceType_PREFERENCE_TYPE_HOME_SHORTCUTS},
	}
	userPrefResp, err := s.usersClient.GetUserPreferences(ctx, &userPreferencesRequest)
	if rpcErr := epifigrpc.RPCError(userPrefResp, err); rpcErr != nil {
		if userPrefResp.GetStatus().IsRecordNotFound() {
			logger.InfoForActor(ctx, s.genconf.ActorsWhitelistedForLogs(), "home shortcut preferences not found", zap.Error(rpcErr))
			defaultShortcutIcons, defaultShortcutIconsErr := s.getDefaultShortcutIcons(ctx, actorId, appPlatform, appVersionCode, appName, isFeatureHomeDesignEnhancementsEnabled, commonUserDetails)
			if defaultShortcutIconsErr != nil {
				logger.Error(ctx, "error in getting default home shortcuts", zap.Error(defaultShortcutIconsErr))
				return nil, false, fmt.Errorf("error in getting default home shortcuts, defaultShortcutIconsErr: %w", defaultShortcutIconsErr)
			}
			return defaultShortcutIcons, true, nil
		}
		logger.Error(ctx, "error in fetching home shortcuts", zap.Error(rpcErr))
		return nil, false, fmt.Errorf("error in fetching home shortcuts, err: %w", rpcErr)
	}
	if len(userPrefResp.GetUserPreferences()) == 0 {
		logger.InfoForActor(ctx, s.genconf.ActorsWhitelistedForLogs(), "got nil user preference for home shortcuts")
		defaultShortcutIcons, defaultShortcutIconsErr := s.getDefaultShortcutIcons(ctx, actorId, appPlatform, appVersionCode, appName, isFeatureHomeDesignEnhancementsEnabled, commonUserDetails)
		if defaultShortcutIconsErr != nil {
			logger.Error(ctx, "error in getting default home shortcuts", zap.Error(defaultShortcutIconsErr))
			return nil, false, fmt.Errorf("error in getting default home shortcuts, defaultShortcutIconsErr: %w", defaultShortcutIconsErr)
		}
		return defaultShortcutIcons, true, nil
	}

	preferenceValues := userPrefResp.GetUserPreferences()[0].GetPreferenceValue().GetHomeShortcutsPreference().GetIconTypes()
	if len(preferenceValues) == 0 {
		defaultShortcutIcons, defaultShortcutIconsErr := s.getDefaultShortcutIcons(ctx, actorId, appPlatform, appVersionCode, appName, isFeatureHomeDesignEnhancementsEnabled, commonUserDetails)
		if defaultShortcutIconsErr != nil {
			logger.Error(ctx, "error in getting default home shortcuts", zap.Error(defaultShortcutIconsErr))
			return nil, false, fmt.Errorf("error in getting default home shortcuts, defaultShortcutIconsErr: %w", defaultShortcutIconsErr)
		}
		return defaultShortcutIcons, true, nil
	}

	homeIcons, err := s.getHomeIconsFromPreferenceValues(ctx, actorId, appPlatform, appVersionCode, preferenceValues, isFeatureHomeDesignEnhancementsEnabled)
	if err != nil {
		return nil, false, fmt.Errorf("error in fetching home shortcuts, err: %w", err)
	}
	return homeIcons, false, nil
}

// getSoftIntentBasedDefaultShortcutIcons returns the list of shortcuts to be shown on home based on the soft intent selected by the user
func (s *Service) getSoftIntentBasedDefaultShortcutIcons(userSelectedSoftIntents []onboarding.OnboardingSoftIntent, defaultFiLiteShortcuts []int32) []int32 {
	maxNoOfShortcuts := int(s.genconf.HomeRevampParams().HomeShortcutParams().MaximumNoOfShortcuts())
	return s.getShortcutsOneFromEachCategory(maxNoOfShortcuts, userSelectedSoftIntents, defaultFiLiteShortcuts)
}

func (s *Service) getShortcutsOneFromEachCategory(maxNoOfShortcuts int, userSelectedSoftIntents []onboarding.OnboardingSoftIntent, defaultShortcuts []int32) []int32 {
	var shortcutIcons []int32
	shortcutIconLookupMap := make(map[int32]bool)                                             // lookup map to ensure no duplicate shortcuts are added to the list
	categoryWiseOrderedShortcuts := make([][]int32, len(onboarding.CategoryToSoftIntentsMap)) // each row contains exactly one shortcut that corresponds to an intent category
	categoryFreqMap := make(map[onboarding.OnboardingSoftIntentCategory]int)                  // map to store the number of shortcuts corresponding to the category is added to the categoryWiseOrderedShortcuts list
	for _, intent := range userSelectedSoftIntents {
		shortcutIcon := s.genconf.HomeRevampParams().HomeShortcutParams().SoftIntentToShortcutMap().Get(intent.String())
		if shortcutIconLookupMap[shortcutIcon] {
			continue // continue if shortcut is already picked
		}
		shortcutIconLookupMap[shortcutIcon] = true
		category := onboarding.SoftIntentToCategoryMap[intent]
		row := categoryFreqMap[category]
		categoryWiseOrderedShortcuts[row] = append(categoryWiseOrderedShortcuts[row], shortcutIcon)
		categoryFreqMap[category]++
	}

	row := 0
	for row < len(categoryWiseOrderedShortcuts) && len(shortcutIcons) < maxNoOfShortcuts {
		shortcutIcons = append(shortcutIcons, categoryWiseOrderedShortcuts[row]...)
		row++
	}

	for _, shortcutIcon := range defaultShortcuts {
		if !shortcutIconLookupMap[shortcutIcon] && len(shortcutIcons) < maxNoOfShortcuts {
			shortcutIconLookupMap[shortcutIcon] = true
			shortcutIcons = append(shortcutIcons, shortcutIcon)
		}
	}

	if len(shortcutIcons) > maxNoOfShortcuts {
		shortcutIcons = shortcutIcons[:maxNoOfShortcuts]
	}

	return shortcutIcons
}

// Method to get the default shortcuts from config (powered by quest).
func (s *Service) getDefaultShortcutIcons(ctx context.Context, actorId string, appPlatform commontypes.Platform, appVersionCode uint32, _ commontypes.AppName, isFeatureHomeDesignEnhancementsEnabled bool, commonUserDetails *CommonUserDetails) ([]*pb.Icon, error) {
	var (
		defaultShortcutsFromConfig  []int32
		userTypeToShortcutParamsMap = s.genconf.HomeRevampParams().HomeShortcutParams().UserTypeToShortcutParamsMap()
	)
	// get default shortcuts
	userTypeFiSASpecificShortcutParams, ok := userTypeToShortcutParamsMap.Load(homePkg.UserTypeFiSA.String())
	if !ok {
		logger.Error(ctx, "failed to get default shortcuts from config", zap.String(logger.ACTOR_ID_V2, actorId), zap.String("userType", homePkg.UserTypeFiSA.String()))
		return nil, fmt.Errorf("failed to get default shortcuts from config, userType: %s", homePkg.UserTypeFiSA.String())
	}
	defaultShortcutsFromConfig = userTypeFiSASpecificShortcutParams.DefaultShortcuts()

	if commonUserDetails == nil {
		// get suitable user type
		userType, getDetailsRes, _, err := homePkg.GetSuitableUserType(ctx, &homePkg.GetSuitableUserTypeRequest{
			ActorId: actorId,
			ExternalDeps: &common.ExternalDependencies{
				Evaluator:            s.evaluator,
				OnboardingClient:     s.onboardingClient,
				QuestSdkClient:       s.questSdkClient,
				UserAttributeFetcher: s.userAttributeFetcher,
				NetWorthClient:       s.networthClient,
			},
		})
		if err != nil {
			logger.Error(ctx, "failed to get user type", zap.Error(err))
			return nil, fmt.Errorf("failed to get user type, err: %w", err)
		}
		commonUserDetails = &CommonUserDetails{UserType: userType, OnbDetailsRes: getDetailsRes}
	}

	userSelectedSoftIntents := []onboarding.OnboardingSoftIntent{}
	if commonUserDetails.OnbDetailsRes != nil && commonUserDetails.OnbDetailsRes.GetDetails() != nil &&
		commonUserDetails.OnbDetailsRes.GetDetails().GetStageMetadata() != nil &&
		commonUserDetails.OnbDetailsRes.GetDetails().GetStageMetadata().GetSoftIntentSelectionMetadata() != nil {
		userSelectedSoftIntents = commonUserDetails.OnbDetailsRes.GetDetails().GetStageMetadata().GetSoftIntentSelectionMetadata().GetSelection()
	}
	userTypeSpecificShortcutParams, ok := userTypeToShortcutParamsMap.Load(commonUserDetails.UserType.String())
	if !ok {
		logger.Error(ctx, "failed to get default shortcuts from config", zap.String(logger.ACTOR_ID_V2, actorId), zap.String("userType", commonUserDetails.UserType.String()))
	} else {
		defaultShortcutsFromConfig = userTypeSpecificShortcutParams.DefaultShortcuts()

		// updating default shortcuts for user type specific logic
		switch commonUserDetails.UserType {
		case homePkg.UserTypeFiSA:
			defaultShortcutsFromConfig = s.getDefaultShortcutsForSAUsers(ctx, actorId, defaultShortcutsFromConfig)
		case homePkg.UserTypeFiLite:
			if commonUserDetails.OnbDetailsRes != nil && commonUserDetails.OnbDetailsRes.GetDetails() != nil {
				defaultShortcutsFromConfig = s.getDefaultShortcutsForFiLiteUsers(ctx, actorId, commonUserDetails.OnbDetailsRes.GetDetails(), defaultShortcutsFromConfig)
			} else {
				logger.Warn("getDetailsRes or getDetailsRes.GetDetails() is nil for UserTypeFiLite in getDefaultShortcutIcons", zap.String(logger.ACTOR_ID_V2, actorId))
			}
		case homePkg.UserTypeFiSAWithD0To7:
			defaultShortcutsFromConfig = s.getDefaultShortcutsForD0To7SAUsers(userSelectedSoftIntents, defaultShortcutsFromConfig)
		case homePkg.UserTypeFiSAWithD8To14:
			defaultShortcutsFromConfig = s.getDefaultShortcutsForD8To14SAUsers(userSelectedSoftIntents, defaultShortcutsFromConfig)
		case homePkg.UserTypeWealthAnalyser:
			defaultShortcutsFromConfig = s.getDefaultShortcutsForWealthBuilderUsers(ctx, actorId, defaultShortcutsFromConfig)
		}
	}

	defaultShortcutsJsonFromExperiment := s.genconf.HomeRevampParams().HomeShortcutParams().DefaultShortcutsFromExperiment(ctx)
	if defaultShortcutsJsonFromExperiment != "" {
		defaultShortcutsFromExperiment, err := s.GetDefaultShortcutsFromString(ctx, defaultShortcutsJsonFromExperiment)
		if err != nil {
			logger.Info(ctx, fmt.Sprintf("error getting default shortcuts for user from experiment loaded for actor: %v", actorId))
		} else {
			defaultShortcutsFromConfig = defaultShortcutsFromExperiment
		}
	}

	var defaultShortcutIconTypes []homeTypesPb.IconType
	for _, val := range defaultShortcutsFromConfig {
		iconType := homeTypesPb.IconType(val)
		defaultShortcutIconTypes = append(defaultShortcutIconTypes, iconType)
	}

	homeIconsFromPreferenceValues, err := s.getHomeIconsFromPreferenceValues(ctx, actorId, appPlatform, appVersionCode, defaultShortcutIconTypes, isFeatureHomeDesignEnhancementsEnabled)
	if err != nil {
		return nil, fmt.Errorf("error in fetching home shortcuts using preference values, err: %w", err)
	}

	return homeIconsFromPreferenceValues, nil
}

func (s *Service) getDefaultShortcutsForD0To7SAUsers(userSelectedSoftIntents []onboarding.OnboardingSoftIntent, shortcuts []int32) []int32 {
	if len(userSelectedSoftIntents) > 0 {
		softIntentCategory := s.fetchDerivedSoftIntentCategoryBasedOnPriority(userSelectedSoftIntents, P0)
		if softIntentBasedShortcuts, ok := softIntentCategoryToOrderedShortcutsMapForNewSAUsers[softIntentCategory]; ok {
			shortcuts = softIntentBasedShortcuts
		}
	}

	return shortcuts
}

func (s *Service) getDefaultShortcutsForD8To14SAUsers(userSelectedSoftIntents []onboarding.OnboardingSoftIntent, shortcuts []int32) []int32 {
	if len(userSelectedSoftIntents) > 0 {
		softIntentCategory := s.fetchDerivedSoftIntentCategoryBasedOnPriority(userSelectedSoftIntents, P0)
		if softIntentBasedShortcuts, ok := softIntentCategoryToOrderedShortcutsMapForNewSAUsers[softIntentCategory]; ok {
			shortcuts = softIntentBasedShortcuts
		}
	}

	return shortcuts
}

func (s *Service) getDefaultShortcutsForFiLiteUsers(ctx context.Context, actorId string, onbDetails *onboarding.OnboardingDetails, shortcuts []int32) []int32 {
	// if user has selected soft intents, then we need to show the shortcuts based on the soft intents
	userSelectedSoftIntents := onbDetails.GetStageMetadata().GetSoftIntentSelectionMetadata().GetSelection()
	if len(userSelectedSoftIntents) > 0 {
		shortcuts = s.getSoftIntentBasedDefaultShortcutIcons(userSelectedSoftIntents, shortcuts)
	}

	getProductsRes, err := s.productClient.GetProductsStatus(ctx, &productPb.GetProductsStatusRequest{
		ActorId:      actorId,
		ProductTypes: []productPb.ProductType{productPb.ProductType_PRODUCT_TYPE_TPAP},
	})
	if rpcErr := epifigrpc.RPCError(getProductsRes, err); rpcErr != nil {
		logger.Error(ctx, "error in fetching products status details", zap.Error(rpcErr))
	}

	// if user has activated the UPI TPAP feature, then we need to show the scan and pay shortcut
	if getProductsRes.GetProductInfoMap()[productPb.ProductType_PRODUCT_TYPE_TPAP.String()].GetProductStatus() == productPb.ProductStatus_PRODUCT_STATUS_ACTIVE {
		shortcuts = append([]int32{int32(homeTypesPb.IconType_SHORTCUT_PAY_TO_UPI)}, shortcuts[:len(shortcuts)-1]...)
	}

	// add networth if either of talk to ai or magic lens is not enabled and len(shortcuts) < 4
	isMcpEnabled, isMagicImportEnabled := s.areAiShortcutsEnabled(ctx, actorId)
	networthShortcut := int32(51)
	if !isMcpEnabled && !isMagicImportEnabled {
		shortcuts = append(shortcuts, networthShortcut)
	}

	return shortcuts
}

func (s *Service) getDefaultShortcutsForSAUsers(ctx context.Context, actorId string, defaultShortcutsFromConfig []int32) []int32 {
	shortcuts := make([]int32, 0)
	shortcuts = append(shortcuts, 26) // SHORTCUT_PAY_TO_PHONE
	defaultShortcutValuesForAreas, defaultShortcutValuesForAreasErr := s.getDefaultShortcutValuesForAreas(ctx, actorId)
	if defaultShortcutValuesForAreasErr != nil {
		logger.Error(ctx, "failed to get default shortcut values for areas", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(defaultShortcutValuesForAreasErr))
		return defaultShortcutsFromConfig
	}
	loanShortcutVal := defaultShortcutValuesForAreas.LoanShortcutValue
	ccShortcutVal := defaultShortcutValuesForAreas.CCShortcutValue
	usStocksVal := defaultShortcutValuesForAreas.USStocksShortcutValue
	prepayLoanShortcutVal := defaultShortcutValuesForAreas.PrePayLoanShortcutValue
	if loanShortcutVal != 0 {
		shortcuts = append(shortcuts, loanShortcutVal)
	}
	if ccShortcutVal != 0 {
		shortcuts = append(shortcuts, ccShortcutVal)
	}
	if usStocksVal != 0 {
		shortcuts = append(shortcuts, usStocksVal)
	}
	if prepayLoanShortcutVal != 0 {
		shortcuts = append(shortcuts, prepayLoanShortcutVal)
	}
	if len(shortcuts) < 4 {
		shortcuts = append(shortcuts, s.getRemainingDefaultShortcuts(ctx, actorId, 4-len(shortcuts))...)
	}
	return shortcuts
}

func (s *Service) getDefaultShortcutsForWealthBuilderUsers(ctx context.Context, actorId string, defaultShortcutsFromConfig []int32) []int32 {
	// if either of magic lens and talk to ai are not enabled for user then we will append networth to WB Home Screen
	isMcpEnabled, isMagicImportEnabled := s.areAiShortcutsEnabled(ctx, actorId)
	networthShortcut := int32(51)
	if !isMcpEnabled && !isMagicImportEnabled {
		defaultShortcutsFromConfig = append(defaultShortcutsFromConfig, networthShortcut)
	}
	return defaultShortcutsFromConfig
}

func (s *Service) getRemainingDefaultShortcuts(ctx context.Context, actorId string, remainingShortcutSlots int) []int32 {
	selfTransferShortcut := int32(76)
	// debitCardShortcut := int32(28)
	creditScoreShortcut := int32(36)
	giftCardStoreShortcut := int32(61)
	shortcuts := make([]int32, 0)

	// code below priotises magic lens and talk to ai over other icons, if they are enabled
	talkToAiShortcut := int32(92)
	magicImportShortcut := int32(93)
	isMcpEnabled, isMagicImportEnabled := s.areAiShortcutsEnabled(ctx, actorId)
	if isMcpEnabled && isMagicImportEnabled {
		switch {
		case remainingShortcutSlots >= 2:
			shortcuts = append(shortcuts, talkToAiShortcut, magicImportShortcut)
		case remainingShortcutSlots == 1:
			shortcuts = append(shortcuts, talkToAiShortcut)
		}
		remainingShortcutSlots -= len(shortcuts)
	}

	switch remainingShortcutSlots {
	case 3:
		shortcuts = append(shortcuts, selfTransferShortcut, creditScoreShortcut, giftCardStoreShortcut)
	case 2:
		shortcuts = append(shortcuts, selfTransferShortcut, creditScoreShortcut)
	case 1:
		shortcuts = append(shortcuts, selfTransferShortcut)
	}
	return shortcuts
}

func (s *Service) getRandomIndexForDefaultShortcutForUser(ctx context.Context, actorId string, length int) int32 {
	generatedIndex := 0
	hashedNumber, hashError := crypto.GetStringToIntHash(actorId)
	if hashError != nil {
		logger.Error(ctx, "failed to generate hash for actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(hashError))
	} else {
		// to round about in the array we are taking the remainder with length of array
		hashedNumberByLength := hashedNumber % uint32(length)
		// current week is used to randomize the index in an incremental way for the user, so that they see one particular
		// random shortcut for that week
		_, currentWeekNumber := time.Now().ISOWeek()
		generatedIndex = int((hashedNumberByLength + uint32(currentWeekNumber)) % uint32(length))
	}
	logger.Debug(ctx, "generated index for actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Int("generatedIndex", generatedIndex))
	return int32(generatedIndex)
}

func (s *Service) getDefaultShortcutValuesForAreas(ctx context.Context, actorId string) (*config.DefaultShortcutsValueForAreas, error) {
	homeShortcutParams := s.genconf.HomeRevampParams().HomeShortcutParams()
	loanShortcutValue := int32(0)
	ccShortcutValue := int32(0)
	usStocksShortcutValue := int32(0)
	prepayLoanShortcutValue := int32(0)
	segmentIds := make([]string, 0)

	shortcutsSegmentConfigsForLoan, ok := homeShortcutParams.AreaToSegmentConfigMap()["Loan"]
	if !ok {
		logger.Error(ctx, "failed to get segment configs for shortcuts", zap.String(logger.ACTOR_ID_V2, actorId), zap.String("area", "loan"))
	} else {
		segmentIds = append(segmentIds, shortcutsSegmentConfigsForLoan.SegmentId)
	}

	shortcutsSegmentConfigsForCC, ok := homeShortcutParams.AreaToSegmentConfigMap()["CC"]
	if !ok {
		logger.Error(ctx, "failed to get segment configs for shortcuts", zap.String(logger.ACTOR_ID_V2, actorId), zap.String("area", "cc"))
	} else {
		segmentIds = append(segmentIds, shortcutsSegmentConfigsForCC.SegmentId)
	}

	shortcutsSegmentConfigsForUSStocks, ok := homeShortcutParams.AreaToSegmentConfigMap()["USStocks"]
	if !ok {
		logger.Error(ctx, "failed to get segment configs for shortcuts", zap.String(logger.ACTOR_ID_V2, actorId), zap.String("area", "usstocks"))
	} else {
		segmentIds = append(segmentIds, shortcutsSegmentConfigsForUSStocks.SegmentId)
	}

	shortcutsSegmentConfigsForPrePayLoan, ok := homeShortcutParams.AreaToSegmentConfigMap()["PrePayLoan"]
	if !ok {
		logger.Error(ctx, "failed to get segment configs for shortcuts", zap.String(logger.ACTOR_ID_V2, actorId), zap.String("area", "prepayloan"))
	} else {
		segmentIds = append(segmentIds, shortcutsSegmentConfigsForPrePayLoan.SegmentId)
	}

	segmentMembershipMap, err := s.getSegmentMembershipMapForSegmentIds(ctx, segmentIds, actorId)
	if err != nil {
		logger.Error(ctx, "failed to get segment membership map", zap.String(logger.ACTOR_ID_V2, actorId))
		return nil, fmt.Errorf("failed to get segment membership map for actor %s, err: %w", actorId, err)
	}
	if shortcutsSegmentConfigsForLoan != nil &&
		segmentMembershipMap[shortcutsSegmentConfigsForLoan.SegmentId].GetSegmentStatus() == segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND &&
		segmentMembershipMap[shortcutsSegmentConfigsForLoan.SegmentId].GetIsActorMember() {
		loanShortcutValue = shortcutsSegmentConfigsForLoan.ShortcutEnumValue
	}
	if shortcutsSegmentConfigsForCC != nil &&
		segmentMembershipMap[shortcutsSegmentConfigsForCC.SegmentId].GetSegmentStatus() == segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND &&
		segmentMembershipMap[shortcutsSegmentConfigsForCC.SegmentId].GetIsActorMember() {
		ccShortcutValue = shortcutsSegmentConfigsForCC.ShortcutEnumValue
	}
	if shortcutsSegmentConfigsForUSStocks != nil &&
		segmentMembershipMap[shortcutsSegmentConfigsForUSStocks.SegmentId].GetSegmentStatus() == segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND &&
		segmentMembershipMap[shortcutsSegmentConfigsForUSStocks.SegmentId].GetIsActorMember() {
		usStocksShortcutValue = shortcutsSegmentConfigsForUSStocks.ShortcutEnumValue
	}
	if shortcutsSegmentConfigsForPrePayLoan != nil &&
		segmentMembershipMap[shortcutsSegmentConfigsForPrePayLoan.SegmentId].GetSegmentStatus() == segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND &&
		segmentMembershipMap[shortcutsSegmentConfigsForPrePayLoan.SegmentId].GetIsActorMember() {
		prepayLoanShortcutValue = shortcutsSegmentConfigsForPrePayLoan.ShortcutEnumValue
	}

	return &config.DefaultShortcutsValueForAreas{
		LoanShortcutValue:       loanShortcutValue,
		CCShortcutValue:         ccShortcutValue,
		USStocksShortcutValue:   usStocksShortcutValue,
		PrePayLoanShortcutValue: prepayLoanShortcutValue,
	}, nil
}

func (s *Service) getSegmentMembershipMapForSegmentIds(ctx context.Context, segmentIds []string, actorId string) (map[string]*segmentPb.SegmentMembership, error) {
	isMemberResp, err := s.segmentClient.IsMember(ctx, &segmentPb.IsMemberRequest{
		ActorId:    actorId,
		SegmentIds: segmentIds,
	})
	if err = epifigrpc.RPCError(isMemberResp, err); err != nil {
		logger.Error(ctx, "failed to get segmentation response of IsMember() for segment",
			zap.String("segmentIds", strings.Join(segmentIds, ",")), zap.Error(err))
		return nil, fmt.Errorf("failed to get segmentation response of IsMember(), err : %w", err)
	}
	return isMemberResp.GetSegmentMembershipMap(), nil
}

// Method to unmarshal string of default shortcuts list (containing numbers)
func (s *Service) GetDefaultShortcutsFromString(ctx context.Context, defaultShortcutsString string) ([]int32, error) {
	var defaultShortcuts []int32
	err := json2.Unmarshal([]byte(defaultShortcutsString), &defaultShortcuts)
	if err != nil {
		logger.Error(ctx, "failed to unmarshal val")
		return nil, err
	}
	return defaultShortcuts, nil
}

// Method to get the state to cta list map for bottom ctas
func getBottomCtasChosenShortcutsSectionStateToCtaListMap() map[string]*pb.GetHomeShortcutOptionsResponse_BottomCtas {
	return map[string]*pb.GetHomeShortcutOptionsResponse_BottomCtas{
		pb.GetHomeShortcutOptionsResponse_CHOSEN_SHORTCUTS_SECTION_STATE_ZERO.String(): {
			Ctas: []*pb.CTA{
				getCancelCtaForChosenShortcutsSectionStateZero(),
				getSaveCtaForChosenShortcutsSectionStateZero(),
			},
		},
		pb.GetHomeShortcutOptionsResponse_CHOSEN_SHORTCUTS_SECTION_STATE_UPDATED.String(): {
			Ctas: []*pb.CTA{
				getCancelCtaForChosenShortcutsSectionStateUpdated(),
				getSaveCtaForChosenShortcuts(),
			},
		},
	}
}

// Method to get the state to cta list map for back btn cta
func getBackBtnCtaChosenShortcutsSectionStateToListMap() map[string]*pb.GetHomeShortcutOptionsResponse_BottomCtas {
	return map[string]*pb.GetHomeShortcutOptionsResponse_BottomCtas{
		pb.GetHomeShortcutOptionsResponse_CHOSEN_SHORTCUTS_SECTION_STATE_ZERO.String(): {
			Ctas: []*pb.CTA{
				getBackBtnCtaForChosenShortcutsSectionStateZero(),
			},
		},
		pb.GetHomeShortcutOptionsResponse_CHOSEN_SHORTCUTS_SECTION_STATE_UPDATED.String(): {
			Ctas: []*pb.CTA{
				getBackBtnCtaForChosenShortcutsSectionStateUpdated(),
			},
		},
	}
}

// Cancel cta for chosen shortcuts section state zero, i.e. when user selects Cancel button without selecting any
// shortcut option. It opens up a bottom sheet dialog which displays 'Not now' and 'Add shortcut' options.
func getCancelCtaForChosenShortcutsSectionStateZero() *pb.CTA {
	return &pb.CTA{
		Text: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: "Cancel",
			},
			FontColor: "#00B899",
			FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_M},
		},
		BgColor: &ui.BackgroundColour{
			Colour: &ui.BackgroundColour_BlockColour{
				BlockColour: "#F7F9FC",
			},
		},
		Action: &pb.CTA_DeeplinkAction{
			DeeplinkAction: &deeplink.Deeplink{
				Screen: deeplink.Screen_HOME,
			},
		},
		Shadow: &ui.Shadow{
			Height: 4,
			Colour: &ui.BackgroundColour{
				Colour: &ui.BackgroundColour_BlockColour{
					BlockColour: "#CED2D6",
				},
			},
		},
	}
}

// Cancel cta for chosen shortcuts section state updated, i.e. when user selects Cancel button after selecting shortcut options.
// It opens up a bottom sheet dialog which displays 'Skip' and 'Save' options.
func getCancelCtaForChosenShortcutsSectionStateUpdated() *pb.CTA {
	return &pb.CTA{
		Text: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: "Cancel",
			},
			FontColor: "#00B899",
			FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_M},
		},
		BgColor: &ui.BackgroundColour{
			Colour: &ui.BackgroundColour_BlockColour{
				BlockColour: "#F7F9FC",
			},
		},
		Action: getActionForCancelOrBackBtnCtaForStateUpdated(),
		Shadow: &ui.Shadow{
			Height: 4,
			Colour: &ui.BackgroundColour{
				Colour: &ui.BackgroundColour_BlockColour{
					BlockColour: "#CED2D6",
				},
			},
		},
	}
}

// Method to get the cta for back button for zero state, i.e. when user doesn't select and shortcut.
func getBackBtnCtaForChosenShortcutsSectionStateZero() *pb.CTA {
	return &pb.CTA{
		Action: getActionForRemoveShortcutsBottomSheetDialogue(),
	}
}

// Method to get the cta for back button for updated state, i.e. when user has selected any shortcut.
func getBackBtnCtaForChosenShortcutsSectionStateUpdated() *pb.CTA {
	return &pb.CTA{
		Action: getActionForCancelOrBackBtnCtaForStateUpdated(),
	}
}

// Method to get the action for remove shortcuts bottom sheet dialog
// (save or back button for zero state, i.e. when user doesn't select any shortcut).
func getActionForRemoveShortcutsBottomSheetDialogue() *pb.CTA_CustomAction {
	return &pb.CTA_CustomAction{
		CustomAction: &pb.CustomAction{
			Action: pb.Action_OPEN_BOTTOM_SHEET_DIALOG,
			ActionData: &pb.CustomAction_OpenBottomSheetDialog{
				OpenBottomSheetDialog: &pb.OpenBottomSheetDialogActionData{
					Title:    &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: RemoveShortcutsBottomSheetDialogTitle}, FontColor: "#313234"},
					Subtitle: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: RemoveShortcutsBottomSheetDialogSubtitle}, FontColor: "#606265"},
					Ctas: []*pb.CTA{
						getKeepShortcutsCtaForChosenShortcutsSectionStateZero(),
						getRemoveShortcutsCtaForChosenShortcutsSectionStateZero(),
					},
				},
			},
		},
	}
}

// Method to get the action for cancel or back button for zero state, i.e. when user has selected any shortcut.
func getActionForCancelOrBackBtnCtaForStateUpdated() *pb.CTA_CustomAction {
	return &pb.CTA_CustomAction{
		CustomAction: &pb.CustomAction{
			Action: pb.Action_OPEN_BOTTOM_SHEET_DIALOG,
			ActionData: &pb.CustomAction_OpenBottomSheetDialog{
				OpenBottomSheetDialog: &pb.OpenBottomSheetDialogActionData{
					Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Save changes before leaving?"}, FontColor: "#313234"},
					Subtitle: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{PlainString: "Save to make sure your changes appear on the Home screen"},
						FontColor:    "#606265",
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
					},
					Ctas: []*pb.CTA{
						getSkipCtaForChosenShortcutsSectionStateUpdated(),
						getSaveCtaForChosenShortcuts(),
					},
				},
			},
		},
	}
}

// nolint:dupl
// 'No, keep them' cta for chosen shortcuts section state zero, i.e. when user selects save or back button without selecting any shortcuts.
func getKeepShortcutsCtaForChosenShortcutsSectionStateZero() *pb.CTA {
	return &pb.CTA{
		Text: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: NoKeepThemTxt,
			},
			FontColor: "#00B899",
			FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_M},
		},
		BgColor: &ui.BackgroundColour{
			Colour: &ui.BackgroundColour_BlockColour{
				BlockColour: "#F7F9FC",
			},
		},
		Action: &pb.CTA_DeeplinkAction{
			DeeplinkAction: &deeplink.Deeplink{
				Screen: deeplink.Screen_HOME,
			},
		},
		Shadow: &ui.Shadow{
			Height: 4,
			Colour: &ui.BackgroundColour{
				Colour: &ui.BackgroundColour_BlockColour{
					BlockColour: "#CED2D6",
				},
			},
		},
	}
}

// nolint: dupl
// 'Save' cta for chosen shortcuts section state states. It has the ActionApi to set home shortcuts api.
func getSaveCtaForChosenShortcuts() *pb.CTA {
	return &pb.CTA{
		Text: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: "Save",
			},
			FontColor: "#FFFFFF",
			FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_M},
		},
		BgColor: &ui.BackgroundColour{
			Colour: &ui.BackgroundColour_BlockColour{
				BlockColour: "#00B899",
			},
		},
		Action: &pb.CTA_CustomAction{
			CustomAction: &pb.CustomAction{
				ActionApi: pb.CustomActionApi_SET_HOME_SHORTCUTS_API,
			},
		},
		Shadow: &ui.Shadow{
			Height: 4,
			Colour: &ui.BackgroundColour{
				Colour: &ui.BackgroundColour_BlockColour{
					BlockColour: "#00866F",
				},
			},
		},
	}
}

// 'Save' cta for chosen shortcuts section zero state. It opens up 'not now', 'add shortcuts' bottom sheet.
func getSaveCtaForChosenShortcutsSectionStateZero() *pb.CTA {
	return &pb.CTA{
		Text: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: "Save",
			},
			FontColor: "#FFFFFF",
			FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_M},
		},
		BgColor: &ui.BackgroundColour{
			Colour: &ui.BackgroundColour_BlockColour{
				BlockColour: "#00B899",
			},
		},
		Action: getActionForRemoveShortcutsBottomSheetDialogue(),
		Shadow: &ui.Shadow{
			Height: 4,
			Colour: &ui.BackgroundColour{
				Colour: &ui.BackgroundColour_BlockColour{
					BlockColour: "#00866F",
				},
			},
		},
	}
}

// nolint: dupl
// 'Yes, remove' cta for chosen shortcuts section state zero, i.e. when user selects save or back btn without selecting any shortcuts.
func getRemoveShortcutsCtaForChosenShortcutsSectionStateZero() *pb.CTA {
	return &pb.CTA{
		Text: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: YesRemoveTxt,
			},
			FontColor: "#FFFFFF",
			FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_M},
		},
		BgColor: &ui.BackgroundColour{
			Colour: &ui.BackgroundColour_BlockColour{
				BlockColour: "#00B899",
			},
		},
		Action: &pb.CTA_CustomAction{
			CustomAction: &pb.CustomAction{
				ActionApi: pb.CustomActionApi_SET_HOME_SHORTCUTS_API,
			},
		},
		Shadow: &ui.Shadow{
			Height: 4,
			Colour: &ui.BackgroundColour{
				Colour: &ui.BackgroundColour_BlockColour{
					BlockColour: "#00866F",
				},
			},
		},
	}
}

// 'Skip' cta for chosen shortcuts section state updated, i.e. when user selects Cancel button after selecting
// shortcut options and then on the bottom sheet selects 'Add Shortcuts'. It has the deeplink to home explore page.
func getSkipCtaForChosenShortcutsSectionStateUpdated() *pb.CTA {
	return &pb.CTA{
		Text: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: "Skip",
			},
			FontColor: "#00B899",
			FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_M},
		},
		BgColor: &ui.BackgroundColour{
			Colour: &ui.BackgroundColour_BlockColour{
				BlockColour: "#F7F9FC",
			},
		},
		Action: &pb.CTA_DeeplinkAction{
			DeeplinkAction: &deeplink.Deeplink{
				Screen: deeplink.Screen_HOME,
			},
		},
		Shadow: &ui.Shadow{
			Height: 4,
			Colour: &ui.BackgroundColour{
				Colour: &ui.BackgroundColour_BlockColour{
					BlockColour: "#CED2D6",
				},
			},
		},
	}
}

// Method to add zero state icon to home shortcuts, called in case the GET result does not contain any icons
func getZeroStateIconForHomeShortcuts() *ui.IconTextComponent {
	return &ui.IconTextComponent{
		LeftIcon: &commontypes.Image{
			ImageUrl:  homeUtils.ShortcutsWidgetZeroStateIcon,
			ImageType: commontypes.ImageType_PNG,
			Width:     80,
			Height:    32,
		},
		Texts: []*commontypes.Text{
			{
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Add Shortcuts"},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
				FontColor:    "#6A6D70",
			},
		},
		RightIcon: &commontypes.Image{
			ImageUrl:  homeUtils.ShortcutsWidgetPlusIcon,
			ImageType: commontypes.ImageType_PNG,
			Width:     32,
			Height:    32,
		},
		LeftImgTxtPadding:  4,
		RightImgTxtPadding: 4,
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_HOME_SHORTCUT_OPTIONS_SCREEN,
		},
	}
}

// Method to add plus or edit icon at the end of home shortcut icons
func getExtraIconInHomeShortcutIcons(imageUrl string, displayText string) *pb.Icon {
	return &pb.Icon{
		IconImage: &commontypes.Image{
			ImageUrl:  imageUrl,
			ImageType: commontypes.ImageType_PNG,
			Width:     36,
			Height:    36,
		},
		VisualElement: &commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source:     &commontypes.VisualElement_Image_Url{Url: imageUrl},
					ImageType:  commontypes.ImageType_PNG,
					Properties: &commontypes.VisualElementProperties{Height: 36, Width: 36},
				},
			},
		},
		Action: &pb.Icon_Deeplink{
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_HOME_SHORTCUT_OPTIONS_SCREEN,
			},
		},
		BgColour: &ui.BackgroundColour{
			Colour: &ui.BackgroundColour_BlockColour{
				BlockColour: "#FFFFFF",
			},
		},
		Title: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: displayText},
			FontColor:    colors.ColorOnDarkLowEmphasis,
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
		},
	}
}

func getExploreFiIcon() *pb.Icon {
	return &pb.Icon{
		IconImage: &commontypes.Image{
			ImageUrl:  "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/ExploreFi.png",
			ImageType: commontypes.ImageType_PNG,
			Width:     36,
			Height:    36,
		},
		VisualElement: &commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source:     &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/ExploreFi.png"},
					ImageType:  commontypes.ImageType_PNG,
					Properties: &commontypes.VisualElementProperties{Height: 36, Width: 36},
				},
			},
		},
		Title: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: "Explore\nFi"},
			FontColor:    colors.ColorOnDarkLowEmphasis,
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
		},
		Id: homeTypesPb.IconType_SHORTCUT_EXPLORE_FI.String(),
		Action: &pb.Icon_Deeplink{
			Deeplink: &deeplink.Deeplink{Screen: deeplink.Screen_HOME_EXPLORE},
		},
		BgColour: &ui.BackgroundColour{
			Colour: &ui.BackgroundColour_BlockColour{
				BlockColour: "#00B899",
			},
		},
		Shadow: &ui.Shadow{
			Height: 4,
			Colour: &ui.BackgroundColour{
				Colour: &ui.BackgroundColour_BlockColour{
					BlockColour: "#CED2D6",
				},
			},
		},
	}
}

// Method which generates the icons, from the icon types present in the preference values
func (s *Service) getHomeIconsFromPreferenceValues(ctx context.Context, actorId string,
	appPlatform commontypes.Platform, appVersionCode uint32, preferenceValues []homeTypesPb.IconType, isFeatureHomeDesignEnhancementsEnabled bool) ([]*pb.Icon, error) {
	homeIcons := make([]*pb.Icon, 0)
	for _, iconType := range preferenceValues {
		iconDetails, found := s.conf.HomeRevampParams.ShortcutIconTypeToDetailsMap[iconType.String()]
		if !found {
			logger.Error(ctx, "icon details not found in config", zap.String("IconType", iconType.String()))
			continue
		}
		var (
			imageUrl  = iconDetails.ImageUrl
			bgColorV2 = widget.GetLinearGradientBackgroundColour(0, []*widget.ColorStop{
				{
					Color:          "#E3F7F2",
					StopPercentage: 0,
				},
				{
					Color:          "#F4FCFA",
					StopPercentage: 100,
				},
			})
			borderColor *widget.BackgroundColour
		)
		if isFeatureHomeDesignEnhancementsEnabled {
			imageUrl = iconDetails.ImageUrlV2
			var colorStops []*widget.ColorStop
			for _, colorStop := range iconDetails.BgColor.BackgroundColour.LinearGradient.LinearColorStops {
				colorStops = append(colorStops, &widget.ColorStop{
					Color:          colorStop.Color,
					StopPercentage: colorStop.StopPercentage,
				})
			}
			if len(colorStops) > 0 {
				bgColorV2 = widget.GetLinearGradientBackgroundColour(0, colorStops)
			}
		}
		// doing this to avoid panic in android
		if iconDetails.BorderColor != "" {
			borderColor = widget.GetBlockBackgroundColour(iconDetails.BorderColor)
		}

		// assigning fi-point icon for offer catalogs if called after August 1, 2025 (prod)
		imageUrl = accrual.ReturnApplicableValue(imageUrl, "https://epifi-icons.pointz.in/home/<USER>", nil, iconType == homeTypesPb.IconType_SHORTCUT_OFFERS_CATALOG).(string)

		// checking and ignoring the icon if the deeplink we get from getHomeExploreIconDeeplink is nil or an empty deeplink
		nextDeeplink := s.getHomeExploreIconDeeplink(ctx, actorId, iconType.String(), appPlatform, appVersionCode)
		if nextDeeplink == nil || nextDeeplink.GetScreen() == deeplink.Screen_DEEP_LINK_URI_UNSPECIFIED {
			continue
		}

		eachHomeIcon := &pb.Icon{
			IconImage: &commontypes.Image{
				ImageUrl:  imageUrl,
				ImageType: commontypes.ImageType_PNG,
				Width:     36,
				Height:    36,
			},
			VisualElement: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source:     &commontypes.VisualElement_Image_Url{Url: imageUrl},
						ImageType:  commontypes.ImageType_PNG,
						Properties: &commontypes.VisualElementProperties{Height: 36, Width: 36},
					},
				},
			},
			Title: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: accrual.ReplaceCoinWithPointIfApplicable(iconDetails.Title, nil)},
				FontColor:    colors.ColorOnDarkLowEmphasis,
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			},
			Id: iconType.String(),
			Action: &pb.Icon_Deeplink{
				Deeplink: nextDeeplink,
			},
			BgColour: &ui.BackgroundColour{
				Colour: &ui.BackgroundColour_BlockColour{
					BlockColour: "#FFFFFF",
				},
			},
			Shadow: &ui.Shadow{
				Height: 4,
				Colour: &ui.BackgroundColour{
					Colour: &ui.BackgroundColour_BlockColour{
						BlockColour: "#CED2D6",
					},
				},
			},
			FeatureTag:  getFeatureTagForShortcut(iconDetails),
			BorderColor: borderColor,
			BgColourV2:  bgColorV2,
		}
		homeIcons = append(homeIcons, eachHomeIcon)
	}
	return homeIcons, nil
}

func (s *Service) getHomeExploreIconDeeplink(ctx context.Context, actorId string, iconType string,
	appPlatform commontypes.Platform, appVersionCode uint32) *deeplink.Deeplink {
	defaultDl := &deeplink.Deeplink{}
	iconDetails := s.conf.HomeRevampParams.ShortcutIconTypeToDetailsMap[iconType]
	err := protojson.Unmarshal([]byte(iconDetails.Deeplink), defaultDl)
	if err != nil {
		logger.Error(ctx, "failed to unmarshal dl", zap.String("Deeplink", iconDetails.Deeplink), zap.Error(err))
		return nil
	}

	dl := s.getAppropriateDeeplink(ctx, actorId, homeTypesPb.IconType(homeTypesPb.IconType_value[iconType]), defaultDl, appPlatform, appVersionCode)
	if dl == nil {
		return nil
	}
	return dl
}

func (s *Service) SetHomeShortcuts(ctx context.Context, req *pb.SetHomeShortcutsRequest) (*pb.SetHomeShortcutsResponse, error) {
	shortcutPreferenceEnumTypes := make([]homeTypesPb.IconType, len(req.ShortcutIconIds))
	for idx, shortcutIconTypeString := range req.GetShortcutIconIds() {
		shortcutIconTypeEnum, found := homeTypesPb.IconType_value[shortcutIconTypeString]
		if !found {
			logger.Error(ctx, "invalid shortcut icon type string", zap.String("IconType", shortcutIconTypeString))
			return &pb.SetHomeShortcutsResponse{
				RespHeader: &header.ResponseHeader{Status: rpc.StatusInvalidArgument()},
			}, nil
		}
		shortcutPreferenceEnumTypes[idx] = homeTypesPb.IconType(shortcutIconTypeEnum)
	}

	preferenceTypeValuePair := &userPb.PreferenceTypeValuePair{
		PreferenceType: userPb.PreferenceType_PREFERENCE_TYPE_HOME_SHORTCUTS,
		PreferenceValue: &userPb.PreferenceValue{
			PrefVal: &userPb.PreferenceValue_HomeShortcutsPreference{
				HomeShortcutsPreference: &userPb.ShortcutsPreference{
					IconTypes: shortcutPreferenceEnumTypes,
				},
			},
		},
	}
	setUserPreferenceRequest := &userPb.SetUserPreferencesRequest{
		ActorId:     req.GetReq().GetAuth().GetActorId(),
		Preferences: []*userPb.PreferenceTypeValuePair{preferenceTypeValuePair},
	}
	_, err := s.usersClient.SetUserPreferences(ctx, setUserPreferenceRequest)
	if err != nil {
		logger.Error(ctx, "Error setting the user preference for selected shortcut icons",
			zap.String("Error", err.Error()))
		return &pb.SetHomeShortcutsResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(err.Error())},
		}, nil
	}

	return &pb.SetHomeShortcutsResponse{
		RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
	}, nil
}

func (s *Service) shouldShowExploreFiShortcut(ctx context.Context, userType homePkg.UserType) bool {
	// check if explore as shortcut is enabled for all user types
	if s.genconf.HomeRevampParams().HomeShortcutParams().ShouldShowExploreFiShortcut(ctx) {
		return true
	}

	// check if explore as shortcut is enabled for specific user type
	userTypeSpecificShortcutParams, ok := s.genconf.HomeRevampParams().HomeShortcutParams().UserTypeToShortcutParamsMap().Load(userType.String())
	if ok && userTypeSpecificShortcutParams.ShowExploreFiShortcut(ctx) {
		return true
	}

	return false
}

func (s *Service) shouldShowAddOrEditShortcut(userType homePkg.UserType) bool {
	// check if add or edit shortcut is disabled for specific user type
	userTypeSpecificShortcutParams, ok := s.genconf.HomeRevampParams().HomeShortcutParams().UserTypeToShortcutParamsMap().Load(userType.String())
	if ok && !userTypeSpecificShortcutParams.HideAddOrEditShortcut() {
		return true
	}
	return false
}

// function to check if the new AI shortcuts are enabled
func (s *Service) areAiShortcutsEnabled(ctx context.Context, actorId string) (bool, bool) {
	isMagicImportEnabled, err := s.IsFeatureEnabled(ctx, types.Feature_FEATURE_WB_MAGIC_IMPORT, actorId)
	if err != nil {
		logger.Error(ctx, "error while checking if portfolio tracker feature is enabled", zap.Error(err))
		isMagicImportEnabled = false
	}
	isMcpEnabled, err := s.IsFeatureEnabled(ctx, types.Feature_FEATURE_FI_MCP_TOTP_CODE, actorId)
	if err != nil {
		logger.Error(ctx, "error while checking if mcp totp feature is enabled", zap.Error(err))
		isMcpEnabled = false
	}
	isNetworthAiEnabled, err := s.IsFeatureEnabled(ctx, types.Feature_FEATURE_NET_WORTH_AI_ICONS, actorId)
	if err != nil {
		logger.Error(ctx, "error while checking if new networth ai icons are enabled", zap.Error(err))
		isNetworthAiEnabled = false
	}
	if isNetworthAiEnabled {
		return isMcpEnabled, isMagicImportEnabled
	}
	return false, false
}

func getFeatureTagForShortcut(iconDetails *config.ShortcutIconDetails) *ui.IconTextComponent {
	if iconDetails.FeatureTag == "" {
		return nil
	}
	return &ui.IconTextComponent{
		Texts: []*commontypes.Text{
			commontypes.GetTextFromStringFontColourFontStyle(iconDetails.FeatureTag, "#313234", commontypes.FontStyle_OVERLINE_2XS_CAPS),
		},
		ContainerProperties: &ui.IconTextComponent_ContainerProperties{
			BgColor:          "#FACE5F",
			CornerRadius:     8,
			Height:           14,
			Width:            14,
			LeftPadding:      4,
			RightPadding:     4,
			TopPadding:       2,
			BottomPadding:    2,
			BackgroundColour: widget.GetBlockBackgroundColour("#FACE5F"),
		},
	}
}
