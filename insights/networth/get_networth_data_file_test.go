package networth

import (
	"context"
	"fmt"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"

	connectedAccountPb "github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/connected_account/enums"
	connectedAccountExternalPb "github.com/epifi/gamma/api/connected_account/external"
	mockCa "github.com/epifi/gamma/api/connected_account/mocks"
	creditReportPb "github.com/epifi/gamma/api/creditreportv2"
	mockCreditReport "github.com/epifi/gamma/api/creditreportv2/mocks"
	beEpfPb "github.com/epifi/gamma/api/insights/epf"
	mockEpf "github.com/epifi/gamma/api/insights/epf/mocks"
	epfModelPb "github.com/epifi/gamma/api/insights/epf/model"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	networthEnums "github.com/epifi/gamma/api/insights/networth/enums"
	mockMfExternal "github.com/epifi/gamma/api/investment/mutualfund/external/mocks"
	networthTools "github.com/epifi/gamma/api/mcp/networth"
)

// TestService_GetNetworthDataFile_ErrorCases tests error cases for GetNetworthDataFile
func TestService_GetNetworthDataFile_ErrorCases(t *testing.T) {
	logger.Init(cfg.TestEnv)

	type args struct {
		ctx context.Context
		req *networthPb.GetNetworthDataFileRequest
	}

	tests := []struct {
		name    string
		args    args
		want    *networthPb.GetNetworthDataFileResponse
		wantErr bool
	}{
		{
			name: "error - no file types specified",
			args: args{
				ctx: context.Background(),
				req: &networthPb.GetNetworthDataFileRequest{
					ActorId: "test-actor-123",
					// No file types specified
				},
			},
			want: &networthPb.GetNetworthDataFileResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("no networth data file types specified"),
			},
			wantErr: false,
		},
		{
			name: "success - backward compatibility with deprecated field",
			args: args{
				ctx: context.Background(),
				req: &networthPb.GetNetworthDataFileRequest{
					ActorId:              "test-actor-123",
					NetworthDataFileType: networthEnums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_CREDIT_REPORT,
				},
			},
			// This will return an error because we don't have all the dependencies set up,
			// but it should not fail on the validation step
			want: &networthPb.GetNetworthDataFileResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "success - new field with multiple types",
			args: args{
				ctx: context.Background(),
				req: &networthPb.GetNetworthDataFileRequest{
					ActorId: "test-actor-123",
					NetWorthDataFileTypes: []networthEnums.NetworthDataFileType{
						networthEnums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_CREDIT_REPORT,
						networthEnums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_EPF_DETAILS,
					},
				},
			},
			// This will return an error because we don't have all the dependencies set up,
			// but it should not fail on the validation step
			want: &networthPb.GetNetworthDataFileResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a basic service with minimal dependencies for testing
			service := &Service{
				config: genConf,
			}

			got, err := service.GetNetworthDataFile(tt.args.ctx, tt.args.req)

			if (err != nil) != tt.wantErr {
				t.Errorf("Service.GetNetworthDataFile() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// Check the status code
			if got.Status.Code != tt.want.Status.Code {
				t.Errorf("Service.GetNetworthDataFile() status code = %v, want %v", got.Status.Code, tt.want.Status.Code)
			}

			// For invalid argument cases, check the exact message
			if tt.want.Status.IsInvalidArgument() {
				if diff := cmp.Diff(tt.want.Status, got.Status, protocmp.Transform()); diff != "" {
					t.Errorf("Service.GetNetworthDataFile() status mismatch (-want +got):\n%s", diff)
				}
			}
		})
	}
}

// TestService_getAllNetworthData_DataOptimization tests that getAllNetworthData only fetches required data
func TestService_getAllNetworthData_DataOptimization(t *testing.T) {
	logger.Init(cfg.TestEnv)

	tests := []struct {
		name       string
		fileTypes  []networthEnums.NetworthDataFileType
		setupMocks func(
			mockConnectedAccClient *mockCa.MockConnectedAccountClient,
			mockCreditReportClient *mockCreditReport.MockCreditReportManagerClient,
			mockEpfClient *mockEpf.MockEpfClient,
			mockMfExternalClient *mockMfExternal.MockMFExternalOrdersClient,
		)
		wantErr bool
	}{
		{
			name: "credit report only - should not call other services",
			fileTypes: []networthEnums.NetworthDataFileType{
				networthEnums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_CREDIT_REPORT,
			},
			setupMocks: func(
				mockConnectedAccClient *mockCa.MockConnectedAccountClient,
				mockCreditReportClient *mockCreditReport.MockCreditReportManagerClient,
				mockEpfClient *mockEpf.MockEpfClient,
				mockMfExternalClient *mockMfExternal.MockMFExternalOrdersClient,
			) {
				// Should only call credit report client
				mockCreditReportClient.EXPECT().GetCreditReports(gomock.Any(), gomock.Any()).Return(
					&creditReportPb.GetCreditReportsResponse{
						Status: rpc.StatusOk(),
						CreditReports: []*creditReportPb.CreditReportDownloadDetails{
							{},
						},
					}, nil)

				// Should NOT call other clients - this is the key test
				mockConnectedAccClient.EXPECT().GetAllAccounts(gomock.Any(), gomock.Any()).Times(0)
				mockConnectedAccClient.EXPECT().GetAccountDetailsBulk(gomock.Any(), gomock.Any()).Times(0)
				mockConnectedAccClient.EXPECT().GetRawTxnsForAccountV2(gomock.Any(), gomock.Any()).Times(0)
				mockEpfClient.EXPECT().GetUANAccounts(gomock.Any(), gomock.Any()).Times(0)
				mockMfExternalClient.EXPECT().FetchAllExternalMFOrders(gomock.Any(), gomock.Any()).Times(0)
			},
			wantErr: false,
		},
		{
			name: "EPF details only - should not call other services",
			fileTypes: []networthEnums.NetworthDataFileType{
				networthEnums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_EPF_DETAILS,
			},
			setupMocks: func(
				mockConnectedAccClient *mockCa.MockConnectedAccountClient,
				mockCreditReportClient *mockCreditReport.MockCreditReportManagerClient,
				mockEpfClient *mockEpf.MockEpfClient,
				mockMfExternalClient *mockMfExternal.MockMFExternalOrdersClient,
			) {
				// Should only call EPF client
				mockEpfClient.EXPECT().GetUANAccounts(gomock.Any(), gomock.Any()).Return(
					&beEpfPb.GetUANAccountsResponse{
						Status: rpc.StatusOk(),
						UanAccounts: []*epfModelPb.UANAccount{
							{UanNumber: "*********"},
						},
					}, nil)

				// Should NOT call other clients
				mockConnectedAccClient.EXPECT().GetAllAccounts(gomock.Any(), gomock.Any()).Times(0)
				mockConnectedAccClient.EXPECT().GetAccountDetailsBulk(gomock.Any(), gomock.Any()).Times(0)
				mockConnectedAccClient.EXPECT().GetRawTxnsForAccountV2(gomock.Any(), gomock.Any()).Times(0)
				mockCreditReportClient.EXPECT().GetCreditReports(gomock.Any(), gomock.Any()).Times(0)
				mockMfExternalClient.EXPECT().FetchAllExternalMFOrders(gomock.Any(), gomock.Any()).Times(0)
			},
			wantErr: false,
		},
		{
			name: "bank transactions only - should call connected account service only",
			fileTypes: []networthEnums.NetworthDataFileType{
				networthEnums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_BANK_DETAILS_DATA,
			},
			setupMocks: func(
				mockConnectedAccClient *mockCa.MockConnectedAccountClient,
				mockCreditReportClient *mockCreditReport.MockCreditReportManagerClient,
				mockEpfClient *mockEpf.MockEpfClient,
				mockMfExternalClient *mockMfExternal.MockMFExternalOrdersClient,
			) {
				// Should call connected account service for bank transactions
				mockConnectedAccClient.EXPECT().GetAllAccounts(gomock.Any(), gomock.Any()).Return(
					&connectedAccountPb.GetAllAccountsResponse{
						Status: rpc.StatusOk(),
						AccountDetailsList: []*connectedAccountExternalPb.AccountDetails{
							{
								AccountId:         "acc-1",
								AccInstrumentType: enums.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT,
								FipMeta:           &connectedAccountExternalPb.FipMeta{Name: "Test Bank"},
							},
						},
					}, nil)

				mockConnectedAccClient.EXPECT().GetRawTxnsForAccountV2(gomock.Any(), gomock.Any()).Return(
					&connectedAccountPb.GetRawTxnsForAccountV2Response{
						Status: rpc.StatusOk(),
						RawTxnList: []*connectedAccountPb.RawAaTransaction{
							{
								AaTransaction: &connectedAccountPb.AaTransaction{
									Amount: "1000",
								},
							},
						},
						PageContext: &rpc.PageContextResponse{
							HasAfter: false,
						},
					}, nil)

				// Should NOT call other clients
				mockConnectedAccClient.EXPECT().GetAccountDetailsBulk(gomock.Any(), gomock.Any()).Times(0)
				mockCreditReportClient.EXPECT().GetCreditReports(gomock.Any(), gomock.Any()).Times(0)
				mockEpfClient.EXPECT().GetUANAccounts(gomock.Any(), gomock.Any()).Times(0)
				mockMfExternalClient.EXPECT().FetchAllExternalMFOrders(gomock.Any(), gomock.Any()).Times(0)
			},
			wantErr: false,
		},
		{
			name: "error - one service fails",
			fileTypes: []networthEnums.NetworthDataFileType{
				networthEnums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_CREDIT_REPORT,
				networthEnums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_EPF_DETAILS,
			},
			setupMocks: func(
				mockConnectedAccClient *mockCa.MockConnectedAccountClient,
				mockCreditReportClient *mockCreditReport.MockCreditReportManagerClient,
				mockEpfClient *mockEpf.MockEpfClient,
				mockMfExternalClient *mockMfExternal.MockMFExternalOrdersClient,
			) {
				mockCreditReportClient.EXPECT().GetCreditReports(gomock.Any(), gomock.Any()).Return(
					&creditReportPb.GetCreditReportsResponse{
						Status: rpc.StatusOk(),
					}, nil)

				mockEpfClient.EXPECT().GetUANAccounts(gomock.Any(), gomock.Any()).Return(
					nil, fmt.Errorf("EPF service error"))
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			// Create mock clients
			mockConnectedAccClient := mockCa.NewMockConnectedAccountClient(ctrl)
			mockCreditReportClient := mockCreditReport.NewMockCreditReportManagerClient(ctrl)
			mockEpfClient := mockEpf.NewMockEpfClient(ctrl)
			mockMfExternalClient := mockMfExternal.NewMockMFExternalOrdersClient(ctrl)

			// Create service with mock clients
			service := &Service{
				beConnectedAccClient: mockConnectedAccClient,
				creditReportClient:   mockCreditReportClient,
				epfClient:            mockEpfClient,
				mfExternalClient:     mockMfExternalClient,
				config:               genConf,
			}

			if tt.setupMocks != nil {
				tt.setupMocks(mockConnectedAccClient, mockCreditReportClient, mockEpfClient, mockMfExternalClient)
			}

			_, err := service.getAllNetworthData(context.Background(), "test-actor-123", tt.fileTypes)

			if (err != nil) != tt.wantErr {
				t.Errorf("Service.getAllNetworthData() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// TestService_needsDataHelpers tests the helper functions that determine data requirements
func TestService_needsDataHelpers(t *testing.T) {
	service := &Service{}

	tests := []struct {
		name      string
		fileTypes []networthEnums.NetworthDataFileType
		helpers   map[string]func([]networthEnums.NetworthDataFileType) bool
		expected  map[string]bool
	}{
		{
			name: "NETWORTH_DATA file type needs all data",
			fileTypes: []networthEnums.NetworthDataFileType{
				networthEnums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_NETWORTH_DATA,
			},
			helpers: map[string]func([]networthEnums.NetworthDataFileType) bool{
				"needsNetWorthResp":     service.needsNetWorthResp,
				"needsMfAnalytics":      service.needsMfAnalytics,
				"needsAaDetails":        service.needsAaDetails,
				"needsCreditReports":    service.needsCreditReports,
				"needsUanAccounts":      service.needsUanAccounts,
				"needsMfOrders":         service.needsMfOrders,
				"needsBankTransactions": service.needsBankTransactions,
				"needsManualAssets":     service.needsManualAssets,
			},
			expected: map[string]bool{
				"needsNetWorthResp":     true,
				"needsMfAnalytics":      true,
				"needsAaDetails":        true,
				"needsCreditReports":    true,
				"needsUanAccounts":      true,
				"needsMfOrders":         true,
				"needsBankTransactions": false,
				"needsManualAssets":     true,
			},
		},
		{
			name: "CREDIT_REPORT file type needs only credit reports",
			fileTypes: []networthEnums.NetworthDataFileType{
				networthEnums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_CREDIT_REPORT,
			},
			helpers: map[string]func([]networthEnums.NetworthDataFileType) bool{
				"needsNetWorthResp":     service.needsNetWorthResp,
				"needsMfAnalytics":      service.needsMfAnalytics,
				"needsAaDetails":        service.needsAaDetails,
				"needsCreditReports":    service.needsCreditReports,
				"needsUanAccounts":      service.needsUanAccounts,
				"needsMfOrders":         service.needsMfOrders,
				"needsBankTransactions": service.needsBankTransactions,
				"needsManualAssets":     service.needsManualAssets,
			},
			expected: map[string]bool{
				"needsNetWorthResp":     false,
				"needsMfAnalytics":      false,
				"needsAaDetails":        false,
				"needsCreditReports":    true,
				"needsUanAccounts":      false,
				"needsMfOrders":         false,
				"needsBankTransactions": false,
				"needsManualAssets":     false,
			},
		},
		{
			name: "BANK_DETAILS_DATA file type needs only bank transactions",
			fileTypes: []networthEnums.NetworthDataFileType{
				networthEnums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_BANK_DETAILS_DATA,
			},
			helpers: map[string]func([]networthEnums.NetworthDataFileType) bool{
				"needsNetWorthResp":     service.needsNetWorthResp,
				"needsMfAnalytics":      service.needsMfAnalytics,
				"needsAaDetails":        service.needsAaDetails,
				"needsCreditReports":    service.needsCreditReports,
				"needsUanAccounts":      service.needsUanAccounts,
				"needsMfOrders":         service.needsMfOrders,
				"needsBankTransactions": service.needsBankTransactions,
				"needsManualAssets":     service.needsManualAssets,
			},
			expected: map[string]bool{
				"needsNetWorthResp":     false,
				"needsMfAnalytics":      false,
				"needsAaDetails":        false,
				"needsCreditReports":    false,
				"needsUanAccounts":      false,
				"needsMfOrders":         false,
				"needsBankTransactions": true,
				"needsManualAssets":     false,
			},
		},
		{
			name: "EPF_DETAILS file type needs only UAN accounts",
			fileTypes: []networthEnums.NetworthDataFileType{
				networthEnums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_EPF_DETAILS,
			},
			helpers: map[string]func([]networthEnums.NetworthDataFileType) bool{
				"needsNetWorthResp":     service.needsNetWorthResp,
				"needsMfAnalytics":      service.needsMfAnalytics,
				"needsAaDetails":        service.needsAaDetails,
				"needsCreditReports":    service.needsCreditReports,
				"needsUanAccounts":      service.needsUanAccounts,
				"needsMfOrders":         service.needsMfOrders,
				"needsBankTransactions": service.needsBankTransactions,
				"needsManualAssets":     service.needsManualAssets,
			},
			expected: map[string]bool{
				"needsNetWorthResp":     false,
				"needsMfAnalytics":      false,
				"needsAaDetails":        false,
				"needsCreditReports":    false,
				"needsUanAccounts":      true,
				"needsMfOrders":         false,
				"needsBankTransactions": false,
				"needsManualAssets":     false,
			},
		},
		{
			name: "MF_TRANSACTIONS file type needs only MF orders",
			fileTypes: []networthEnums.NetworthDataFileType{
				networthEnums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_MF_TRANSACTIONS,
			},
			helpers: map[string]func([]networthEnums.NetworthDataFileType) bool{
				"needsNetWorthResp":     service.needsNetWorthResp,
				"needsMfAnalytics":      service.needsMfAnalytics,
				"needsAaDetails":        service.needsAaDetails,
				"needsCreditReports":    service.needsCreditReports,
				"needsUanAccounts":      service.needsUanAccounts,
				"needsMfOrders":         service.needsMfOrders,
				"needsBankTransactions": service.needsBankTransactions,
				"needsManualAssets":     service.needsManualAssets,
			},
			expected: map[string]bool{
				"needsNetWorthResp":     false,
				"needsMfAnalytics":      false,
				"needsAaDetails":        false,
				"needsCreditReports":    false,
				"needsUanAccounts":      false,
				"needsMfOrders":         true,
				"needsBankTransactions": false,
				"needsManualAssets":     false,
			},
		},
		{
			name: "NET_WORTH_VALUES file type needs specific data",
			fileTypes: []networthEnums.NetworthDataFileType{
				networthEnums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_NET_WORTH_VALUES,
			},
			helpers: map[string]func([]networthEnums.NetworthDataFileType) bool{
				"needsNetWorthResp":     service.needsNetWorthResp,
				"needsMfAnalytics":      service.needsMfAnalytics,
				"needsAaDetails":        service.needsAaDetails,
				"needsCreditReports":    service.needsCreditReports,
				"needsUanAccounts":      service.needsUanAccounts,
				"needsMfOrders":         service.needsMfOrders,
				"needsBankTransactions": service.needsBankTransactions,
				"needsManualAssets":     service.needsManualAssets,
			},
			expected: map[string]bool{
				"needsNetWorthResp":     true,
				"needsMfAnalytics":      true,
				"needsAaDetails":        true,
				"needsCreditReports":    false,
				"needsUanAccounts":      false,
				"needsMfOrders":         false,
				"needsBankTransactions": false,
				"needsManualAssets":     true,
			},
		},
		{
			name: "Multiple file types - should combine requirements",
			fileTypes: []networthEnums.NetworthDataFileType{
				networthEnums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_CREDIT_REPORT,
				networthEnums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_EPF_DETAILS,
			},
			helpers: map[string]func([]networthEnums.NetworthDataFileType) bool{
				"needsNetWorthResp":     service.needsNetWorthResp,
				"needsMfAnalytics":      service.needsMfAnalytics,
				"needsAaDetails":        service.needsAaDetails,
				"needsCreditReports":    service.needsCreditReports,
				"needsUanAccounts":      service.needsUanAccounts,
				"needsMfOrders":         service.needsMfOrders,
				"needsBankTransactions": service.needsBankTransactions,
				"needsManualAssets":     service.needsManualAssets,
			},
			expected: map[string]bool{
				"needsNetWorthResp":     false,
				"needsMfAnalytics":      false,
				"needsAaDetails":        false,
				"needsCreditReports":    true, // needed for CREDIT_REPORT
				"needsUanAccounts":      true, // needed for EPF_DETAILS
				"needsMfOrders":         false,
				"needsBankTransactions": false,
				"needsManualAssets":     false,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			for helperName, helperFunc := range tt.helpers {
				got := helperFunc(tt.fileTypes)
				expected := tt.expected[helperName]
				if got != expected {
					t.Errorf("%s() = %v, want %v for file types %v", helperName, got, expected, tt.fileTypes)
				}
			}
		})
	}
}

// TestService_composeNetworthDataCollection tests the composition logic
func TestService_composeNetworthDataCollection(t *testing.T) {
	service := &Service{
		config: genConf,
	}

	// Create sample data components
	sampleData := &networthDataComponents{
		netWorthResp: &networthPb.GetNetWorthValueResponse{
			Status: rpc.StatusOk(),
			AssetValues: []*networthPb.AssetValue{
				{Value: &money.Money{CurrencyCode: "INR", Units: 100000}},
			},
		},
		creditReportsResp: &creditReportPb.GetCreditReportsResponse{
			Status: rpc.StatusOk(),
			CreditReports: []*creditReportPb.CreditReportDownloadDetails{
				{},
			},
		},
		uanAccountsResp: &beEpfPb.GetUANAccountsResponse{
			Status: rpc.StatusOk(),
			UanAccounts: []*epfModelPb.UANAccount{
				{UanNumber: "*********"},
			},
		},
		rawBankTransactions: []*networthTools.BankTransactions{
			{
				BankName: "Test Bank",
				RawBankTransactions: []*connectedAccountPb.RawAaTransaction{
					{
						AaTransaction: &connectedAccountPb.AaTransaction{Amount: "1000"},
					},
				},
			},
		},
	}

	tests := []struct {
		name      string
		fileTypes []networthEnums.NetworthDataFileType
		wantItems int // Expected number of data items in the collection
	}{
		{
			name: "credit report only",
			fileTypes: []networthEnums.NetworthDataFileType{
				networthEnums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_CREDIT_REPORT,
			},
			wantItems: 1, // Only credit report data
		},
		{
			name: "EPF details only",
			fileTypes: []networthEnums.NetworthDataFileType{
				networthEnums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_EPF_DETAILS,
			},
			wantItems: 1, // Only EPF data
		},
		{
			name: "bank transactions only",
			fileTypes: []networthEnums.NetworthDataFileType{
				networthEnums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_BANK_DETAILS_DATA,
			},
			wantItems: 1, // Only bank transaction data
		},
		{
			name: "multiple file types",
			fileTypes: []networthEnums.NetworthDataFileType{
				networthEnums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_CREDIT_REPORT,
				networthEnums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_EPF_DETAILS,
			},
			wantItems: 2, // Credit report + EPF data
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.composeNetworthDataCollection(tt.fileTypes, sampleData)

			if result == nil {
				t.Errorf("composeNetworthDataCollection() returned nil")
				return
			}

			if len(result.DataItems) != tt.wantItems {
				t.Errorf("composeNetworthDataCollection() returned %d data items, want %d",
					len(result.DataItems), tt.wantItems)
			}

			// Verify that the description is set
			if result.NetworthDataDescription == "" {
				t.Errorf("composeNetworthDataCollection() NetworthDataDescription is empty")
			}
		})
	}
}
