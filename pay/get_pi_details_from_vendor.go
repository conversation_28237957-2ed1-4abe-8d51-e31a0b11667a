package pay

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	pb "github.com/epifi/gamma/api/pay"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	typesPb "github.com/epifi/gamma/api/typesv2"
	upiPb "github.com/epifi/gamma/api/upi"
	vgPaymentPb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment"

	"github.com/samber/lo"
	"go.uber.org/zap"
)

var (
	getPiDetailsFromVendorSupportedPP = []paymentPb.PaymentProtocol{paymentPb.PaymentProtocol_UPI}
	ErrFetchRemitterDetails           = errors.New("error in fetching remitter details")
	ErrValidateAddress                = errors.New("error in validating address")
)

func (s *Service) GetPiDetailsFromVendorByTxnId(ctx context.Context, req *pb.GetPiDetailsFromVendorByTxnIdRequest) (*pb.GetPiDetailsFromVendorByTxnIdResponse, error) {
	var (
		res = &pb.GetPiDetailsFromVendorByTxnIdResponse{}
	)

	orderWithTxn, err := s.orderWithTxnProcessor.GetOrderWithTxnByTxnId(ctx, req.GetTxnId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error in fetching orders for get payment details from vendor", zap.String(logger.TXN_ID, req.GetTxnId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	actors, err := s.actorProcessor.GetActorsByIds(ctx, []string{orderWithTxn.GetOrder().GetFromActorId(), orderWithTxn.GetOrder().GetToActorId()})
	if err != nil {
		logger.Error(ctx, "error in fetching actor details for fetching payment details from vendor", zap.String(logger.ORDER, orderWithTxn.GetOrder().GetId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	if err = isValidGetPiDetailsFromVendorByTxnIdRequest(orderWithTxn, actors); err != nil {
		logger.Error(ctx, "request can't be processed due to ", zap.String(logger.TXN_ID, req.GetTxnId()), zap.Error(err))
		res.Status = rpc.StatusInvalidArgument()
		return res, nil
	}

	payerPi, payeePi, err := s.getPaymentDetailsFromVendor(ctx, orderWithTxn, actors)

	if err != nil {
		// Given that multiple RPCs get executed inside getPaymentDetailsFromVendor, relying on just
		// statusFromErr can potentially result in unexpected issues e.g.,
		// Validate Address RPC can return RecordNotFound
		// Remitter Details RPC can return RecordNotFound
		// We will not be able to disambiguate basead on just status code. To help disambiguate, we are using error code
		statusFromErr := rpc.StatusFromError(err)
		switch {
		case errors.Is(err, ErrValidateAddress) &&
			(statusFromErr.GetCode() == uint32(upiPb.ValidateAddressAndCreatePiResponse_INVALID_VPA) ||
				statusFromErr.GetCode() == uint32(upiPb.ValidateAddressAndCreatePiResponse_PSP_NOT_REGISTERED)):
			// Different Validate Address failures are intentionally being abstracted and mapped to Invalid VPA
			// This is to shield the caller code from understanding various UPI codes
			// If there is a use case, we can introduce more status codes to this RPC and map it accordingly
			res.Status = rpc.NewStatusWithoutDebug(uint32(pb.GetPiDetailsFromVendorByTxnIdResponse_INVALID_VPA), "invalid vpa")
		case errors.Is(err, ErrFetchRemitterDetails) && statusFromErr.IsRecordNotFound():
			res.Status = rpc.StatusRecordNotFound()
		default:
			res.Status = rpc.StatusInternal()
			logger.Error(ctx, "error in getting payment details from vendor ", zap.Error(err))
		}
		return res, nil
	}

	if payerPi != nil {
		res.PayerPi = payerPi
	}
	if payeePi != nil {
		res.PayeePi = payeePi
	}
	res.Status = rpc.StatusOk()
	return res, nil

}

// isValidGetPiDetailsFromVendorByTxnIdRequest validate the condition when a payment details can be fetched from vendor.
// Some validations are:
// 1. Both actor should not be user otherwise we should have all the payment details at our end and no need to fetch from vendor
// 2. Currently only UPI remitter fetch is implemented.
func isValidGetPiDetailsFromVendorByTxnIdRequest(orderWithTxn *pb.OrderWithTransactions, actors []*typesPb.Actor) error {
	if len(actors) < 2 {
		return fmt.Errorf("both actors not fetched from actor service")
	}

	// if both actor is User then we do not need to call get pi details from vendor
	if actors[0].GetType() == typesPb.Actor_USER &&
		actors[1].GetType() == typesPb.Actor_USER {
		return fmt.Errorf("api will not work for both internal user %w", epifierrors.ErrInvalidArgument)
	}

	if len(orderWithTxn.GetTransactions()) > 0 && !lo.Contains(getPiDetailsFromVendorSupportedPP, orderWithTxn.GetTransactions()[0].GetPaymentProtocol()) {
		return fmt.Errorf("unsupported PP for GetPiDetailsFromVendorByTxnId %v", orderWithTxn.GetTransactions()[0].GetPaymentProtocol())
	}
	return nil
}

// getPaymentDetailsFromVendor fetch PI details based on payment protocol.
func (s *Service) getPaymentDetailsFromVendor(ctx context.Context, orderWithTxn *pb.OrderWithTransactions, actors []*typesPb.Actor) (*piPb.PaymentInstrument, *piPb.PaymentInstrument, error) {

	if len(orderWithTxn.GetTransactions()) > 0 && orderWithTxn.GetTransactions()[0].GetPaymentProtocol() == paymentPb.PaymentProtocol_UPI {
		return s.getPaymentDetailsFromVendorFromRemitterInfoV1(ctx, orderWithTxn, actors)
	}
	return nil, nil, epifierrors.ErrUnimplemented
}

// getPaymentDetailsFromVendorFromRemitterInfoV1 fetch payment details from vendor for other actor, create and return PI.
// In case of any error, it will return error.
// nolint:funlen
func (s *Service) getPaymentDetailsFromVendorFromRemitterInfoV1(ctx context.Context, orderWithTxn *pb.OrderWithTransactions, actors []*typesPb.Actor) (*piPb.PaymentInstrument, *piPb.PaymentInstrument, error) {

	var (
		payerVpa            string
		payeeVpa            string
		payeePiId           string
		payerPiId           string
		payerPi             *piPb.PaymentInstrument
		payeePi             *piPb.PaymentInstrument
		remitterDetailsV1   *vgPaymentPb.GetRemitterDetailsV1Response
		err                 error
		notificationDetails *paymentPb.NotificationDetails
	)

	if len(orderWithTxn.GetTransactions()) > 0 &&
		orderWithTxn.GetTransactions()[0].GetPaymentProtocol() == paymentPb.PaymentProtocol_UPI {

		notificationDetails = fetchNotificationDetailsForTransaction(orderWithTxn.GetTransactions()[0])
		if notificationDetails == nil {
			logger.Error(ctx, "unable to find credit or debit notification detail for the given transaction",
				zap.String(logger.TXN_ID, orderWithTxn.GetTransactions()[0].GetId()))
			return nil, nil, fmt.Errorf("error in finding raw notification detail for the transaction %s", orderWithTxn.GetTransactions()[0].GetId())
		}

		remitterDetailsV1, err = s.vgPaymentClient.GetRemitterDetailsV1(ctx, &vgPaymentPb.GetRemitterDetailsV1Request{
			Header: &commonvgpb.RequestHeader{
				Vendor: commonvgpb.Vendor_FEDERAL_BANK,
			},
			TxnDatetime:     orderWithTxn.GetTransactions()[0].GetExecutionTS(),
			PaymentProtocol: paymentPb.PaymentProtocol_UPI,
			Utr:             notificationDetails.GetAdditionalDetails(),
		})
		if err = epifigrpc.RPCError(remitterDetailsV1, err); err != nil {
			return nil, nil, errors.Join(ErrFetchRemitterDetails, err)
		}
		payerVpa = remitterDetailsV1.GetRemitterUpiInfo().GetPayerVpa()
		payeeVpa = remitterDetailsV1.GetRemitterUpiInfo().GetPayeeVpa()
	}

	// TODO(Team): A cache layer can be introduced to temporarily cache remitter fetch vendor response which can be used to further to do validate address (in case validate address has more failure rate).

	if isUserActor(orderWithTxn.GetOrder().GetFromActorId(), actors) {
		payeePiId, err = s.upiProcessor.ValidateAddressAndCreatePi(ctx, payeeVpa, orderWithTxn.GetOrder().GetFromActorId())
		if err != nil {
			return nil, nil, errors.Join(ErrValidateAddress, err)
		}
		payerPiId, err = s.upiProcessor.ValidateAddressAndCreatePi(ctx, payerVpa, orderWithTxn.GetOrder().GetFromActorId())
		if err != nil {
			return nil, nil, errors.Join(ErrValidateAddress, err)
		}
	}

	if isUserActor(orderWithTxn.GetOrder().GetToActorId(), actors) {
		payerPiId, err = s.upiProcessor.ValidateAddressAndCreatePi(ctx, payerVpa, orderWithTxn.GetOrder().GetToActorId())
		if err != nil {
			return nil, nil, errors.Join(ErrValidateAddress, err)
		}
		payeePiId, err = s.upiProcessor.ValidateAddressAndCreatePi(ctx, payeeVpa, orderWithTxn.GetOrder().GetToActorId())
		if err != nil {
			return nil, nil, errors.Join(ErrValidateAddress, err)
		}
	}

	payerPi, err = s.piProcessor.GetPiById(ctx, payerPiId)
	if err != nil {
		return nil, nil, fmt.Errorf("error in getting payer pi details %w", err)
	}

	payeePi, err = s.piProcessor.GetPiById(ctx, payeePiId)
	if err != nil {
		return nil, nil, fmt.Errorf("error in getting payee pi details %w", err)
	}

	return payerPi, payeePi, nil
}

// isUserActor check if actorId is UserActor by iterating over of Actors list
func isUserActor(actorId string, actors []*typesPb.Actor) bool {
	for _, actor := range actors {
		if actor.GetId() == actorId && actor.GetType() == typesPb.Actor_USER {
			return true
		}
	}
	return false
}

func fetchNotificationDetailsForTransaction(txn *paymentPb.Transaction) *paymentPb.NotificationDetails {
	if _, ok := txn.GetRawNotificationDetails()["DEBIT"]; ok {
		return txn.GetRawNotificationDetails()["DEBIT"]
	} else if _, ok = txn.GetRawNotificationDetails()["CREDIT"]; ok {
		return txn.GetRawNotificationDetails()["CREDIT"]
	} else {
		return nil
	}
}
