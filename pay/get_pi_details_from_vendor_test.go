package pay_test

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"reflect"
	"testing"

	"github.com/epifi/be-common/api/rpc"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	pb "github.com/epifi/gamma/api/pay"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	typesPb "github.com/epifi/gamma/api/typesv2"
	vgPaymentPb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment"
)

func TestService_GetPiDetailsFromVendorByTxnId(t *testing.T) {
	type args struct {
		ctx context.Context
		req *pb.GetPiDetailsFromVendorByTxnIdRequest
	}

	type mockGetOrderWithTxnByTxnIdProcessor struct {
		enable bool
		req    string
		res    *pb.OrderWithTransactions
		err    error
	}

	type mockGetActorByIdsProcessor struct {
		enable bool
		req    []string
		res    []*typesPb.Actor
		err    error
	}

	type mockVgGetRemitterDetailsV1 struct {
		enable bool
		req    *vgPaymentPb.GetRemitterDetailsV1Request
		res    *vgPaymentPb.GetRemitterDetailsV1Response
		err    error
	}

	type mockValidateAddressAndCreatePi struct {
		enable  bool
		vpa     string
		actorId string
		piId    string
		err     error
	}

	type mockGetPiByIdProcessor struct {
		enable bool
		piId   string
		pi     *piPb.PaymentInstrument
		err    error
	}

	tests := []struct {
		name                                string
		args                                args
		mockGetOrderWithTxnByTxnIdProcessor mockGetOrderWithTxnByTxnIdProcessor
		mockGetActorByIdsProcessor          mockGetActorByIdsProcessor
		mockVgGetRemitterDetailsV1          mockVgGetRemitterDetailsV1
		mockValidateAddressAndCreatePi      []mockValidateAddressAndCreatePi
		mockGetPiByIdProcessor              []mockGetPiByIdProcessor
		want                                *pb.GetPiDetailsFromVendorByTxnIdResponse
		wantErr                             bool
	}{
		{
			name: "successful fetch payer && payee pi from remitter info for debit transaction",
			args: args{
				ctx: context.Background(),
				req: &pb.GetPiDetailsFromVendorByTxnIdRequest{
					TxnId: "txn-id",
				},
			},
			mockGetOrderWithTxnByTxnIdProcessor: mockGetOrderWithTxnByTxnIdProcessor{
				enable: true,
				req:    "txn-id",
				res: &pb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id:          "order-id",
						FromActorId: "from-actor-id",
						ToActorId:   "to-actor-id",
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id:              "txn-id",
							PiFrom:          "pi-from",
							PiTo:            "pi-to",
							PaymentProtocol: paymentPb.PaymentProtocol_UPI,
							RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
								"DEBIT": {
									AdditionalDetails: "request-id",
								},
							},
						},
					},
				},
				err: nil,
			},
			mockGetActorByIdsProcessor: mockGetActorByIdsProcessor{
				enable: true,
				req:    []string{"from-actor-id", "to-actor-id"},
				res: []*typesPb.Actor{
					{
						Id:   "from-actor-id",
						Type: typesPb.Actor_EXTERNAL_USER,
					},
					{
						Id:   "to-actor-id",
						Type: typesPb.Actor_USER,
					},
				},
				err: nil,
			},
			mockVgGetRemitterDetailsV1: mockVgGetRemitterDetailsV1{
				enable: true,
				req: &vgPaymentPb.GetRemitterDetailsV1Request{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					PaymentProtocol: paymentPb.PaymentProtocol_UPI,
					Utr:             "request-id",
				},
				res: &vgPaymentPb.GetRemitterDetailsV1Response{
					Status: rpc.StatusOk(),
					RemitterInfo: &vgPaymentPb.GetRemitterDetailsV1Response_RemitterUpiInfo{
						RemitterUpiInfo: &vgPaymentPb.RemitterUpiInfo{
							PayerVpa: "payer-vpa@ybl",
							PayeeVpa: "payee-vpa@fbl",
						},
					},
				},
				err: nil,
			},
			mockValidateAddressAndCreatePi: []mockValidateAddressAndCreatePi{
				{
					enable:  true,
					vpa:     "payer-vpa@ybl",
					actorId: "to-actor-id",
					piId:    "payer-pi-id",
					err:     nil,
				},
				{
					enable:  true,
					vpa:     "payee-vpa@fbl",
					actorId: "to-actor-id",
					piId:    "payee-pi-id",
					err:     nil,
				},
			},
			mockGetPiByIdProcessor: []mockGetPiByIdProcessor{
				{
					enable: true,
					piId:   "payer-pi-id",
					pi: &piPb.PaymentInstrument{
						Id:   "payer-pi-id",
						Type: piPb.PaymentInstrumentType_UPI,
					},
					err: nil,
				},
				{
					enable: true,
					piId:   "payee-pi-id",
					pi: &piPb.PaymentInstrument{
						Id:   "payee-pi-id",
						Type: piPb.PaymentInstrumentType_UPI,
					},
					err: nil,
				},
			},
			want: &pb.GetPiDetailsFromVendorByTxnIdResponse{
				Status: rpc.StatusOk(),
				PayerPi: &piPb.PaymentInstrument{
					Id:   "payer-pi-id",
					Type: piPb.PaymentInstrumentType_UPI,
				},
				PayeePi: &piPb.PaymentInstrument{
					Id:   "payee-pi-id",
					Type: piPb.PaymentInstrumentType_UPI,
				},
			},
			wantErr: false,
		},
		{
			name: "successfull fetch payee pi & payer pi for credit txn from remitter info ",
			args: args{
				ctx: context.Background(),
				req: &pb.GetPiDetailsFromVendorByTxnIdRequest{
					TxnId: "txn-id",
				},
			},
			mockGetOrderWithTxnByTxnIdProcessor: mockGetOrderWithTxnByTxnIdProcessor{
				enable: true,
				req:    "txn-id",
				res: &pb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id:          "order-id",
						FromActorId: "from-actor-id",
						ToActorId:   "to-actor-id",
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id:              "txn-id",
							PiFrom:          "pi-from",
							PiTo:            "pi-to",
							PaymentProtocol: paymentPb.PaymentProtocol_UPI,
							RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
								"CREDIT": {
									AdditionalDetails: "request-id",
								},
							},
						},
					},
				},
				err: nil,
			},
			mockGetActorByIdsProcessor: mockGetActorByIdsProcessor{
				enable: true,
				req:    []string{"from-actor-id", "to-actor-id"},
				res: []*typesPb.Actor{
					{
						Id:   "from-actor-id",
						Type: typesPb.Actor_USER,
					},
					{
						Id:   "to-actor-id",
						Type: typesPb.Actor_EXTERNAL_USER,
					},
				},
				err: nil,
			},
			mockVgGetRemitterDetailsV1: mockVgGetRemitterDetailsV1{
				enable: true,
				req: &vgPaymentPb.GetRemitterDetailsV1Request{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					PaymentProtocol: paymentPb.PaymentProtocol_UPI,
					Utr:             "request-id",
				},
				res: &vgPaymentPb.GetRemitterDetailsV1Response{
					Status: rpc.StatusOk(),
					RemitterInfo: &vgPaymentPb.GetRemitterDetailsV1Response_RemitterUpiInfo{
						RemitterUpiInfo: &vgPaymentPb.RemitterUpiInfo{
							PayerVpa: "payer-vpa@fbl",
							PayeeVpa: "payee-vpa@ybl",
						},
					},
				},
				err: nil,
			},
			mockValidateAddressAndCreatePi: []mockValidateAddressAndCreatePi{
				{
					enable:  true,
					vpa:     "payee-vpa@ybl",
					actorId: "from-actor-id",
					piId:    "payee-pi-id",
					err:     nil,
				},
				{
					enable:  true,
					vpa:     "payer-vpa@fbl",
					actorId: "from-actor-id",
					piId:    "payer-pi-id",
					err:     nil,
				},
			},
			mockGetPiByIdProcessor: []mockGetPiByIdProcessor{
				{
					enable: true,
					piId:   "payee-pi-id",
					pi: &piPb.PaymentInstrument{
						Id:   "payee-pi-id",
						Type: piPb.PaymentInstrumentType_UPI,
					},
					err: nil,
				},
				{
					enable: true,
					piId:   "payer-pi-id",
					pi: &piPb.PaymentInstrument{
						Id:   "payer-pi-id",
						Type: piPb.PaymentInstrumentType_UPI,
					},
					err: nil,
				},
			},
			want: &pb.GetPiDetailsFromVendorByTxnIdResponse{
				Status: rpc.StatusOk(),
				PayeePi: &piPb.PaymentInstrument{
					Id:   "payee-pi-id",
					Type: piPb.PaymentInstrumentType_UPI,
				},
				PayerPi: &piPb.PaymentInstrument{
					Id:   "payer-pi-id",
					Type: piPb.PaymentInstrumentType_UPI,
				},
			},
			wantErr: false,
		},
		{
			name: "record not found from vendor for payee pi from remitter info ",
			args: args{
				ctx: context.Background(),
				req: &pb.GetPiDetailsFromVendorByTxnIdRequest{
					TxnId: "txn-id",
				},
			},
			mockGetOrderWithTxnByTxnIdProcessor: mockGetOrderWithTxnByTxnIdProcessor{
				enable: true,
				req:    "txn-id",
				res: &pb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id:          "order-id",
						FromActorId: "from-actor-id",
						ToActorId:   "to-actor-id",
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id:              "txn-id",
							PiFrom:          "pi-from",
							PiTo:            "pi-to",
							PaymentProtocol: paymentPb.PaymentProtocol_UPI,
							RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
								"CREDIT": {
									AdditionalDetails: "utr",
								},
							},
						},
					},
				},
				err: nil,
			},
			mockGetActorByIdsProcessor: mockGetActorByIdsProcessor{
				enable: true,
				req:    []string{"from-actor-id", "to-actor-id"},
				res: []*typesPb.Actor{
					{
						Id:   "from-actor-id",
						Type: typesPb.Actor_USER,
					},
					{
						Id:   "to-actor-id",
						Type: typesPb.Actor_EXTERNAL_USER,
					},
				},
				err: nil,
			},
			mockVgGetRemitterDetailsV1: mockVgGetRemitterDetailsV1{
				enable: true,
				req: &vgPaymentPb.GetRemitterDetailsV1Request{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					PaymentProtocol: paymentPb.PaymentProtocol_UPI,
					Utr:             "utr",
				},
				res: &vgPaymentPb.GetRemitterDetailsV1Response{
					Status: rpc.StatusRecordNotFound(),
				},
				err: nil,
			},
			want: &pb.GetPiDetailsFromVendorByTxnIdResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			paySvc, md, deferFunc := getPayServiceWithMock(t)
			defer deferFunc()

			if tt.mockGetOrderWithTxnByTxnIdProcessor.enable {
				md.mockOrderWithTxnProcessor.EXPECT().GetOrderWithTxnByTxnId(tt.args.ctx, tt.mockGetOrderWithTxnByTxnIdProcessor.req).
					Return(tt.mockGetOrderWithTxnByTxnIdProcessor.res, tt.mockGetOrderWithTxnByTxnIdProcessor.err)
			}

			if tt.mockGetActorByIdsProcessor.enable {
				md.mockActorProcessor.EXPECT().GetActorsByIds(tt.args.ctx, tt.mockGetActorByIdsProcessor.req).
					Return(tt.mockGetActorByIdsProcessor.res, tt.mockGetActorByIdsProcessor.err)
			}

			if tt.mockVgGetRemitterDetailsV1.enable {
				md.mockVgPayClient.EXPECT().GetRemitterDetailsV1(tt.args.ctx, tt.mockVgGetRemitterDetailsV1.req).
					Return(tt.mockVgGetRemitterDetailsV1.res, tt.mockVgGetRemitterDetailsV1.err)
			}

			for idx := range tt.mockValidateAddressAndCreatePi {
				if tt.mockValidateAddressAndCreatePi[idx].enable {
					md.mockUpiProcessor.EXPECT().ValidateAddressAndCreatePi(tt.args.ctx, tt.mockValidateAddressAndCreatePi[idx].vpa, tt.mockValidateAddressAndCreatePi[idx].actorId).
						Return(tt.mockValidateAddressAndCreatePi[idx].piId, tt.mockValidateAddressAndCreatePi[idx].err)
				}
			}

			for idx := range tt.mockGetPiByIdProcessor {
				if tt.mockGetPiByIdProcessor[idx].enable {
					md.mockIPiProcessor.EXPECT().GetPiById(tt.args.ctx, tt.mockGetPiByIdProcessor[idx].piId).
						Return(tt.mockGetPiByIdProcessor[idx].pi, tt.mockGetPiByIdProcessor[idx].err)
				}
			}

			got, err := paySvc.GetPiDetailsFromVendorByTxnId(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPiDetailsFromVendorByTxnId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetPiDetailsFromVendorByTxnId() got = %v, want %v", got, tt.want)
			}
		})
	}
}
