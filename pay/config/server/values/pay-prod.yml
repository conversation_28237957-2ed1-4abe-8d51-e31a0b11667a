Application:
  Environment: "prod"
  Name: "pay"

# Payment service is actually initialized on the port defined in order-<env>.yml
# These properties are kept here from forward compatibility POV when we may want Pay service to be running on a
# different port in the order server
Server:
  Ports:
    GrpcPort: 8091
    GrpcSecurePort: 9513
    HttpPort: 9853

# Pay service uses DB connection initialized by order-<env>.yml
# These properties are kept from forward compatibility POV when we may want to have DB separate connection
# for Pay
EpifiDb:
  DbType: "CRDB"
  AppName: "pay"
  StatementTimeout: 10s
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: false
  SSLMode: "verify-full"
  SSLRootCert: "prod/cockroach/ca.crt"
  SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 20
  MaxIdleConn: 5
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

PayDb:
  DbType: "PGDB"
  AppName: "pay"
  StatementTimeout: 5m
  Name: "pay"
  EnableDebug: false
  SSLMode: "verify-full"
  SecretName: "prod/rds/epifimetis/pay_dev_user"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false


Tracing:
  Enable: true

Profiling:
  StackDriverProfiling:
    ProjectId: "epifi-prod"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-prod-automatic-profiling"
    CPUPercentageLimit: 80
    MemoryPercentageLimit: 80
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

ExecutionReportGenerationParams:
  ReportStalenessDuration: "5s"

SignalWorkflowPublisher:
  QueueName: "prod-celestial-signal-workflow-queue"

InPaymentOrderUpdatePublisher:
  QueueName: "prod-order-in-payment-order-update-queue"

OrderUpdateEventPublisher:
  TopicName: "prod-order-update-topic"

IFTProcessFileSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-pay-international-fund-transfer-process-file-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 25
      MaxAttempts: 3
      TimeUnit: "Minute"

OrderUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-pay-order-update-event-consumer-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 25
      MaxAttempts: 3
      TimeUnit: "Minute"

InternationalFundTransfer:
  EnableFederalSherlock: false
  EnableLRSCheckFromVendor: false
  EnableSofStateColumnInSwiftFileFlag: true
  GenerateLrsCheckFileUsingCutOffTime: false
  IsSofBasedRemittanceLimitCheckEnabled: true
  S3Bucket: "epifi-prod-pay-international-fund-transfer"
  PoolInwardAccountPI: "paymentinstrument-us-stock-inward-account"
  UsStocksVendorPI: "paymentinstrument-alpaca-international-account"
  SkipSofDocumentGenerationStage: false
  SkipOrderFulfilmentStage: false
  SherlockHost: "https://federal.epifi.in"
  FederalSherlockHost: "https://federal-sherlock.epifi.in"
  GenerateSwiftReportRetry: 5
  GenerateSwiftReportSleep: 25
  MaxPageSize: 15
  NoDataExistForPANCode: "Data does not exist for given pan"
  MaxLRSPageSize: 5000
  DailyAggregateLimit:
    CurrencyCode: "INR"
    Units: 4_00_000
  AnnualAggregateLimit:
    CurrencyCode: "INR"
    Units: 1_00_00_000
  # 60 days
  AccountVintageCheckDuration: "1440h"
  AccountVintageTxnCount: 5
  EnableMinTxnCountCheckForVintageAccounts: false
  ForexRate:
    UpdateAmountInUse:
      EnableIdempotency: true


PayIncidentMgrOrderUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-order-update-payincidentmanager-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 60
        Period: 1s
    Namespace: "order-consumer"

TransactionDetailedStatusUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-txn-detailed-status-update-payincidentmanager-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 40
        Period: 1s
    Namespace: "order-consumer"

FeatureFlags:
  EnableFundTransferViaCelestial: false

FundTransferParams:
  DefaultFundTransferExpiryDuration: "10m"

FundTransferCelestialParams:
  IsFundTransferViaCelestialRestricted: true
  AllowedUserGrpForFundTransferViaCelestial:
    - 1 # INTERNAL = user belongs to epiFi
    - 17 # CREDIT_CARD_INTERNAL = user belongs to credit card cug testing group.
  Version: "V1"
VersionSupport:
  MinAndroidAppVersionToSupportSofCheck: 216
  MinIOSAppVersionToSupportSofCheck: 306
  MinAndroidAppVersionToSupportAddCA: 216
  MinIOSAppVersionToSupportAddCA: 318

PinotIngestionDelay:
  DelayDuration: 5m

NonTpapPspHandles: ["fbl"]

FeatureReleaseConfig:
  FeatureConstraints:
    - PAY_INCIDENT_MANAGER_UPI_PIN_FLOW_ERROR:
          AppVersionConstraintConfig:
            MinAndroidVersion: 1
            MinIOSVersion: 1
          StickyPercentageConstraintConfig:
            RolloutPercentage: 100
          UserGroupConstraintConfig:
            AllowedGroups:
              - 1 # INTERNAL
    - PAY_INCIDENT_MANAGER_P2P_DEEMED_TRANSACTION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - PAY_INCIDENT_MANAGER_P2M_DEEMED_TRANSACTION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FUND_TRANSFER_V1:
        AppVersionConstraintConfig:
          MinAndroidVersion: 0
          MinIOSVersion: 0
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_PAY_INCIDENT_MANAGER_DEBITED_AND_FAILED_TRANSACTION_CATCH_ALL_ON_APP:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 37 # PAY_EXPERIMENTAL
    - FEATURE_PAY_INCIDENT_MANAGER_DEBITED_AND_IN_PROGRESS_TRANSACTION_CATCH_ALL_ON_APP:
        # most of the Transactions which comes under this either go into DEEMED or go into DEBITED_AND_FAILED_CATCH_ALL within 5 mins (even sooner though).
        AppVersionConstraintConfig:
          MinAndroidVersion: 10000
          MinIOSVersion: 10000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 37 # PAY_EXPERIMENTAL
    - FEATURE_PAY_INCIDENT_MANAGER_IN_PROGRESS_TRANSACTION_CATCH_ALL_ON_APP:
        # Disabling because we are rarely having any transaction which stays in IN_PROGRESS state for more than threshold(5 mins)
        AppVersionConstraintConfig:
          MinAndroidVersion: 10000
          MinIOSVersion: 10000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 37 # PAY_EXPERIMENTAL
    - FEATURE_ENRICH_ORDER_AND_TXN_FROM_DC_SWITCH:
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_PAY_INCIDENT_MANAGER_CHEQUE_CREDIT_TRANSACTION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_PAY_INCIDENT_MANAGER_ONBOARD_ADD_FUNDS_SECOND_LEG_FAILURE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 436
          MinIOSVersion: 600
        StickyPercentageConstraintConfig:
          RolloutPercentage: 10
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_QR_SCAN_ENHANCEMENTS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 9999
          MinIOSVersion: 598
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 37 # PAY_EXPERIMENTAL
    - FEATURE_PAY_LANDING_SCREEN_BANNER_DYNAMIC_ELEMENTS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 471 # Client issues are not yet fixed, using different bg color to avoid user of border
          MinIOSVersion: 647 # Client issues are not yet fixed, using different bg color to avoid user of border
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_OC215A_SKIP_VALIDATE_ADDRESS:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 37 # PAY_EXPERIMENTAL

#this is used in the init service
RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "redis-15404.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:15404"
  AuthDetails:
    SecretPath: "prod/redis/pay/prefixaccess"
    Environment: "prod"
    Region: "ap-south-1"
  ClientName: pay

IFTRemittanceFileProcessingEventPublisher:
  TopicName: "prod-ift-remittance-file-processing-events-topic"

TxnBackfillBucketName: "epifi-prod-pay-txn-backfill"

IncidentManagerParams:
  DebitedTransactionThresholdBreachDuration: 5m

MaximumActiveDaysForAutoIdTickets: 21

TpapEntryPointSwitchConfigForSaUser:
  FeatureConstraints:
    FEATURE_CREDIT_CARD_TPAP_PAYMENTS:
      AppVersionConstraintConfig:
        MinAndroidVersion: 312
        MinIOSVersion: 446
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 37 # PAY_EXPERIMENTAL
TpapEntryPointSwitchConfigForNonSaUser:
  FeatureConstraints:
    FEATURE_CREDIT_CARD_TPAP_PAYMENTS:
      AppVersionConstraintConfig:
        MinAndroidVersion: 312
        MinIOSVersion: 446
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 37 # PAY_EXPERIMENTAL


GenerateSofLimitStrategiesValuesSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-generate-sof-limit-strategies-values-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~20 min post that regular interval is followed for next 200 mins
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 1
          MaxAttempts: 10
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 10
          MaxAttempts: 20
          TimeUnit: "Minute"
      MaxAttempts: 30
      CutOff: 10
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1s
    Namespace: "internationalfundtransfer"

GenerateSofLimitStrategiesValuesPublisher:
  QueueName: "prod-generate-sof-limit-strategies-values-queue"

PennyDropConfig:
  PennyDropPollRetryThreshold: 20
  PennyDropSourceAccountMapping:
    PENNY_DROP_PROVENANCE_SECURED_CREDIT_CARDS_NTB_FLOW:
      # todo(@saurabh): add ids here post pi and actor creation
      FEDERAL_BANK:
        ActorId:
        PaymentInstrumentId:
        Amount:
          CurrencyCode: "INR"
          Units: 1
    PENNY_DROP_PROVENANCE_STOCKGUARDIAN_TSP:
      FEDERAL_BANK:
        ActorId: "actor-stock-guardian"
        PaymentInstrumentId: "paymentinstrument-stockguardian_MbZRPgHhafW"
        Amount:
          CurrencyCode: "INR"
          Units: 1
    PENNY_DROP_PROVENANCE_VALIDATE_BANK_ACCOUNT:
      FEDERAL_BANK:
        ActorId: "actor-epifi-business-account"
        PaymentInstrumentId: "paymentinstrument-epifi-business-account"
        Amount:
          CurrencyCode: "INR"
          Units: 1
    PENNY_DROP_PROVENANCE_LOANS_LSP:
      FEDERAL_BANK:
        ActorId: "actor-loans-federal-penny-drop-pool-account"
        PaymentInstrumentId: "paymentinstrument-loans-federal-penny-drop-pool-account"
        Amount:
          CurrencyCode: "INR"
          Units: 1

# This configuration outlines different combination of transaction status, statusCodePayer and statusCodePayee
# Comparing transaction attributes with these combinations to decide whether incident type should be of DEBITED_AND_FAILED_TRANSACTION
TransactionStatusDetailsCombinationsForDebitAndFailedIncident:
  - IsEnabled: true
    DetailedApiStatus: "FAILURE"
    StatusCodePayer: "UPI1204"
    StatusCodePayee: "UPI825"
  - IsEnabled: true
    DetailedApiStatus: "FAILURE"
    StatusCodePayer: "UPI171"
    StatusCodePayee: "UPI784"
  - IsEnabled: true
    DetailedApiStatus: "FAILURE"
    StatusCodePayer: "UPI784"
    StatusCodePayee: "UPI784"
  - IsEnabled: true
    DetailedApiStatus: "FAILURE"
    StatusCodePayer: "UPI_SUCCESS"
    StatusCodePayee: "UPI_SUCCESS"

PayOrderCacheConfig:
  IsCachingEnabled: true
  UseCaseToCacheConfigMap:
    # CacheConfig for RecentActivityCache used in GetRecentActivityOrdersFromCache for storing recent pay activity
    # (RecentPayActivityFromActorIdPrefix, RecentPayActivityToActorIdPrefix are cache prefixes used here).
    - RecentActivityCache:
        IsCachingEnabled: true
        CacheTTL: "48h" # 2 days
    # CacheConfig for OrderCache used in GetById fpr storing order
    # (OrderKeyPrefix is cache prefix used here).
    - OrderCache:
        IsCachingEnabled: true
        CacheTTL: "2h"

PayTransactionCacheConfig:
  IsCachingEnabled: true
  UseCaseToCacheConfigMap:
    # CacheConfig for TransactionCache used in GetById for storing transaction
    # (TransactionKeyPrefix is cache prefix used here).
    - TransactionCache:
        IsCachingEnabled: true
        CacheTTL: "2h"

Secrets:
  Ids:
    MT199MessageAttachmentPassword: "prod/ift/mt199-message-attachment-password"

EnableEntitySegregation: true

PgProgramToAuthSecretMap:
  "RAZORPAY:LIQUILOANS_PL:PILEcFKdcmQFWa8BfipSET/Q230915==":
    AuthParam: "prod/vendorgateway/razorpay-liquiloans-loans-api-key"
  "RAZORPAY:STOCK_GUARDIAN_TSP:paymentinstrument-stockguardian_MbZRPgHhafW":
    AuthParam: "prod/vendorgateway/razorpay-stock-guardian-loans-api-key"

PgParams:
  # TODO(Sundeep): Increase the values once we determine the reliability of webhooks.
  # For one time payments, we expect the payments to be complete within 12m, hence setting the signal wait timeout to
  # this. If the webhook event is not received from paymentgateway within this timeout, then the workflow polls the
  # payment gateway for a while to check if payment is completed.
  OneTimeFundTransferStatusWaitSignalTimeout: "6m"
  # For recurring payment executions (using mandates), for the payments to be authorised it takes T + 1 days. Hence,
  # for recurring payment executions we set the signal wait timeout to 3 days, as we expect the payments to be
  # completed within that duration.
  # https://razorpay.com/docs/payments/recurring-payments/emandate/faqs/#4-for-emandates-how-long-does-it-take
  RpExecutionFundTransferStatusWaitSignalTimeout: "1440m" # 1 day

PayloadSigningCreds:
  SigningKeyJson: "prod/pay/signing_credentials"

ProcessPaymentGatewayWebhookEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-pg-razorpay-inbound-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 5
      MaxAttempts: 11
      TimeUnit: "Second"

PayIncidentManager:
  DefaultTransactionHandler:
    IncidentProductCategoryMap:
      "CHEQUE_CREDIT_TRANSACTION":
        ProductCategory: "PRODUCT_CATEGORY_TRANSACTIONS"
        ProductCategoryDetails: "PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_VIA_FI_APP"
        SubCategory: "SUB_CATEGORY_TRANSACTIONS_VIA_FI_APP_BUT_NOT_DEBITED"
        # In-App Transactions | Amount not debited(Auto ID) | UPI - Pending
        PaymentProtocolToIssueCategoryIdMap:
          "CTS": "708be32c-dc6f-5637-9a4e-26a2dc4748d4"

PaymentEnquiryParams:
  NotFoundMaxRetryDurationVendorMap:
    "FEDERAL_BANK":
      IntraBank: "24h"
      # We are currently enquiring at 90s and then 2h90s and then 4h90s
      # So we can mark Txn as Failed if 2nd enquiry gave U48 [a.k.a NotFound], hence we are keeping this less than 2h.
      UPI: "110m"
      NEFT: "24h"
      RTGS: "24h"
      IMPS: "24h"
  InProgressToSuccessMap: # time duration and error codes for in progress response status will be move to success
    "FEDERAL_BANK":
      FiErrorCodes:
        - "FI304"
      PaymentProtocolToDurationMap:
        "NEFT": "120m"
        "RTGS": "120m"
