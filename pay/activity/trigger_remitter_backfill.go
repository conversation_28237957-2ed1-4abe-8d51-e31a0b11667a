package activity

import (
	"context"
	"fmt"
	"strings"

	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/parser"
	offAppUpiActivity "github.com/epifi/gamma/api/pay/activity/offappupiv2flow"
	"github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/be-common/pkg/epifitemporal"
)

// nolint:funlen
func (p *Processor) TriggerRemitterBackfill(ctx context.Context, req *offAppUpiActivity.TriggerRemitterBackfillRequest) (*offAppUpiActivity.TriggerRemitterBackfillResponse, error) {
	var (
		lg        = activity.GetLogger(ctx)
		txnId     = req.GetTransactionId()
		orderId   = req.GetOrderId()
		actorId   = req.GetActorId()
		otherPiId string
	)

	// fetch the txn and order corresponding to the IDs passed
	txn, order, err := p.getTxnAndOrderById(ctx, txnId, orderId)
	if err != nil {
		return nil, err
	}

	// Skip triggering remitter backfill if the txn is an Extended PI case.
	// Why?
	//	1. This is to prevent the remitter-workflow from linking the orders and txns to original entities (PI and Actor), leading to duplicate orders for an actor.
	//	More about Extended PIs here: https://docs.google.com/document/d/1_2AoB_cMH1V2QSb67_1FxqIE1dBwyjqlf_dei_-mOjg/edit#heading=h.oc2e7qx0vvzg
	if strings.HasPrefix(txn.GetUtr(), "EXTENDED:") {
		return &offAppUpiActivity.TriggerRemitterBackfillResponse{}, nil
	}

	// figure out the "other" PI ID to validate whether the PI is verified or not
	otherPiId = txn.GetPiTo()
	if order.IsCreditForActor(actorId) {
		otherPiId = txn.GetPiFrom()
	}

	// fetch pi details and check whether its verified or not
	piRes, err := p.piClient.GetPiById(ctx, &paymentinstrument.GetPiByIdRequest{Id: otherPiId})
	if rpcErr := epifigrpc.RPCError(piRes, err); rpcErr != nil {
		lg.Error("error in fetching PI by ID for triggering remitter backfill", zap.Error(rpcErr), zap.String(logger.PI_ID, otherPiId),
			zap.String(logger.TXN_ID, txnId), zap.String(logger.ORDER_ID, orderId),
		)

		return nil, epifitemporal.NewTransientError(fmt.Errorf("error in fetching PI by ID: %s, err: %w", otherPiId, rpcErr))
	}

	// skip triggering remitter backfill if the other PI is already in VERIFIED state
	if piRes.GetPaymentInstrument().GetState() == paymentinstrument.PaymentInstrumentState_VERIFIED {
		return &offAppUpiActivity.TriggerRemitterBackfillResponse{}, nil
	}

	// trigger/initiate the remitter info backfill call
	initiateRes, err := p.parserClient.InitiateRemitterInfoBackfillWorkflow(ctx, &parser.InitiateRemitterInfoBackfillWorkflowRequest{
		OrderDetails:                            order,
		TransactionDetails:                      txn,
		MaximumPauseTimeForRemitterInfoWorkflow: 240, // in minutes
	})
	if rpcErr := epifigrpc.RPCError(initiateRes, err); rpcErr != nil {
		lg.Error("error in calling RPC InitiateRemitterInfoBackfillWorkflow", zap.Error(rpcErr), zap.String(logger.PI_ID, otherPiId),
			zap.String(logger.TXN_ID, txnId), zap.String(logger.ORDER_ID, orderId),
		)

		return nil, epifitemporal.NewTransientError(fmt.Errorf("error in calling RPC InitiateRemitterInfoBackfillWorkflow, err: %w", rpcErr))
	}

	return &offAppUpiActivity.TriggerRemitterBackfillResponse{}, nil
}
