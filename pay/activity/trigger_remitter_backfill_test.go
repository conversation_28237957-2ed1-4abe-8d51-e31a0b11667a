package activity

import (
	"fmt"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"

	orderPb "github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/parser"
	"github.com/epifi/gamma/api/pay/activity/offappupiv2flow"
	"github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/be-common/pkg/epifitemporal"
	payNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/pay"
)

func TestProcessor_TriggerRemitterBackfill(t *testing.T) {
	type mockGetTxnById struct {
		enable bool
		txnId  string
		txn    *payment.Transaction
		err    error
	}

	type mockGetOrderById struct {
		enable  bool
		orderId string
		order   *orderPb.Order
		err     error
	}

	type mockGetPiById struct {
		enable bool
		req    *paymentinstrument.GetPiByIdRequest
		res    *paymentinstrument.GetPiByIdResponse
		err    error
	}

	type mockInitiateRemitter struct {
		enable bool
		req    *parser.InitiateRemitterInfoBackfillWorkflowRequest
		res    *parser.InitiateRemitterInfoBackfillWorkflowResponse
		err    error
	}

	// struct to keep together all type of mocked data required for each test by the setupMocks func
	type mockData struct {
		mockGetTxnById       mockGetTxnById
		mockGetOrderById     mockGetOrderById
		mockGetPiById        mockGetPiById
		mockInitiateRemitter mockInitiateRemitter
	}

	tests := []struct {
		name      string
		req       *offappupiv2flow.TriggerRemitterBackfillRequest
		mockData  mockData
		want      *offappupiv2flow.TriggerRemitterBackfillResponse
		wantErr   bool
		assertErr func(err error) bool
	}{
		{
			name: "should return permanent error if no txn is found by the id",
			req: &offappupiv2flow.TriggerRemitterBackfillRequest{
				ActorId:       "actor-id",
				TransactionId: "txn-id",
				OrderId:       "order-id",
			},
			mockData: mockData{
				mockGetTxnById: mockGetTxnById{
					enable: true,
					txnId:  "txn-id",
					err:    epifierrors.ErrRecordNotFound,
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "should return transient error if unknown error occurs while fetching txn by id",
			req: &offappupiv2flow.TriggerRemitterBackfillRequest{
				ActorId:       "actor-id",
				TransactionId: "txn-id",
				OrderId:       "order-id",
			},
			mockData: mockData{
				mockGetTxnById: mockGetTxnById{
					enable: true,
					txnId:  "txn-id",
					err:    fmt.Errorf("random error"),
				},
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should return permanent error if no order is found by the id",
			req: &offappupiv2flow.TriggerRemitterBackfillRequest{
				ActorId:       "actor-id",
				TransactionId: "txn-id",
				OrderId:       "order-id",
			},
			mockData: mockData{
				mockGetTxnById: mockGetTxnById{
					enable: true,
					txnId:  "txn-id",
					txn:    &payment.Transaction{Id: "txn-id"},
				},
				mockGetOrderById: mockGetOrderById{
					enable:  true,
					orderId: "order-id",
					err:     epifierrors.ErrRecordNotFound,
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "should return transient error if unknown error occurs while fetching order by id",
			req: &offappupiv2flow.TriggerRemitterBackfillRequest{
				ActorId:       "actor-id",
				TransactionId: "txn-id",
				OrderId:       "order-id",
			},
			mockData: mockData{
				mockGetTxnById: mockGetTxnById{
					enable: true,
					txnId:  "txn-id",
					txn:    &payment.Transaction{Id: "txn-id"},
				},
				mockGetOrderById: mockGetOrderById{
					enable:  true,
					orderId: "order-id",
					err:     fmt.Errorf("random error"),
				},
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should return transient error if fetching order PI by ID fails",
			req: &offappupiv2flow.TriggerRemitterBackfillRequest{
				ActorId:       "actor-id",
				TransactionId: "txn-id",
				OrderId:       "order-id",
			},
			mockData: mockData{
				mockGetTxnById: mockGetTxnById{
					enable: true,
					txnId:  "txn-id",
					txn:    &payment.Transaction{Id: "txn-id", PiFrom: "pi-from"},
				},
				mockGetOrderById: mockGetOrderById{
					enable:  true,
					orderId: "order-id",
					order:   &orderPb.Order{Id: "order-id", ToActorId: "actor-id"},
				},
				mockGetPiById: mockGetPiById{
					enable: true,
					req:    &paymentinstrument.GetPiByIdRequest{Id: "pi-from"},
					res: &paymentinstrument.GetPiByIdResponse{
						Status: rpc.StatusInternal(),
					},
				},
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should return transient error if initiating remitter-backfill call fails",
			req: &offappupiv2flow.TriggerRemitterBackfillRequest{
				ActorId:       "actor-id",
				TransactionId: "txn-id",
				OrderId:       "order-id",
			},
			mockData: mockData{
				mockGetTxnById: mockGetTxnById{
					enable: true,
					txnId:  "txn-id",
					txn:    &payment.Transaction{Id: "txn-id", PiFrom: "pi-from"},
				},
				mockGetOrderById: mockGetOrderById{
					enable:  true,
					orderId: "order-id",
					order:   &orderPb.Order{Id: "order-id", ToActorId: "actor-id"},
				},
				mockGetPiById: mockGetPiById{
					enable: true,
					req:    &paymentinstrument.GetPiByIdRequest{Id: "pi-from"},
					res: &paymentinstrument.GetPiByIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstrument: &paymentinstrument.PaymentInstrument{
							Id:    "pi-from",
							State: paymentinstrument.PaymentInstrumentState_CREATED,
						},
					},
				},
				mockInitiateRemitter: mockInitiateRemitter{
					enable: true,
					req: &parser.InitiateRemitterInfoBackfillWorkflowRequest{
						TransactionDetails:                      &payment.Transaction{Id: "txn-id", PiFrom: "pi-from"},
						OrderDetails:                            &orderPb.Order{Id: "order-id", ToActorId: "actor-id"},
						MaximumPauseTimeForRemitterInfoWorkflow: 240,
					},
					res: &parser.InitiateRemitterInfoBackfillWorkflowResponse{
						Status: rpc.StatusInternal(),
					},
				},
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should return success response without initiating remitter-backfill if other PI is already verified",
			req: &offappupiv2flow.TriggerRemitterBackfillRequest{
				ActorId:       "actor-id",
				TransactionId: "txn-id",
				OrderId:       "order-id",
			},
			mockData: mockData{
				mockGetTxnById: mockGetTxnById{
					enable: true,
					txnId:  "txn-id",
					txn:    &payment.Transaction{Id: "txn-id", PiFrom: "pi-from"},
				},
				mockGetOrderById: mockGetOrderById{
					enable:  true,
					orderId: "order-id",
					order:   &orderPb.Order{Id: "order-id", ToActorId: "actor-id"},
				},
				mockGetPiById: mockGetPiById{
					enable: true,
					req:    &paymentinstrument.GetPiByIdRequest{Id: "pi-from"},
					res: &paymentinstrument.GetPiByIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstrument: &paymentinstrument.PaymentInstrument{
							Id:    "pi-from",
							State: paymentinstrument.PaymentInstrumentState_VERIFIED,
						},
					},
				},
			},
			want:      &offappupiv2flow.TriggerRemitterBackfillResponse{},
			wantErr:   false,
			assertErr: nil,
		},
		{
			name: "should return success response after initiating remitter-backfill if other PI is not verified",
			req: &offappupiv2flow.TriggerRemitterBackfillRequest{
				ActorId:       "actor-id",
				TransactionId: "txn-id",
				OrderId:       "order-id",
			},
			mockData: mockData{
				mockGetTxnById: mockGetTxnById{
					enable: true,
					txnId:  "txn-id",
					txn:    &payment.Transaction{Id: "txn-id", PiFrom: "pi-from"},
				},
				mockGetOrderById: mockGetOrderById{
					enable:  true,
					orderId: "order-id",
					order:   &orderPb.Order{Id: "order-id", ToActorId: "actor-id"},
				},
				mockGetPiById: mockGetPiById{
					enable: true,
					req:    &paymentinstrument.GetPiByIdRequest{Id: "pi-from"},
					res: &paymentinstrument.GetPiByIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstrument: &paymentinstrument.PaymentInstrument{
							Id:    "pi-from",
							State: paymentinstrument.PaymentInstrumentState_CREATED,
						},
					},
				},
				mockInitiateRemitter: mockInitiateRemitter{
					enable: true,
					req: &parser.InitiateRemitterInfoBackfillWorkflowRequest{
						TransactionDetails:                      &payment.Transaction{Id: "txn-id", PiFrom: "pi-from"},
						OrderDetails:                            &orderPb.Order{Id: "order-id", ToActorId: "actor-id"},
						MaximumPauseTimeForRemitterInfoWorkflow: 240,
					},
					res: &parser.InitiateRemitterInfoBackfillWorkflowResponse{
						Status: rpc.StatusOk(),
					},
				},
			},
			want:      &offappupiv2flow.TriggerRemitterBackfillResponse{},
			wantErr:   false,
			assertErr: nil,
		},
		{
			name: "should return success response without initiating remitter-backfill if it's an Extended PI case",
			req: &offappupiv2flow.TriggerRemitterBackfillRequest{
				ActorId:       "actor-id",
				TransactionId: "txn-id",
				OrderId:       "order-id",
			},
			mockData: mockData{
				mockGetTxnById: mockGetTxnById{
					enable: true,
					txnId:  "txn-id",
					txn:    &payment.Transaction{Id: "txn-id", PiFrom: "pi-from", Utr: "EXTENDED:utr"},
				},
				mockGetOrderById: mockGetOrderById{
					enable:  true,
					orderId: "order-id",
					order:   &orderPb.Order{Id: "order-id", ToActorId: "actor-id"},
				},
			},
			want:      &offappupiv2flow.TriggerRemitterBackfillResponse{},
			wantErr:   false,
			assertErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			act, md := newProcessorWithMocks(t)
			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(act)

			if tt.mockData.mockGetTxnById.enable {
				md.txnDao.EXPECT().
					GetById(gomock.Any(), tt.mockData.mockGetTxnById.txnId).
					Return(tt.mockData.mockGetTxnById.txn, tt.mockData.mockGetTxnById.err)
			}
			if tt.mockData.mockGetOrderById.enable {
				md.orderDao.EXPECT().
					GetById(gomock.Any(), tt.mockData.mockGetOrderById.orderId).
					Return(tt.mockData.mockGetOrderById.order, tt.mockData.mockGetOrderById.err)
			}
			if tt.mockData.mockGetPiById.enable {
				md.piClient.EXPECT().
					GetPiById(gomock.Any(), tt.mockData.mockGetPiById.req).
					Return(tt.mockData.mockGetPiById.res, tt.mockData.mockGetPiById.err)
			}
			if tt.mockData.mockInitiateRemitter.enable {
				md.parserClient.EXPECT().
					InitiateRemitterInfoBackfillWorkflow(gomock.Any(), tt.mockData.mockInitiateRemitter.req).
					Return(tt.mockData.mockInitiateRemitter.res, tt.mockData.mockInitiateRemitter.err)
			}

			var result *offappupiv2flow.TriggerRemitterBackfillResponse
			got, err := env.ExecuteActivity(payNs.TriggerRemitterBackfill, tt.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("TriggerRemitterBackfill() error = %v, failed to fetch type value from convertible", err)
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("TriggerRemitterBackfill() error = %v, wantErr %v", err, tt.wantErr)
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("TriggerRemitterBackfill() error = %v, assertion failed", err)
			case tt.want != nil && !proto.Equal(result, tt.want):
				t.Errorf("TriggerRemitterBackfill() \ngot = %v,\nwant = %v", result.String(), tt.want.String())
			}
		})
	}
}
