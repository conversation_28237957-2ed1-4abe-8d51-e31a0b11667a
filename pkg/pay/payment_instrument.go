package pay

import (
	"context"
	"fmt"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/order/payment/notification"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
)

// createUpiPiInCreatedState creates a UPI payment instrument in CREATED state for a given VPA
// Parameters:
// - vpa: The VPA (Virtual Payment Address) for which to create the PI
// - verifiedName: The verified name of the VPA owner, set in the VerifiedName field of the PI
// - name: The name to be set in the Name field of the UPI PI, typically the truncated VPA (without the PSP handle)
func createUpiPiInCreatedState(ctx context.Context, piClient piPb.PiClient, vpa, verifiedName, name string) (string, error) {
	if verifiedName == "" {
		logger.Info(ctx, "verifiedName is empty for vpa PI, creating PI without any name")
		// Set the verified name to be a non-alphanumeric string so that the PI is created without any name.
		// This is needed because CreatePi RPC fails for UPI PI if the verifiedName is empty. But the RPC internally
		// filters non-alphanumeric characters from the verifiedName. So, setting it to a non-alphanumeric string
		// results in the verifiedName being set as empty.
		verifiedName = "*****"
	}

	createPiResp, createPiErr := piClient.CreatePi(ctx, &piPb.CreatePiRequest{
		Type: piPb.PaymentInstrumentType_UPI,
		Identifier: &piPb.CreatePiRequest_Upi{
			Upi: &piPb.Upi{
				Vpa:  vpa,
				Name: name,
			},
		},
		VerifiedName: verifiedName,
		Capabilities: map[string]bool{
			piPb.Capability_INBOUND_TXN.String():  true,
			piPb.Capability_OUTBOUND_TXN.String(): true,
		},
		State:     piPb.PaymentInstrumentState_CREATED,
		Ownership: piPb.Ownership_EPIFI_TECH,
	})
	if err := epifigrpc.RPCError(createPiResp, createPiErr); err != nil {
		return "", fmt.Errorf("error in creating pi for given VPA: %w", err)
	}
	return createPiResp.GetPaymentInstrument().GetId(), nil
}

// CreateUpiPiForVpaDetails creates PIs for payer and payee VPAs and returns their IDs
// Parameters:
//   - parsedParticulars: Contains the VPA details (payer/payee VPA and names)
//   - payerName: The name to be set in the Name field of the payer's UPI PI, typically the truncated VPA
//     (without the PSP handle) using upiPkg.TruncatePspHandle
//   - payeeName: The name to be set in the Name field of the payee's UPI PI, typically the truncated VPA
//     (without the PSP handle) using upiPkg.TruncatePspHandle
//
// Returns the PI IDs for payer and payee VPAs, and any error that occurred
func CreateUpiPiForVpaDetails(
	ctx context.Context,
	piClient piPb.PiClient,
	parsedParticulars *notification.ParsedTxnParticulars,
	payerName string,
	payeeName string,
) (payerVpaPiId, payeeVpaPiId string, err error) {
	var createUpiPiErr error
	group := errgroup.New()

	if parsedParticulars.GetVpaDetails().GetPayerVpa() != "" {
		group.Go(func() error {
			var createPiErr error
			payerVpaPiId, createPiErr = createUpiPiInCreatedState(
				ctx,
				piClient,
				parsedParticulars.GetVpaDetails().GetPayerVpa(),
				parsedParticulars.GetVpaDetails().GetPayerName(),
				payerName,
			)
			if createPiErr != nil {
				payerVpaPiId = ""
			}
			return createPiErr
		})
	}

	if parsedParticulars.GetVpaDetails().GetPayeeVpa() != "" {
		group.Go(func() error {
			var createPiErr error
			payeeVpaPiId, createPiErr = createUpiPiInCreatedState(
				ctx,
				piClient,
				parsedParticulars.GetVpaDetails().GetPayeeVpa(),
				parsedParticulars.GetVpaDetails().GetPayeeName(),
				payeeName,
			)
			if createPiErr != nil {
				payeeVpaPiId = ""
			}
			return createPiErr
		})
	}

	if createUpiPiErr = group.Wait(); createUpiPiErr != nil {
		// Return the error but with empty PI IDs, so the caller can decide how to handle it
		return "", "", fmt.Errorf("error in creating vpa pi: %w", createUpiPiErr)
	}

	return payerVpaPiId, payeeVpaPiId, nil
}
