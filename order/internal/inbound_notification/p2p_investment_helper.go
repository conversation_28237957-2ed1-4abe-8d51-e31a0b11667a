package inbound_notification

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	paymentNotificationPb "github.com/epifi/gamma/api/order/payment/notification"
	p2pPb "github.com/epifi/gamma/api/p2pinvestment"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	timelinePb "github.com/epifi/gamma/api/timeline"
	"github.com/epifi/gamma/pkg/pay"
)

var (
	ErrNotRelatedToP2PInvestment = errors.New("payment is not related to p2p investment")
)

// nolint:funlen
func (p *Processor) CheckAndProcessP2PInvestmentPayment(ctx context.Context, actorID, internalSavingsAccountId string,
	txnDetails *paymentNotificationPb.TransactionDetails, parsedParticulars *paymentNotificationPb.ParsedTxnParticulars,
	source paymentPb.TransactionDetailedStatus_DetailedStatus_API) error {
	// we need to process loan category as well because we cannot differentiate between p2p and loan since we have
	// the same liquioans vendor and the txn particular is same for both
	// TODO (sharath): identify a proper fix for this issue and remove actor hardcoding as well
	if parsedParticulars.GetTxnCategory() != paymentNotificationPb.ParsedTxnParticulars_P2P_INVESTMENT_CREDIT &&
		parsedParticulars.GetTxnCategory() != paymentNotificationPb.ParsedTxnParticulars_LOAN_DISBURSEMENT &&
		parsedParticulars.GetOtherActorName() != pay.LiquiloansActorName {
		return ErrNotRelatedToP2PInvestment
	}
	p2pReq := &p2pPb.IsP2PInboundNotificationRequest{
		ActorId:          actorID,
		Amount:           txnDetails.GetAmount(),
		CreditedAt:       txnDetails.GetTimestamp(),
		CreditIdentifier: parsedParticulars.GetUtr(),
	}
	if txnDetails.GetNotificationEventType() == paymentNotificationPb.TransactionDetails_DEBIT {
		p2pReq.IsDebitTransaction = true
	}

	p2pRes, err := p.p2pInvestmentClient.IsP2PInboundNotification(ctx, p2pReq)
	if te := epifigrpc.RPCError(p2pRes, err); te != nil {
		return fmt.Errorf("failed to check if inbound belongs to p2p, %w", te)
	}

	// Only process the inbound if it belongs to p2p investment
	if !p2pRes.GetIsP2PInbound() {
		logger.SecureInfo(ctx, commonvgpb.Vendor_VENDOR_UNSPECIFIED, "unable to map credit notification",
			zap.String("txn_details", txnDetails.String()), zap.String("parsed_particulars", parsedParticulars.String()))
		return ErrNotRelatedToP2PInvestment
	}
	// Step 1: resolve internal actor's PI for which notifications is received
	internalPiId, internalActorName, err := p.piProcessor.ResolveInternalActorPiDetails(ctx, internalSavingsAccountId,
		txnDetails.GetAccountNumber(), false, commonvgpb.Vendor_FEDERAL_BANK, source, "")
	if err != nil {
		return fmt.Errorf("failed to resolve internal PI details: %w", err)
	}

	// Step 2: Resolve other actor's PI on best effort basis from particulars, else fallback to generic PI
	// PI from which money was transferred from, in case of CREDIT event,
	// or PI to which money was transferred to, in case of DEBIT event.
	otherPiId, otherActorName, _, err := p.piProcessor.ResolveOtherActorPiDetails(ctx, parsedParticulars, txnDetails,
		actorID, piPb.Ownership_EPIFI_TECH)
	if err != nil {
		return fmt.Errorf("error fetching other pi details from parsed particulars: %w", err)
	}

	// Step 3: Resolve timeline. If order event is of type credit
	//    a. Resolve actorFrom using ActorService.ResolveActorFrom(actor_to, pi_from). internal_actor_id is actorTo.
	// else if order event is of type debit
	//    a. Resolve actorTo using ActorService.ResolveActorTo(actor_from, pi_to). internal_actor_id is actorFrom.
	_, fromPiId, _, toPiId, err := p.timelineProcessor.ResolveTimeline(
		ctx,
		actorID,
		internalPiId,
		internalActorName,
		otherPiId,
		otherActorName,
		txnDetails.GetNotificationEventType() == paymentNotificationPb.TransactionDetails_CREDIT,
		timelinePb.Ownership_EPIFI_TECH,
	)
	if err != nil {
		return err
	}

	// Step 4: Get Order by client request ID returned from p2p domain
	order, orderErr := p.orderDao.GetByClientReqId(ctx, p2pRes.GetOrderClientReqId())
	if orderErr != nil {
		return fmt.Errorf("failed to fetch order by client request id, %v, %w", orderErr, epifierrors.ErrTransient)
	}

	txnDetailedStatus := &paymentPb.TransactionDetailedStatus{DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
		{
			CreatedAt: timestampPb.Now(),
			Api:       source,
			State:     paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
		},
	}}
	// Step 5: Create a transaction and link p2p order with it
	txn, err := p.createTxnAndLinkOrder(ctx, txnDetails, parsedParticulars, fromPiId, toPiId, parsedParticulars.GetUtr(),
		order.GetId(), commonvgpb.Vendor_FEDERAL_BANK, paymentPb.TransactionStatus_SUCCESS, txnDetailedStatus, nil, nil)
	if err != nil {
		return fmt.Errorf("failed to create p2p transaction, %w", err)
	}

	orderWithTxn := &orderPb.OrderWithTransactions{
		Order:        order,
		Transactions: []*paymentPb.Transaction{txn}}
	_, err = pay.PublishOrderUpdate(ctx, p.orderEventPublisher, orderWithTxn, p.piClient)
	if err != nil {
		return fmt.Errorf("failed to publish order update event: %s: %w", err.Error(), epifierrors.ErrTransient)
	}

	// Step 6: store transaction in p2p investment transactions
	inbResp, err := p.p2pInvestmentClient.StoreInboundNotificationTransactionInformation(ctx, &p2pPb.StoreInboundNotificationTransactionInformationRequest{
		OrderClientReqId: p2pRes.GetOrderClientReqId(),
		Utr:              txn.GetUtr(),
		TxnCreditedAt:    txn.GetCreditedAt(),
	})
	if te := epifigrpc.RPCError(inbResp, err); te != nil {
		return fmt.Errorf("failed to store transaction information in p2p transactions, %w", te)
	}
	return nil
}
