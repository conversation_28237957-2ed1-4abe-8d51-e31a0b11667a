//go:generate mockgen -source=$PWD/processor.go -destination=$PWD/mocks/mock_processor.go -package=mocks

// nolint:dupl,funlen
package inbound_notification

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/google/wire"
	"github.com/samber/lo"
	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/mask"
	"github.com/epifi/be-common/pkg/queue"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	depositPb "github.com/epifi/gamma/api/deposit"
	mPb "github.com/epifi/gamma/api/merchant"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	paymentNotificationPb "github.com/epifi/gamma/api/order/payment/notification"
	parsedTxnEnums "github.com/epifi/gamma/api/order/payment/notification/enums"
	p2pPb "github.com/epifi/gamma/api/p2pinvestment"
	parserPb "github.com/epifi/gamma/api/parser"
	payPb "github.com/epifi/gamma/api/pay"
	"github.com/epifi/gamma/api/pay/internationalfundtransfer"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	timelinePb "github.com/epifi/gamma/api/timeline"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/upi"
	upiConsumerPb "github.com/epifi/gamma/api/upi/consumer"
	merchantResolutionPb "github.com/epifi/gamma/api/vendorgateway/merchantresolution"
	config "github.com/epifi/gamma/order/config/genconf"
	"github.com/epifi/gamma/order/dao"
	errors2 "github.com/epifi/gamma/order/errors"
	payEvents "github.com/epifi/gamma/order/events"
	"github.com/epifi/gamma/order/internal"
	merchantProcessor "github.com/epifi/gamma/order/internal/merchant"
	piProcessor "github.com/epifi/gamma/order/internal/pi"
	timelineProcessor "github.com/epifi/gamma/order/internal/timeline"
	upiProcessor "github.com/epifi/gamma/order/internal/upi"
	"github.com/epifi/gamma/order/metrics"
	orderTypes "github.com/epifi/gamma/order/types"
	"github.com/epifi/gamma/order/wire/types"
	"github.com/epifi/gamma/pkg/feature/release"
	"github.com/epifi/gamma/pkg/pay"
	upiPkg "github.com/epifi/gamma/pkg/upi"
	vendorPkg "github.com/epifi/gamma/pkg/vendors/federal"
)

const OFF_APP = "Off-app"

var (

	// adds a keyword to transaction unique identifiers to avoid violation on unique constraints while creating 2nd order
	// and transaction in case of extended PI payments
	convertToExtendedIdentifier = func(id string) string {
		return fmt.Sprintf("EXTENDED:%s", id)
	}

	// offAppTxnStatusToOrderStatusMap maps the status of off-app Txn to Order status.
	// No other txn status apart from the given ones are allowed during creation of off-app txn and order in the system.
	offAppTxnStatusToOrderStatusMap = map[paymentPb.TransactionStatus]orderPb.OrderStatus{
		paymentPb.TransactionStatus_SUCCESS:     orderPb.OrderStatus_PAID,
		paymentPb.TransactionStatus_FAILED:      orderPb.OrderStatus_PAYMENT_FAILED,
		paymentPb.TransactionStatus_IN_PROGRESS: orderPb.OrderStatus_IN_PAYMENT,
		paymentPb.TransactionStatus_UNKNOWN:     orderPb.OrderStatus_IN_PAYMENT,
	}

	// offAppTxnTerminalStatuses contains the terminal statuses which can be assigned to off-app txns during creation in the system
	offAppTxnTerminalStatuses = []paymentPb.TransactionStatus{paymentPb.TransactionStatus_SUCCESS, paymentPb.TransactionStatus_FAILED}
)

var WireSet = wire.NewSet(NewProcessor, wire.Bind(new(InboundNotificationProcessor), new(*Processor)))

// Args a list of common args required for executing notification processing related business logic
type ProcessingArgs struct {
	Td              *paymentNotificationPb.TransactionDetails
	ParsedDetails   *paymentNotificationPb.ParsedTxnParticulars
	PartnerBank     commonvgpb.Vendor
	InternalAcctId  string
	InternalActorId string
}

func NewProcessingArgs(
	td *paymentNotificationPb.TransactionDetails,
	parsedDetails *paymentNotificationPb.ParsedTxnParticulars,
	partnerBank commonvgpb.Vendor,
	savingsAcctId string,
	internalActorId string,
) *ProcessingArgs {
	return &ProcessingArgs{
		Td:              td,
		ParsedDetails:   parsedDetails,
		PartnerBank:     partnerBank,
		InternalAcctId:  savingsAcctId,
		InternalActorId: internalActorId,
	}
}

type InboundNotificationProcessor interface {
	// ProcessOffAppPayment processes off-app payments received from vendor and creates order & transactions based on parsed information.
	//
	// It also supports OrderWorkflow under which the Order has to be created. If not passed, NO_OP should be expected by-default.
	// It should return the recorded Order and Txn in the system in successful scenario.
	//
	// Note:
	// 1. If OrderWorkflow passed is NO_OP, the Txn-Status passed should be one of the terminal ones.
	ProcessOffAppPayment(
		ctx context.Context,
		internalSavingsAccountId, internalActorId, accountType string,
		txnDetails *paymentNotificationPb.TransactionDetails, parsedParticulars *paymentNotificationPb.ParsedTxnParticulars,
		partnerBank commonvgpb.Vendor,
		txnStatus paymentPb.TransactionStatus, txnDetailedStatus *paymentPb.TransactionDetailedStatus,
		orderWorkflow orderPb.OrderWorkflow,
		vendorNotificationTimestamp *timestampPb.Timestamp,
		isLastAttempt bool,
		attemptRemitterInfoBackill bool,
	) (*orderPb.Order, *paymentPb.Transaction, error)

	// CheckAndProcessDepositPayment checks if the off app payment is related to a deposit account or not.
	// If YES, it forwards the processing request to deposit domain's process CreateDeposit/CloseDeposit InboundTxn RPCs.
	// If No, then ErrNotRelatedToDeposit is returned
	CheckAndProcessDepositPayment(
		ctx context.Context,
		txnDetails *paymentNotificationPb.TransactionDetails,
		parsedTxnParticulars *paymentNotificationPb.ParsedTxnParticulars,
		partnerBank commonvgpb.Vendor,
		source paymentPb.TransactionDetailedStatus_DetailedStatus_API,
	) error

	// ProcessDepositInboundNotifications redirects the deposit account inbound notifications processing to deposit service
	// and returns the error returned by deposit domain service.
	ProcessDepositInboundNotifications(
		ctx context.Context,
		txnDetails *paymentNotificationPb.TransactionDetails,
		partnerBank commonvgpb.Vendor,
		parsedDetails *paymentNotificationPb.ParsedTxnParticulars,
		source paymentPb.TransactionDetailedStatus_DetailedStatus_API,
	) error

	// Checks if the notification is for dynamic VPA
	// if not returns ErrNotDynamicVPANotification
	// if yes creates a txn for second leg (payment from epifi pool account to user's savings account )
	//
	// Note - there can be cases where the first leg transaction not created/not terminal, but that will be taken care by
	// central order orchestrator
	CheckAndProcessAddFundsPayment(
		ctx context.Context,
		td *paymentNotificationPb.TransactionDetails,
		parsedDetails *paymentNotificationPb.ParsedTxnParticulars,
		partnerBank commonvgpb.Vendor,
		internalSavingsAccountId string,
		source paymentPb.TransactionDetailedStatus_DetailedStatus_API,
		actor string,
	) error

	// EnrichExistingTransaction updates existing transaction fields based on the notification data points.
	// It optionally published event to order orchestration queue, in case transaction status is updated
	// since, enrichment is done on best effort basis, caller can also choose to strictly exclude
	// certain fields from being enriched using enrichmentExclusions
	EnrichExistingTransaction(
		ctx context.Context,
		txn *paymentPb.Transaction,
		td *paymentNotificationPb.TransactionDetails,
		parsedDetails *paymentNotificationPb.ParsedTxnParticulars,
		updatedTxnStatus paymentPb.TransactionStatus,
		source paymentPb.TransactionDetailedStatus_DetailedStatus_API,
		order *orderPb.Order,
		enrichmentExclusions []paymentPb.TransactionFieldMask,
		actorId, savingsAccountId string,
	) error

	// IsExtendedPiPayment checks if the notification corresponds to an extended PI payment
	IsExtendedPiPayment(ctx context.Context, txn *paymentPb.Transaction, isCredit bool) (bool, error)

	// We need to handle payment to and from extended PI based on the debit/credit notifications that we receive for fi account.
	//
	// Case 1: Transaction done from Internal VPA to Extended VPA
	//
	// Txn is created while initiating a payment from the App.
	// a) On receiving Debit notification we do No-Op as pi from is internal in the deduped txn.
	// b) On receiving Credit notification post dedupe if pi to is either generic or external then we record another
	// 	  order and transaction to balance the ledger for the payee.
	//
	//	  The UTR, in this case, will be appended with the `EXTENDED` keyword while storing in the DB and pi from
	//	  will be a generic PI with internal payer actor's name.
	//
	// Case 2: Transaction done from Extended VPA to Internal VPA
	//
	// Txn is created from ReqAuth Flow.
	// a) On receiving Debit notification,  post dedupe if pi from is external then we record another order and
	// 	  transaction to balance the ledger for the payer
	//
	// 	  The UTR in this case will be appended with the `EXTENDED` keyword while storing in the DB and pi to will be
	// 	  generic PI with internal payee actor name.
	// b) On receiving Credit notification we do No-Op as pi to is internal in the deduped txn.
	//
	// Case 3: Transaction done from Extended VPA to Account
	//
	// a) On receiving Debit notification create T1 where Pi from will be the payer internal account and Pi to will be
	// 	  internal payee name-based generic PI. This is again given the fact that we can parser internal payee account
	// 	  from the particulars. If not we will use a generic PI with Others name.
	// b) On receiving Credit notification create T2 where pi from will be external VPA given we can parse from particulars
	// 	  if not we will use generic PI and pi to will be internal account PI of payee.
	// c) Depending on which notification came first we will append the keyword `EXTENDED`for store the data in the DB.
	//
	//
	// Case 4: Transaction done from Account to Account
	//
	// a) On receiving Debit notification create T1 where Pi from will be the payer internal account and Pi to will be
	// 	  internal payee name-based generic PI. This is again given the fact that we can parser internal payee account
	// 	  from the particulars. If not we will use a generic PI with Others name.
	// b) On receiving Credit notification create T2 where Pi to will be the payee internal account and Pi from will be
	// 	  internal payer name-based generic PI. This is again given the fact that we can parser internal payer account
	// 	  from the particulars. If not we will use a generic PI with Others name
	// c) Depending on which notification came first we will append the keyword `EXTENDED`for store the data in the DB.
	//
	// The same can be extended to Card to account payment. In future when we receive name and other details of the other actor.
	// The only thing that will change will be name in the generic actor.
	//
	// Ref- https://docs.google.com/document/d/1_2AoB_cMH1V2QSb67_1FxqIE1dBwyjqlf_dei_-mOjg/edit?usp=sharing
	//
	// GetUpdatedParsedDetailsForExtendedPayment updates parsed particulars in case of extended PI payments
	GetUpdatedDetailsForExtendedPayment(
		ctx context.Context,
		txn *paymentPb.Transaction,
		isDebit bool,
		parsedDetails *paymentNotificationPb.ParsedTxnParticulars,
		partnerBank commonvgpb.Vendor,
	) *paymentNotificationPb.ParsedTxnParticulars

	// MatchAndFetchTransaction checks for an existing transactions in the system based on the following:
	// 1. Dedupe ID
	// 2. Request ID
	// 3. UTR
	//
	// The implementation should return the matched transaction if found.
	// The implementation should return error which indicates no txn found, internal server errors or invalid arguments passed.
	// The implementation should return errors explicitly stating whether they are retryable or not.
	MatchAndFetchTransaction(ctx context.Context, actorId string, transactionDetails *paymentNotificationPb.TransactionDetails, parsedTxnParticulars *paymentNotificationPb.ParsedTxnParticulars) (*paymentPb.Transaction, error)

	// ProcessDeDupedPayment processes de-duped payment transaction and performs a sequence of checks.
	ProcessDeDupedPayment(
		ctx context.Context,
		notifArgs *ProcessingArgs,
		txn *paymentPb.Transaction,
		source paymentPb.TransactionDetailedStatus_DetailedStatus_API,
		actorId string,
	) (createNewOrder bool, isReversal bool, err error)

	// IsExtendedAddFundsPayment
	// An add funds initiated from an external vpa linked to a Fi account underneath is termed as extended
	// add funds payment
	// this method checks if payment notification belongs to extended add funds payment case or not
	IsExtendedAddFundsPayment(ctx context.Context, notifArgs *ProcessingArgs) bool

	// GetUpdatedDetailsForExtendedAddFundsPayment updates the necessary identifiers in the parsed details to make sure
	// extended transaction created doesn't collide with the 1st leg debit transaction in add funds.
	// The collision can happen since utr for both 1st leg txn in add funds and extended vpa debit will be same.
	GetUpdatedDetailsForExtendedAddFundsPayment(ctx context.Context, notifArgs *ProcessingArgs, piId string) *paymentNotificationPb.ParsedTxnParticulars

	// CheckAndProcessP2PInvestmentPayment checks if the inbound notification is received for a P2P transaction. If yes, it will
	// redirect the processing to p2p_investment service.
	CheckAndProcessP2PInvestmentPayment(ctx context.Context, actorId, internalSavingsAccountId string, txn *paymentNotificationPb.TransactionDetails,
		parsedDetails *paymentNotificationPb.ParsedTxnParticulars, source paymentPb.TransactionDetailedStatus_DetailedStatus_API) error

	// CheckAndProcessOnAppEnachExecution checks if the inbound notification is received for an onapp enach execution. If yes, it will
	// create a txn for the received notification, link it to the already created enach execution order and update the order status to terminal state.
	CheckAndProcessOnAppEnachExecution(ctx context.Context, internalSavingsAccountId string, txnDetails *paymentNotificationPb.TransactionDetails,
		parsedDetails *paymentNotificationPb.ParsedTxnParticulars, source paymentPb.TransactionDetailedStatus_DetailedStatus_API) error

	// CheckAndProcessUSStocksTransactions checks if the inbound notification is received for a US Stocks transaction. If yes, it will
	// redirect the processing to ProcessUSStocksNotification service in pay service.
	CheckAndProcessUSStocksTransactions(ctx context.Context, internalSavingsAccountId, internalActorId string,
		txnDetails *paymentNotificationPb.TransactionDetails, parsedParticulars *paymentNotificationPb.ParsedTxnParticulars,
		partnerBank commonvgpb.Vendor, txnStatus paymentPb.TransactionStatus, txnDetailedStatus *paymentPb.TransactionDetailedStatus) error
}

type Processor struct {
	depositClient                       depositPb.DepositClient
	txnDao                              dao.TransactionDao
	orderDao                            dao.OrderDao
	orderEventPublisher                 orderTypes.OrderUpdateEventPublisher
	tncEventPublisher                   orderTypes.EventsCompletedTnCPublisher
	eventBroker                         events.Broker
	timelineProcessor                   timelineProcessor.TimelineProcessor
	piProcessor                         piProcessor.PiProcessor
	upiProcessor                        upiProcessor.UpiProcessor
	orderProcessor                      internal.OrderProcessor
	orderOrchestrationPublisher         orderTypes.OrderOrchestrationPublisher
	orderNotificationPublisher          orderTypes.OrderNotificationPublisher
	orderSearchPublisher                orderTypes.OrderSearchPublisher
	merchantClient                      mPb.MerchantServiceClient
	p2pInvestmentClient                 p2pPb.P2PInvestmentClient
	conf                                *config.Config
	orderVpaVerificationPublisher       orderTypes.OrderVpaVerificationPublisher
	txnNotificationPublisher            orderTypes.TxnNotificationPublisher
	celestialProcessor                  internal.CelestialProcessor
	iftClient                           internationalfundtransfer.InternationalFundTransferClient
	merchantResolutionVgClient          merchantResolutionPb.MerchantResolutionClient
	parserClient                        parserPb.ParserClient
	evaluator                           release.IEvaluator
	enachSvcClient                      enachPb.EnachServiceClient
	recurringPaymentSvcClient           rpPb.RecurringPaymentServiceClient
	txnDetailedStatusUpdateSnsPublisher types.TxnDetailedStatusUpdateSnsPublisher
	piClient                            piPb.PiClient
	payClient                           payPb.PayClient
	merchantProcessor                   merchantProcessor.MerchantProcessor
}

var _ InboundNotificationProcessor = &Processor{}

func NewProcessor(
	depositClient depositPb.DepositClient,
	txnDao dao.TransactionDao,
	orderDao dao.OrderDao,
	orderEventPublisher orderTypes.OrderUpdateEventPublisher,
	tncEventPublisher orderTypes.EventsCompletedTnCPublisher,
	eventBroker events.Broker,
	timelineHelperSvc timelineProcessor.TimelineProcessor,
	piProcessor piProcessor.PiProcessor,
	upiProcessor upiProcessor.UpiProcessor,
	orderProcessor internal.OrderProcessor,
	orderOrchestrationPublisher orderTypes.OrderOrchestrationPublisher,
	orderNotificationPublisher orderTypes.OrderNotificationPublisher,
	orderSearchPublisher orderTypes.OrderSearchPublisher,
	merchantClient mPb.MerchantServiceClient,
	p2pInvestmentClient p2pPb.P2PInvestmentClient,
	orderVpaVerificationPublisher orderTypes.OrderVpaVerificationPublisher,
	txnNotificationPublisher orderTypes.TxnNotificationPublisher,
	celestialProcessor internal.CelestialProcessor,
	iftClient internationalfundtransfer.InternationalFundTransferClient,
	merchantResolutionVgClient merchantResolutionPb.MerchantResolutionClient,
	parserClient parserPb.ParserClient,
	conf *config.Config,
	evaluator release.IEvaluator,
	enachSvcClient enachPb.EnachServiceClient,
	recurringPaymentSvcClient rpPb.RecurringPaymentServiceClient,
	txnDetailedStatusUpdateSnsPublisher types.TxnDetailedStatusUpdateSnsPublisher,
	piClient piPb.PiClient,
	payClient payPb.PayClient,
	merchantProcessor merchantProcessor.MerchantProcessor,
) *Processor {
	return &Processor{
		depositClient:                       depositClient,
		txnDao:                              txnDao,
		orderDao:                            orderDao,
		orderEventPublisher:                 orderEventPublisher,
		tncEventPublisher:                   tncEventPublisher,
		eventBroker:                         eventBroker,
		timelineProcessor:                   timelineHelperSvc,
		piProcessor:                         piProcessor,
		upiProcessor:                        upiProcessor,
		orderProcessor:                      orderProcessor,
		orderOrchestrationPublisher:         orderOrchestrationPublisher,
		orderNotificationPublisher:          orderNotificationPublisher,
		orderSearchPublisher:                orderSearchPublisher,
		merchantClient:                      merchantClient,
		p2pInvestmentClient:                 p2pInvestmentClient,
		orderVpaVerificationPublisher:       orderVpaVerificationPublisher,
		txnNotificationPublisher:            txnNotificationPublisher,
		celestialProcessor:                  celestialProcessor,
		iftClient:                           iftClient,
		merchantResolutionVgClient:          merchantResolutionVgClient,
		parserClient:                        parserClient,
		conf:                                conf,
		evaluator:                           evaluator,
		enachSvcClient:                      enachSvcClient,
		recurringPaymentSvcClient:           recurringPaymentSvcClient,
		txnDetailedStatusUpdateSnsPublisher: txnDetailedStatusUpdateSnsPublisher,
		piClient:                            piClient,
		payClient:                           payClient,
		merchantProcessor:                   merchantProcessor,
	}
}

// ProcessOffAppPayment processes off-app payments received from vendor and creates order & transactions based on parsed information.
//
// It also supports OrderWorkflow under which the Order has to be created. If not passed, NO_OP will be assumed.
// It returns the recorded Order and Txn in the system in successful scenario.
//
// Note:
// 1. If OrderWorkflow passed is NO_OP, the Txn Status-passed should be one of the terminal ones.
// nolint:funlen
func (p *Processor) ProcessOffAppPayment(ctx context.Context, internalSavingsAccountId, internalActorId, accountType string,
	txnDetails *paymentNotificationPb.TransactionDetails, parsedParticulars *paymentNotificationPb.ParsedTxnParticulars,
	partnerBank commonvgpb.Vendor, txnStatus paymentPb.TransactionStatus, txnDetailedStatus *paymentPb.TransactionDetailedStatus,
	orderWorkflow orderPb.OrderWorkflow, vendorNotificationTimestamp *timestampPb.Timestamp, isLastAttempt, attemptRemitterInfoBackfill bool) (*orderPb.Order, *paymentPb.Transaction, error) {
	var (
		createdTransaction             = &paymentPb.Transaction{}
		createdOrder                   = &orderPb.Order{}
		internalPiId                   string
		internalActorName              string
		err                            error
		isPredictedMerchant            = false
		fetchedRemitterDetailsMetadata *paymentPb.EnrichedPiDetailsOfTransactingActors
	)

	// assign the default workflow NO_OP in case nothing is received from the caller.
	// this is done to keep the behaviour backward compatible as the callers were not expected to pass the Workflow earlier.
	orderWorkflow = lo.Ternary(orderWorkflow == orderPb.OrderWorkflow_ORDER_WORKFLOW_UNSPECIFIED, orderPb.OrderWorkflow_NO_OP, orderWorkflow)

	if err = p.validateForOffAppPaymentProcessing(orderWorkflow, txnStatus, txnDetails); err != nil {
		return nil, nil, fmt.Errorf("validation failure for off-app payment: %w", err)
	}

	args := NewProcessingArgs(txnDetails, parsedParticulars, partnerBank, internalSavingsAccountId, internalActorId)
	if p.IsExtendedAddFundsPayment(ctx, args) {
		poolAccPiId := pay.FederalPoolAccountPiId
		// checking if new add funds vpa is enabled for user
		isEnabled, procErr := p.upiProcessor.IsNewAddFundsVpaEnabledForActor(ctx, internalActorId)
		if procErr != nil {
			return nil, nil, fmt.Errorf("failed to check if new add funds is enabled for actor: %w", err)
		}
		if isEnabled {
			poolAccPiId = pay.FederalPoolAccountV1PiId
		}
		parsedParticulars = p.GetUpdatedDetailsForExtendedAddFundsPayment(ctx, args, poolAccPiId)
	}

	// TODO(brighu): add support to store resolve to merchant here
	// Step 1: resolve internal actor's PI for which notifications is received
	internalPiId, internalActorName, err = p.piProcessor.ResolveInternalActorPiDetails(ctx, internalSavingsAccountId, txnDetails.GetAccountNumber(),
		parsedParticulars.GetProtocol() == paymentPb.PaymentProtocol_CARD, partnerBank,
		txnDetailedStatus.GetDetailedStatusList()[0].GetApi(), parsedParticulars.GetMaskedCardNumber())
	if err != nil {
		return nil, nil, fmt.Errorf("failed to resolve internal PI details: %w", err)
	}

	// Step 2: Resolve other actor's PI on best effort basis from particulars, else fallback to generic PI
	// PI from which money was transferred from, in case of CREDIT event,
	// or PI to which money was transferred to, in case of DEBIT event.
	otherPiId, otherActorName, isMerchantPi, err := p.piProcessor.ResolveOtherActorPiDetails(ctx, parsedParticulars, txnDetails,
		internalActorId, piPb.Ownership_EPIFI_TECH)

	// Only set fetchedRemitterDetails in metadata for credit transactions with NEFT/RTGS protocol
	// Why?
	// - We need the transaction's point-in-time remitter details for salary team's analysis.
	//   Why point-in-time? Because payment gateways may transact with different names corresponding to the same account number and IFSC at different times.
	// - For debit transactions, the internal user is the remitter (whose details we already know).
	// - For credit transactions, we need to store the remitter details.
	// - For other transaction protocols (e.g., IMPS), we can't store remitter details for compliance reasons.
	if txnDetails.GetNotificationEventType() == paymentNotificationPb.TransactionDetails_CREDIT &&
		(parsedParticulars.GetProtocol() == paymentPb.PaymentProtocol_NEFT ||
			parsedParticulars.GetProtocol() == paymentPb.PaymentProtocol_RTGS) {
		fetchedRemitterDetailsMetadata = &paymentPb.EnrichedPiDetailsOfTransactingActors{}
		fetchedRemitterDetailsMetadata.PayerPiId = lo.Ternary(otherPiId != pay.GenericPiId, otherPiId, "")
		fetchedRemitterDetailsMetadata.PayerName = lo.Ternary(otherActorName != pay.DefaultActorName, otherActorName, "")
		if fetchedRemitterDetailsMetadata.GetPayerName() == "" && fetchedRemitterDetailsMetadata.GetPayerPiId() == "" {
			fetchedRemitterDetailsMetadata = nil
		}
	}

	switch {
	case (errors.Is(err, piProcessor.ErrorValidatingVpa) && isLastAttempt) || errors.Is(err, upiProcessor.PSPNotRegisterError):
		var createUpiPiErr error
		logger.Info(ctx, "validate address last attempt failed", zap.String(logger.ACTOR_ID_V2, internalActorId),
			zap.String(logger.PI_ID, internalPiId), zap.String(logger.VPA, mask.GetMaskedString(mask.MaskCharTillAtSign, parsedParticulars.GetVpa())))
		name := upiPkg.TruncatePspHandle(parsedParticulars.GetVpa())
		if name == "" {
			name = pay.DefaultActorName
		}

		otherPiId, createUpiPiErr = p.piProcessor.CreateUpiPi(
			ctx,
			name,
			parsedParticulars.GetVpa(),
			"",
			nil,
			piPb.Ownership_EPIFI_TECH,
			false,
		)
		if createUpiPiErr != nil {
			return nil, nil, fmt.Errorf("failed to create pi for vpa: %v %w", parsedParticulars.GetVpa(), err)
		}
		otherActorName = name
		logger.Info(ctx, "created pi in ProcessOffAppPayment processor", zap.String(logger.PI_ID, otherPiId))

		if !errors.Is(err, upiProcessor.PSPNotRegisterError) {
			_, err = p.orderVpaVerificationPublisher.PublishWithDelay(ctx, &upiConsumerPb.VerifyVpaRequest{
				UpiVpa:       parsedParticulars.GetVpa(),
				PayerActorId: internalActorId,
			}, p.conf.ReVerifyAddressDelay())
			if err != nil {
				return nil, nil, fmt.Errorf("failed to publish to vpa verification queue: %w", epifierrors.ErrInProgress)
			}
		}

	case errors.Is(err, piProcessor.ErrorFetchingRemitterInfo) && isLastAttempt:
		logger.Error(ctx, "failed to fetch the remitter info in all retries", zap.String(logger.PARTNER_TXN_ID, txnDetails.GetId()))
		otherActorAccountNumber := pay.SanitizeAccountNumber(parsedParticulars.GetAccount().GetAccountNumber())
		otherPi, createAccountPiErr := p.piProcessor.CreateAccountPi(
			ctx,
			otherActorAccountNumber,
			parsedParticulars.GetAccount().GetIfscCode(),
			"", parsedParticulars.GetOtherActorName(),
			parsedParticulars.GetAccount().GetPiType(),
			parsedParticulars.GetAccount().GetAccountType(),
			mask.MaskLastNDigits(otherActorAccountNumber, 3, ""),
			piPb.Ownership_EPIFI_TECH,
		)
		if createAccountPiErr != nil {
			logger.WarnWithCtx(ctx, "failed to create pi for account on best effort basis", zap.Error(createAccountPiErr), zap.String(logger.PARTNER_TXN_ID, txnDetails.GetId()))
		} else {
			otherPiId = otherPi.GetId()
			otherActorName = otherPi.GetName()
			// TODO: Remove this log after monitoring in prod
			logger.Info(ctx, "created account pi in ProcessOffAppPayment processor from parsed particulars", zap.String(logger.PI_ID, otherPiId))
		}
	case err != nil:
		return nil, nil, fmt.Errorf("error fetching other pi details from parsed particulars: %w", err)
	}

	// Step 3: Resolve timeline. If order event is of type credit
	//    a. Resolve actorFrom using ActorService.ResolveActorFrom(actor_to, pi_from). internal_actor_id is actorTo.
	// else if order event is of type debit
	//    a. Resolve actorTo using ActorService.ResolveActorTo(actor_from, pi_to). internal_actor_id is actorFrom.
	fromActorId, fromPiId, toActorId, toPiId, err := p.timelineProcessor.ResolveTimeline(
		ctx,
		internalActorId,
		internalPiId,
		internalActorName,
		otherPiId,
		otherActorName,
		txnDetails.GetNotificationEventType() == paymentNotificationPb.TransactionDetails_CREDIT,
		timelinePb.Ownership_EPIFI_TECH,
	)
	if err != nil {
		return nil, nil, fmt.Errorf("error in resolving timeline : %w", err)
	}

	var payerVpaPiId, payeeVpaPiId string
	if parsedParticulars.GetProtocol() == paymentPb.PaymentProtocol_UPI {
		// Extract the VPA without the PSP handle (e.g., "user" from "user@okaxis") to use as the name in the PI
		// The truncated VPA is used as the name field, while the full name from parsedParticulars is used as the verified name
		payerVpaPiId, payeeVpaPiId, err = pay.CreateUpiPiForVpaDetails(ctx, p.piClient, parsedParticulars,
			upiPkg.TruncatePspHandle(parsedParticulars.GetVpaDetails().GetPayerVpa()),
			upiPkg.TruncatePspHandle(parsedParticulars.GetVpaDetails().GetPayeeVpa()))
	}
	if err != nil {
		logger.Error(ctx, "failed to create vpa pi for vpa details", zap.Error(err))
	}

	clientReqId := generateOrderClientRequestId(parsedParticulars)

	if txnDetails.GetNotificationEventType() == paymentNotificationPb.TransactionDetails_CREDIT && parsedParticulars.GetProtocol().IsAccountPaymentProtocol() {
		var merchErr error
		isPredictedMerchant, merchErr = p.merchantProcessor.CheckNameIsMerchant(ctx, otherActorName)
		if merchErr != nil {
			logger.Error(ctx, "failed to check if name is merchant",
				zap.String("otherActorName", otherActorName),
				zap.Error(merchErr))
		}
	}

	// Step 4: create order and transaction in a db transaction block
	err = storagev2.RunCRDBTxn(ctx, func(txnCtx context.Context) error {
		var creationErr error
		createdOrder, createdTransaction, creationErr = p.createOffAppOrderAndTxn(txnCtx, parsedParticulars, txnDetails,
			fromActorId, fromPiId, toActorId, toPiId, clientReqId, partnerBank, isMerchantPi, isPredictedMerchant, txnStatus, txnDetailedStatus, orderWorkflow,
			&paymentPb.VpaDetails{
				PayerVpaPiId: payerVpaPiId,
				PayeeVpaPiId: payeeVpaPiId,
			},
			fetchedRemitterDetailsMetadata,
		)
		if creationErr != nil {
			return creationErr
		}

		return nil
	})
	if err != nil {
		return nil, nil, fmt.Errorf("error while creating order and transaction: %w", err)
	}

	// publish event if the notification is related to charges of any type
	p.publishEventForCharges(ctx, createdOrder, parsedParticulars, txnDetails, internalActorId)

	// Publish incoming credit event
	if txnDetails.IsCredit() {
		metrics.RecordOffAppCreditTxnCount(parsedParticulars.GetProtocol())
		//nocustomlint:goroutine
		// go eventsUtil.PublishIncomingCreditEvent(ctx, toActorId, txnDetails.Amount, p.conf.Events.IncomingCreditMaxPublishDelay, OFF_APP, p.tncEventPublisher)
	} else {
		metrics.RecordOffAppDebitTxnCount(parsedParticulars.GetProtocol())
	}

	// logging the transaction details for transaction related to miscellaneous charges
	if parsedParticulars.GetTxnCategory() == paymentNotificationPb.ParsedTxnParticulars_MISCELLANEOUS_CHARGES {
		logger.Info(ctx, fmt.Sprintf("Transaction particular matches miscellaneous charges parsing rule : %v", txnDetails.GetParticular()),
			zap.String(logger.PARTNER_TXN_ID, parsedParticulars.GetUniqPartnerRefId()),
			zap.String(logger.ACCOUNT_TYPE, accountType))
	}

	if orderPb.IsTagExist(createdOrder.GetTags(), orderPb.OrderTag_AMB_CHARGE) {
		logger.Info(
			ctx,
			"Amb non-maintenance charge applied for user",
			zap.String(logger.ORDER_ID, createdOrder.GetId()),
			zap.String(logger.ACTOR_ID_V2, internalActorId),
		)
	}

	logger.Debug(ctx, "Order and transaction successfully created",
		zap.String(logger.TXN_ID, createdTransaction.Id),
		zap.String(logger.ORDER_ID, createdOrder.Id),
		zap.String(logger.PARTNER_TXN_ID, txnDetails.Id))

	if attemptRemitterInfoBackfill && p.isRemitterInfoBackfillActive(ctx, internalActorId) {
		err = p.matchCriteriaAndInitiateRemitterInfoWorkflow(ctx, createdTransaction, createdOrder, otherPiId)
		if err != nil {
			// Not returning transient error here since the order and txn are already created. Even if we do return, the
			// consumer would retry the inbound-notification and return early upon dedupe. Thus, execution won't reach this flow anyway.
			logger.Error(ctx, "unable to start remitter info workflow", zap.Error(err), zap.String(logger.TXN_ID, createdTransaction.GetId()))
		}
	}

	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		p.eventBroker.AddToBatch(ctx, payEvents.NewProcessInboundTxnOrder(internalActorId, createdTransaction.GetId(), p.conf.Secrets().Ids[cfg.EventSalt], txnDetails.GetTimestamp().AsTime(), vendorNotificationTimestamp.AsTime(), time.Now()))
	})

	return createdOrder, createdTransaction, nil
}

// validateForOffAppPaymentProcessing validates whether the received off-app payment can be processed or not.
//
// Checks/Conditions which can be used for early return/failure can be added here.
func (p *Processor) validateForOffAppPaymentProcessing(orderWorkflow orderPb.OrderWorkflow, txnStatus paymentPb.TransactionStatus, txnDetails *paymentNotificationPb.TransactionDetails) error {
	// guard rail to make sure no other Workflow gets used accidentally for off-app payments.
	if !lo.Contains([]orderPb.OrderWorkflow{orderPb.OrderWorkflow_NO_OP, orderPb.OrderWorkflow_OFF_APP_UPI}, orderWorkflow) {
		return fmt.Errorf("unsupported Order Workflow passed for Processing Off App Payment. Workflow: %s, err: %w", orderWorkflow.String(), epifierrors.ErrPermanent)
	}

	// in case of NO_OP, we allow processing only if the txn is supposed to be created in one of the terminal statuses.
	if orderWorkflow == orderPb.OrderWorkflow_NO_OP && !lo.Contains(offAppTxnTerminalStatuses, txnStatus) {
		return fmt.Errorf("invalid combination of Order Workflow and Allowed Txn-Status. Workflow: %s, Txn-Status: %s, err: %w", orderWorkflow.String(), txnStatus.String(), epifierrors.ErrPermanent)
	}

	// off-app payment should be either a CREDIT or DEBIT type
	if txnDetails.GetNotificationEventType() == paymentNotificationPb.TransactionDetails_NOTIFICATION_TYPE_UNSPECIFIED {
		return fmt.Errorf("notification event type unspecified: %w", epifierrors.ErrPermanent)
	}

	return nil
}

// IsExtendedPiPayment checks if the notification corresponds to an extended PI payment
func (p *Processor) IsExtendedPiPayment(ctx context.Context, txn *paymentPb.Transaction, isCredit bool) (bool, error) {
	piId := txn.GetPiFrom()

	if isCredit {
		piId = txn.GetPiTo()
	}

	pi, err := p.piProcessor.GetPiById(ctx, piId)
	if err != nil {
		return false, err
	}

	return !pi.IsIssuedInternally(), nil
}

// GetUpdatedDetailsForExtendedPayment updates parsed particulars in case of extended PI payments
// (ignoring fun len due to extensive documentation)
// nolint:funlen
func (p *Processor) GetUpdatedDetailsForExtendedPayment(
	ctx context.Context,
	txn *paymentPb.Transaction,
	isDebit bool,
	parsedDetails *paymentNotificationPb.ParsedTxnParticulars,
	partnerBank commonvgpb.Vendor,
) *paymentNotificationPb.ParsedTxnParticulars {
	otherInternalPi := txn.GetPiFrom()

	if isDebit {
		otherInternalPi = txn.GetPiTo()
	}

	parsedDetails.Utr = convertToExtendedIdentifier(parsedDetails.GetUtr())

	// overwrite pi to GenericAccount in case beneficiary VPA has a epiFi handle.
	// This can happen in following scenarios
	// 1) if a user pays from external PSP app using epiFi bank account(Extended VPA) to an internal epiFi handle.
	// 2) if a user pays from internal APP to an extended VPA which is linked to Fi account.
	// For case(1), since in the ReqAuth we already created transactions between external VPA to internal VPA, we
	// need to avoid establishing the link to same internal VPA.
	// This avoids double counting of same transaction in the payee aggregates and enables balancing off the
	// ledger for the payer.
	// For case(2), the transaction entry is created while transaction initiation. To balance of the credit in the
	// payee account we create 2nd transaction.
	if vpa := parsedDetails.GetVpa(); vpa != "" {
		pi, err := p.piProcessor.GetPiByVPA(ctx, vpa)
		if err != nil && !errors.Is(err, piProcessor.ErrPiNotFound) {
			parsedDetails.PiIdentifier = pay.GenericAccount()
		}
		if pi != nil && pi.IsIssuedInternally() {
			parsedDetails.PiIdentifier = pay.GenericAccount()
		}

	}

	// overwrite pi to GenericAccount in case other actor full account identifier is present in the particulars
	// and IFSC belongs to epiFi.
	// This can happen in following scenarios.
	// 1. If there is transfer from Extended VPA to an epiFi account (Applicable to debit notification)
	// In both case we create two transactions and orders via credit and debit notification linking
	// the other end to a generic PI. This is because in pt(1) we don't want to expose that Extended VPA
	// is linked to a epiFI account and also because.
	if acct := parsedDetails.GetAccount(); acct.GetPiType() == piPb.PaymentInstrumentType_BANK_ACCOUNT {
		checkIfIfscBelongsToEpifi, checkIfIFscBelongsToEpifiErr := vendorPkg.CheckIfIfscBelongsToEpifi(partnerBank, acct.GetIfscCode())
		if checkIfIFscBelongsToEpifiErr != nil {
			logger.Error(ctx, "failed to check if ifsc belongs to epifi", zap.Error(checkIfIFscBelongsToEpifiErr))
		}
		if checkIfIfscBelongsToEpifi {
			parsedDetails.PiIdentifier = pay.GenericAccount()
		}
	}

	// overwrite pi to GenericAccount in case of intra bank payment.
	// This can happen if two epiFi user transact using Partner mobile banking application.
	// Since, we dont get the other actor pi info in either credit or debit notifications
	// in this case, we need to create another order and transaction for same payment in order
	// to balance off transaction ledger for both parties.
	// TODO(nitesh): remove the check when we start getting other actor account details for intra bank
	if parsedDetails.GetProtocol() == paymentPb.PaymentProtocol_INTRA_BANK {
		parsedDetails.PiIdentifier = pay.GenericAccount()

		// for intra bank transactions cbd id is same for both credit and debit transactions
		parsedDetails.UniqPartnerRefId = convertToExtendedIdentifier(parsedDetails.GetUniqPartnerRefId())
	}

	// over write the actor name based on the verified user name from the other actor's PI.
	// This ensures all the transaction are accumulated in a single timeline.
	if parsedDetails.OtherActorName == "" || parsedDetails.OtherActorName == pay.DefaultActorName {
		otherActorInternalPI, err := p.piProcessor.GetPiById(ctx, otherInternalPi)
		if err != nil {
			logger.Error(ctx, "failed to get pi to", zap.String(logger.PI_ID, txn.GetPiFrom()),
				zap.Error(err))
			return parsedDetails
		}

		parsedDetails.OtherActorName = otherActorInternalPI.GetName()
	}

	return parsedDetails
}

// createOffAppOrderAndTxn creates order and txn in the database based on the txn-details and parsed-txn-particulars
// received from the vendor.
//
// How has it evolved?
// 1. It assumed the OrderWorkflow to be NO_OP since that was the only way via which the off-app txns landed in our system.
// 2. Over time, we have added OFF_APP_UPI workflow as well to treat the off-app UPI txns a bit differently.
// nolint:funlen
func (p *Processor) createOffAppOrderAndTxn(
	ctx context.Context,
	parsedParticulars *paymentNotificationPb.ParsedTxnParticulars,
	txnDetails *paymentNotificationPb.TransactionDetails,
	fromActorId, fromPiId, toActorId, toPiId, clientReqId string, partnerBank commonvgpb.Vendor, isMerchantPayment bool,
	isPredictedMerchant bool,
	txnStatus paymentPb.TransactionStatus, txnDetailedStatus *paymentPb.TransactionDetailedStatus,
	orderWorkflow orderPb.OrderWorkflow,
	vpaMetadata *paymentPb.VpaDetails,
	fetchedRemitterDetailsMetadata *paymentPb.EnrichedPiDetailsOfTransactingActors,
) (*orderPb.Order, *paymentPb.Transaction, error) {

	utr := parsedParticulars.GetUtr()

	provenance := pay.TxnCategoryToProvenanceMap[parsedParticulars.GetTxnCategory()]

	if parsedParticulars.GetProtocol() == paymentPb.PaymentProtocol_CARD &&
		(provenance == orderPb.OrderProvenance_POS || provenance == orderPb.OrderProvenance_ECOMM) {
		isMerchantPayment = true
	}

	var orderTags []orderPb.OrderTag

	orderTags = pay.BuildOrderTagList(parsedParticulars, isMerchantPayment, isPredictedMerchant)

	orderStatus, ok := offAppTxnStatusToOrderStatusMap[txnStatus]
	if !ok {
		return nil, nil, fmt.Errorf("failed to fetch order-status from txn-status: %s: %w", txnStatus.String(), epifierrors.ErrPermanent)
	}

	// Step 1: Create Order
	createdOrder, err := p.orderDao.Create(ctx, &orderPb.Order{
		FromActorId:  fromActorId,
		ToActorId:    toActorId,
		ClientReqId:  clientReqId,
		Workflow:     orderWorkflow,
		Status:       orderStatus,
		OrderPayload: nil,
		Provenance:   provenance,
		Amount:       txnDetails.GetAmount(),
		Tags:         orderTags,
	}, commontypes.Ownership_EPIFI_TECH)
	if storagev2.IsDuplicateRowError(err) {
		return nil, nil, fmt.Errorf("failed to create order: %s: %w", err.Error(), epifierrors.ErrPermanent)
	}

	if err != nil {
		return nil, nil, fmt.Errorf("failed to create order: %s: %w", err.Error(), epifierrors.ErrTransient)
	}

	// Step 2: Create Transaction
	createdTransaction, err := p.createTxnAndLinkOrder(ctx, txnDetails, parsedParticulars, fromPiId, toPiId, utr,
		createdOrder.GetId(), partnerBank, txnStatus, txnDetailedStatus, vpaMetadata, fetchedRemitterDetailsMetadata)
	switch {
	case errors.Is(err, epifierrors.ErrPermanent):
		return nil, nil, fmt.Errorf("failed to create transaction and link order (permanent failure): %s, %w", parsedParticulars.GetUniqPartnerRefId(), err)
	case err != nil:
		return nil, nil, fmt.Errorf("failed to create transaction and link order: %s: %s: %w", parsedParticulars.GetUniqPartnerRefId(), err.Error(), epifierrors.ErrTransient)
	}

	if len(txnDetailedStatus.GetDetailedStatusList()) != 0 {
		metrics.RecordOffAppTxnCreation(txnDetailedStatus.GetDetailedStatusList()[0].GetApi().String(), metrics.OffAppTransaction)
		metrics.RecordOffAppTxnOrderCreationDuration(parsedParticulars.GetProtocol().String(), time.Since(txnDetails.GetTimestamp().AsTime()))
	}

	//nocustomlint:goroutine
	go p.eventBroker.AddToBatch(
		epificontext.WithEventAttributes(ctx),
		payEvents.NewTransactionParsed(fromActorId, payEvents.Payer, "", "", "",
			createdTransaction.GetId(), payEvents.Notification, createdTransaction.GetParticulars(),
			parsedParticulars.String(), payEvents.Success, time.Now(), p.conf.Secrets().Ids[cfg.EventSalt],
		))
	// Step 3: Publish Order update event
	orderWithTxn := &orderPb.OrderWithTransactions{
		Order:        createdOrder,
		Transactions: []*paymentPb.Transaction{createdTransaction}}

	_, err = pay.PublishOrderUpdate(ctx, p.orderEventPublisher, orderWithTxn, p.piClient)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to publish order update event: %s: %w", err.Error(), epifierrors.ErrTransient)
	}

	// todo (rohan): should we relax the condition here on Status? Ideally no since the event will lose its significance.
	if createdTransaction.GetStatus() == paymentPb.TransactionStatus_SUCCESS && txnDetails.IsDebit() {
		//nocustomlint:goroutine
		go p.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx),
			payEvents.NewDebitTransactions(createdOrder.GetFromActorId(), createdOrder.GetFromActorId(), createdTransaction.GetId(), createdTransaction.GetPaymentProtocol().String(),
				createdTransaction.GetStatus().String(), createdTransaction.GetExecutionTS().AsTime()))
	}

	return createdOrder, createdTransaction, nil
}

// dedupeByUTR dedupes a transaction by utr
func (p *Processor) dedupeByUTR(ctx context.Context, amount *moneyPb.Money, utr, actorId string) (*paymentPb.Transaction, error) {
	var txn *paymentPb.Transaction
	ordersWithTxn, err := p.orderDao.GetByUtrWithTransactionsV1(ctx, utr)
	switch {
	case err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound):
		return nil, fmt.Errorf("error while fetching transactio for UTR: %s, err: %s, queueErr: %w",
			utr, err.Error(), queue.ErrTransient)
	case err == nil:
		logger.Info(ctx, "transaction already present for off app payment", zap.String(logger.UTR, utr))

		// In ideal case one transaction should have unique utr, but we have observed that conflicting utr exist.
		// We are adding a second level check to filter only those transactions which belongs to current user and
		// amount is same. If we still not able to find unique transaction, we will log the error and get an alert on top of it.
		// After getting alert we need to identify another parameter to add for uniqueness.
		txn, _, err = pay.FilterTxnOnActorAndAmountForUtr(ordersWithTxn, amount, utr, actorId)
		if err != nil {
			logger.Error(ctx, "more than one transaction found for utr and actor while de-duping refund/reversal", zap.String(logger.UTR, utr))
			metrics.RecordUtrCollisionForSameActorAndAmount()
			return nil, err
		}
		if txn != nil {
			logger.Info(ctx, "one transaction found for utr and actor while de-duping refund/reversal", zap.String(logger.UTR, utr), zap.String(logger.TXN_ID, txn.GetId()))
		}
		return txn, nil
	}
	return nil, nil
}

// nolint:funlen
// createTxnAndLinkOrder method to create a transaction record and map it to an order ID.
// TODO(pay team): club this method with createTransactionForAddFunds method to keep txn creation logic stays at a single place
func (p *Processor) createTxnAndLinkOrder(ctx context.Context, txnDetails *paymentNotificationPb.TransactionDetails,
	parsedParticulars *paymentNotificationPb.ParsedTxnParticulars, fromPiId, toPiId, utr, orderId string, partnerBank commonvgpb.Vendor,
	txnStatus paymentPb.TransactionStatus, txnDetailedStatus *paymentPb.TransactionDetailedStatus, vpaMetadata *paymentPb.VpaDetails, fetchedRemitterDetailsMetadata *paymentPb.EnrichedPiDetailsOfTransactingActors) (*paymentPb.Transaction, error) {
	var (
		reqInfo        *paymentPb.PaymentRequestInformation
		otherActorPiId string
	)

	txn := &paymentPb.Transaction{
		PiFrom:                 fromPiId,
		PiTo:                   toPiId,
		PartnerRefId:           parsedParticulars.GetUniqPartnerRefId(),
		Utr:                    utr,
		PartnerBank:            partnerBank,
		Amount:                 txnDetails.GetAmount(),
		Status:                 txnStatus,
		DetailedStatus:         txnDetailedStatus,
		PaymentProtocol:        parsedParticulars.GetProtocol(),
		Remarks:                txnDetails.GetRemarks(),
		PartnerExecutedAt:      txnDetails.GetTimestamp(),
		Particulars:            txnDetails.GetParticular(),
		RawNotificationDetails: make(map[string]*paymentPb.NotificationDetails),
		DedupeId: &paymentPb.DedupeId{
			RequestId:     parsedParticulars.GetReqId(),
			Utr:           utr,
			BatchSerialId: txnDetails.GetBatchSerialId(),
			CbsId:         txnDetails.GetId(),
			TxnTime:       txnDetails.GetTimestamp(),
		},
		Ownership: commontypes.Ownership_FEDERAL_BANK,
		Metadata: &paymentPb.Metadata{
			ParserTemplate: &paymentPb.ParserTemplate{
				Id:      parsedParticulars.GetParserTemplate().GetId(),
				Version: parsedParticulars.GetParserTemplate().GetVersion(),
			},
			FetchedRemitterDetails: fetchedRemitterDetailsMetadata,
		},
	}

	if vpaMetadata.GetPayerVpaPiId() != "" || vpaMetadata.GetPayeeVpaPiId() != "" {
		txn.GetMetadata().VpaDetails = &paymentPb.VpaDetails{
			PayerVpaPiId: vpaMetadata.GetPayerVpaPiId(),
			PayeeVpaPiId: vpaMetadata.GetPayeeVpaPiId(),
		}
	}

	// in case of miscellaneous charges, visa refunds and forex adjustment txn description field is populated in the parser with the description of charges,
	// we store this value in remarks for the transaction
	if parsedParticulars.GetTxnDescription() != "" {
		txn.Remarks = parsedParticulars.GetTxnDescription()
	}

	if txnDetails.IsDebit() {
		otherActorPiId = toPiId
		txn.DebitedAt = txnDetails.GetTimestamp()
		txn.ParticularsDebit = txnDetails.GetParticular()
		txn.PartnerRefIdDebit = parsedParticulars.GetUniqPartnerRefId()
		txn.RawNotificationDetails[paymentPb.AccountingEntryType_DEBIT.String()] = &paymentPb.NotificationDetails{
			Particulars:       txnDetails.GetParticular(),
			AdditionalDetails: txnDetails.GetAdditionalParticular(),
			BatchSerialId:     txnDetails.GetBatchSerialId(),
			ValueDate:         timestampPb.New(*datetime.DateToTime(txnDetails.GetValueDate(), datetime.IST)),
			CountryCode:       parsedParticulars.GetCountryCode(),
		}
		if txnDetails.GetInstrumentDetails() != nil {
			txn.RawNotificationDetails[paymentPb.AccountingEntryType_DEBIT.String()].InstrumentDetails = txnDetails.GetInstrumentDetails()
		}
	} else if txnDetails.IsCredit() {
		otherActorPiId = fromPiId
		txn.CreditedAt = txnDetails.GetTimestamp()
		txn.ParticularsCredit = txnDetails.GetParticular()
		txn.PartnerRefIdCredit = parsedParticulars.GetUniqPartnerRefId()
		txn.RawNotificationDetails[paymentPb.AccountingEntryType_CREDIT.String()] = &paymentPb.NotificationDetails{
			Particulars:       txnDetails.GetParticular(),
			AdditionalDetails: txnDetails.GetAdditionalParticular(),
			BatchSerialId:     txnDetails.GetBatchSerialId(),
			ValueDate:         timestampPb.New(*datetime.DateToTime(txnDetails.GetValueDate(), datetime.IST)),
			CountryCode:       parsedParticulars.GetCountryCode(),
		}
		if txnDetails.GetInstrumentDetails() != nil {
			txn.RawNotificationDetails[paymentPb.AccountingEntryType_CREDIT.String()].InstrumentDetails = txnDetails.GetInstrumentDetails()
		}
	}

	// for upi payment if ifsc code is present we want to populate it in transaction payload given
	// the same vpa can be linked to a different bank account later and the ifsc code will change
	if txn.GetPaymentProtocol() == paymentPb.PaymentProtocol_UPI {
		otherActorPi, err := p.piProcessor.GetPiById(ctx, otherActorPiId)
		if err != nil {
			return nil, fmt.Errorf("failed to fetch pi for other actor pi id :%s %w", otherActorPiId, err)
		}

		if otherActorPi.GetUpi().GetIfscCode() != "" {
			reqInfo = &paymentPb.PaymentRequestInformation{
				UpiInfo: &paymentPb.PaymentRequestInformation_UPI{
					CustomerAccountInfo: &upi.CustomerAccountDetails{
						Ifsc: otherActorPi.GetUpi().GetIfscCode(),
						Type: otherActorPi.GetUpi().GetAccountType(),
						Apo:  otherActorPi.GetUpi().GetApo(),
					},
				},
			}
		}
	}
	// omit fields from transaction before creating db entry, based on matching conditions
	omitTransactionFieldsConditionally(txn)

	createdTransaction, err := p.txnDao.Create(ctx, txn, reqInfo, orderId, commontypes.Ownership_EPIFI_TECH)
	switch {
	case errors.Is(err, errors2.ErrPartnerRefIdUniqueConstraintViolation):
		logger.Error(ctx, "temp log: failed to create txn because of partner-ref-id collision", zap.String(logger.PARTNER_TXN_ID, txn.GetPartnerRefId()),
			zap.String(logger.UTR, txn.GetUtr()), zap.Any(logger.DEDUPE_ID, txn.GetDedupeId()), zap.String(logger.PI_FROM, txn.GetPiFrom()), zap.String(logger.PI_TO, txn.GetPiTo()),
			zap.Any("rawNotificationDetails", txn.GetRawNotificationDetails()),
		)
		return nil, fmt.Errorf("failed to create transaction because of partner-ref-id collision: %w", epifierrors.ErrPermanent)
	case err != nil:
		return nil, fmt.Errorf("failed to create transaction: %s: %w", err.Error(), epifierrors.ErrTransient)
	}

	return createdTransaction, nil
}

// publishEventForCharges will publish events to rudder for all the notifications related to rudder
func (p *Processor) publishEventForCharges(ctx context.Context, order *orderPb.Order, parsedParticulars *paymentNotificationPb.ParsedTxnParticulars, txnDetails *paymentNotificationPb.TransactionDetails, actorIdForWhichNotificationIsReceived string) {
	switch {
	case orderPb.IsTagExist(order.GetTags(), orderPb.OrderTag_ECS_ENACH_CHARGES):
		goroutine.RunWithDefaultTimeout(ctx, func(cloneCtx context.Context) {
			p.eventBroker.AddToBatch(
				epificontext.WithEventAttributes(cloneCtx),
				payEvents.NewChargesOnSavingsAccount(true, "", orderPb.OrderTag_ECS_ENACH_CHARGES.String(), p.getChargeSubtype(parsedParticulars), time.Now(), txnDetails.GetDate(), parsedParticulars.GetEcsEnacheMandateDetails().GetCharges().GetDeclineDate(), actorIdForWhichNotificationIsReceived),
			)
		})
	case orderPb.IsTagExist(order.GetTags(), orderPb.OrderTag_DEBIT_CARD_AMC_CHARGE):
		goroutine.RunWithDefaultTimeout(ctx, func(cloneCtx context.Context) {
			p.eventBroker.AddToBatch(
				epificontext.WithEventAttributes(cloneCtx),
				payEvents.NewChargesOnSavingsAccount(false, "", orderPb.OrderTag_DEBIT_CARD_AMC_CHARGE.String(), p.getChargeSubtype(parsedParticulars), time.Now(), txnDetails.GetDate(), nil, actorIdForWhichNotificationIsReceived),
			)
		})
	case orderPb.IsTagExist(order.GetTags(), orderPb.OrderTag_AMB_CHARGE):
		goroutine.RunWithDefaultTimeout(ctx, func(cloneCtx context.Context) {
			p.eventBroker.AddToBatch(
				epificontext.WithEventAttributes(cloneCtx),
				payEvents.NewChargesOnSavingsAccount(false, "", orderPb.OrderTag_AMB_CHARGE.String(), p.getChargeSubtype(parsedParticulars), time.Now(), txnDetails.GetDate(), nil, actorIdForWhichNotificationIsReceived),
			)
		})
	default:
	}
}

// nolint:gocritic
// matchCriteriaAndInitiateRemitterInfoWorkflow is used to check conditions under which remitter info backfill workflow can be initiated.
// If the conditions are true it calls InitiateRemitterInfoBackfillWorkflow RPC to initiate remitter info workflow.
func (p *Processor) matchCriteriaAndInitiateRemitterInfoWorkflow(ctx context.Context, txn *paymentPb.Transaction,
	order *orderPb.Order, otherPiId string) error {

	var (
		initiateRemitterInfoBackfillWorkflowResp *parserPb.InitiateRemitterInfoBackfillWorkflowResponse
		err                                      error
		otherPi                                  *piPb.PaymentInstrument
	)
	// Start remitter info workflow for different payment protocols if below conditions match
	switch txn.GetPaymentProtocol() {
	case paymentPb.PaymentProtocol_UPI:
		// Skip triggering remitter backfill if the txn is an Extended PI case.
		// Why?
		//	1. This is to prevent the remitter-workflow from linking the orders and txns to original entities (PI and Actor), leading to duplicate orders for an actor.
		//	More about Extended PIs here: https://docs.google.com/document/d/1_2AoB_cMH1V2QSb67_1FxqIE1dBwyjqlf_dei_-mOjg/edit#heading=h.oc2e7qx0vvzg
		if strings.HasPrefix(txn.GetUtr(), "EXTENDED:") {
			logger.Info(ctx, "skipping remitter info backfill for extended pi txn", zap.String(logger.TXN_ID, txn.GetId()), zap.String(logger.UTR, txn.GetUtr()))
			return nil
		}

		otherPi, err = p.piProcessor.GetPiById(ctx, otherPiId)
		if err != nil {
			logger.Error(ctx, "failed to fetch other pi details", zap.Error(err))
			return err
		}

		if otherPi.GetState() != piPb.PaymentInstrumentState_VERIFIED {
			// Initiate Remitter Info Backfill Workflow in case of UPI payment only if other PI id is in not verified state
			initiateRemitterInfoBackfillWorkflowResp, err = p.parserClient.InitiateRemitterInfoBackfillWorkflow(ctx, &parserPb.InitiateRemitterInfoBackfillWorkflowRequest{
				TransactionDetails:                      txn,
				OrderDetails:                            order,
				MaximumPauseTimeForRemitterInfoWorkflow: int32(p.conf.MaximumPauseTimeForRemitterInfoWorkflow()),
			})
			if err = epifigrpc.RPCError(initiateRemitterInfoBackfillWorkflowResp, err); err != nil {
				logger.Error(ctx, "failed to initiate remitter info backfill workflow for given pi Id", zap.String(logger.PI_ID, otherPiId),
					zap.Error(err), zap.String(logger.TXN_ID, txn.GetId()), zap.String(logger.ORDER_ID, order.GetId()),
				)
				return err
			}
			logger.Info(ctx, "Remitter Info Backfill workflow successfully initiated", zap.String(logger.PI_ID, otherPiId),
				zap.String(logger.TXN_ID, txn.GetId()), zap.String(logger.ORDER_ID, order.GetId()),
			)
		}
	}
	return nil
}

func (p *Processor) isRemitterInfoBackfillActive(ctx context.Context, actorId string) bool {
	releaseConstraint := release.NewCommonConstraintData(typesPb.Feature_FEATURE_REMITTER_INFO_BACKFILL).WithActorId(actorId)
	isFeatureOpen, err := p.evaluator.Evaluate(ctx, releaseConstraint)
	if err != nil {
		logger.Error(ctx, "failed to evaluate remitter info backfill feature constraints", zap.Error(err))
		return false
	}
	return isFeatureOpen
}

func (p *Processor) getChargeSubtype(parsedParticulars *paymentNotificationPb.ParsedTxnParticulars) string {
	// using parser template id as proxy for charge subtype
	splits := strings.Split(parsedParticulars.GetParserTemplate().GetId().String(), "PARSER_TEMPLATE_ID_")
	if len(splits) > 1 {
		return splits[1]
	}
	return splits[0]
}

// generateOrderClientRequestId returns the client request id for different order use cases
func generateOrderClientRequestId(parsedParticulars *paymentNotificationPb.ParsedTxnParticulars) string {
	// for off app enach transactions we generate a new client request id to link the txn to corresponding RP action later
	if parsedParticulars.GetParserTemplate().GetId() == parsedTxnEnums.ParserTemplateId_PARSER_TEMPLATE_ID_OFF_APP_ENACH {
		return uuid.NewString()
	}
	return ""
}

// omitTransactionFieldsConditionally - Determines whether to omit certain fields in transaction while creating a transaction entry in the database.
//
// This function evaluates specific conditions (e.g., transaction protocol and status etc.)
// to decide whether and what fields to omit.
//
// Current implementation omits `DebitedAt` field in transaction db entry for `FAILED` debit card transactions.
// This can be extended further as new use-cases come in.
func omitTransactionFieldsConditionally(txn *paymentPb.Transaction) {
	switch {
	// Sample failed txn ID: TXNE7hYHV2SFRSM250519_5feFb8zhrk
	case txn.GetPaymentProtocol() == paymentPb.PaymentProtocol_CARD &&
		txn.GetStatus() == paymentPb.TransactionStatus_FAILED:
		txn.DebitedAt = nil

	default:
	}
}
