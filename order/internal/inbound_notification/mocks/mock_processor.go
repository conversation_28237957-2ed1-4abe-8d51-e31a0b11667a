// Code generated by MockGen. DO NOT EDIT.
// Source: order/internal/inbound_notification/processor.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	order "github.com/epifi/gamma/api/order"
	payment "github.com/epifi/gamma/api/order/payment"
	notification "github.com/epifi/gamma/api/order/payment/notification"
	inbound_notification "github.com/epifi/gamma/order/internal/inbound_notification"
	gomock "github.com/golang/mock/gomock"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

// MockInboundNotificationProcessor is a mock of InboundNotificationProcessor interface.
type MockInboundNotificationProcessor struct {
	ctrl     *gomock.Controller
	recorder *MockInboundNotificationProcessorMockRecorder
}

// MockInboundNotificationProcessorMockRecorder is the mock recorder for MockInboundNotificationProcessor.
type MockInboundNotificationProcessorMockRecorder struct {
	mock *MockInboundNotificationProcessor
}

// NewMockInboundNotificationProcessor creates a new mock instance.
func NewMockInboundNotificationProcessor(ctrl *gomock.Controller) *MockInboundNotificationProcessor {
	mock := &MockInboundNotificationProcessor{ctrl: ctrl}
	mock.recorder = &MockInboundNotificationProcessorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInboundNotificationProcessor) EXPECT() *MockInboundNotificationProcessorMockRecorder {
	return m.recorder
}

// CheckAndProcessAddFundsPayment mocks base method.
func (m *MockInboundNotificationProcessor) CheckAndProcessAddFundsPayment(ctx context.Context, td *notification.TransactionDetails, parsedDetails *notification.ParsedTxnParticulars, partnerBank vendorgateway.Vendor, internalSavingsAccountId string, source payment.TransactionDetailedStatus_DetailedStatus_API, actor string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckAndProcessAddFundsPayment", ctx, td, parsedDetails, partnerBank, internalSavingsAccountId, source, actor)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckAndProcessAddFundsPayment indicates an expected call of CheckAndProcessAddFundsPayment.
func (mr *MockInboundNotificationProcessorMockRecorder) CheckAndProcessAddFundsPayment(ctx, td, parsedDetails, partnerBank, internalSavingsAccountId, source, actor interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckAndProcessAddFundsPayment", reflect.TypeOf((*MockInboundNotificationProcessor)(nil).CheckAndProcessAddFundsPayment), ctx, td, parsedDetails, partnerBank, internalSavingsAccountId, source, actor)
}

// CheckAndProcessDepositPayment mocks base method.
func (m *MockInboundNotificationProcessor) CheckAndProcessDepositPayment(ctx context.Context, txnDetails *notification.TransactionDetails, parsedTxnParticulars *notification.ParsedTxnParticulars, partnerBank vendorgateway.Vendor, source payment.TransactionDetailedStatus_DetailedStatus_API) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckAndProcessDepositPayment", ctx, txnDetails, parsedTxnParticulars, partnerBank, source)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckAndProcessDepositPayment indicates an expected call of CheckAndProcessDepositPayment.
func (mr *MockInboundNotificationProcessorMockRecorder) CheckAndProcessDepositPayment(ctx, txnDetails, parsedTxnParticulars, partnerBank, source interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckAndProcessDepositPayment", reflect.TypeOf((*MockInboundNotificationProcessor)(nil).CheckAndProcessDepositPayment), ctx, txnDetails, parsedTxnParticulars, partnerBank, source)
}

// CheckAndProcessOnAppEnachExecution mocks base method.
func (m *MockInboundNotificationProcessor) CheckAndProcessOnAppEnachExecution(ctx context.Context, internalSavingsAccountId string, txnDetails *notification.TransactionDetails, parsedDetails *notification.ParsedTxnParticulars, source payment.TransactionDetailedStatus_DetailedStatus_API) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckAndProcessOnAppEnachExecution", ctx, internalSavingsAccountId, txnDetails, parsedDetails, source)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckAndProcessOnAppEnachExecution indicates an expected call of CheckAndProcessOnAppEnachExecution.
func (mr *MockInboundNotificationProcessorMockRecorder) CheckAndProcessOnAppEnachExecution(ctx, internalSavingsAccountId, txnDetails, parsedDetails, source interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckAndProcessOnAppEnachExecution", reflect.TypeOf((*MockInboundNotificationProcessor)(nil).CheckAndProcessOnAppEnachExecution), ctx, internalSavingsAccountId, txnDetails, parsedDetails, source)
}

// CheckAndProcessP2PInvestmentPayment mocks base method.
func (m *MockInboundNotificationProcessor) CheckAndProcessP2PInvestmentPayment(ctx context.Context, actorId, internalSavingsAccountId string, txn *notification.TransactionDetails, parsedDetails *notification.ParsedTxnParticulars, source payment.TransactionDetailedStatus_DetailedStatus_API) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckAndProcessP2PInvestmentPayment", ctx, actorId, internalSavingsAccountId, txn, parsedDetails, source)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckAndProcessP2PInvestmentPayment indicates an expected call of CheckAndProcessP2PInvestmentPayment.
func (mr *MockInboundNotificationProcessorMockRecorder) CheckAndProcessP2PInvestmentPayment(ctx, actorId, internalSavingsAccountId, txn, parsedDetails, source interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckAndProcessP2PInvestmentPayment", reflect.TypeOf((*MockInboundNotificationProcessor)(nil).CheckAndProcessP2PInvestmentPayment), ctx, actorId, internalSavingsAccountId, txn, parsedDetails, source)
}

// CheckAndProcessUSStocksTransactions mocks base method.
func (m *MockInboundNotificationProcessor) CheckAndProcessUSStocksTransactions(ctx context.Context, internalSavingsAccountId, internalActorId string, txnDetails *notification.TransactionDetails, parsedParticulars *notification.ParsedTxnParticulars, partnerBank vendorgateway.Vendor, txnStatus payment.TransactionStatus, txnDetailedStatus *payment.TransactionDetailedStatus) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckAndProcessUSStocksTransactions", ctx, internalSavingsAccountId, internalActorId, txnDetails, parsedParticulars, partnerBank, txnStatus, txnDetailedStatus)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckAndProcessUSStocksTransactions indicates an expected call of CheckAndProcessUSStocksTransactions.
func (mr *MockInboundNotificationProcessorMockRecorder) CheckAndProcessUSStocksTransactions(ctx, internalSavingsAccountId, internalActorId, txnDetails, parsedParticulars, partnerBank, txnStatus, txnDetailedStatus interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckAndProcessUSStocksTransactions", reflect.TypeOf((*MockInboundNotificationProcessor)(nil).CheckAndProcessUSStocksTransactions), ctx, internalSavingsAccountId, internalActorId, txnDetails, parsedParticulars, partnerBank, txnStatus, txnDetailedStatus)
}

// EnrichExistingTransaction mocks base method.
func (m *MockInboundNotificationProcessor) EnrichExistingTransaction(ctx context.Context, txn *payment.Transaction, td *notification.TransactionDetails, parsedDetails *notification.ParsedTxnParticulars, updatedTxnStatus payment.TransactionStatus, source payment.TransactionDetailedStatus_DetailedStatus_API, order *order.Order, enrichmentExclusions []payment.TransactionFieldMask, actorId, savingsAccountId string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EnrichExistingTransaction", ctx, txn, td, parsedDetails, updatedTxnStatus, source, order, enrichmentExclusions, actorId, savingsAccountId)
	ret0, _ := ret[0].(error)
	return ret0
}

// EnrichExistingTransaction indicates an expected call of EnrichExistingTransaction.
func (mr *MockInboundNotificationProcessorMockRecorder) EnrichExistingTransaction(ctx, txn, td, parsedDetails, updatedTxnStatus, source, order, enrichmentExclusions, actorId, savingsAccountId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnrichExistingTransaction", reflect.TypeOf((*MockInboundNotificationProcessor)(nil).EnrichExistingTransaction), ctx, txn, td, parsedDetails, updatedTxnStatus, source, order, enrichmentExclusions, actorId, savingsAccountId)
}

// GetUpdatedDetailsForExtendedAddFundsPayment mocks base method.
func (m *MockInboundNotificationProcessor) GetUpdatedDetailsForExtendedAddFundsPayment(ctx context.Context, notifArgs *inbound_notification.ProcessingArgs, piId string) *notification.ParsedTxnParticulars {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUpdatedDetailsForExtendedAddFundsPayment", ctx, notifArgs, piId)
	ret0, _ := ret[0].(*notification.ParsedTxnParticulars)
	return ret0
}

// GetUpdatedDetailsForExtendedAddFundsPayment indicates an expected call of GetUpdatedDetailsForExtendedAddFundsPayment.
func (mr *MockInboundNotificationProcessorMockRecorder) GetUpdatedDetailsForExtendedAddFundsPayment(ctx, notifArgs, piId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUpdatedDetailsForExtendedAddFundsPayment", reflect.TypeOf((*MockInboundNotificationProcessor)(nil).GetUpdatedDetailsForExtendedAddFundsPayment), ctx, notifArgs, piId)
}

// GetUpdatedDetailsForExtendedPayment mocks base method.
func (m *MockInboundNotificationProcessor) GetUpdatedDetailsForExtendedPayment(ctx context.Context, txn *payment.Transaction, isDebit bool, parsedDetails *notification.ParsedTxnParticulars, partnerBank vendorgateway.Vendor) *notification.ParsedTxnParticulars {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUpdatedDetailsForExtendedPayment", ctx, txn, isDebit, parsedDetails, partnerBank)
	ret0, _ := ret[0].(*notification.ParsedTxnParticulars)
	return ret0
}

// GetUpdatedDetailsForExtendedPayment indicates an expected call of GetUpdatedDetailsForExtendedPayment.
func (mr *MockInboundNotificationProcessorMockRecorder) GetUpdatedDetailsForExtendedPayment(ctx, txn, isDebit, parsedDetails, partnerBank interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUpdatedDetailsForExtendedPayment", reflect.TypeOf((*MockInboundNotificationProcessor)(nil).GetUpdatedDetailsForExtendedPayment), ctx, txn, isDebit, parsedDetails, partnerBank)
}

// IsExtendedAddFundsPayment mocks base method.
func (m *MockInboundNotificationProcessor) IsExtendedAddFundsPayment(ctx context.Context, notifArgs *inbound_notification.ProcessingArgs) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsExtendedAddFundsPayment", ctx, notifArgs)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsExtendedAddFundsPayment indicates an expected call of IsExtendedAddFundsPayment.
func (mr *MockInboundNotificationProcessorMockRecorder) IsExtendedAddFundsPayment(ctx, notifArgs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsExtendedAddFundsPayment", reflect.TypeOf((*MockInboundNotificationProcessor)(nil).IsExtendedAddFundsPayment), ctx, notifArgs)
}

// IsExtendedPiPayment mocks base method.
func (m *MockInboundNotificationProcessor) IsExtendedPiPayment(ctx context.Context, txn *payment.Transaction, isCredit bool) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsExtendedPiPayment", ctx, txn, isCredit)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsExtendedPiPayment indicates an expected call of IsExtendedPiPayment.
func (mr *MockInboundNotificationProcessorMockRecorder) IsExtendedPiPayment(ctx, txn, isCredit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsExtendedPiPayment", reflect.TypeOf((*MockInboundNotificationProcessor)(nil).IsExtendedPiPayment), ctx, txn, isCredit)
}

// MatchAndFetchTransaction mocks base method.
func (m *MockInboundNotificationProcessor) MatchAndFetchTransaction(ctx context.Context, actorId string, transactionDetails *notification.TransactionDetails, parsedTxnParticulars *notification.ParsedTxnParticulars) (*payment.Transaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MatchAndFetchTransaction", ctx, actorId, transactionDetails, parsedTxnParticulars)
	ret0, _ := ret[0].(*payment.Transaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MatchAndFetchTransaction indicates an expected call of MatchAndFetchTransaction.
func (mr *MockInboundNotificationProcessorMockRecorder) MatchAndFetchTransaction(ctx, actorId, transactionDetails, parsedTxnParticulars interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MatchAndFetchTransaction", reflect.TypeOf((*MockInboundNotificationProcessor)(nil).MatchAndFetchTransaction), ctx, actorId, transactionDetails, parsedTxnParticulars)
}

// ProcessDeDupedPayment mocks base method.
func (m *MockInboundNotificationProcessor) ProcessDeDupedPayment(ctx context.Context, notifArgs *inbound_notification.ProcessingArgs, txn *payment.Transaction, source payment.TransactionDetailedStatus_DetailedStatus_API, actorId string) (bool, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessDeDupedPayment", ctx, notifArgs, txn, source, actorId)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ProcessDeDupedPayment indicates an expected call of ProcessDeDupedPayment.
func (mr *MockInboundNotificationProcessorMockRecorder) ProcessDeDupedPayment(ctx, notifArgs, txn, source, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessDeDupedPayment", reflect.TypeOf((*MockInboundNotificationProcessor)(nil).ProcessDeDupedPayment), ctx, notifArgs, txn, source, actorId)
}

// ProcessDepositInboundNotifications mocks base method.
func (m *MockInboundNotificationProcessor) ProcessDepositInboundNotifications(ctx context.Context, txnDetails *notification.TransactionDetails, partnerBank vendorgateway.Vendor, parsedDetails *notification.ParsedTxnParticulars, source payment.TransactionDetailedStatus_DetailedStatus_API) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessDepositInboundNotifications", ctx, txnDetails, partnerBank, parsedDetails, source)
	ret0, _ := ret[0].(error)
	return ret0
}

// ProcessDepositInboundNotifications indicates an expected call of ProcessDepositInboundNotifications.
func (mr *MockInboundNotificationProcessorMockRecorder) ProcessDepositInboundNotifications(ctx, txnDetails, partnerBank, parsedDetails, source interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessDepositInboundNotifications", reflect.TypeOf((*MockInboundNotificationProcessor)(nil).ProcessDepositInboundNotifications), ctx, txnDetails, partnerBank, parsedDetails, source)
}

// ProcessOffAppPayment mocks base method.
func (m *MockInboundNotificationProcessor) ProcessOffAppPayment(ctx context.Context, internalSavingsAccountId, internalActorId, accountType string, txnDetails *notification.TransactionDetails, parsedParticulars *notification.ParsedTxnParticulars, partnerBank vendorgateway.Vendor, txnStatus payment.TransactionStatus, txnDetailedStatus *payment.TransactionDetailedStatus, orderWorkflow order.OrderWorkflow, vendorNotificationTimestamp *timestamppb.Timestamp, isLastAttempt, attemptRemitterInfoBackill bool) (*order.Order, *payment.Transaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessOffAppPayment", ctx, internalSavingsAccountId, internalActorId, accountType, txnDetails, parsedParticulars, partnerBank, txnStatus, txnDetailedStatus, orderWorkflow, vendorNotificationTimestamp, isLastAttempt, attemptRemitterInfoBackill)
	ret0, _ := ret[0].(*order.Order)
	ret1, _ := ret[1].(*payment.Transaction)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ProcessOffAppPayment indicates an expected call of ProcessOffAppPayment.
func (mr *MockInboundNotificationProcessorMockRecorder) ProcessOffAppPayment(ctx, internalSavingsAccountId, internalActorId, accountType, txnDetails, parsedParticulars, partnerBank, txnStatus, txnDetailedStatus, orderWorkflow, vendorNotificationTimestamp, isLastAttempt, attemptRemitterInfoBackill interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessOffAppPayment", reflect.TypeOf((*MockInboundNotificationProcessor)(nil).ProcessOffAppPayment), ctx, internalSavingsAccountId, internalActorId, accountType, txnDetails, parsedParticulars, partnerBank, txnStatus, txnDetailedStatus, orderWorkflow, vendorNotificationTimestamp, isLastAttempt, attemptRemitterInfoBackill)
}
