// nolint: goimports
package inbound_notification_test

import (
	"context"
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"google.golang.org/genproto/googleapis/type/date"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	proto2 "google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/mock"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/queue"

	accountPb "github.com/epifi/gamma/api/accounts"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	paymentNotificationPb "github.com/epifi/gamma/api/order/payment/notification"
	enumsPb "github.com/epifi/gamma/api/order/payment/notification/enums"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/api/timeline"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/upi"
	"github.com/epifi/gamma/api/upi/consumer"
	config "github.com/epifi/gamma/order/config/genconf"
	inboundNotifProcessor "github.com/epifi/gamma/order/internal/inbound_notification"
	piProcessor "github.com/epifi/gamma/order/internal/pi"
	"github.com/epifi/gamma/pkg/feature/release"
	"github.com/epifi/gamma/pkg/pay"
	vendorPkg "github.com/epifi/gamma/pkg/vendors/federal"
)

type InboundNotificationTestSuite struct {
	conf *config.Config
}

func NewInboundNotificationTestSuite(conf *config.Config) InboundNotificationTestSuite {
	return InboundNotificationTestSuite{conf: conf}
}

var (
	ts InboundNotificationTestSuite

	// adds a keyword to transaction unique identifiers to avoid violation on unique constraints while creating 2nd order
	// and transaction in case of extended PI payments
	convertToExtendedIdentifier = func(id string) string {
		return fmt.Sprintf("EXTENDED:%s", id)
	}
)

type createOrderArgMatcher struct {
	want *orderPb.Order
}

func newCreateOrderArgMatcher(order *orderPb.Order) *createOrderArgMatcher {
	return &createOrderArgMatcher{want: order}
}

func (t *createOrderArgMatcher) Matches(x interface{}) bool {
	got, ok := x.(*orderPb.Order)
	if !ok {
		return false
	}

	t.want.DeletedAt = got.DeletedAt
	t.want.CreatedAt = got.CreatedAt
	t.want.UpdatedAt = got.UpdatedAt
	t.want.ExpireAt = got.ExpireAt
	// client req id is not deterministic, so we only validate presence
	if len(t.want.GetClientReqId()) > 0 && len(got.GetClientReqId()) > 0 {
		t.want.ClientReqId = got.GetClientReqId()
	}

	return proto2.Equal(got, t.want)
}

func (t *createOrderArgMatcher) String() string {
	return t.want.String()
}

// TODO(harish/kunal): https://monorail.pointz.in/p/fi-app/issues/detail?id=5076
func TestService_ProcessOffAppPayment(t *testing.T) {
	var (
		testTs   = timestamppb.New(time.Date(2021, time.January, 9, 1, 43, 00, 00, time.UTC))
		testDate = &date.Date{
			Year:  2021,
			Month: 1,
			Day:   9,
		}
		testValueTs               = timestamppb.New(*datetime.DateToTime(testDate, datetime.IST))
		testAccountId             = "account-id-1"
		testAccountNumber         = "account-1"
		testInternalActorId       = "actor-1"
		testInternalActorName     = "actor-1-name"
		testOtherActorId          = "actor-2"
		testOtherActorName        = "actor-2-name"
		testInternalActorPidId    = "pi-1"
		testOtherActorPiId        = "pi-2"
		genericPiId               = "paymentinstrument-generic"
		genericActorName          = "Others"
		testVPA                   = "test@fede"
		basicCreditTestTxnDetails = &paymentNotificationPb.TransactionDetails{
			AccountNumber: testAccountNumber,
			Id:            "S1234",
			Timestamp:     testTs,
			Date:          testDate,
			ValueDate:     testDate,
			Amount: &moneyPb.Money{
				CurrencyCode: money.RupeeCurrencyCode,
				Units:        10,
				Nanos:        0,
			},
			Particular:            "particulars",
			ReferenceNumber:       "utr-1",
			Remarks:               "utr-2/remarks-1",
			NotificationEventType: paymentNotificationPb.TransactionDetails_CREDIT,
		}
		testNEFTParsedTxnDetails = &paymentNotificationPb.ParsedTxnParticulars{
			Protocol:         paymentPb.PaymentProtocol_NEFT,
			OtherActorName:   "actor enterprises",
			Utr:              "utr-1",
			UniqPartnerRefId: "S1234",
		}
		testCardParsedTxnDetails = &paymentNotificationPb.ParsedTxnParticulars{
			Protocol:         paymentPb.PaymentProtocol_CARD,
			OtherActorName:   testOtherActorName,
			TxnCategory:      paymentNotificationPb.ParsedTxnParticulars_POS,
			Utr:              "S1234",
			UniqPartnerRefId: "S1234",
		}
		testUPIParsedTxnDetails = &paymentNotificationPb.ParsedTxnParticulars{
			Protocol:       paymentPb.PaymentProtocol_UPI,
			OtherActorName: testOtherActorName,
			PiIdentifier: &paymentNotificationPb.ParsedTxnParticulars_Vpa{
				Vpa: testVPA,
			},
			Utr:              "utr-2",
			UniqPartnerRefId: "S1234",
			VpaDetails: &paymentNotificationPb.VpaDetails{
				PayerVpa: "payer@fifederal",
				PayeeVpa: "payee@fifederal",
			},
		}
		testLoanDisbursementParsedTxnDetails = &paymentNotificationPb.ParsedTxnParticulars{
			Protocol:         paymentPb.PaymentProtocol_INTRA_BANK,
			OtherActorName:   pay.DefaultFederalBankActorName,
			PiIdentifier:     pay.FederalGenericAccount(),
			Utr:              "S1234",
			UniqPartnerRefId: "S1234",
			TxnCategory:      paymentNotificationPb.ParsedTxnParticulars_LOAN_DISBURSEMENT,
		}
		testParsedTxnDetailsForMetadata = &paymentNotificationPb.ParsedTxnParticulars{
			Protocol:       paymentPb.PaymentProtocol_UPI,
			OtherActorName: testOtherActorName,
			PiIdentifier: &paymentNotificationPb.ParsedTxnParticulars_Vpa{
				Vpa: testVPA,
			},
			Utr:              "utr-2",
			UniqPartnerRefId: "S1234",
			ParserTemplate: &paymentNotificationPb.ParserTemplate{
				Id:      enumsPb.ParserTemplateId_PARSER_TEMPLATE_ID_UPI_OUT_1,
				Version: "v1.0",
			},
		}
		testEnachParsedTxnDetails = &paymentNotificationPb.ParsedTxnParticulars{
			Protocol:         paymentPb.PaymentProtocol_ENACH,
			OtherActorName:   pay.DefaultFederalBankActorName,
			PiIdentifier:     pay.FederalGenericAccount(),
			Utr:              "S1234",
			UniqPartnerRefId: "S1234",
			ParserTemplate: &paymentNotificationPb.ParserTemplate{
				Id:      enumsPb.ParserTemplateId_PARSER_TEMPLATE_ID_OFF_APP_ENACH,
				Version: "v1.0",
			},
		}
	)

	basicDebitTestTxnDetails := proto2.Clone(basicCreditTestTxnDetails).(*paymentNotificationPb.TransactionDetails)
	basicDebitTestTxnDetails.NotificationEventType = paymentNotificationPb.TransactionDetails_DEBIT
	basicDebitTestTxnWithInstrumentDetails := proto2.Clone(basicCreditTestTxnDetails).(*paymentNotificationPb.TransactionDetails)
	basicDebitTestTxnWithInstrumentDetails.NotificationEventType = paymentNotificationPb.TransactionDetails_DEBIT
	basicDebitTestTxnWithInstrumentDetails.InstrumentDetails = &paymentPb.InstrumentDetails{
		InstrumentNumber: "1",
	}
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockTxnDao, mockOrderDao, mockP2PInvestmentClient, mockPiProcessor, mockTimelineProcessor, mockPiClient, mockDepositClient, mockOrderEventPub, mockTncEventPub, mockEventsBroker, mocksUpiProcessor, mockOrderProcessor, mockOrderOrchPub, mockOrderNotifPub, mockOrderSearchPub, mockMerchantCl, mockOrderVPAVerfPub,
		mockTxnNotifPub, mockCelestialProcessor, mockIFTClient, mockMerchantResCl, mockParserCl, mockEval, mockEnachCl, mockRecurPayCl, mockTxnDetailedStatusPub, mockPayCl, mockMerchantProcessor := newInboundNotifDependencies(ctr)
	processor := inboundNotifProcessor.NewProcessor(mockDepositClient, mockTxnDao, mockOrderDao, mockOrderEventPub, mockTncEventPub, mockEventsBroker, mockTimelineProcessor, mockPiProcessor, mocksUpiProcessor, mockOrderProcessor, mockOrderOrchPub, mockOrderNotifPub, mockOrderSearchPub, mockMerchantCl,
		mockP2PInvestmentClient, mockOrderVPAVerfPub, mockTxnNotifPub, mockCelestialProcessor, mockIFTClient, mockMerchantResCl, mockParserCl, conf, mockEval, mockEnachCl, mockRecurPayCl, mockTxnDetailedStatusPub, mockPiClient, mockPayCl, mockMerchantProcessor)

	mockEventsBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()

	type args struct {
		internalSavingsAccountId    string
		internalActorId             string
		txnDetails                  *paymentNotificationPb.TransactionDetails
		parsedParticulars           *paymentNotificationPb.ParsedTxnParticulars
		partnerBank                 commonvgpb.Vendor
		txnStatus                   paymentPb.TransactionStatus
		txnDetailedStatus           *paymentPb.TransactionDetailedStatus
		isLastAttempt               bool
		vendorNotificationTimestamp *timestamppb.Timestamp
		orderWorkflow               orderPb.OrderWorkflow
		attemptRemitterInfoBackfill bool
	}
	type mockResolveInternalActorPiDetails struct {
		enable                   bool
		internalSavingsAccountId string
		internalSavingsAcctNo    string
		isCardTxn                bool
		partnerBank              commonvgpb.Vendor
		internalPiId             string
		internalActorName        string
		source                   paymentPb.TransactionDetailedStatus_DetailedStatus_API
		maskedCardNumber         string
		err                      error
	}
	type mockResolveTimeline struct {
		enable            bool
		internalActorId   string
		internalPiId      string
		internalActorName string
		otherPiId         string
		otherActorName    string
		isCredit          bool
		fromActorId       string
		fromPiId          string
		toActorId         string
		toPiId            string
		err               error
	}
	type mockCreateOrder struct {
		enable bool
		req    *orderPb.Order
		want   *orderPb.Order
		err    error
	}
	type mockCreateTxn struct {
		enable  bool
		txn     *paymentPb.Transaction
		reqInfo *paymentPb.PaymentRequestInformation
		orderId string
		want    *paymentPb.Transaction
		err     error
	}
	type mockPublish struct {
		enable bool
		err    error
	}
	type mockIncomingCreditEventPublish struct {
		enable bool
	}
	type mockVpaVerificationPublisher struct {
		enable bool
		req    *consumer.VerifyVpaRequest
		delay  time.Duration
		res    string
		err    error
	}
	type mockCreateAccountPi struct {
		enable        bool
		name          string
		verifiedName  string
		accountNumber string
		ifscCode      string
		piType        piPb.PaymentInstrumentType
		accountType   accountPb.Type
		want          *piPb.PaymentInstrument
	}

	type mockResolveOtherActorPiDetails struct {
		enable            bool
		parsedParticulars *paymentNotificationPb.ParsedTxnParticulars
		txnDetails        *paymentNotificationPb.TransactionDetails
		actorId           string
		ownership         piPb.Ownership
		otherActorPiId    string
		otherActorName    string
		isMerchant        bool
		err               error
	}

	type mockCreateUpiPi struct {
		enable          bool
		name            string
		vpa             string
		mcc             string
		ownership       piPb.Ownership
		merchantDetails *upi.MerchantDetails
		isVpaVerified   bool
		piId            string
		err             error
	}

	type mockGetPiById struct {
		enable bool
		piId   string
		pi     *piPb.PaymentInstrument
		err    error
	}

	type mockEvaluate struct {
		enable      bool
		constraints *release.CommonConstraintData
		res         bool
		err         error
	}

	type mockIsNewAddFundsVpaEnabledForActor struct {
		enable  bool
		actorId string
		want    bool
		err     error
	}

	type mockCreatePi struct {
		enable    bool
		wantReq   *piPb.CreatePiRequest
		returnRes *piPb.CreatePiResponse
		err       error
	}

	type mockCheckNameIsMerchant struct {
		enable bool
		name   string
		want   bool
		err    error
	}

	tests := []struct {
		name                                string
		args                                args
		mockResolveInternalPiDetails        mockResolveInternalActorPiDetails
		mockResolveTimeline                 mockResolveTimeline
		mockCreateOrder                     mockCreateOrder
		mockCreateTxn                       mockCreateTxn
		mockPublish                         mockPublish
		mockIncomingCreditEventPublish      mockIncomingCreditEventPublish
		mockCreateAccountPi                 mockCreateAccountPi
		mockResolveOtherActorPiDetails      mockResolveOtherActorPiDetails
		mockCreateUpiPi                     mockCreateUpiPi
		mockVpaVerificationPublisher        mockVpaVerificationPublisher
		mockGetPiById                       mockGetPiById
		mockEvaluate                        mockEvaluate
		mockIsNewAddFundsVpaEnabledForActor mockIsNewAddFundsVpaEnabledForActor
		mockCreatePi                        []mockCreatePi
		mockCheckNameIsMerchant             mockCheckNameIsMerchant
		wantErr                             bool
		wantErrType                         error
	}{
		{
			name: "Should not store remitter names in metadata for RTGS debit transaction",
			mockResolveInternalPiDetails: mockResolveInternalActorPiDetails{
				enable:                   true,
				internalSavingsAccountId: testAccountId,
				internalSavingsAcctNo:    testAccountNumber,
				isCardTxn:                false,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				internalPiId:             testInternalActorPidId,
				internalActorName:        testInternalActorName,
				source:                   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
				err:                      nil,
			},
			mockResolveOtherActorPiDetails: mockResolveOtherActorPiDetails{
				enable: true,
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol:         paymentPb.PaymentProtocol_RTGS,
					OtherActorName:   "Recipient Company Ltd",
					Utr:              "rtgs-utr-456",
					UniqPartnerRefId: "S7890",
				},
				txnDetails:     basicDebitTestTxnDetails,
				actorId:        testInternalActorId,
				otherActorName: "Recipient Company Ltd",
				otherActorPiId: genericPiId,
			},
			mockResolveTimeline: mockResolveTimeline{
				enable:            true,
				internalActorId:   testInternalActorId,
				internalPiId:      testInternalActorPidId,
				internalActorName: testInternalActorName,
				otherPiId:         genericPiId,
				otherActorName:    "Recipient Company Ltd",
				fromActorId:       testInternalActorId,
				fromPiId:          testInternalActorPidId,
				toActorId:         testOtherActorId,
				toPiId:            genericPiId,
				isCredit:          false,
				err:               nil,
			},
			mockCreateOrder: mockCreateOrder{
				enable: true,
				req: &orderPb.Order{
					FromActorId: testInternalActorId,
					ToActorId:   testOtherActorId,
					Workflow:    orderPb.OrderWorkflow_NO_OP,
					Status:      orderPb.OrderStatus_PAID,
					Provenance:  orderPb.OrderProvenance_EXTERNAL,
					Amount:      basicDebitTestTxnDetails.GetAmount(),
				},
				want: &orderPb.Order{
					Id: "order-1",
				},
				err: nil,
			},
			mockCreateTxn: mockCreateTxn{
				enable: true,
				txn: &paymentPb.Transaction{
					PiFrom:            testInternalActorPidId,
					PiTo:              genericPiId,
					PartnerRefId:      "S7890",
					Utr:               "rtgs-utr-456",
					Amount:            basicDebitTestTxnDetails.GetAmount(),
					Status:            paymentPb.TransactionStatus_SUCCESS,
					PaymentProtocol:   paymentPb.PaymentProtocol_RTGS,
					Remarks:           basicDebitTestTxnDetails.GetRemarks(),
					Particulars:       basicDebitTestTxnDetails.GetParticular(),
					PartnerExecutedAt: testTs,
					PartnerBank:       commonvgpb.Vendor_FEDERAL_BANK,
					DebitedAt:         testTs,
					RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
						paymentPb.AccountingEntryType_DEBIT.String(): {
							Particulars: basicDebitTestTxnDetails.GetParticular(),
							ValueDate:   testValueTs,
						},
					},
					ParticularsDebit:  basicDebitTestTxnDetails.GetParticular(),
					PartnerRefIdDebit: "S7890",
					DetailedStatus: &paymentPb.TransactionDetailedStatus{
						DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
							{
								CreatedAt: timestamppb.Now(),
								Api:       paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
								State:     paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
							},
						},
					},
					DedupeId: &paymentPb.DedupeId{
						Utr:     "rtgs-utr-456",
						CbsId:   "S1234",
						TxnTime: testTs,
					},
					Ownership: commontypes.Ownership_FEDERAL_BANK,
					Metadata: &paymentPb.Metadata{
						ParserTemplate: &paymentPb.ParserTemplate{},
						// FetchedRemitterDetails field is intentionally omitted since it should not
						// be populated for RTGS debit transactions
					},
				},
				orderId: "order-1",
				want: &paymentPb.Transaction{
					Id: "txn-1",
				},
				err: nil,
			},
			mockPublish: mockPublish{
				enable: true,
				err:    nil,
			},
			mockIncomingCreditEventPublish: mockIncomingCreditEventPublish{
				enable: false,
			},
			mockCheckNameIsMerchant: mockCheckNameIsMerchant{
				enable: true,
				name:   "Recipient Company Ltd",
				want:   false,
				err:    nil,
			},
			args: args{
				internalSavingsAccountId: testAccountId,
				internalActorId:          testInternalActorId,
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol:         paymentPb.PaymentProtocol_RTGS,
					OtherActorName:   "Recipient Company Ltd",
					Utr:              "rtgs-utr-456",
					UniqPartnerRefId: "S7890",
				},
				txnDetails:  basicDebitTestTxnDetails,
				partnerBank: commonvgpb.Vendor_FEDERAL_BANK,
				txnStatus:   paymentPb.TransactionStatus_SUCCESS,
				txnDetailedStatus: &paymentPb.TransactionDetailedStatus{
					DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
						{
							Api:   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
							State: paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
						},
					},
				},
				attemptRemitterInfoBackfill: false,
			},
			wantErr:     false,
			wantErrType: nil,
		},
		{
			name: "Should successfully process off app RTGS credit notification and store remitter names in metadata",
			mockResolveInternalPiDetails: mockResolveInternalActorPiDetails{
				enable:                   true,
				internalSavingsAccountId: testAccountId,
				internalSavingsAcctNo:    testAccountNumber,
				isCardTxn:                false,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				internalPiId:             testInternalActorPidId,
				internalActorName:        testInternalActorName,
				source:                   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
				err:                      nil,
			},
			mockResolveOtherActorPiDetails: mockResolveOtherActorPiDetails{
				enable: true,
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol:         paymentPb.PaymentProtocol_RTGS,
					OtherActorName:   "Remitter Company Ltd",
					Utr:              "rtgs-utr-123",
					UniqPartnerRefId: "S5678",
				},
				txnDetails:     basicCreditTestTxnDetails,
				actorId:        testInternalActorId,
				otherActorName: "Remitter Company Ltd",
				otherActorPiId: testOtherActorPiId,
			},
			mockResolveTimeline: mockResolveTimeline{
				enable:            true,
				internalActorId:   testInternalActorId,
				internalPiId:      testInternalActorPidId,
				internalActorName: testInternalActorName,
				otherPiId:         testOtherActorPiId,
				otherActorName:    "Remitter Company Ltd",
				fromActorId:       testOtherActorId,
				fromPiId:          testOtherActorPiId,
				toActorId:         testInternalActorId,
				toPiId:            testInternalActorPidId,
				isCredit:          true,
				err:               nil,
			},
			mockCreateOrder: mockCreateOrder{
				enable: true,
				req: &orderPb.Order{
					FromActorId: testOtherActorId,
					ToActorId:   testInternalActorId,
					Workflow:    orderPb.OrderWorkflow_NO_OP,
					Status:      orderPb.OrderStatus_PAID,
					Provenance:  orderPb.OrderProvenance_EXTERNAL,
					Amount:      basicCreditTestTxnDetails.GetAmount(),
					Tags:        []orderPb.OrderTag{orderPb.OrderTag_PREDICTED_MERCHANT},
				},
				want: &orderPb.Order{
					Id: "order-1",
				},
				err: nil,
			},
			mockCreateTxn: mockCreateTxn{
				enable: true,
				txn: &paymentPb.Transaction{
					PiFrom:            testOtherActorPiId,
					PiTo:              testInternalActorPidId,
					PartnerRefId:      "S5678",
					Utr:               "rtgs-utr-123",
					Amount:            basicCreditTestTxnDetails.GetAmount(),
					Status:            paymentPb.TransactionStatus_SUCCESS,
					PaymentProtocol:   paymentPb.PaymentProtocol_RTGS,
					Remarks:           basicCreditTestTxnDetails.GetRemarks(),
					Particulars:       basicCreditTestTxnDetails.GetParticular(),
					PartnerExecutedAt: testTs,
					PartnerBank:       commonvgpb.Vendor_FEDERAL_BANK,
					CreditedAt:        testTs,
					RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
						paymentPb.AccountingEntryType_CREDIT.String(): {
							Particulars: basicCreditTestTxnDetails.GetParticular(),
							ValueDate:   testValueTs,
						},
					},
					ParticularsCredit:  basicCreditTestTxnDetails.GetParticular(),
					PartnerRefIdCredit: "S5678",
					DetailedStatus: &paymentPb.TransactionDetailedStatus{
						DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
							{
								CreatedAt: timestamppb.Now(),
								Api:       paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
								State:     paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
							},
						},
					},
					DedupeId: &paymentPb.DedupeId{
						Utr:     "rtgs-utr-123",
						CbsId:   "S1234",
						TxnTime: testTs,
					},
					Ownership: commontypes.Ownership_FEDERAL_BANK,
					Metadata: &paymentPb.Metadata{
						ParserTemplate: &paymentPb.ParserTemplate{},
						FetchedRemitterDetails: &paymentPb.EnrichedPiDetailsOfTransactingActors{
							PayerName: "Remitter Company Ltd",
							PayerPiId: "pi-2",
						},
					},
				},
				orderId: "order-1",
				want: &paymentPb.Transaction{
					Id: "txn-1",
				},
				err: nil,
			},
			mockPublish: mockPublish{
				enable: true,
				err:    nil,
			},
			mockIncomingCreditEventPublish: mockIncomingCreditEventPublish{
				enable: true,
			},
			mockEvaluate: mockEvaluate{
				enable:      true,
				constraints: release.NewCommonConstraintData(typesPb.Feature_FEATURE_REMITTER_INFO_BACKFILL).WithActorId(testInternalActorId),
				res:         true,
			},
			mockCheckNameIsMerchant: mockCheckNameIsMerchant{
				enable: true,
				name:   "Remitter Company Ltd",
				want:   true,
				err:    nil,
			},
			args: args{
				internalSavingsAccountId: testAccountId,
				internalActorId:          testInternalActorId,
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol:         paymentPb.PaymentProtocol_RTGS,
					OtherActorName:   "Remitter Company Ltd",
					Utr:              "rtgs-utr-123",
					UniqPartnerRefId: "S5678",
				},
				txnDetails:  basicCreditTestTxnDetails,
				partnerBank: commonvgpb.Vendor_FEDERAL_BANK,
				txnStatus:   paymentPb.TransactionStatus_SUCCESS,
				txnDetailedStatus: &paymentPb.TransactionDetailedStatus{
					DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
						{
							Api:   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
							State: paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
						},
					},
				},
				attemptRemitterInfoBackfill: true,
			},
			wantErr:     false,
			wantErrType: nil,
		},
		{
			name: "Should successfully process off app neft credit notification and fallback to generic PI",
			mockResolveInternalPiDetails: mockResolveInternalActorPiDetails{
				enable:                   true,
				internalSavingsAccountId: testAccountId,
				internalSavingsAcctNo:    testAccountNumber,
				isCardTxn:                false,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				internalPiId:             testInternalActorPidId,
				internalActorName:        testInternalActorName,
				source:                   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
				err:                      nil,
			},
			mockResolveOtherActorPiDetails: mockResolveOtherActorPiDetails{
				enable:            true,
				parsedParticulars: testNEFTParsedTxnDetails,
				txnDetails:        basicCreditTestTxnDetails,
				actorId:           testInternalActorId,
				otherActorName:    "actor enterprises",
				otherActorPiId:    testOtherActorPiId,
			},
			mockResolveTimeline: mockResolveTimeline{
				enable:            true,
				internalActorId:   testInternalActorId,
				internalPiId:      testInternalActorPidId,
				internalActorName: testInternalActorName,
				otherPiId:         testOtherActorPiId,
				otherActorName:    "actor enterprises",
				fromActorId:       testOtherActorId,
				fromPiId:          testOtherActorPiId,
				toActorId:         testInternalActorId,
				toPiId:            testInternalActorPidId,
				isCredit:          true,
				err:               nil,
			},
			mockCreateOrder: mockCreateOrder{
				enable: true,
				req: &orderPb.Order{
					FromActorId: testOtherActorId,
					ToActorId:   testInternalActorId,
					Workflow:    orderPb.OrderWorkflow_NO_OP,
					Status:      orderPb.OrderStatus_PAID,
					Provenance:  orderPb.OrderProvenance_EXTERNAL,
					Amount:      basicCreditTestTxnDetails.GetAmount(),
					Tags:        []orderPb.OrderTag{orderPb.OrderTag_PREDICTED_MERCHANT},
				},
				want: &orderPb.Order{
					Id: "order-1",
				},
				err: nil,
			},
			mockCreateTxn: mockCreateTxn{
				enable: true,
				txn: &paymentPb.Transaction{
					PiFrom:            testOtherActorPiId,
					PiTo:              testInternalActorPidId,
					PartnerRefId:      testNEFTParsedTxnDetails.GetUniqPartnerRefId(),
					Utr:               basicCreditTestTxnDetails.GetReferenceNumber(),
					Amount:            basicCreditTestTxnDetails.GetAmount(),
					Status:            paymentPb.TransactionStatus_SUCCESS,
					PaymentProtocol:   paymentPb.PaymentProtocol_NEFT,
					Remarks:           basicCreditTestTxnDetails.GetRemarks(),
					Particulars:       basicCreditTestTxnDetails.GetParticular(),
					PartnerExecutedAt: testTs,
					PartnerBank:       commonvgpb.Vendor_FEDERAL_BANK,
					CreditedAt:        testTs,
					RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
						paymentPb.AccountingEntryType_CREDIT.String(): {
							Particulars: basicCreditTestTxnDetails.GetParticular(),
							ValueDate:   testValueTs,
						},
					},
					ParticularsCredit:  basicCreditTestTxnDetails.GetParticular(),
					PartnerRefIdCredit: testNEFTParsedTxnDetails.GetUniqPartnerRefId(),
					DetailedStatus: &paymentPb.TransactionDetailedStatus{
						DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
							{
								CreatedAt: timestamppb.Now(),
								Api:       paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
								State:     paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
							},
						},
					},
					DedupeId: &paymentPb.DedupeId{
						Utr:     "utr-1",
						CbsId:   "S1234",
						TxnTime: testTs,
					},
					Ownership: commontypes.Ownership_FEDERAL_BANK,
					Metadata: &paymentPb.Metadata{
						ParserTemplate:         &paymentPb.ParserTemplate{},
						FetchedRemitterDetails: &paymentPb.EnrichedPiDetailsOfTransactingActors{PayerName: "actor enterprises", PayerPiId: testOtherActorPiId},
					},
				},
				orderId: "order-1",
				want: &paymentPb.Transaction{
					Id: "txn-1",
				},
				err: nil,
			},
			mockPublish: mockPublish{
				enable: true,
				err:    nil,
			},
			mockIncomingCreditEventPublish: mockIncomingCreditEventPublish{
				enable: true,
			},
			mockEvaluate: mockEvaluate{
				enable:      true,
				constraints: release.NewCommonConstraintData(typesPb.Feature_FEATURE_REMITTER_INFO_BACKFILL).WithActorId(testInternalActorId),
				res:         true,
			},
			mockCheckNameIsMerchant: mockCheckNameIsMerchant{
				enable: true,
				name:   "actor enterprises",
				want:   true,
				err:    nil,
			},
			args: args{
				internalSavingsAccountId: testAccountId,
				internalActorId:          testInternalActorId,
				parsedParticulars:        testNEFTParsedTxnDetails,
				txnDetails:               basicCreditTestTxnDetails,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				txnStatus:                paymentPb.TransactionStatus_SUCCESS,
				txnDetailedStatus: &paymentPb.TransactionDetailedStatus{
					DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
						{
							Api:   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
							State: paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
						},
					},
				},
				attemptRemitterInfoBackfill: true,
			},
			wantErr:     false,
			wantErrType: nil,
		},
		{
			name: "Should successfully process off app card debit notification and fallback to generic PI",
			mockResolveInternalPiDetails: mockResolveInternalActorPiDetails{
				enable:                   true,
				internalSavingsAccountId: testAccountId,
				internalSavingsAcctNo:    testAccountNumber,
				isCardTxn:                true,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				internalPiId:             testInternalActorPidId,
				internalActorName:        testInternalActorName,
				source:                   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
				err:                      nil,
			},
			mockResolveOtherActorPiDetails: mockResolveOtherActorPiDetails{
				enable:            true,
				parsedParticulars: testCardParsedTxnDetails,
				txnDetails:        basicDebitTestTxnDetails,
				actorId:           testInternalActorId,
				otherActorName:    genericActorName,
				otherActorPiId:    genericPiId,
			},
			mockResolveTimeline: mockResolveTimeline{
				enable:            true,
				internalActorId:   testInternalActorId,
				internalPiId:      testInternalActorPidId,
				internalActorName: testInternalActorName,
				otherPiId:         genericPiId,
				otherActorName:    genericActorName,
				fromActorId:       testInternalActorId,
				fromPiId:          testInternalActorPidId,
				toActorId:         testOtherActorId,
				toPiId:            genericPiId,
				isCredit:          false,
				err:               nil,
			},
			mockCreateOrder: mockCreateOrder{
				enable: true,
				req: &orderPb.Order{
					FromActorId: testInternalActorId,
					ToActorId:   testOtherActorId,
					Workflow:    orderPb.OrderWorkflow_NO_OP,
					Status:      orderPb.OrderStatus_PAID,
					Provenance:  orderPb.OrderProvenance_POS,
					Amount:      basicCreditTestTxnDetails.GetAmount(),
					Tags:        []orderPb.OrderTag{orderPb.OrderTag_MERCHANT},
				},
				want: &orderPb.Order{
					Id: "order-1",
				},
				err: nil,
			},
			mockCreateTxn: mockCreateTxn{
				enable: true,
				txn: &paymentPb.Transaction{
					PiFrom:            testInternalActorPidId,
					PiTo:              genericPiId,
					PartnerRefId:      testCardParsedTxnDetails.GetUniqPartnerRefId(),
					Utr:               basicDebitTestTxnDetails.Id,
					Amount:            basicCreditTestTxnDetails.GetAmount(),
					Status:            paymentPb.TransactionStatus_SUCCESS,
					PaymentProtocol:   paymentPb.PaymentProtocol_CARD,
					Remarks:           basicCreditTestTxnDetails.GetRemarks(),
					Particulars:       basicCreditTestTxnDetails.GetParticular(),
					PartnerExecutedAt: testTs,
					PartnerBank:       commonvgpb.Vendor_FEDERAL_BANK,
					DebitedAt:         testTs,
					RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
						paymentPb.AccountingEntryType_DEBIT.String(): {
							Particulars: basicCreditTestTxnDetails.GetParticular(),
							ValueDate:   testValueTs,
						},
					},
					ParticularsDebit:  basicCreditTestTxnDetails.GetParticular(),
					PartnerRefIdDebit: testCardParsedTxnDetails.GetUniqPartnerRefId(),
					DetailedStatus: &paymentPb.TransactionDetailedStatus{
						DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
							{
								CreatedAt: timestamppb.Now(),
								Api:       paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
								State:     paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
							},
						},
					},
					DedupeId: &paymentPb.DedupeId{
						Utr:     "S1234",
						CbsId:   "S1234",
						TxnTime: basicDebitTestTxnDetails.GetTimestamp(),
					},
					Ownership: commontypes.Ownership_FEDERAL_BANK,
					Metadata:  &paymentPb.Metadata{ParserTemplate: &paymentPb.ParserTemplate{}},
				},
				orderId: "order-1",
				want: &paymentPb.Transaction{
					Id: "txn-1",
				},
				err: nil,
			},
			mockPublish: mockPublish{
				enable: true,
				err:    nil,
			},
			mockEvaluate: mockEvaluate{
				enable:      true,
				constraints: release.NewCommonConstraintData(typesPb.Feature_FEATURE_REMITTER_INFO_BACKFILL).WithActorId(testInternalActorId),
				res:         true,
			},
			args: args{
				internalSavingsAccountId: testAccountId,
				internalActorId:          testInternalActorId,
				parsedParticulars:        testCardParsedTxnDetails,
				txnDetails:               basicDebitTestTxnDetails,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				txnStatus:                paymentPb.TransactionStatus_SUCCESS,
				txnDetailedStatus: &paymentPb.TransactionDetailedStatus{
					DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
						{
							Api:   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
							State: paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
						},
					},
				},
				attemptRemitterInfoBackfill: true,
			},
			wantErr:     false,
			wantErrType: nil,
		},
		{
			name: "Should successfully process off app card debit transaction with instrument details populated",
			mockResolveInternalPiDetails: mockResolveInternalActorPiDetails{
				enable:                   true,
				internalSavingsAccountId: testAccountId,
				internalSavingsAcctNo:    testAccountNumber,
				isCardTxn:                true,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				internalPiId:             testInternalActorPidId,
				internalActorName:        testInternalActorName,
				err:                      nil,
				source:                   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
			},
			mockResolveOtherActorPiDetails: mockResolveOtherActorPiDetails{
				enable:            true,
				parsedParticulars: testCardParsedTxnDetails,
				txnDetails:        basicDebitTestTxnWithInstrumentDetails,
				actorId:           testInternalActorId,
				otherActorName:    genericActorName,
				otherActorPiId:    genericPiId,
			},
			mockResolveTimeline: mockResolveTimeline{
				enable:            true,
				internalActorId:   testInternalActorId,
				internalPiId:      testInternalActorPidId,
				internalActorName: testInternalActorName,
				otherPiId:         genericPiId,
				otherActorName:    genericActorName,
				fromActorId:       testInternalActorId,
				fromPiId:          testInternalActorPidId,
				toActorId:         testOtherActorId,
				toPiId:            genericPiId,
				isCredit:          false,
				err:               nil,
			},
			mockCreateOrder: mockCreateOrder{
				enable: true,
				req: &orderPb.Order{
					FromActorId: testInternalActorId,
					ToActorId:   testOtherActorId,
					Workflow:    orderPb.OrderWorkflow_NO_OP,
					Status:      orderPb.OrderStatus_PAID,
					Provenance:  orderPb.OrderProvenance_POS,
					Amount:      basicCreditTestTxnDetails.GetAmount(),
					Tags:        []orderPb.OrderTag{orderPb.OrderTag_MERCHANT},
				},
				want: &orderPb.Order{
					Id: "order-1",
				},
				err: nil,
			},
			mockCreateTxn: mockCreateTxn{
				enable: true,
				txn: &paymentPb.Transaction{
					PiFrom:            testInternalActorPidId,
					PiTo:              genericPiId,
					PartnerRefId:      testCardParsedTxnDetails.GetUniqPartnerRefId(),
					Utr:               basicDebitTestTxnDetails.Id,
					Amount:            basicCreditTestTxnDetails.GetAmount(),
					Status:            paymentPb.TransactionStatus_SUCCESS,
					PaymentProtocol:   paymentPb.PaymentProtocol_CARD,
					Remarks:           basicCreditTestTxnDetails.GetRemarks(),
					Particulars:       basicCreditTestTxnDetails.GetParticular(),
					PartnerExecutedAt: testTs,
					PartnerBank:       commonvgpb.Vendor_FEDERAL_BANK,
					DebitedAt:         testTs,
					RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
						paymentPb.AccountingEntryType_DEBIT.String(): {
							Particulars: basicCreditTestTxnDetails.GetParticular(),
							ValueDate:   testValueTs,
							InstrumentDetails: &paymentPb.InstrumentDetails{
								InstrumentNumber: basicDebitTestTxnWithInstrumentDetails.GetInstrumentDetails().GetInstrumentNumber(),
							},
						},
					},
					ParticularsDebit:  basicCreditTestTxnDetails.GetParticular(),
					PartnerRefIdDebit: testCardParsedTxnDetails.GetUniqPartnerRefId(),
					DetailedStatus: &paymentPb.TransactionDetailedStatus{
						DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
							{
								CreatedAt: timestamppb.Now(),
								Api:       paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
								State:     paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
							},
						},
					},
					DedupeId: &paymentPb.DedupeId{
						Utr:     "S1234",
						CbsId:   "S1234",
						TxnTime: basicDebitTestTxnDetails.GetTimestamp(),
					},
					Ownership: commontypes.Ownership_FEDERAL_BANK,
					Metadata:  &paymentPb.Metadata{ParserTemplate: &paymentPb.ParserTemplate{}},
				},
				orderId: "order-1",
				want: &paymentPb.Transaction{
					Id: "txn-1",
				},
				err: nil,
			},
			mockPublish: mockPublish{
				enable: true,
				err:    nil,
			},
			mockEvaluate: mockEvaluate{
				enable:      true,
				constraints: release.NewCommonConstraintData(typesPb.Feature_FEATURE_REMITTER_INFO_BACKFILL).WithActorId(testInternalActorId),
				res:         true,
			},
			args: args{
				internalSavingsAccountId: testAccountId,
				internalActorId:          testInternalActorId,
				parsedParticulars:        testCardParsedTxnDetails,
				txnDetails:               basicDebitTestTxnWithInstrumentDetails,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				txnStatus:                paymentPb.TransactionStatus_SUCCESS,
				txnDetailedStatus: &paymentPb.TransactionDetailedStatus{
					DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
						{
							Api:   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
							State: paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
						},
					},
				},
				attemptRemitterInfoBackfill: true,
			},
			wantErr:     false,
			wantErrType: nil,
		},
		{
			name: "Should successfully process off-app UPI debit notification under OFF_APP_UPI workflow as FAILED txn (Received Failure upon Enquiry)",
			mockResolveInternalPiDetails: mockResolveInternalActorPiDetails{
				enable:                   true,
				internalSavingsAccountId: testAccountId,
				internalSavingsAcctNo:    testAccountNumber,
				isCardTxn:                false,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				internalPiId:             testInternalActorPidId,
				internalActorName:        testInternalActorName,
				source:                   paymentPb.TransactionDetailedStatus_DetailedStatus_UPI_PAYMENT_ENQUIRY,
				err:                      nil,
			},
			mockResolveOtherActorPiDetails: mockResolveOtherActorPiDetails{
				enable:            true,
				parsedParticulars: testUPIParsedTxnDetails,
				txnDetails:        basicDebitTestTxnDetails,
				actorId:           testInternalActorId,
				otherActorName:    testOtherActorName,
				otherActorPiId:    testOtherActorPiId,
			},
			mockResolveTimeline: mockResolveTimeline{
				enable:            true,
				internalActorId:   testInternalActorId,
				internalPiId:      testInternalActorPidId,
				internalActorName: testInternalActorName,
				otherPiId:         testOtherActorPiId,
				otherActorName:    testOtherActorName,
				fromActorId:       testInternalActorId,
				fromPiId:          testInternalActorPidId,
				toActorId:         testOtherActorId,
				toPiId:            testOtherActorPiId,
				isCredit:          false,
				err:               nil,
			},
			mockCreateOrder: mockCreateOrder{
				enable: true,
				req: &orderPb.Order{
					FromActorId: testInternalActorId,
					ToActorId:   testOtherActorId,
					Workflow:    orderPb.OrderWorkflow_OFF_APP_UPI,
					Status:      orderPb.OrderStatus_PAYMENT_FAILED,
					Provenance:  orderPb.OrderProvenance_EXTERNAL,
					Amount:      basicDebitTestTxnDetails.GetAmount(),
				},
				want: &orderPb.Order{
					Id: "order-1",
				},
				err: nil,
			},
			mockGetPiById: mockGetPiById{
				enable: true,
				piId:   testOtherActorPiId,
				pi: &piPb.PaymentInstrument{
					Identifier: &piPb.PaymentInstrument_Upi{
						Upi: &piPb.Upi{
							IfscCode: "IFSC0011",
						},
					},
				},
			},
			mockCreateTxn: mockCreateTxn{
				enable: true,
				txn: &paymentPb.Transaction{
					PiFrom:            testInternalActorPidId,
					PiTo:              testOtherActorPiId,
					PartnerRefId:      testUPIParsedTxnDetails.GetUniqPartnerRefId(),
					Utr:               testUPIParsedTxnDetails.GetUtr(),
					Amount:            basicDebitTestTxnDetails.GetAmount(),
					Status:            paymentPb.TransactionStatus_FAILED,
					PaymentProtocol:   paymentPb.PaymentProtocol_UPI,
					Remarks:           basicDebitTestTxnDetails.GetRemarks(),
					Particulars:       basicDebitTestTxnDetails.GetParticular(),
					PartnerExecutedAt: testTs,
					PartnerBank:       commonvgpb.Vendor_FEDERAL_BANK,
					DebitedAt:         testTs,
					RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
						paymentPb.AccountingEntryType_DEBIT.String(): {
							Particulars: basicDebitTestTxnDetails.GetParticular(),
							ValueDate:   testValueTs,
						},
					},
					ParticularsDebit:  basicDebitTestTxnDetails.GetParticular(),
					PartnerRefIdDebit: testUPIParsedTxnDetails.GetUniqPartnerRefId(),
					DetailedStatus: &paymentPb.TransactionDetailedStatus{
						DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
							{
								CreatedAt: timestamppb.Now(),
								Api:       paymentPb.TransactionDetailedStatus_DetailedStatus_UPI_PAYMENT_ENQUIRY,
								State:     paymentPb.TransactionDetailedStatus_DetailedStatus_FAILURE,
							},
						},
					},
					DedupeId: &paymentPb.DedupeId{
						Utr:     "utr-2",
						CbsId:   "S1234",
						TxnTime: basicDebitTestTxnDetails.GetTimestamp(),
					},
					Metadata: &paymentPb.Metadata{
						ParserTemplate: &paymentPb.ParserTemplate{},
						VpaDetails: &paymentPb.VpaDetails{
							PayerVpaPiId: "payer-pi-id",
							PayeeVpaPiId: "payee-pi-id",
						},
					},
					Ownership: commontypes.Ownership_FEDERAL_BANK,
				},
				reqInfo: &paymentPb.PaymentRequestInformation{
					ReqId: "",
					UpiInfo: &paymentPb.PaymentRequestInformation_UPI{
						CustomerAccountInfo: &upi.CustomerAccountDetails{
							Ifsc: "IFSC0011",
						},
					},
				},
				orderId: "order-1",
				want: &paymentPb.Transaction{
					Id: "txn-1",
				},
				err: nil,
			},
			mockPublish: mockPublish{
				enable: true,
				err:    nil,
			},
			mockIncomingCreditEventPublish: mockIncomingCreditEventPublish{
				enable: false,
			},
			mockEvaluate: mockEvaluate{
				enable:      false,
				constraints: release.NewCommonConstraintData(typesPb.Feature_FEATURE_REMITTER_INFO_BACKFILL).WithActorId(testInternalActorId),
				res:         true,
			},
			mockCreatePi: []mockCreatePi{
				{
					enable: true,
					wantReq: &piPb.CreatePiRequest{
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.CreatePiRequest_Upi{
							Upi: &piPb.Upi{
								Vpa:  "payer@fifederal",
								Name: "payer",
							},
						},
						VerifiedName: "*****",
						Capabilities: map[string]bool{
							piPb.Capability_INBOUND_TXN.String():  true,
							piPb.Capability_OUTBOUND_TXN.String(): true,
						},
						State:     piPb.PaymentInstrumentState_CREATED,
						Ownership: piPb.Ownership_EPIFI_TECH,
					},
					returnRes: &piPb.CreatePiResponse{
						Status: rpc.StatusOk(),
						PaymentInstrument: &piPb.PaymentInstrument{
							Id: "payer-pi-id",
						},
					},
				},
				{
					enable: true,
					wantReq: &piPb.CreatePiRequest{
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.CreatePiRequest_Upi{
							Upi: &piPb.Upi{
								Vpa:  "payee@fifederal",
								Name: "payee",
							},
						},
						VerifiedName: "*****",
						Capabilities: map[string]bool{
							piPb.Capability_INBOUND_TXN.String():  true,
							piPb.Capability_OUTBOUND_TXN.String(): true,
						},
						State:     piPb.PaymentInstrumentState_CREATED,
						Ownership: piPb.Ownership_EPIFI_TECH,
					},
					returnRes: &piPb.CreatePiResponse{
						Status: rpc.StatusOk(),
						PaymentInstrument: &piPb.PaymentInstrument{
							Id: "payee-pi-id",
						},
					},
				},
			},
			args: args{
				internalSavingsAccountId: testAccountId,
				internalActorId:          testInternalActorId,
				parsedParticulars:        testUPIParsedTxnDetails,
				txnDetails:               basicDebitTestTxnDetails,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				txnStatus:                paymentPb.TransactionStatus_FAILED,
				txnDetailedStatus: &paymentPb.TransactionDetailedStatus{
					DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
						{
							Api:   paymentPb.TransactionDetailedStatus_DetailedStatus_UPI_PAYMENT_ENQUIRY,
							State: paymentPb.TransactionDetailedStatus_DetailedStatus_FAILURE,
						},
					},
				},
				orderWorkflow:               orderPb.OrderWorkflow_OFF_APP_UPI,
				attemptRemitterInfoBackfill: false,
			},
			wantErr:     false,
			wantErrType: nil,
		},
		{
			name: "Should successfully process off-app UPI debit notification under OFF_APP_UPI workflow as UNKNOWN txn (Received UNKNOWN upon Enquiry)",
			mockResolveInternalPiDetails: mockResolveInternalActorPiDetails{
				enable:                   true,
				internalSavingsAccountId: testAccountId,
				internalSavingsAcctNo:    testAccountNumber,
				isCardTxn:                false,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				internalPiId:             testInternalActorPidId,
				internalActorName:        testInternalActorName,
				source:                   paymentPb.TransactionDetailedStatus_DetailedStatus_UPI_PAYMENT_ENQUIRY,
				err:                      nil,
			},
			mockResolveOtherActorPiDetails: mockResolveOtherActorPiDetails{
				enable:            true,
				parsedParticulars: testUPIParsedTxnDetails,
				txnDetails:        basicDebitTestTxnDetails,
				actorId:           testInternalActorId,
				otherActorName:    testOtherActorName,
				otherActorPiId:    testOtherActorPiId,
			},
			mockResolveTimeline: mockResolveTimeline{
				enable:            true,
				internalActorId:   testInternalActorId,
				internalPiId:      testInternalActorPidId,
				internalActorName: testInternalActorName,
				otherPiId:         testOtherActorPiId,
				otherActorName:    testOtherActorName,
				fromActorId:       testInternalActorId,
				fromPiId:          testInternalActorPidId,
				toActorId:         testOtherActorId,
				toPiId:            testOtherActorPiId,
				isCredit:          false,
				err:               nil,
			},
			mockCreateOrder: mockCreateOrder{
				enable: true,
				req: &orderPb.Order{
					FromActorId: testInternalActorId,
					ToActorId:   testOtherActorId,
					Workflow:    orderPb.OrderWorkflow_OFF_APP_UPI,
					Status:      orderPb.OrderStatus_IN_PAYMENT,
					Provenance:  orderPb.OrderProvenance_EXTERNAL,
					Amount:      basicDebitTestTxnDetails.GetAmount(),
				},
				want: &orderPb.Order{
					Id: "order-1",
				},
				err: nil,
			},
			mockGetPiById: mockGetPiById{
				enable: true,
				piId:   testOtherActorPiId,
				pi: &piPb.PaymentInstrument{
					Identifier: &piPb.PaymentInstrument_Upi{
						Upi: &piPb.Upi{
							IfscCode: "IFSC0011",
						},
					},
				},
			},
			mockCreateTxn: mockCreateTxn{
				enable: true,
				txn: &paymentPb.Transaction{
					PiFrom:            testInternalActorPidId,
					PiTo:              testOtherActorPiId,
					PartnerRefId:      testUPIParsedTxnDetails.GetUniqPartnerRefId(),
					Utr:               testUPIParsedTxnDetails.GetUtr(),
					Amount:            basicDebitTestTxnDetails.GetAmount(),
					Status:            paymentPb.TransactionStatus_UNKNOWN,
					PaymentProtocol:   paymentPb.PaymentProtocol_UPI,
					Remarks:           basicDebitTestTxnDetails.GetRemarks(),
					Particulars:       basicDebitTestTxnDetails.GetParticular(),
					PartnerExecutedAt: testTs,
					PartnerBank:       commonvgpb.Vendor_FEDERAL_BANK,
					DebitedAt:         testTs,
					RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
						paymentPb.AccountingEntryType_DEBIT.String(): {
							Particulars: basicDebitTestTxnDetails.GetParticular(),
							ValueDate:   testValueTs,
						},
					},
					ParticularsDebit:  basicDebitTestTxnDetails.GetParticular(),
					PartnerRefIdDebit: testUPIParsedTxnDetails.GetUniqPartnerRefId(),
					DetailedStatus: &paymentPb.TransactionDetailedStatus{
						DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
							{
								CreatedAt: timestamppb.Now(),
								Api:       paymentPb.TransactionDetailedStatus_DetailedStatus_UPI_PAYMENT_ENQUIRY,
								State:     paymentPb.TransactionDetailedStatus_DetailedStatus_UNKNOWN,
							},
						},
					},
					DedupeId: &paymentPb.DedupeId{
						Utr:     "utr-2",
						CbsId:   "S1234",
						TxnTime: basicDebitTestTxnDetails.GetTimestamp(),
					},
					Metadata: &paymentPb.Metadata{
						ParserTemplate: &paymentPb.ParserTemplate{},
						VpaDetails: &paymentPb.VpaDetails{
							PayerVpaPiId: "payer-pi-id",
							PayeeVpaPiId: "payee-pi-id",
						},
					},
					Ownership: commontypes.Ownership_FEDERAL_BANK,
				},
				reqInfo: &paymentPb.PaymentRequestInformation{
					ReqId: "",
					UpiInfo: &paymentPb.PaymentRequestInformation_UPI{
						CustomerAccountInfo: &upi.CustomerAccountDetails{
							Ifsc: "IFSC0011",
						},
					},
				},
				orderId: "order-1",
				want: &paymentPb.Transaction{
					Id: "txn-1",
				},
				err: nil,
			},
			mockPublish: mockPublish{
				enable: true,
				err:    nil,
			},
			mockIncomingCreditEventPublish: mockIncomingCreditEventPublish{
				enable: false,
			},
			mockEvaluate: mockEvaluate{
				enable:      false,
				constraints: release.NewCommonConstraintData(typesPb.Feature_FEATURE_REMITTER_INFO_BACKFILL).WithActorId(testInternalActorId),
				res:         true,
			},
			mockCreatePi: []mockCreatePi{
				{
					enable: true,
					wantReq: &piPb.CreatePiRequest{
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.CreatePiRequest_Upi{
							Upi: &piPb.Upi{
								Vpa:  "payer@fifederal",
								Name: "payer",
							},
						},
						VerifiedName: "*****",
						Capabilities: map[string]bool{
							piPb.Capability_INBOUND_TXN.String():  true,
							piPb.Capability_OUTBOUND_TXN.String(): true,
						},
						State:     piPb.PaymentInstrumentState_CREATED,
						Ownership: piPb.Ownership_EPIFI_TECH,
					},
					returnRes: &piPb.CreatePiResponse{
						Status: rpc.StatusOk(),
						PaymentInstrument: &piPb.PaymentInstrument{
							Id: "payer-pi-id",
						},
					},
				},
				{
					enable: true,
					wantReq: &piPb.CreatePiRequest{
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.CreatePiRequest_Upi{
							Upi: &piPb.Upi{
								Vpa:  "payee@fifederal",
								Name: "payee",
							},
						},
						VerifiedName: "*****",
						Capabilities: map[string]bool{
							piPb.Capability_INBOUND_TXN.String():  true,
							piPb.Capability_OUTBOUND_TXN.String(): true,
						},
						State:     piPb.PaymentInstrumentState_CREATED,
						Ownership: piPb.Ownership_EPIFI_TECH,
					},
					returnRes: &piPb.CreatePiResponse{
						Status: rpc.StatusOk(),
						PaymentInstrument: &piPb.PaymentInstrument{
							Id: "payee-pi-id",
						},
					},
				},
			},
			args: args{
				internalSavingsAccountId: testAccountId,
				internalActorId:          testInternalActorId,
				parsedParticulars:        testUPIParsedTxnDetails,
				txnDetails:               basicDebitTestTxnDetails,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				txnStatus:                paymentPb.TransactionStatus_UNKNOWN,
				txnDetailedStatus: &paymentPb.TransactionDetailedStatus{
					DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
						{
							Api:   paymentPb.TransactionDetailedStatus_DetailedStatus_UPI_PAYMENT_ENQUIRY,
							State: paymentPb.TransactionDetailedStatus_DetailedStatus_UNKNOWN,
						},
					},
				},
				orderWorkflow:               orderPb.OrderWorkflow_OFF_APP_UPI,
				attemptRemitterInfoBackfill: false,
			},
			wantErr:     false,
			wantErrType: nil,
		},
		{
			name: "Should successfully process off-app UPI debit notification under OFF_APP_UPI workflow as in-progress txn (DEEMED scenario)",
			mockResolveInternalPiDetails: mockResolveInternalActorPiDetails{
				enable:                   true,
				internalSavingsAccountId: testAccountId,
				internalSavingsAcctNo:    testAccountNumber,
				isCardTxn:                false,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				internalPiId:             testInternalActorPidId,
				internalActorName:        testInternalActorName,
				source:                   paymentPb.TransactionDetailedStatus_DetailedStatus_UPI_PAYMENT_ENQUIRY,
				err:                      nil,
			},
			mockResolveOtherActorPiDetails: mockResolveOtherActorPiDetails{
				enable:            true,
				parsedParticulars: testUPIParsedTxnDetails,
				txnDetails:        basicDebitTestTxnDetails,
				actorId:           testInternalActorId,
				otherActorName:    testOtherActorName,
				otherActorPiId:    testOtherActorPiId,
			},
			mockResolveTimeline: mockResolveTimeline{
				enable:            true,
				internalActorId:   testInternalActorId,
				internalPiId:      testInternalActorPidId,
				internalActorName: testInternalActorName,
				otherPiId:         testOtherActorPiId,
				otherActorName:    testOtherActorName,
				fromActorId:       testInternalActorId,
				fromPiId:          testInternalActorPidId,
				toActorId:         testOtherActorId,
				toPiId:            testOtherActorPiId,
				isCredit:          false,
				err:               nil,
			},
			mockCreateOrder: mockCreateOrder{
				enable: true,
				req: &orderPb.Order{
					FromActorId: testInternalActorId,
					ToActorId:   testOtherActorId,
					Workflow:    orderPb.OrderWorkflow_OFF_APP_UPI,
					Status:      orderPb.OrderStatus_IN_PAYMENT,
					Provenance:  orderPb.OrderProvenance_EXTERNAL,
					Amount:      basicDebitTestTxnDetails.GetAmount(),
				},
				want: &orderPb.Order{
					Id: "order-1",
				},
				err: nil,
			},
			mockGetPiById: mockGetPiById{
				enable: true,
				piId:   testOtherActorPiId,
				pi: &piPb.PaymentInstrument{
					Identifier: &piPb.PaymentInstrument_Upi{
						Upi: &piPb.Upi{
							IfscCode: "IFSC0011",
						},
					},
				},
			},
			mockCreateTxn: mockCreateTxn{
				enable: true,
				txn: &paymentPb.Transaction{
					PiFrom:            testInternalActorPidId,
					PiTo:              testOtherActorPiId,
					PartnerRefId:      testUPIParsedTxnDetails.GetUniqPartnerRefId(),
					Utr:               testUPIParsedTxnDetails.GetUtr(),
					Amount:            basicDebitTestTxnDetails.GetAmount(),
					Status:            paymentPb.TransactionStatus_IN_PROGRESS,
					PaymentProtocol:   paymentPb.PaymentProtocol_UPI,
					Remarks:           basicDebitTestTxnDetails.GetRemarks(),
					Particulars:       basicDebitTestTxnDetails.GetParticular(),
					PartnerExecutedAt: testTs,
					PartnerBank:       commonvgpb.Vendor_FEDERAL_BANK,
					DebitedAt:         testTs,
					RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
						paymentPb.AccountingEntryType_DEBIT.String(): {
							Particulars: basicDebitTestTxnDetails.GetParticular(),
							ValueDate:   testValueTs,
						},
					},
					ParticularsDebit:  basicDebitTestTxnDetails.GetParticular(),
					PartnerRefIdDebit: testUPIParsedTxnDetails.GetUniqPartnerRefId(),
					DetailedStatus: &paymentPb.TransactionDetailedStatus{
						DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
							{
								CreatedAt: timestamppb.Now(),
								Api:       paymentPb.TransactionDetailedStatus_DetailedStatus_UPI_PAYMENT_ENQUIRY,
								State:     paymentPb.TransactionDetailedStatus_DetailedStatus_DEEMED,
							},
						},
					},
					DedupeId: &paymentPb.DedupeId{
						Utr:     "utr-2",
						CbsId:   "S1234",
						TxnTime: basicDebitTestTxnDetails.GetTimestamp(),
					},
					Metadata: &paymentPb.Metadata{
						ParserTemplate: &paymentPb.ParserTemplate{},
						VpaDetails: &paymentPb.VpaDetails{
							PayerVpaPiId: "payer-pi-id",
							PayeeVpaPiId: "payee-pi-id",
						},
					},
					Ownership: commontypes.Ownership_FEDERAL_BANK,
				},
				reqInfo: &paymentPb.PaymentRequestInformation{
					ReqId: "",
					UpiInfo: &paymentPb.PaymentRequestInformation_UPI{
						CustomerAccountInfo: &upi.CustomerAccountDetails{
							Ifsc: "IFSC0011",
						},
					},
				},
				orderId: "order-1",
				want: &paymentPb.Transaction{
					Id: "txn-1",
				},
				err: nil,
			},
			mockPublish: mockPublish{
				enable: true,
				err:    nil,
			},
			mockIncomingCreditEventPublish: mockIncomingCreditEventPublish{
				enable: false,
			},
			mockEvaluate: mockEvaluate{
				enable:      false,
				constraints: release.NewCommonConstraintData(typesPb.Feature_FEATURE_REMITTER_INFO_BACKFILL).WithActorId(testInternalActorId),
				res:         true,
			},
			mockCreatePi: []mockCreatePi{
				{
					enable: true,
					wantReq: &piPb.CreatePiRequest{
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.CreatePiRequest_Upi{
							Upi: &piPb.Upi{
								Vpa:  "payer@fifederal",
								Name: "payer",
							},
						},
						VerifiedName: "*****",
						Capabilities: map[string]bool{
							piPb.Capability_INBOUND_TXN.String():  true,
							piPb.Capability_OUTBOUND_TXN.String(): true,
						},
						State:     piPb.PaymentInstrumentState_CREATED,
						Ownership: piPb.Ownership_EPIFI_TECH,
					},
					returnRes: &piPb.CreatePiResponse{
						Status: rpc.StatusOk(),
						PaymentInstrument: &piPb.PaymentInstrument{
							Id: "payer-pi-id",
						},
					},
				},
				{
					enable: true,
					wantReq: &piPb.CreatePiRequest{
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.CreatePiRequest_Upi{
							Upi: &piPb.Upi{
								Vpa:  "payee@fifederal",
								Name: "payee",
							},
						},
						VerifiedName: "*****",
						Capabilities: map[string]bool{
							piPb.Capability_INBOUND_TXN.String():  true,
							piPb.Capability_OUTBOUND_TXN.String(): true,
						},
						State:     piPb.PaymentInstrumentState_CREATED,
						Ownership: piPb.Ownership_EPIFI_TECH,
					},
					returnRes: &piPb.CreatePiResponse{
						Status: rpc.StatusOk(),
						PaymentInstrument: &piPb.PaymentInstrument{
							Id: "payee-pi-id",
						},
					},
				},
			},
			args: args{
				internalSavingsAccountId: testAccountId,
				internalActorId:          testInternalActorId,
				parsedParticulars:        testUPIParsedTxnDetails,
				txnDetails:               basicDebitTestTxnDetails,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				txnStatus:                paymentPb.TransactionStatus_IN_PROGRESS,
				txnDetailedStatus: &paymentPb.TransactionDetailedStatus{
					DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
						{
							Api:   paymentPb.TransactionDetailedStatus_DetailedStatus_UPI_PAYMENT_ENQUIRY,
							State: paymentPb.TransactionDetailedStatus_DetailedStatus_DEEMED,
						},
					},
				},
				orderWorkflow:               orderPb.OrderWorkflow_OFF_APP_UPI,
				attemptRemitterInfoBackfill: false,
			},
			wantErr:     false,
			wantErrType: nil,
		},
		{
			name: "Should return permanent error if the OrderWorkflow isn't supported",
			args: args{
				internalSavingsAccountId: testAccountId,
				internalActorId:          testInternalActorId,
				parsedParticulars:        testUPIParsedTxnDetails,
				txnDetails:               basicDebitTestTxnDetails,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				txnStatus:                paymentPb.TransactionStatus_IN_PROGRESS,
				txnDetailedStatus: &paymentPb.TransactionDetailedStatus{
					DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
						{
							Api:   paymentPb.TransactionDetailedStatus_DetailedStatus_UPI_PAYMENT_ENQUIRY,
							State: paymentPb.TransactionDetailedStatus_DetailedStatus_DEEMED,
						},
					},
				},
				orderWorkflow:               orderPb.OrderWorkflow_P2P_FUND_TRANSFER,
				attemptRemitterInfoBackfill: true,
			},
			wantErr:     true,
			wantErrType: epifierrors.ErrPermanent,
		},
		{
			name: "Should return permanent error if the OrderWorkflow and TxnStatus combination isn't supported",
			args: args{
				internalSavingsAccountId: testAccountId,
				internalActorId:          testInternalActorId,
				parsedParticulars:        testUPIParsedTxnDetails,
				txnDetails:               basicDebitTestTxnDetails,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				txnStatus:                paymentPb.TransactionStatus_IN_PROGRESS,
				txnDetailedStatus: &paymentPb.TransactionDetailedStatus{
					DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
						{
							Api:   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
							State: paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
						},
					},
				},
				orderWorkflow:               orderPb.OrderWorkflow_NO_OP,
				attemptRemitterInfoBackfill: true,
			},
			wantErr:     true,
			wantErrType: epifierrors.ErrPermanent,
		},
		{
			name: "Should successfully process off app UPI credit notification and use VPA from parsed particulars",
			mockResolveInternalPiDetails: mockResolveInternalActorPiDetails{
				enable:                   true,
				internalSavingsAccountId: testAccountId,
				internalSavingsAcctNo:    testAccountNumber,
				isCardTxn:                false,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				internalPiId:             testInternalActorPidId,
				internalActorName:        testInternalActorName,
				source:                   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
				err:                      nil,
			},
			mockResolveOtherActorPiDetails: mockResolveOtherActorPiDetails{
				enable:            true,
				parsedParticulars: testUPIParsedTxnDetails,
				txnDetails:        basicCreditTestTxnDetails,
				actorId:           testInternalActorId,
				otherActorName:    testOtherActorName,
				otherActorPiId:    testOtherActorPiId,
			},
			mockResolveTimeline: mockResolveTimeline{
				enable:            true,
				internalActorId:   testInternalActorId,
				internalPiId:      testInternalActorPidId,
				internalActorName: testInternalActorName,
				otherPiId:         testOtherActorPiId,
				otherActorName:    testOtherActorName,
				fromActorId:       testOtherActorId,
				fromPiId:          testOtherActorPiId,
				toActorId:         testInternalActorId,
				toPiId:            testInternalActorPidId,
				isCredit:          true,
				err:               nil,
			},
			mockCreateOrder: mockCreateOrder{
				enable: true,
				req: &orderPb.Order{
					FromActorId: testOtherActorId,
					ToActorId:   testInternalActorId,
					Workflow:    orderPb.OrderWorkflow_NO_OP,
					Status:      orderPb.OrderStatus_PAID,
					Provenance:  orderPb.OrderProvenance_EXTERNAL,
					Amount:      basicCreditTestTxnDetails.GetAmount(),
				},
				want: &orderPb.Order{
					Id: "order-1",
				},
				err: nil,
			},
			mockGetPiById: mockGetPiById{
				enable: true,
				piId:   testOtherActorPiId,
				pi: &piPb.PaymentInstrument{
					Identifier: &piPb.PaymentInstrument_Upi{
						Upi: &piPb.Upi{
							IfscCode: "IFSC0011",
						},
					},
				},
			},
			mockCreateTxn: mockCreateTxn{
				enable: true,
				txn: &paymentPb.Transaction{
					PiFrom:            testOtherActorPiId,
					PiTo:              testInternalActorPidId,
					PartnerRefId:      testUPIParsedTxnDetails.GetUniqPartnerRefId(),
					Utr:               testUPIParsedTxnDetails.GetUtr(),
					Amount:            basicCreditTestTxnDetails.GetAmount(),
					Status:            paymentPb.TransactionStatus_SUCCESS,
					PaymentProtocol:   paymentPb.PaymentProtocol_UPI,
					Remarks:           basicCreditTestTxnDetails.GetRemarks(),
					Particulars:       basicCreditTestTxnDetails.GetParticular(),
					PartnerExecutedAt: testTs,
					PartnerBank:       commonvgpb.Vendor_FEDERAL_BANK,
					CreditedAt:        testTs,
					RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
						paymentPb.AccountingEntryType_CREDIT.String(): {
							Particulars: basicCreditTestTxnDetails.GetParticular(),
							ValueDate:   testValueTs,
						},
					},
					ParticularsCredit:  basicCreditTestTxnDetails.GetParticular(),
					PartnerRefIdCredit: testUPIParsedTxnDetails.GetUniqPartnerRefId(),
					DetailedStatus: &paymentPb.TransactionDetailedStatus{
						DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
							{
								CreatedAt: timestamppb.Now(),
								Api:       paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
								State:     paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
							},
						},
					},
					DedupeId: &paymentPb.DedupeId{
						Utr:     "utr-2",
						CbsId:   "S1234",
						TxnTime: basicDebitTestTxnDetails.GetTimestamp(),
					},
					Metadata: &paymentPb.Metadata{
						ParserTemplate: &paymentPb.ParserTemplate{},
						VpaDetails: &paymentPb.VpaDetails{
							PayerVpaPiId: "payer-pi-id",
							PayeeVpaPiId: "payee-pi-id",
						},
					},
					Ownership: commontypes.Ownership_FEDERAL_BANK,
				},
				reqInfo: &paymentPb.PaymentRequestInformation{
					ReqId: "",
					UpiInfo: &paymentPb.PaymentRequestInformation_UPI{
						CustomerAccountInfo: &upi.CustomerAccountDetails{
							Ifsc: "IFSC0011",
						},
					},
				},
				orderId: "order-1",
				want: &paymentPb.Transaction{
					Id: "txn-1",
				},
				err: nil,
			},
			mockPublish: mockPublish{
				enable: true,
				err:    nil,
			},
			mockIncomingCreditEventPublish: mockIncomingCreditEventPublish{
				enable: true,
			},
			mockEvaluate: mockEvaluate{
				enable:      true,
				constraints: release.NewCommonConstraintData(typesPb.Feature_FEATURE_REMITTER_INFO_BACKFILL).WithActorId(testInternalActorId),
				res:         true,
			},
			mockCreatePi: []mockCreatePi{
				{
					enable: true,
					wantReq: &piPb.CreatePiRequest{
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.CreatePiRequest_Upi{
							Upi: &piPb.Upi{
								Vpa:  "payer@fifederal",
								Name: "payer",
							},
						},
						VerifiedName: "*****",
						Capabilities: map[string]bool{
							piPb.Capability_INBOUND_TXN.String():  true,
							piPb.Capability_OUTBOUND_TXN.String(): true,
						},
						State:     piPb.PaymentInstrumentState_CREATED,
						Ownership: piPb.Ownership_EPIFI_TECH,
					},
					returnRes: &piPb.CreatePiResponse{
						Status: rpc.StatusOk(),
						PaymentInstrument: &piPb.PaymentInstrument{
							Id: "payer-pi-id",
						},
					},
				},
				{
					enable: true,
					wantReq: &piPb.CreatePiRequest{
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.CreatePiRequest_Upi{
							Upi: &piPb.Upi{
								Vpa:  "payee@fifederal",
								Name: "payee",
							},
						},
						VerifiedName: "*****",
						Capabilities: map[string]bool{
							piPb.Capability_INBOUND_TXN.String():  true,
							piPb.Capability_OUTBOUND_TXN.String(): true,
						},
						State:     piPb.PaymentInstrumentState_CREATED,
						Ownership: piPb.Ownership_EPIFI_TECH,
					},
					returnRes: &piPb.CreatePiResponse{
						Status: rpc.StatusOk(),
						PaymentInstrument: &piPb.PaymentInstrument{
							Id: "payee-pi-id",
						},
					},
				},
			},
			args: args{
				internalSavingsAccountId: testAccountId,
				internalActorId:          testInternalActorId,
				parsedParticulars:        testUPIParsedTxnDetails,
				txnDetails:               basicCreditTestTxnDetails,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				txnStatus:                paymentPb.TransactionStatus_SUCCESS,
				txnDetailedStatus: &paymentPb.TransactionDetailedStatus{
					DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
						{
							Api:   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
							State: paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
						},
					},
				},
				attemptRemitterInfoBackfill: true,
			},
			wantErr:     false,
			wantErrType: nil,
		},
		{
			name: "Should successfully process off app UPI credit notification and create PI as the PI corresponding to the parsed VPA is missing",
			mockResolveInternalPiDetails: mockResolveInternalActorPiDetails{
				enable:                   true,
				internalSavingsAccountId: testAccountId,
				internalSavingsAcctNo:    testAccountNumber,
				isCardTxn:                false,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				internalPiId:             testInternalActorPidId,
				internalActorName:        testInternalActorName,
				source:                   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
				err:                      nil,
			},
			mockResolveOtherActorPiDetails: mockResolveOtherActorPiDetails{
				enable:            true,
				parsedParticulars: testUPIParsedTxnDetails,
				txnDetails:        basicCreditTestTxnDetails,
				actorId:           testInternalActorId,
				otherActorName:    testOtherActorName,
				otherActorPiId:    testOtherActorPiId,
			},
			mockResolveTimeline: mockResolveTimeline{
				enable:            true,
				internalActorId:   testInternalActorId,
				internalPiId:      testInternalActorPidId,
				internalActorName: testInternalActorName,
				otherPiId:         testOtherActorPiId,
				otherActorName:    testOtherActorName,
				fromActorId:       testOtherActorId,
				fromPiId:          testOtherActorPiId,
				toActorId:         testInternalActorId,
				toPiId:            testInternalActorPidId,
				isCredit:          true,
				err:               nil,
			},
			mockCreateOrder: mockCreateOrder{
				enable: true,
				req: &orderPb.Order{
					FromActorId: testOtherActorId,
					ToActorId:   testInternalActorId,
					Workflow:    orderPb.OrderWorkflow_NO_OP,
					Status:      orderPb.OrderStatus_PAID,
					Provenance:  orderPb.OrderProvenance_EXTERNAL,
					Amount:      basicCreditTestTxnDetails.GetAmount(),
				},
				want: &orderPb.Order{
					Id: "order-1",
				},
				err: nil,
			},
			mockCreatePi: []mockCreatePi{
				{
					enable: true,
					wantReq: &piPb.CreatePiRequest{
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.CreatePiRequest_Upi{
							Upi: &piPb.Upi{
								Vpa:  "payer@fifederal",
								Name: "payer",
							},
						},
						VerifiedName: "*****",
						Capabilities: map[string]bool{
							piPb.Capability_INBOUND_TXN.String():  true,
							piPb.Capability_OUTBOUND_TXN.String(): true,
						},
						State:     piPb.PaymentInstrumentState_CREATED,
						Ownership: piPb.Ownership_EPIFI_TECH,
					},
					returnRes: &piPb.CreatePiResponse{
						Status: rpc.StatusOk(),
						PaymentInstrument: &piPb.PaymentInstrument{
							Id: "payer-pi-id",
						},
					},
				},
				{
					enable: true,
					wantReq: &piPb.CreatePiRequest{
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.CreatePiRequest_Upi{
							Upi: &piPb.Upi{
								Vpa:  "payee@fifederal",
								Name: "payee",
							},
						},
						VerifiedName: "*****",
						Capabilities: map[string]bool{
							piPb.Capability_INBOUND_TXN.String():  true,
							piPb.Capability_OUTBOUND_TXN.String(): true,
						},
						State:     piPb.PaymentInstrumentState_CREATED,
						Ownership: piPb.Ownership_EPIFI_TECH,
					},
					returnRes: &piPb.CreatePiResponse{
						Status: rpc.StatusOk(),
						PaymentInstrument: &piPb.PaymentInstrument{
							Id: "payee-pi-id",
						},
					},
				},
			},
			mockCreateTxn: mockCreateTxn{
				enable: true,
				txn: &paymentPb.Transaction{
					PiFrom:            testOtherActorPiId,
					PiTo:              testInternalActorPidId,
					PartnerRefId:      testUPIParsedTxnDetails.GetUniqPartnerRefId(),
					Utr:               testUPIParsedTxnDetails.GetUtr(),
					Amount:            basicCreditTestTxnDetails.GetAmount(),
					Status:            paymentPb.TransactionStatus_SUCCESS,
					PaymentProtocol:   paymentPb.PaymentProtocol_UPI,
					Remarks:           basicCreditTestTxnDetails.GetRemarks(),
					Particulars:       basicCreditTestTxnDetails.GetParticular(),
					PartnerExecutedAt: testTs,
					PartnerBank:       commonvgpb.Vendor_FEDERAL_BANK,
					CreditedAt:        testTs,
					RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
						paymentPb.AccountingEntryType_CREDIT.String(): {
							Particulars: basicCreditTestTxnDetails.GetParticular(),
							ValueDate:   testValueTs,
						},
					},
					ParticularsCredit:  basicCreditTestTxnDetails.GetParticular(),
					PartnerRefIdCredit: testUPIParsedTxnDetails.GetUniqPartnerRefId(),
					DetailedStatus: &paymentPb.TransactionDetailedStatus{
						DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
							{
								CreatedAt: timestamppb.Now(),
								Api:       paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
								State:     paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
							},
						},
					},
					DedupeId: &paymentPb.DedupeId{
						Utr:     "utr-2",
						CbsId:   "S1234",
						TxnTime: basicDebitTestTxnDetails.GetTimestamp(),
					},
					Metadata: &paymentPb.Metadata{
						ParserTemplate: &paymentPb.ParserTemplate{},
						VpaDetails: &paymentPb.VpaDetails{
							PayerVpaPiId: "payer-pi-id",
							PayeeVpaPiId: "payee-pi-id",
						},
					},
					Ownership: commontypes.Ownership_FEDERAL_BANK,
				},
				orderId: "order-1",
				want: &paymentPb.Transaction{
					Id: "txn-1",
				},
				err: nil,
			},
			mockPublish: mockPublish{
				enable: true,
				err:    nil,
			},
			mockGetPiById: mockGetPiById{
				enable: true,
				piId:   testOtherActorPiId,
				pi: &piPb.PaymentInstrument{
					Identifier: &piPb.PaymentInstrument_Upi{},
				},
			},
			mockIncomingCreditEventPublish: mockIncomingCreditEventPublish{
				enable: true,
			},
			mockEvaluate: mockEvaluate{
				enable:      true,
				constraints: release.NewCommonConstraintData(typesPb.Feature_FEATURE_REMITTER_INFO_BACKFILL).WithActorId(testInternalActorId),
				res:         true,
			},
			args: args{
				internalSavingsAccountId: testAccountId,
				internalActorId:          testInternalActorId,
				parsedParticulars:        testUPIParsedTxnDetails,
				txnDetails:               basicCreditTestTxnDetails,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				txnStatus:                paymentPb.TransactionStatus_SUCCESS,
				txnDetailedStatus: &paymentPb.TransactionDetailedStatus{
					DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
						{
							Api:   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
							State: paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
						},
					},
				},
				attemptRemitterInfoBackfill: true,
			},
			wantErr:     false,
			wantErrType: nil,
		},
		{
			name: "Should successfully process off app UPI credit notification and fallback to generic PI as parsed VPA is invalid",
			mockResolveInternalPiDetails: mockResolveInternalActorPiDetails{
				enable:                   true,
				internalSavingsAccountId: testAccountId,
				internalSavingsAcctNo:    testAccountNumber,
				isCardTxn:                false,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				internalPiId:             testInternalActorPidId,
				internalActorName:        testInternalActorName,
				source:                   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
				err:                      nil,
			},
			mockResolveOtherActorPiDetails: mockResolveOtherActorPiDetails{
				enable: true,
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol:       paymentPb.PaymentProtocol_UPI,
					OtherActorName: genericActorName,
					PiIdentifier: &paymentNotificationPb.ParsedTxnParticulars_Vpa{
						Vpa: testVPA,
					},
					Utr:              "utr-2",
					UniqPartnerRefId: "S1234",
				},
				txnDetails:     basicCreditTestTxnDetails,
				actorId:        testInternalActorId,
				otherActorName: genericActorName,
				otherActorPiId: genericPiId,
			},
			mockResolveTimeline: mockResolveTimeline{
				enable:            true,
				internalActorId:   testInternalActorId,
				internalPiId:      testInternalActorPidId,
				internalActorName: testInternalActorName,
				otherPiId:         genericPiId,
				otherActorName:    genericActorName,
				fromActorId:       testOtherActorId,
				fromPiId:          genericPiId,
				toActorId:         testInternalActorId,
				toPiId:            testInternalActorPidId,
				isCredit:          true,
				err:               nil,
			},
			mockCreateOrder: mockCreateOrder{
				enable: true,
				req: &orderPb.Order{
					FromActorId: testOtherActorId,
					ToActorId:   testInternalActorId,
					Workflow:    orderPb.OrderWorkflow_NO_OP,
					Status:      orderPb.OrderStatus_PAID,
					Provenance:  orderPb.OrderProvenance_EXTERNAL,
					Amount:      basicCreditTestTxnDetails.GetAmount(),
				},
				want: &orderPb.Order{
					Id: "order-1",
				},
				err: nil,
			},
			mockCreateTxn: mockCreateTxn{
				enable: true,
				txn: &paymentPb.Transaction{
					PiFrom:            genericPiId,
					PiTo:              testInternalActorPidId,
					PartnerRefId:      "S1234",
					Utr:               testUPIParsedTxnDetails.GetUtr(),
					Amount:            basicCreditTestTxnDetails.GetAmount(),
					Status:            paymentPb.TransactionStatus_SUCCESS,
					PaymentProtocol:   paymentPb.PaymentProtocol_UPI,
					Remarks:           basicCreditTestTxnDetails.GetRemarks(),
					Particulars:       basicCreditTestTxnDetails.GetParticular(),
					PartnerExecutedAt: testTs,
					PartnerBank:       commonvgpb.Vendor_FEDERAL_BANK,
					CreditedAt:        testTs,
					RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
						paymentPb.AccountingEntryType_CREDIT.String(): {
							Particulars: basicCreditTestTxnDetails.GetParticular(),
							ValueDate:   testValueTs,
						},
					},
					ParticularsCredit:  basicCreditTestTxnDetails.GetParticular(),
					PartnerRefIdCredit: "S1234",
					DetailedStatus: &paymentPb.TransactionDetailedStatus{
						DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
							{
								CreatedAt: timestamppb.Now(),
								Api:       paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
								State:     paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
							},
						},
					},
					DedupeId: &paymentPb.DedupeId{
						Utr:     "utr-2",
						CbsId:   "S1234",
						TxnTime: basicDebitTestTxnDetails.GetTimestamp(),
					},
					Metadata:  &paymentPb.Metadata{ParserTemplate: &paymentPb.ParserTemplate{}},
					Ownership: commontypes.Ownership_FEDERAL_BANK,
				},
				orderId: "order-1",
				want: &paymentPb.Transaction{
					Id: "txn-1",
				},
				err: nil,
			},
			mockGetPiById: mockGetPiById{
				enable: true,
				piId:   genericPiId,
				pi: &piPb.PaymentInstrument{
					Identifier: &piPb.PaymentInstrument_Upi{},
				},
			},
			mockPublish: mockPublish{
				enable: true,
				err:    nil,
			},
			mockIncomingCreditEventPublish: mockIncomingCreditEventPublish{
				enable: true,
			},
			mockEvaluate: mockEvaluate{
				enable:      true,
				constraints: release.NewCommonConstraintData(typesPb.Feature_FEATURE_REMITTER_INFO_BACKFILL).WithActorId(testInternalActorId),
				res:         true,
			},
			args: args{
				internalSavingsAccountId: testAccountId,
				internalActorId:          testInternalActorId,
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol:       paymentPb.PaymentProtocol_UPI,
					OtherActorName: genericActorName,
					PiIdentifier: &paymentNotificationPb.ParsedTxnParticulars_Vpa{
						Vpa: testVPA,
					},
					Utr:              "utr-2",
					UniqPartnerRefId: "S1234",
				},
				txnDetails:  basicCreditTestTxnDetails,
				partnerBank: commonvgpb.Vendor_FEDERAL_BANK,
				txnStatus:   paymentPb.TransactionStatus_SUCCESS,
				txnDetailedStatus: &paymentPb.TransactionDetailedStatus{
					DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
						{
							Api:   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
							State: paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
						},
					},
				},
				attemptRemitterInfoBackfill: true,
			},
			wantErr:     false,
			wantErrType: nil,
		},
		{
			name: "Should successfully process off app UPI credit notification and fallback to generic PI as parsed VPA is invalid",
			mockResolveInternalPiDetails: mockResolveInternalActorPiDetails{
				enable:                   true,
				internalSavingsAccountId: testAccountId,
				internalSavingsAcctNo:    testAccountNumber,
				isCardTxn:                false,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				internalPiId:             testInternalActorPidId,
				internalActorName:        testInternalActorName,
				source:                   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
				err:                      nil,
			},
			mockResolveOtherActorPiDetails: mockResolveOtherActorPiDetails{
				enable: true,
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol:       paymentPb.PaymentProtocol_UPI,
					OtherActorName: genericActorName,
					PiIdentifier: &paymentNotificationPb.ParsedTxnParticulars_Vpa{
						Vpa: testVPA,
					},
					Utr:              "utr-2",
					UniqPartnerRefId: "S1234",
				},
				txnDetails:     basicCreditTestTxnDetails,
				actorId:        testInternalActorId,
				otherActorName: genericActorName,
				otherActorPiId: genericPiId,
			},
			mockResolveTimeline: mockResolveTimeline{
				enable:            true,
				internalActorId:   testInternalActorId,
				internalPiId:      testInternalActorPidId,
				internalActorName: testInternalActorName,
				otherPiId:         genericPiId,
				otherActorName:    genericActorName,
				fromActorId:       testOtherActorId,
				fromPiId:          genericPiId,
				toActorId:         testInternalActorId,
				toPiId:            testInternalActorPidId,
				isCredit:          true,
				err:               nil,
			},
			mockCreateOrder: mockCreateOrder{
				enable: true,
				req: &orderPb.Order{
					FromActorId: testOtherActorId,
					ToActorId:   testInternalActorId,
					Workflow:    orderPb.OrderWorkflow_NO_OP,
					Status:      orderPb.OrderStatus_PAID,
					Provenance:  orderPb.OrderProvenance_EXTERNAL,
					Amount:      basicCreditTestTxnDetails.GetAmount(),
				},
				want: &orderPb.Order{
					Id: "order-1",
				},
				err: nil,
			},
			mockGetPiById: mockGetPiById{
				enable: true,
				piId:   genericPiId,
				pi: &piPb.PaymentInstrument{
					Identifier: &piPb.PaymentInstrument_Upi{},
				},
			},
			mockCreateTxn: mockCreateTxn{
				enable: true,
				txn: &paymentPb.Transaction{
					PiFrom:            genericPiId,
					PiTo:              testInternalActorPidId,
					PartnerRefId:      "S1234",
					Utr:               testUPIParsedTxnDetails.GetUtr(),
					Amount:            basicCreditTestTxnDetails.GetAmount(),
					Status:            paymentPb.TransactionStatus_SUCCESS,
					PaymentProtocol:   paymentPb.PaymentProtocol_UPI,
					Remarks:           basicCreditTestTxnDetails.GetRemarks(),
					Particulars:       basicCreditTestTxnDetails.GetParticular(),
					PartnerExecutedAt: testTs,
					PartnerBank:       commonvgpb.Vendor_FEDERAL_BANK,
					CreditedAt:        testTs,
					RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
						paymentPb.AccountingEntryType_CREDIT.String(): {
							Particulars: basicCreditTestTxnDetails.GetParticular(),
							ValueDate:   testValueTs,
						},
					},
					ParticularsCredit:  basicCreditTestTxnDetails.GetParticular(),
					PartnerRefIdCredit: "S1234",
					DetailedStatus: &paymentPb.TransactionDetailedStatus{
						DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
							{
								CreatedAt: timestamppb.Now(),
								Api:       paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
								State:     paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
							},
						},
					},
					DedupeId: &paymentPb.DedupeId{
						Utr:     "utr-2",
						CbsId:   "S1234",
						TxnTime: basicDebitTestTxnDetails.GetTimestamp(),
					},
					Metadata:  &paymentPb.Metadata{ParserTemplate: &paymentPb.ParserTemplate{}},
					Ownership: commontypes.Ownership_FEDERAL_BANK,
				},
				orderId: "order-1",
				want: &paymentPb.Transaction{
					Id: "txn-1",
				},
				err: nil,
			},
			mockPublish: mockPublish{
				enable: true,
				err:    nil,
			},
			mockIncomingCreditEventPublish: mockIncomingCreditEventPublish{
				enable: true,
			},
			mockEvaluate: mockEvaluate{
				enable:      true,
				constraints: release.NewCommonConstraintData(typesPb.Feature_FEATURE_REMITTER_INFO_BACKFILL).WithActorId(testInternalActorId),
				res:         true,
			},
			args: args{
				internalSavingsAccountId: testAccountId,
				internalActorId:          testInternalActorId,
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol:       paymentPb.PaymentProtocol_UPI,
					OtherActorName: genericActorName,
					PiIdentifier: &paymentNotificationPb.ParsedTxnParticulars_Vpa{
						Vpa: testVPA,
					},
					Utr:              "utr-2",
					UniqPartnerRefId: "S1234",
				},
				txnDetails:  basicCreditTestTxnDetails,
				partnerBank: commonvgpb.Vendor_FEDERAL_BANK,
				txnStatus:   paymentPb.TransactionStatus_SUCCESS,
				txnDetailedStatus: &paymentPb.TransactionDetailedStatus{
					DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
						{
							Api:   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
							State: paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
						},
					},
				},
				attemptRemitterInfoBackfill: true,
			},
			wantErr:     false,
			wantErrType: nil,
		},
		{
			name: "Should successfully process off app neft credit notification and create a new generic type pi",
			mockResolveInternalPiDetails: mockResolveInternalActorPiDetails{
				enable:                   true,
				internalSavingsAccountId: testAccountId,
				internalSavingsAcctNo:    testAccountNumber,
				isCardTxn:                false,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				internalPiId:             testInternalActorPidId,
				internalActorName:        testInternalActorName,
				source:                   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
				err:                      nil,
			},
			mockResolveOtherActorPiDetails: mockResolveOtherActorPiDetails{
				enable: true,
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol:       paymentPb.PaymentProtocol_NEFT,
					OtherActorName: "actor enterprises",
					PiIdentifier: &paymentNotificationPb.ParsedTxnParticulars_Account{
						Account: &paymentNotificationPb.ParsedTxnParticulars_AccountIdentifier{
							AccountNumber: pay.DefaultAccountNumber,
							IfscCode:      pay.DefaultIfscCode,
							PiType:        piPb.PaymentInstrumentType_GENERIC,
							AccountType:   accountPb.Type_SAVINGS,
						},
					},
					Utr:              "utr-1",
					UniqPartnerRefId: "S1234",
				},
				txnDetails:     basicCreditTestTxnDetails,
				actorId:        testInternalActorId,
				otherActorName: "actor enterprises",
				otherActorPiId: testOtherActorPiId,
			},
			mockResolveTimeline: mockResolveTimeline{
				enable:            true,
				internalActorId:   testInternalActorId,
				internalPiId:      testInternalActorPidId,
				internalActorName: testInternalActorName,
				otherPiId:         testOtherActorPiId,
				otherActorName:    "actor enterprises",
				fromActorId:       testOtherActorId,
				fromPiId:          testOtherActorPiId,
				toActorId:         testInternalActorId,
				toPiId:            testInternalActorPidId,
				isCredit:          true,
				err:               nil,
			},
			mockCreateOrder: mockCreateOrder{
				enable: true,
				req: &orderPb.Order{
					FromActorId: testOtherActorId,
					ToActorId:   testInternalActorId,
					Workflow:    orderPb.OrderWorkflow_NO_OP,
					Status:      orderPb.OrderStatus_PAID,
					Provenance:  orderPb.OrderProvenance_EXTERNAL,
					Amount:      basicCreditTestTxnDetails.GetAmount(),
					Tags:        []orderPb.OrderTag{orderPb.OrderTag_PREDICTED_MERCHANT},
				},
				want: &orderPb.Order{
					Id: "order-1",
				},
				err: nil,
			},
			mockCreateTxn: mockCreateTxn{
				enable: true,
				txn: &paymentPb.Transaction{
					PiFrom:            testOtherActorPiId,
					PiTo:              testInternalActorPidId,
					PartnerRefId:      "S1234",
					Utr:               basicCreditTestTxnDetails.GetReferenceNumber(),
					Amount:            basicCreditTestTxnDetails.GetAmount(),
					Status:            paymentPb.TransactionStatus_SUCCESS,
					PaymentProtocol:   paymentPb.PaymentProtocol_NEFT,
					Remarks:           basicCreditTestTxnDetails.GetRemarks(),
					Particulars:       basicCreditTestTxnDetails.GetParticular(),
					PartnerExecutedAt: testTs,
					PartnerBank:       commonvgpb.Vendor_FEDERAL_BANK,
					CreditedAt:        testTs,
					RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
						paymentPb.AccountingEntryType_CREDIT.String(): {
							Particulars: basicCreditTestTxnDetails.GetParticular(),
							ValueDate:   testValueTs,
						},
					},
					ParticularsCredit:  basicCreditTestTxnDetails.GetParticular(),
					PartnerRefIdCredit: "S1234",
					DetailedStatus: &paymentPb.TransactionDetailedStatus{
						DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
							{
								CreatedAt: timestamppb.Now(),
								Api:       paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
								State:     paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
							},
						},
					},
					DedupeId: &paymentPb.DedupeId{
						Utr:     "utr-1",
						CbsId:   "S1234",
						TxnTime: basicDebitTestTxnDetails.GetTimestamp(),
					},
					Metadata: &paymentPb.Metadata{
						ParserTemplate:         &paymentPb.ParserTemplate{},
						FetchedRemitterDetails: &paymentPb.EnrichedPiDetailsOfTransactingActors{PayerPiId: "pi-2", PayerName: "actor enterprises"}},
					Ownership: commontypes.Ownership_FEDERAL_BANK,
				},
				orderId: "order-1",
				want: &paymentPb.Transaction{
					Id: "txn-1",
				},
				err: nil,
			},
			mockCreateAccountPi: mockCreateAccountPi{
				enable:        false,
				name:          testOtherActorName,
				accountNumber: pay.DefaultAccountNumber,
				ifscCode:      pay.DefaultIfscCode,
				piType:        piPb.PaymentInstrumentType_GENERIC,
				accountType:   accountPb.Type_SAVINGS,
				want:          &piPb.PaymentInstrument{Id: testOtherActorPiId},
			},
			mockPublish: mockPublish{
				enable: true,
				err:    nil,
			},
			mockIncomingCreditEventPublish: mockIncomingCreditEventPublish{
				enable: true,
			},
			mockEvaluate: mockEvaluate{
				enable:      true,
				constraints: release.NewCommonConstraintData(typesPb.Feature_FEATURE_REMITTER_INFO_BACKFILL).WithActorId(testInternalActorId),
				res:         true,
			},
			args: args{
				internalSavingsAccountId: testAccountId,
				internalActorId:          testInternalActorId,
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol:       paymentPb.PaymentProtocol_NEFT,
					OtherActorName: "actor enterprises",
					PiIdentifier: &paymentNotificationPb.ParsedTxnParticulars_Account{
						Account: &paymentNotificationPb.ParsedTxnParticulars_AccountIdentifier{
							AccountNumber: pay.DefaultAccountNumber,
							IfscCode:      pay.DefaultIfscCode,
							PiType:        piPb.PaymentInstrumentType_GENERIC,
							AccountType:   accountPb.Type_SAVINGS,
						},
					},
					Utr:              "utr-1",
					UniqPartnerRefId: "S1234",
				},
				txnDetails:  basicCreditTestTxnDetails,
				partnerBank: commonvgpb.Vendor_FEDERAL_BANK,
				txnStatus:   paymentPb.TransactionStatus_SUCCESS,
				txnDetailedStatus: &paymentPb.TransactionDetailedStatus{
					DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
						{
							Api:   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
							State: paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
						},
					},
				},
				attemptRemitterInfoBackfill: true,
			},
			wantErr:     false,
			wantErrType: nil,
		},
		{
			name: "Should successfully process off app neft credit notification and create a partial account pi",
			mockResolveInternalPiDetails: mockResolveInternalActorPiDetails{
				enable:                   true,
				internalSavingsAccountId: testAccountId,
				internalSavingsAcctNo:    testAccountNumber,
				isCardTxn:                false,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				internalPiId:             testInternalActorPidId,
				internalActorName:        testInternalActorName,
				source:                   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
				err:                      nil,
			},
			mockResolveOtherActorPiDetails: mockResolveOtherActorPiDetails{
				enable: true,
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol:       paymentPb.PaymentProtocol_NEFT,
					OtherActorName: "actor enterprises",
					PiIdentifier: &paymentNotificationPb.ParsedTxnParticulars_Account{
						Account: &paymentNotificationPb.ParsedTxnParticulars_AccountIdentifier{
							AccountNumber: "12345",
							IfscCode:      pay.DefaultIfscCode,
							PiType:        piPb.PaymentInstrumentType_PARTIAL_BANK_ACCOUNT,
							AccountType:   accountPb.Type_SAVINGS,
						},
					},
					Utr:              "utr-1",
					UniqPartnerRefId: "S1234",
				},
				txnDetails:     basicCreditTestTxnDetails,
				actorId:        testInternalActorId,
				otherActorName: "actor enterprises",
				otherActorPiId: testOtherActorPiId,
			},
			mockResolveTimeline: mockResolveTimeline{
				enable:            true,
				internalActorId:   testInternalActorId,
				internalPiId:      testInternalActorPidId,
				internalActorName: testInternalActorName,
				otherPiId:         testOtherActorPiId,
				otherActorName:    "actor enterprises",
				fromActorId:       testOtherActorId,
				fromPiId:          testOtherActorPiId,
				toActorId:         testInternalActorId,
				toPiId:            testInternalActorPidId,
				isCredit:          true,
				err:               nil,
			},
			mockCreateOrder: mockCreateOrder{
				enable: true,
				req: &orderPb.Order{
					FromActorId: testOtherActorId,
					ToActorId:   testInternalActorId,
					Workflow:    orderPb.OrderWorkflow_NO_OP,
					Status:      orderPb.OrderStatus_PAID,
					Provenance:  orderPb.OrderProvenance_EXTERNAL,
					Amount:      basicCreditTestTxnDetails.GetAmount(),
					Tags:        []orderPb.OrderTag{orderPb.OrderTag_PREDICTED_MERCHANT},
				},
				want: &orderPb.Order{
					Id: "order-1",
				},
				err: nil,
			},
			mockCreateTxn: mockCreateTxn{
				enable: true,
				txn: &paymentPb.Transaction{
					PiFrom:            testOtherActorPiId,
					PiTo:              testInternalActorPidId,
					PartnerRefId:      "S1234",
					Utr:               basicCreditTestTxnDetails.GetReferenceNumber(),
					Amount:            basicCreditTestTxnDetails.GetAmount(),
					Status:            paymentPb.TransactionStatus_SUCCESS,
					PaymentProtocol:   paymentPb.PaymentProtocol_NEFT,
					Remarks:           basicCreditTestTxnDetails.GetRemarks(),
					Particulars:       basicCreditTestTxnDetails.GetParticular(),
					PartnerExecutedAt: testTs,
					PartnerBank:       commonvgpb.Vendor_FEDERAL_BANK,
					CreditedAt:        testTs,
					RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
						paymentPb.AccountingEntryType_CREDIT.String(): {
							Particulars: basicCreditTestTxnDetails.GetParticular(),
							ValueDate:   testValueTs,
						},
					},
					ParticularsCredit:  basicCreditTestTxnDetails.GetParticular(),
					PartnerRefIdCredit: "S1234",
					DetailedStatus: &paymentPb.TransactionDetailedStatus{
						DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
							{
								CreatedAt: timestamppb.Now(),
								Api:       paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
								State:     paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
							},
						},
					},
					DedupeId: &paymentPb.DedupeId{
						Utr:     "utr-1",
						CbsId:   "S1234",
						TxnTime: basicDebitTestTxnDetails.GetTimestamp(),
					},
					Metadata:  &paymentPb.Metadata{ParserTemplate: &paymentPb.ParserTemplate{}, FetchedRemitterDetails: &paymentPb.EnrichedPiDetailsOfTransactingActors{PayerPiId: "pi-2", PayerName: "actor enterprises"}},
					Ownership: commontypes.Ownership_FEDERAL_BANK,
				},
				orderId: "order-1",
				want: &paymentPb.Transaction{
					Id: "txn-1",
				},
				err: nil,
			},
			mockCreateAccountPi: mockCreateAccountPi{
				enable:        false,
				name:          testOtherActorName,
				accountNumber: "12345",
				ifscCode:      pay.DefaultIfscCode,
				piType:        piPb.PaymentInstrumentType_PARTIAL_BANK_ACCOUNT,
				accountType:   accountPb.Type_SAVINGS,
				want:          &piPb.PaymentInstrument{Id: testOtherActorPiId},
			},
			mockPublish: mockPublish{
				enable: true,
				err:    nil,
			},
			mockIncomingCreditEventPublish: mockIncomingCreditEventPublish{
				enable: true,
			},
			mockEvaluate: mockEvaluate{
				enable:      true,
				constraints: release.NewCommonConstraintData(typesPb.Feature_FEATURE_REMITTER_INFO_BACKFILL).WithActorId(testInternalActorId),
				res:         true,
			},
			args: args{
				internalSavingsAccountId: testAccountId,
				internalActorId:          testInternalActorId,
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol:       paymentPb.PaymentProtocol_NEFT,
					OtherActorName: "actor enterprises",
					PiIdentifier: &paymentNotificationPb.ParsedTxnParticulars_Account{
						Account: &paymentNotificationPb.ParsedTxnParticulars_AccountIdentifier{
							AccountNumber: "12345",
							IfscCode:      pay.DefaultIfscCode,
							PiType:        piPb.PaymentInstrumentType_PARTIAL_BANK_ACCOUNT,
							AccountType:   accountPb.Type_SAVINGS,
						},
					},
					Utr:              "utr-1",
					UniqPartnerRefId: "S1234",
				},
				txnDetails:  basicCreditTestTxnDetails,
				partnerBank: commonvgpb.Vendor_FEDERAL_BANK,
				txnStatus:   paymentPb.TransactionStatus_SUCCESS,
				txnDetailedStatus: &paymentPb.TransactionDetailedStatus{
					DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
						{
							Api:   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
							State: paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
						},
					},
				},
				attemptRemitterInfoBackfill: true,
			},
			wantErr:     false,
			wantErrType: nil,
		},
		{
			name: "Should successfully process off app UPI credit notification and use pi id parsed particulars",
			mockResolveInternalPiDetails: mockResolveInternalActorPiDetails{
				enable:                   true,
				internalSavingsAccountId: testAccountId,
				internalSavingsAcctNo:    testAccountNumber,
				isCardTxn:                false,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				internalPiId:             testInternalActorPidId,
				internalActorName:        testInternalActorName,
				source:                   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
				err:                      nil,
			},
			mockResolveOtherActorPiDetails: mockResolveOtherActorPiDetails{
				enable: true,
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol:       paymentPb.PaymentProtocol_UPI,
					OtherActorName: testOtherActorName,
					PiIdentifier: &paymentNotificationPb.ParsedTxnParticulars_PiId{
						PiId: testOtherActorPiId,
					},
					Utr:              "utr-2",
					UniqPartnerRefId: "S1234",
				},
				txnDetails:     basicCreditTestTxnDetails,
				actorId:        testInternalActorId,
				otherActorName: testOtherActorName,
				otherActorPiId: testOtherActorPiId,
			},
			mockResolveTimeline: mockResolveTimeline{
				enable:            true,
				internalActorId:   testInternalActorId,
				internalPiId:      testInternalActorPidId,
				internalActorName: testInternalActorName,
				otherPiId:         testOtherActorPiId,
				otherActorName:    testOtherActorName,
				fromActorId:       testOtherActorId,
				fromPiId:          testOtherActorPiId,
				toActorId:         testInternalActorId,
				toPiId:            testInternalActorPidId,
				isCredit:          true,
				err:               nil,
			},
			mockCreateOrder: mockCreateOrder{
				enable: true,
				req: &orderPb.Order{
					FromActorId: testOtherActorId,
					ToActorId:   testInternalActorId,
					Workflow:    orderPb.OrderWorkflow_NO_OP,
					Status:      orderPb.OrderStatus_PAID,
					Provenance:  orderPb.OrderProvenance_EXTERNAL,
					Amount:      basicCreditTestTxnDetails.GetAmount(),
				},
				want: &orderPb.Order{
					Id: "order-1",
				},
				err: nil,
			},
			mockGetPiById: mockGetPiById{
				enable: true,
				piId:   testOtherActorPiId,
				pi: &piPb.PaymentInstrument{
					Identifier: &piPb.PaymentInstrument_Upi{
						Upi: &piPb.Upi{
							IfscCode: "IFSC0011",
						},
					},
				},
			},
			mockCreateTxn: mockCreateTxn{
				enable: true,
				txn: &paymentPb.Transaction{
					PiFrom:            testOtherActorPiId,
					PiTo:              testInternalActorPidId,
					PartnerRefId:      testUPIParsedTxnDetails.GetUniqPartnerRefId(),
					Utr:               testUPIParsedTxnDetails.GetUtr(),
					Amount:            basicCreditTestTxnDetails.GetAmount(),
					Status:            paymentPb.TransactionStatus_SUCCESS,
					PaymentProtocol:   paymentPb.PaymentProtocol_UPI,
					Remarks:           basicCreditTestTxnDetails.GetRemarks(),
					Particulars:       basicCreditTestTxnDetails.GetParticular(),
					PartnerExecutedAt: testTs,
					PartnerBank:       commonvgpb.Vendor_FEDERAL_BANK,
					CreditedAt:        testTs,
					RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
						paymentPb.AccountingEntryType_CREDIT.String(): {
							Particulars: basicCreditTestTxnDetails.GetParticular(),
							ValueDate:   testValueTs,
						},
					},
					ParticularsCredit:  basicCreditTestTxnDetails.GetParticular(),
					PartnerRefIdCredit: testUPIParsedTxnDetails.GetUniqPartnerRefId(),
					DetailedStatus: &paymentPb.TransactionDetailedStatus{
						DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
							{
								CreatedAt: timestamppb.Now(),
								Api:       paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
								State:     paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
							},
						},
					},
					DedupeId: &paymentPb.DedupeId{
						Utr:     "utr-2",
						CbsId:   "S1234",
						TxnTime: basicDebitTestTxnDetails.GetTimestamp(),
					},
					Metadata:  &paymentPb.Metadata{ParserTemplate: &paymentPb.ParserTemplate{}},
					Ownership: commontypes.Ownership_FEDERAL_BANK,
				},
				orderId: "order-1",
				reqInfo: &paymentPb.PaymentRequestInformation{
					UpiInfo: &paymentPb.PaymentRequestInformation_UPI{
						CustomerAccountInfo: &upi.CustomerAccountDetails{
							Ifsc: "IFSC0011",
						},
					},
				},
				want: &paymentPb.Transaction{
					Id: "txn-1",
				},
				err: nil,
			},
			mockPublish: mockPublish{
				enable: true,
				err:    nil,
			},
			mockIncomingCreditEventPublish: mockIncomingCreditEventPublish{
				enable: true,
			},
			mockEvaluate: mockEvaluate{
				enable:      true,
				constraints: release.NewCommonConstraintData(typesPb.Feature_FEATURE_REMITTER_INFO_BACKFILL).WithActorId(testInternalActorId),
				res:         true,
			},
			args: args{
				internalSavingsAccountId: testAccountId,
				internalActorId:          testInternalActorId,
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol:       paymentPb.PaymentProtocol_UPI,
					OtherActorName: testOtherActorName,
					PiIdentifier: &paymentNotificationPb.ParsedTxnParticulars_PiId{
						PiId: testOtherActorPiId,
					},
					Utr:              "utr-2",
					UniqPartnerRefId: "S1234",
				},
				txnDetails:  basicCreditTestTxnDetails,
				partnerBank: commonvgpb.Vendor_FEDERAL_BANK,
				txnStatus:   paymentPb.TransactionStatus_SUCCESS,
				txnDetailedStatus: &paymentPb.TransactionDetailedStatus{
					DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
						{
							Api:   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
							State: paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
						},
					},
				},
				attemptRemitterInfoBackfill: true,
			},
			wantErr:     false,
			wantErrType: nil,
		},
		{
			name: "Should successfully process extended add funds payment",
			mockResolveInternalPiDetails: mockResolveInternalActorPiDetails{
				enable:                   true,
				internalSavingsAccountId: testAccountId,
				internalSavingsAcctNo:    testAccountNumber,
				isCardTxn:                false,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				internalPiId:             testInternalActorPidId,
				internalActorName:        testInternalActorName,
				source:                   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
				err:                      nil,
			},
			mockResolveOtherActorPiDetails: mockResolveOtherActorPiDetails{
				enable: true,
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol:         paymentPb.PaymentProtocol_UPI,
					OtherActorName:   genericActorName,
					PiIdentifier:     &paymentNotificationPb.ParsedTxnParticulars_PiId{PiId: pay.FederalPoolAccountPiId},
					Utr:              "EXTENDED:utr-2",
					UniqPartnerRefId: "S1234",
					ReqId:            pay.AddFundsReqIdPrefix + "123i432",
				},
				txnDetails:     basicDebitTestTxnDetails,
				actorId:        testInternalActorId,
				otherActorName: pay.DefaultFederalBankActorName,
				otherActorPiId: pay.FederalPoolAccountPiId,
			},
			mockResolveTimeline: mockResolveTimeline{
				enable:            true,
				internalActorId:   testInternalActorId,
				internalPiId:      testInternalActorPidId,
				internalActorName: testInternalActorName,
				otherPiId:         pay.FederalPoolAccountPiId,
				otherActorName:    pay.DefaultFederalBankActorName,
				fromActorId:       testInternalActorId,
				fromPiId:          testInternalActorPidId,
				toActorId:         testOtherActorId,
				toPiId:            pay.FederalPoolAccountPiId,
				isCredit:          false,
				err:               nil,
			},
			mockCreateOrder: mockCreateOrder{
				enable: true,
				req: &orderPb.Order{
					FromActorId: testInternalActorId,
					ToActorId:   testOtherActorId,
					Workflow:    orderPb.OrderWorkflow_NO_OP,
					Status:      orderPb.OrderStatus_PAID,
					Provenance:  orderPb.OrderProvenance_EXTERNAL,
					Amount:      basicCreditTestTxnDetails.GetAmount(),
				},
				want: &orderPb.Order{
					Id: "order-1",
				},
				err: nil,
			},
			mockGetPiById: mockGetPiById{
				enable: true,
				piId:   pay.FederalPoolAccountPiId,
				pi: &piPb.PaymentInstrument{
					Identifier: &piPb.PaymentInstrument_Upi{
						Upi: &piPb.Upi{
							IfscCode: "IFSC00000",
						},
					},
				},
			},
			mockCreateTxn: mockCreateTxn{
				enable: true,
				txn: &paymentPb.Transaction{
					PiFrom:            testInternalActorPidId,
					PiTo:              pay.FederalPoolAccountPiId,
					PartnerRefId:      "S1234",
					Utr:               convertToExtendedIdentifier(testUPIParsedTxnDetails.GetUtr()),
					Amount:            basicDebitTestTxnDetails.GetAmount(),
					Status:            paymentPb.TransactionStatus_SUCCESS,
					PaymentProtocol:   paymentPb.PaymentProtocol_UPI,
					Remarks:           basicDebitTestTxnDetails.GetRemarks(),
					Particulars:       basicDebitTestTxnDetails.GetParticular(),
					PartnerExecutedAt: testTs,
					PartnerBank:       commonvgpb.Vendor_FEDERAL_BANK,
					DebitedAt:         testTs,
					RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
						paymentPb.AccountingEntryType_DEBIT.String(): {
							Particulars: basicDebitTestTxnDetails.GetParticular(),
							ValueDate:   testValueTs,
						},
					},
					ParticularsDebit:  basicDebitTestTxnDetails.GetParticular(),
					PartnerRefIdDebit: "S1234",
					DetailedStatus: &paymentPb.TransactionDetailedStatus{
						DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
							{
								CreatedAt: timestamppb.Now(),
								Api:       paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
								State:     paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
							},
						},
					},
					DedupeId: &paymentPb.DedupeId{
						RequestId: "FBLEPIFI123i432",
						Utr:       "EXTENDED:utr-2",
						CbsId:     "S1234",
						TxnTime:   basicDebitTestTxnDetails.GetTimestamp(),
					},
					Metadata:  &paymentPb.Metadata{ParserTemplate: &paymentPb.ParserTemplate{}},
					Ownership: commontypes.Ownership_FEDERAL_BANK,
				},
				orderId: "order-1",
				reqInfo: &paymentPb.PaymentRequestInformation{
					UpiInfo: &paymentPb.PaymentRequestInformation_UPI{
						CustomerAccountInfo: &upi.CustomerAccountDetails{
							Ifsc: "IFSC00000",
						},
					},
				},
				want: &paymentPb.Transaction{
					Id: "txn-1",
				},
				err: nil,
			},
			mockPublish: mockPublish{
				enable: true,
				err:    nil,
			},
			mockEvaluate: mockEvaluate{
				enable:      true,
				constraints: release.NewCommonConstraintData(typesPb.Feature_FEATURE_REMITTER_INFO_BACKFILL).WithActorId(testInternalActorId),
				res:         true,
			},
			mockIsNewAddFundsVpaEnabledForActor: mockIsNewAddFundsVpaEnabledForActor{
				enable:  true,
				actorId: testInternalActorId,
				want:    false,
			},
			args: args{
				internalSavingsAccountId: testAccountId,
				internalActorId:          testInternalActorId,
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol:       paymentPb.PaymentProtocol_UPI,
					OtherActorName: genericActorName,
					PiIdentifier: &paymentNotificationPb.ParsedTxnParticulars_Vpa{
						Vpa: testVPA,
					},
					Utr:              "utr-2",
					UniqPartnerRefId: "S1234",
					ReqId:            pay.AddFundsReqIdPrefix + "123i432",
				},
				txnDetails:  basicDebitTestTxnDetails,
				partnerBank: commonvgpb.Vendor_FEDERAL_BANK,
				txnStatus:   paymentPb.TransactionStatus_SUCCESS,
				txnDetailedStatus: &paymentPb.TransactionDetailedStatus{
					DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
						{
							Api:   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
							State: paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
						},
					},
				},
				attemptRemitterInfoBackfill: true,
			},
			wantErr:     false,
			wantErrType: nil,
		},
		{
			name: "Should successfully process off app card debit POS notification and add INTERNATIONAL tag to order",
			mockResolveInternalPiDetails: mockResolveInternalActorPiDetails{
				enable:                   true,
				internalSavingsAccountId: testAccountId,
				internalSavingsAcctNo:    testAccountNumber,
				isCardTxn:                true,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				internalPiId:             testInternalActorPidId,
				internalActorName:        testInternalActorName,
				source:                   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
				err:                      nil,
			},
			mockResolveOtherActorPiDetails: mockResolveOtherActorPiDetails{
				enable: true,
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol:         paymentPb.PaymentProtocol_CARD,
					OtherActorName:   testOtherActorName,
					TxnCategory:      paymentNotificationPb.ParsedTxnParticulars_POS,
					Utr:              "S1234",
					UniqPartnerRefId: "S1234",
					MerchantDetails: &paymentNotificationPb.MerchantDetails{
						CountryCode: "NY",
					},
				},
				txnDetails:     basicDebitTestTxnDetails,
				actorId:        testInternalActorId,
				otherActorName: genericActorName,
				otherActorPiId: genericPiId,
			},
			mockResolveTimeline: mockResolveTimeline{
				enable:            true,
				internalActorId:   testInternalActorId,
				internalPiId:      testInternalActorPidId,
				internalActorName: testInternalActorName,
				otherPiId:         genericPiId,
				otherActorName:    genericActorName,
				fromActorId:       testInternalActorId,
				fromPiId:          testInternalActorPidId,
				toActorId:         testOtherActorId,
				toPiId:            genericPiId,
				isCredit:          false,
				err:               nil,
			},
			mockCreateOrder: mockCreateOrder{
				enable: true,
				req: &orderPb.Order{
					FromActorId: testInternalActorId,
					ToActorId:   testOtherActorId,
					Workflow:    orderPb.OrderWorkflow_NO_OP,
					Status:      orderPb.OrderStatus_PAID,
					Provenance:  orderPb.OrderProvenance_POS,
					Amount:      basicCreditTestTxnDetails.GetAmount(),
					Tags:        []orderPb.OrderTag{orderPb.OrderTag_MERCHANT, orderPb.OrderTag_INTERNATIONAL},
				},
				want: &orderPb.Order{
					Id:   "order-1",
					Tags: []orderPb.OrderTag{orderPb.OrderTag_MERCHANT, orderPb.OrderTag_INTERNATIONAL},
				},
				err: nil,
			},
			mockCreateTxn: mockCreateTxn{
				enable: true,
				txn: &paymentPb.Transaction{
					PiFrom:            testInternalActorPidId,
					PiTo:              genericPiId,
					PartnerRefId:      testCardParsedTxnDetails.GetUniqPartnerRefId(),
					Utr:               basicDebitTestTxnDetails.Id,
					Amount:            basicCreditTestTxnDetails.GetAmount(),
					Status:            paymentPb.TransactionStatus_SUCCESS,
					PaymentProtocol:   paymentPb.PaymentProtocol_CARD,
					Remarks:           basicCreditTestTxnDetails.GetRemarks(),
					Particulars:       basicCreditTestTxnDetails.GetParticular(),
					PartnerExecutedAt: testTs,
					PartnerBank:       commonvgpb.Vendor_FEDERAL_BANK,
					DebitedAt:         testTs,
					RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
						paymentPb.AccountingEntryType_DEBIT.String(): {
							Particulars: basicCreditTestTxnDetails.GetParticular(),
							ValueDate:   testValueTs,
							CountryCode: "NY",
						},
					},
					ParticularsDebit:  basicCreditTestTxnDetails.GetParticular(),
					PartnerRefIdDebit: testCardParsedTxnDetails.GetUniqPartnerRefId(),
					DetailedStatus: &paymentPb.TransactionDetailedStatus{
						DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
							{
								CreatedAt: timestamppb.Now(),
								Api:       paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
								State:     paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
							},
						},
					},
					DedupeId: &paymentPb.DedupeId{
						Utr:     "S1234",
						CbsId:   "S1234",
						TxnTime: basicDebitTestTxnDetails.GetTimestamp(),
					},
					Metadata:  &paymentPb.Metadata{ParserTemplate: &paymentPb.ParserTemplate{}},
					Ownership: commontypes.Ownership_FEDERAL_BANK,
				},
				orderId: "order-1",
				want: &paymentPb.Transaction{
					Id: "txn-1",
				},
				err: nil,
			},
			mockPublish: mockPublish{
				enable: true,
				err:    nil,
			},
			mockEvaluate: mockEvaluate{
				enable:      true,
				constraints: release.NewCommonConstraintData(typesPb.Feature_FEATURE_REMITTER_INFO_BACKFILL).WithActorId(testInternalActorId),
				res:         true,
			},
			args: args{
				internalSavingsAccountId: testAccountId,
				internalActorId:          testInternalActorId,
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol:         paymentPb.PaymentProtocol_CARD,
					OtherActorName:   testOtherActorName,
					TxnCategory:      paymentNotificationPb.ParsedTxnParticulars_POS,
					Utr:              "S1234",
					UniqPartnerRefId: "S1234",
					MerchantDetails: &paymentNotificationPb.MerchantDetails{
						CountryCode: "NY",
					},
				},
				txnDetails:  basicDebitTestTxnDetails,
				partnerBank: commonvgpb.Vendor_FEDERAL_BANK,
				txnStatus:   paymentPb.TransactionStatus_SUCCESS,
				txnDetailedStatus: &paymentPb.TransactionDetailedStatus{
					DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
						{
							Api:   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
							State: paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
						},
					},
				},
				attemptRemitterInfoBackfill: true,
			},
			wantErr:     false,
			wantErrType: nil,
		},
		{
			name: "Should successfully process off app card debit ATM withdrawl notification and add INTERNATIONAL tag to order",
			mockResolveInternalPiDetails: mockResolveInternalActorPiDetails{
				enable:                   true,
				internalSavingsAccountId: testAccountId,
				internalSavingsAcctNo:    testAccountNumber,
				isCardTxn:                true,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				internalPiId:             testInternalActorPidId,
				internalActorName:        testInternalActorName,
				source:                   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
				err:                      nil,
			},
			mockResolveOtherActorPiDetails: mockResolveOtherActorPiDetails{
				enable: true,
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol:         paymentPb.PaymentProtocol_CARD,
					OtherActorName:   testOtherActorName,
					TxnCategory:      paymentNotificationPb.ParsedTxnParticulars_ATM_WITHDRAWAL,
					Utr:              "S1234",
					UniqPartnerRefId: "S1234",
					MerchantDetails: &paymentNotificationPb.MerchantDetails{
						CountryCode: "NY",
					},
				},
				txnDetails:     basicDebitTestTxnDetails,
				actorId:        testInternalActorId,
				otherActorName: genericActorName,
				otherActorPiId: genericPiId,
			},
			mockResolveTimeline: mockResolveTimeline{
				enable:            true,
				internalActorId:   testInternalActorId,
				internalPiId:      testInternalActorPidId,
				internalActorName: testInternalActorName,
				otherPiId:         genericPiId,
				otherActorName:    genericActorName,
				fromActorId:       testInternalActorId,
				fromPiId:          testInternalActorPidId,
				toActorId:         testOtherActorId,
				toPiId:            genericPiId,
				isCredit:          false,
				err:               nil,
			},
			mockCreateOrder: mockCreateOrder{
				enable: true,
				req: &orderPb.Order{
					FromActorId: testInternalActorId,
					ToActorId:   testOtherActorId,
					Workflow:    orderPb.OrderWorkflow_NO_OP,
					Status:      orderPb.OrderStatus_PAID,
					Provenance:  orderPb.OrderProvenance_ATM,
					Amount:      basicCreditTestTxnDetails.GetAmount(),
					Tags:        []orderPb.OrderTag{orderPb.OrderTag_INTERNATIONAL},
				},
				want: &orderPb.Order{
					Id:   "order-1",
					Tags: []orderPb.OrderTag{orderPb.OrderTag_MERCHANT, orderPb.OrderTag_INTERNATIONAL},
				},
				err: nil,
			},
			mockCreateTxn: mockCreateTxn{
				enable: true,
				txn: &paymentPb.Transaction{
					PiFrom:            testInternalActorPidId,
					PiTo:              genericPiId,
					PartnerRefId:      testCardParsedTxnDetails.GetUniqPartnerRefId(),
					Utr:               basicDebitTestTxnDetails.Id,
					Amount:            basicCreditTestTxnDetails.GetAmount(),
					Status:            paymentPb.TransactionStatus_SUCCESS,
					PaymentProtocol:   paymentPb.PaymentProtocol_CARD,
					Remarks:           basicCreditTestTxnDetails.GetRemarks(),
					Particulars:       basicCreditTestTxnDetails.GetParticular(),
					PartnerExecutedAt: testTs,
					PartnerBank:       commonvgpb.Vendor_FEDERAL_BANK,
					DebitedAt:         testTs,
					RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
						paymentPb.AccountingEntryType_DEBIT.String(): {
							Particulars: basicCreditTestTxnDetails.GetParticular(),
							ValueDate:   testValueTs,
							CountryCode: "NY",
						},
					},
					ParticularsDebit:  basicCreditTestTxnDetails.GetParticular(),
					PartnerRefIdDebit: testCardParsedTxnDetails.GetUniqPartnerRefId(),
					DetailedStatus: &paymentPb.TransactionDetailedStatus{
						DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
							{
								CreatedAt: timestamppb.Now(),
								Api:       paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
								State:     paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
							},
						},
					},
					DedupeId: &paymentPb.DedupeId{
						Utr:     "S1234",
						CbsId:   "S1234",
						TxnTime: basicDebitTestTxnDetails.GetTimestamp(),
					},
					Metadata:  &paymentPb.Metadata{ParserTemplate: &paymentPb.ParserTemplate{}},
					Ownership: commontypes.Ownership_FEDERAL_BANK,
				},
				orderId: "order-1",
				want: &paymentPb.Transaction{
					Id: "txn-1",
				},
				err: nil,
			},
			mockPublish: mockPublish{
				enable: true,
				err:    nil,
			},
			mockEvaluate: mockEvaluate{
				enable:      true,
				constraints: release.NewCommonConstraintData(typesPb.Feature_FEATURE_REMITTER_INFO_BACKFILL).WithActorId(testInternalActorId),
				res:         true,
			},
			args: args{
				internalSavingsAccountId: testAccountId,
				internalActorId:          testInternalActorId,
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol:         paymentPb.PaymentProtocol_CARD,
					OtherActorName:   testOtherActorName,
					TxnCategory:      paymentNotificationPb.ParsedTxnParticulars_ATM_WITHDRAWAL,
					Utr:              "S1234",
					UniqPartnerRefId: "S1234",
					MerchantDetails: &paymentNotificationPb.MerchantDetails{
						CountryCode: "NY",
					},
				},
				txnDetails:  basicDebitTestTxnDetails,
				partnerBank: commonvgpb.Vendor_FEDERAL_BANK,
				txnStatus:   paymentPb.TransactionStatus_SUCCESS,
				txnDetailedStatus: &paymentPb.TransactionDetailedStatus{
					DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
						{
							Api:   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
							State: paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
						},
					},
				},
				attemptRemitterInfoBackfill: true,
			},
			wantErr:     false,
			wantErrType: nil,
		},
		{
			name: "Should successfully process off app UPI credit notification with validate address failing in last attempt",
			mockResolveInternalPiDetails: mockResolveInternalActorPiDetails{
				enable:                   true,
				internalSavingsAccountId: testAccountId,
				internalSavingsAcctNo:    testAccountNumber,
				isCardTxn:                false,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				internalPiId:             testInternalActorPidId,
				internalActorName:        testInternalActorName,
				source:                   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
				err:                      nil,
			},
			mockResolveOtherActorPiDetails: mockResolveOtherActorPiDetails{
				enable:            true,
				parsedParticulars: testUPIParsedTxnDetails,
				txnDetails:        basicCreditTestTxnDetails,
				actorId:           testInternalActorId,
				otherActorName:    "test",
				otherActorPiId:    testOtherActorPiId,
				err:               piProcessor.ErrorValidatingVpa,
			},
			mockCreateUpiPi: mockCreateUpiPi{
				enable:        true,
				name:          "test",
				vpa:           "test@fede",
				isVpaVerified: false,
				piId:          testOtherActorPiId,
			},
			mockResolveTimeline: mockResolveTimeline{
				enable:            true,
				internalActorId:   testInternalActorId,
				internalPiId:      testInternalActorPidId,
				internalActorName: testInternalActorName,
				otherPiId:         testOtherActorPiId,
				otherActorName:    "test",
				fromActorId:       testOtherActorId,
				fromPiId:          testOtherActorPiId,
				toActorId:         testInternalActorId,
				toPiId:            testInternalActorPidId,
				isCredit:          true,
				err:               nil,
			},
			mockCreateOrder: mockCreateOrder{
				enable: true,
				req: &orderPb.Order{
					FromActorId: testOtherActorId,
					ToActorId:   testInternalActorId,
					Workflow:    orderPb.OrderWorkflow_NO_OP,
					Status:      orderPb.OrderStatus_PAID,
					Provenance:  orderPb.OrderProvenance_EXTERNAL,
					Amount:      basicCreditTestTxnDetails.GetAmount(),
				},
				want: &orderPb.Order{
					Id: "order-1",
				},
				err: nil,
			},
			mockGetPiById: mockGetPiById{
				enable: true,
				piId:   testOtherActorPiId,
				pi: &piPb.PaymentInstrument{
					Identifier: &piPb.PaymentInstrument_Upi{},
				},
			},
			mockCreatePi: []mockCreatePi{
				{
					enable: true,
					wantReq: &piPb.CreatePiRequest{
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.CreatePiRequest_Upi{
							Upi: &piPb.Upi{
								Vpa:  "payer@fifederal",
								Name: "payer",
							},
						},
						VerifiedName: "*****",
						Capabilities: map[string]bool{
							piPb.Capability_INBOUND_TXN.String():  true,
							piPb.Capability_OUTBOUND_TXN.String(): true,
						},
						State:     piPb.PaymentInstrumentState_CREATED,
						Ownership: piPb.Ownership_EPIFI_TECH,
					},
					returnRes: &piPb.CreatePiResponse{
						Status: rpc.StatusOk(),
						PaymentInstrument: &piPb.PaymentInstrument{
							Id: "payer-pi-id",
						},
					},
				},
				{
					enable: true,
					wantReq: &piPb.CreatePiRequest{
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.CreatePiRequest_Upi{
							Upi: &piPb.Upi{
								Vpa:  "payee@fifederal",
								Name: "payee",
							},
						},
						VerifiedName: "*****",
						Capabilities: map[string]bool{
							piPb.Capability_INBOUND_TXN.String():  true,
							piPb.Capability_OUTBOUND_TXN.String(): true,
						},
						State:     piPb.PaymentInstrumentState_CREATED,
						Ownership: piPb.Ownership_EPIFI_TECH,
					},
					returnRes: &piPb.CreatePiResponse{
						Status: rpc.StatusOk(),
						PaymentInstrument: &piPb.PaymentInstrument{
							Id: "payee-pi-id",
						},
					},
				},
			},
			mockCreateTxn: mockCreateTxn{
				enable: true,
				txn: &paymentPb.Transaction{
					PiFrom:            testOtherActorPiId,
					PiTo:              testInternalActorPidId,
					PartnerRefId:      testUPIParsedTxnDetails.GetUniqPartnerRefId(),
					Utr:               testUPIParsedTxnDetails.GetUtr(),
					Amount:            basicCreditTestTxnDetails.GetAmount(),
					Status:            paymentPb.TransactionStatus_SUCCESS,
					PaymentProtocol:   paymentPb.PaymentProtocol_UPI,
					Remarks:           basicCreditTestTxnDetails.GetRemarks(),
					Particulars:       basicCreditTestTxnDetails.GetParticular(),
					PartnerExecutedAt: testTs,
					PartnerBank:       commonvgpb.Vendor_FEDERAL_BANK,
					CreditedAt:        testTs,
					RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
						paymentPb.AccountingEntryType_CREDIT.String(): {
							Particulars: basicCreditTestTxnDetails.GetParticular(),
							ValueDate:   testValueTs,
						},
					},
					ParticularsCredit:  basicCreditTestTxnDetails.GetParticular(),
					PartnerRefIdCredit: testUPIParsedTxnDetails.GetUniqPartnerRefId(),
					DetailedStatus: &paymentPb.TransactionDetailedStatus{
						DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
							{
								CreatedAt: timestamppb.Now(),
								Api:       paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
								State:     paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
							},
						},
					},
					DedupeId: &paymentPb.DedupeId{
						Utr:     "utr-2",
						CbsId:   "S1234",
						TxnTime: basicDebitTestTxnDetails.GetTimestamp(),
					},
					Metadata: &paymentPb.Metadata{
						ParserTemplate: &paymentPb.ParserTemplate{},
						VpaDetails: &paymentPb.VpaDetails{
							PayerVpaPiId: "payer-pi-id",
							PayeeVpaPiId: "payee-pi-id",
						},
					},
					Ownership: commontypes.Ownership_FEDERAL_BANK,
				},
				orderId: "order-1",
				want: &paymentPb.Transaction{
					Id: "txn-1",
				},
				err: nil,
			},
			mockPublish: mockPublish{
				enable: true,
				err:    nil,
			},
			mockIncomingCreditEventPublish: mockIncomingCreditEventPublish{
				enable: true,
			},
			mockVpaVerificationPublisher: mockVpaVerificationPublisher{
				enable: true,
				req: &consumer.VerifyVpaRequest{
					UpiVpa:       "test@fede",
					PayerActorId: testInternalActorId,
				},
				delay: ts.conf.ReVerifyAddressDelay(),
			},
			mockEvaluate: mockEvaluate{
				enable:      true,
				constraints: release.NewCommonConstraintData(typesPb.Feature_FEATURE_REMITTER_INFO_BACKFILL).WithActorId(testInternalActorId),
				res:         true,
			},
			args: args{
				internalSavingsAccountId: testAccountId,
				internalActorId:          testInternalActorId,
				parsedParticulars:        testUPIParsedTxnDetails,
				txnDetails:               basicCreditTestTxnDetails,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				txnStatus:                paymentPb.TransactionStatus_SUCCESS,
				txnDetailedStatus: &paymentPb.TransactionDetailedStatus{
					DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
						{
							Api:   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
							State: paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
						},
					},
				},
				isLastAttempt:               true,
				attemptRemitterInfoBackfill: true,
			},
		},
		{
			name: "Should create order with txn in the last attempt even if failed to fetch the remitter info",
			mockResolveInternalPiDetails: mockResolveInternalActorPiDetails{
				enable:                   true,
				internalSavingsAccountId: testAccountId,
				internalSavingsAcctNo:    testAccountNumber,
				isCardTxn:                false,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				internalPiId:             testInternalActorPidId,
				internalActorName:        testInternalActorName,
				source:                   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
				err:                      nil,
			},
			mockResolveOtherActorPiDetails: mockResolveOtherActorPiDetails{
				enable: true,
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol:       paymentPb.PaymentProtocol_NEFT,
					OtherActorName: "actor enterprises",
					PiIdentifier: &paymentNotificationPb.ParsedTxnParticulars_Account{
						Account: &paymentNotificationPb.ParsedTxnParticulars_AccountIdentifier{
							AccountNumber: "12345",
							IfscCode:      pay.DefaultIfscCode,
							PiType:        piPb.PaymentInstrumentType_PARTIAL_BANK_ACCOUNT,
							AccountType:   accountPb.Type_SAVINGS,
						},
					},
					Utr:              "utr-1",
					UniqPartnerRefId: "S1234",
				},
				txnDetails:     basicCreditTestTxnDetails,
				actorId:        testInternalActorId,
				otherActorName: "actor enterprises",
				otherActorPiId: testOtherActorPiId,
				err:            piProcessor.ErrorFetchingRemitterInfo,
			},
			mockResolveTimeline: mockResolveTimeline{
				enable:            true,
				internalActorId:   testInternalActorId,
				internalPiId:      testInternalActorPidId,
				internalActorName: testInternalActorName,
				otherPiId:         testOtherActorPiId,
				otherActorName:    "actor enterprises",
				fromActorId:       testOtherActorId,
				fromPiId:          testOtherActorPiId,
				toActorId:         testInternalActorId,
				toPiId:            testInternalActorPidId,
				isCredit:          true,
				err:               nil,
			},
			mockCreateOrder: mockCreateOrder{
				enable: true,
				req: &orderPb.Order{
					FromActorId: testOtherActorId,
					ToActorId:   testInternalActorId,
					Workflow:    orderPb.OrderWorkflow_NO_OP,
					Status:      orderPb.OrderStatus_PAID,
					Provenance:  orderPb.OrderProvenance_EXTERNAL,
					Amount:      basicCreditTestTxnDetails.GetAmount(),
					Tags:        []orderPb.OrderTag{orderPb.OrderTag_MERCHANT, orderPb.OrderTag_INTERNATIONAL},
				},
				want: &orderPb.Order{
					Id: "order-1",
				},
				err: nil,
			},
			mockCreateTxn: mockCreateTxn{
				enable: true,
				txn: &paymentPb.Transaction{
					PiFrom:            testOtherActorPiId,
					PiTo:              testInternalActorPidId,
					PartnerRefId:      "S1234",
					Utr:               basicCreditTestTxnDetails.GetReferenceNumber(),
					Amount:            basicCreditTestTxnDetails.GetAmount(),
					Status:            paymentPb.TransactionStatus_SUCCESS,
					PaymentProtocol:   paymentPb.PaymentProtocol_NEFT,
					Remarks:           basicCreditTestTxnDetails.GetRemarks(),
					Particulars:       basicCreditTestTxnDetails.GetParticular(),
					PartnerExecutedAt: testTs,
					PartnerBank:       commonvgpb.Vendor_FEDERAL_BANK,
					CreditedAt:        testTs,
					RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
						paymentPb.AccountingEntryType_CREDIT.String(): {
							Particulars: basicCreditTestTxnDetails.GetParticular(),
							ValueDate:   testValueTs,
						},
					},
					ParticularsCredit:  basicCreditTestTxnDetails.GetParticular(),
					PartnerRefIdCredit: "S1234",
					DetailedStatus: &paymentPb.TransactionDetailedStatus{
						DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
							{
								CreatedAt: timestamppb.Now(),
								Api:       paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
								State:     paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
							},
						},
					},
					DedupeId: &paymentPb.DedupeId{
						Utr:     "utr-1",
						CbsId:   "S1234",
						TxnTime: basicDebitTestTxnDetails.GetTimestamp(),
					},
					Metadata:  &paymentPb.Metadata{ParserTemplate: &paymentPb.ParserTemplate{}, FetchedRemitterDetails: &paymentPb.EnrichedPiDetailsOfTransactingActors{PayerPiId: "pi-2", PayerName: "actor enterprises"}},
					Ownership: commontypes.Ownership_FEDERAL_BANK,
				},
				orderId: "order-1",
				want: &paymentPb.Transaction{
					Id: "txn-1",
				},
				err: nil,
			},
			mockCreateAccountPi: mockCreateAccountPi{
				enable:        true,
				name:          "actor enterprises",
				verifiedName:  "",
				accountNumber: "12345",
				ifscCode:      pay.DefaultIfscCode,
				piType:        piPb.PaymentInstrumentType_PARTIAL_BANK_ACCOUNT,
				accountType:   accountPb.Type_SAVINGS,
				want:          &piPb.PaymentInstrument{Id: testOtherActorPiId, VerifiedName: "", Identifier: &piPb.PaymentInstrument_Account{Account: &piPb.Account{Name: "actor enterprises"}}},
			},
			mockPublish: mockPublish{
				enable: true,
				err:    nil,
			},
			mockIncomingCreditEventPublish: mockIncomingCreditEventPublish{
				enable: true,
			},
			mockEvaluate: mockEvaluate{
				enable:      true,
				constraints: release.NewCommonConstraintData(typesPb.Feature_FEATURE_REMITTER_INFO_BACKFILL).WithActorId(testInternalActorId),
				res:         true,
			},
			mockCheckNameIsMerchant: mockCheckNameIsMerchant{
				enable: true,
				name:   "actor enterprises",
				want:   true,
				err:    nil,
			},
			args: args{
				internalSavingsAccountId: testAccountId,
				internalActorId:          testInternalActorId,
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol:       paymentPb.PaymentProtocol_NEFT,
					OtherActorName: "actor enterprises",
					PiIdentifier: &paymentNotificationPb.ParsedTxnParticulars_Account{
						Account: &paymentNotificationPb.ParsedTxnParticulars_AccountIdentifier{
							AccountNumber: "12345",
							IfscCode:      pay.DefaultIfscCode,
							PiType:        piPb.PaymentInstrumentType_PARTIAL_BANK_ACCOUNT,
							AccountType:   accountPb.Type_SAVINGS,
						},
					},
					Utr:              "utr-1",
					UniqPartnerRefId: "S1234",
				},
				txnDetails:  basicCreditTestTxnDetails,
				partnerBank: commonvgpb.Vendor_FEDERAL_BANK,
				txnStatus:   paymentPb.TransactionStatus_SUCCESS,
				txnDetailedStatus: &paymentPb.TransactionDetailedStatus{
					DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
						{
							Api:   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
							State: paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
						},
					},
				},
				isLastAttempt:               true,
				attemptRemitterInfoBackfill: true,
			},
			wantErr:     false,
			wantErrType: nil,
		},
		{
			name: "Should successfully process loan disbursement notifications",
			mockResolveInternalPiDetails: mockResolveInternalActorPiDetails{
				enable:                   true,
				internalSavingsAccountId: testAccountId,
				internalSavingsAcctNo:    testAccountNumber,
				isCardTxn:                false,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				internalPiId:             testInternalActorPidId,
				internalActorName:        testInternalActorName,
				source:                   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
				err:                      nil,
			},
			mockResolveOtherActorPiDetails: mockResolveOtherActorPiDetails{
				enable:            true,
				parsedParticulars: testLoanDisbursementParsedTxnDetails,
				txnDetails:        basicCreditTestTxnDetails,
				actorId:           testInternalActorId,
				otherActorName:    pay.DefaultFederalBankActorName,
				otherActorPiId:    genericPiId,
				err:               nil,
			},
			mockResolveTimeline: mockResolveTimeline{
				enable:            true,
				internalActorId:   testInternalActorId,
				internalPiId:      testInternalActorPidId,
				internalActorName: testInternalActorName,
				otherPiId:         genericPiId,
				otherActorName:    pay.DefaultFederalBankActorName,
				fromActorId:       testOtherActorId,
				fromPiId:          genericPiId,
				toActorId:         testInternalActorId,
				toPiId:            testInternalActorPidId,
				isCredit:          true,
				err:               nil,
			},
			mockCreateOrder: mockCreateOrder{
				enable: true,
				req: &orderPb.Order{
					FromActorId: testOtherActorId,
					ToActorId:   testInternalActorId,
					Workflow:    orderPb.OrderWorkflow_NO_OP,
					Status:      orderPb.OrderStatus_PAID,
					Provenance:  orderPb.OrderProvenance_EXTERNAL,
					Amount:      basicCreditTestTxnDetails.GetAmount(),
					Tags:        []orderPb.OrderTag{orderPb.OrderTag_LOAN},
				},
				want: &orderPb.Order{
					Id: "order-1",
				},
				err: nil,
			},
			mockCreateTxn: mockCreateTxn{
				enable: true,
				txn: &paymentPb.Transaction{
					PiFrom:            genericPiId,
					PiTo:              testInternalActorPidId,
					PartnerRefId:      testLoanDisbursementParsedTxnDetails.GetUniqPartnerRefId(),
					Utr:               testLoanDisbursementParsedTxnDetails.GetUtr(),
					Amount:            basicCreditTestTxnDetails.GetAmount(),
					Status:            paymentPb.TransactionStatus_SUCCESS,
					PaymentProtocol:   paymentPb.PaymentProtocol_INTRA_BANK,
					Remarks:           basicCreditTestTxnDetails.GetRemarks(),
					Particulars:       basicCreditTestTxnDetails.GetParticular(),
					PartnerExecutedAt: testTs,
					PartnerBank:       commonvgpb.Vendor_FEDERAL_BANK,
					CreditedAt:        testTs,
					RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
						paymentPb.AccountingEntryType_CREDIT.String(): {
							Particulars: basicCreditTestTxnDetails.GetParticular(),
							ValueDate:   testValueTs,
						},
					},
					ParticularsCredit:  basicCreditTestTxnDetails.GetParticular(),
					PartnerRefIdCredit: testLoanDisbursementParsedTxnDetails.GetUniqPartnerRefId(),
					DetailedStatus: &paymentPb.TransactionDetailedStatus{
						DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
							{
								CreatedAt: timestamppb.Now(),
								Api:       paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
								State:     paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
							},
						},
					},
					DedupeId: &paymentPb.DedupeId{
						Utr:     "S1234",
						CbsId:   "S1234",
						TxnTime: basicDebitTestTxnDetails.GetTimestamp(),
					},
					Metadata:  &paymentPb.Metadata{ParserTemplate: &paymentPb.ParserTemplate{}},
					Ownership: commontypes.Ownership_FEDERAL_BANK,
				},
				orderId: "order-1",
				want: &paymentPb.Transaction{
					Id: "txn-1",
				},
				err: nil,
			},
			mockPublish: mockPublish{
				enable: true,
				err:    nil,
			},
			mockEvaluate: mockEvaluate{
				enable:      true,
				constraints: release.NewCommonConstraintData(typesPb.Feature_FEATURE_REMITTER_INFO_BACKFILL).WithActorId(testInternalActorId),
				res:         true,
			},
			mockCheckNameIsMerchant: mockCheckNameIsMerchant{
				enable: true,
				name:   "Federal Bank",
				want:   false,
				err:    nil,
			},
			args: args{
				internalSavingsAccountId: testAccountId,
				internalActorId:          testInternalActorId,
				parsedParticulars:        testLoanDisbursementParsedTxnDetails,
				txnDetails:               basicCreditTestTxnDetails,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				txnStatus:                paymentPb.TransactionStatus_SUCCESS,
				txnDetailedStatus: &paymentPb.TransactionDetailedStatus{
					DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
						{
							Api:   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
							State: paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
						},
					},
				},
				isLastAttempt:               true,
				attemptRemitterInfoBackfill: true,
			},
		},
		{
			name: "Should successfully create transaction with Metadata column",
			mockResolveInternalPiDetails: mockResolveInternalActorPiDetails{
				enable:                   true,
				internalSavingsAccountId: testAccountId,
				internalSavingsAcctNo:    testAccountNumber,
				isCardTxn:                false,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				internalPiId:             testInternalActorPidId,
				internalActorName:        testInternalActorName,
				source:                   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
				err:                      nil,
			},
			mockResolveOtherActorPiDetails: mockResolveOtherActorPiDetails{
				enable:            true,
				parsedParticulars: testParsedTxnDetailsForMetadata,
				txnDetails:        basicCreditTestTxnDetails,
				actorId:           testInternalActorId,
				otherActorName:    "test",
				otherActorPiId:    testOtherActorPiId,
				err:               piProcessor.ErrorValidatingVpa,
			},
			mockCreateUpiPi: mockCreateUpiPi{
				enable:        true,
				name:          "test",
				vpa:           "test@fede",
				isVpaVerified: false,
				piId:          testOtherActorPiId,
			},
			mockResolveTimeline: mockResolveTimeline{
				enable:            true,
				internalActorId:   testInternalActorId,
				internalPiId:      testInternalActorPidId,
				internalActorName: testInternalActorName,
				otherPiId:         testOtherActorPiId,
				otherActorName:    "test",
				fromActorId:       testOtherActorId,
				fromPiId:          testOtherActorPiId,
				toActorId:         testInternalActorId,
				toPiId:            testInternalActorPidId,
				isCredit:          true,
				err:               nil,
			},
			mockCreateOrder: mockCreateOrder{
				enable: true,
				req: &orderPb.Order{
					FromActorId: testOtherActorId,
					ToActorId:   testInternalActorId,
					Workflow:    orderPb.OrderWorkflow_NO_OP,
					Status:      orderPb.OrderStatus_PAID,
					Provenance:  orderPb.OrderProvenance_EXTERNAL,
					Amount:      basicCreditTestTxnDetails.GetAmount(),
				},
				want: &orderPb.Order{
					Id: "order-1",
				},
				err: nil,
			},
			mockGetPiById: mockGetPiById{
				enable: true,
				piId:   testOtherActorPiId,
				pi: &piPb.PaymentInstrument{
					Identifier: &piPb.PaymentInstrument_Upi{},
				},
			},
			mockCreateTxn: mockCreateTxn{
				enable: true,
				txn: &paymentPb.Transaction{
					PiFrom:            testOtherActorPiId,
					PiTo:              testInternalActorPidId,
					PartnerRefId:      testParsedTxnDetailsForMetadata.GetUniqPartnerRefId(),
					Utr:               testParsedTxnDetailsForMetadata.GetUtr(),
					Amount:            basicCreditTestTxnDetails.GetAmount(),
					Status:            paymentPb.TransactionStatus_SUCCESS,
					PaymentProtocol:   paymentPb.PaymentProtocol_UPI,
					Remarks:           basicCreditTestTxnDetails.GetRemarks(),
					Particulars:       basicCreditTestTxnDetails.GetParticular(),
					PartnerExecutedAt: testTs,
					PartnerBank:       commonvgpb.Vendor_FEDERAL_BANK,
					CreditedAt:        testTs,
					RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
						paymentPb.AccountingEntryType_CREDIT.String(): {
							Particulars: basicCreditTestTxnDetails.GetParticular(),
							ValueDate:   testValueTs,
						},
					},
					ParticularsCredit:  basicCreditTestTxnDetails.GetParticular(),
					PartnerRefIdCredit: testParsedTxnDetailsForMetadata.GetUniqPartnerRefId(),
					DetailedStatus: &paymentPb.TransactionDetailedStatus{
						DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
							{
								CreatedAt: timestamppb.Now(),
								Api:       paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
								State:     paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
							},
						},
					},
					DedupeId: &paymentPb.DedupeId{
						Utr:     "utr-2",
						CbsId:   "S1234",
						TxnTime: basicDebitTestTxnDetails.GetTimestamp(),
					},
					Metadata: &paymentPb.Metadata{
						ParserTemplate: &paymentPb.ParserTemplate{
							Id:      testParsedTxnDetailsForMetadata.GetParserTemplate().GetId(),
							Version: testParsedTxnDetailsForMetadata.GetParserTemplate().GetVersion(),
						},
					},
					Ownership: commontypes.Ownership_FEDERAL_BANK,
				},
				orderId: "order-1",
				want: &paymentPb.Transaction{
					Id: "txn-1",
				},
				err: nil,
			},
			mockPublish: mockPublish{
				enable: true,
				err:    nil,
			},
			mockIncomingCreditEventPublish: mockIncomingCreditEventPublish{
				enable: true,
			},
			mockVpaVerificationPublisher: mockVpaVerificationPublisher{
				enable: true,
				req: &consumer.VerifyVpaRequest{
					UpiVpa:       "test@fede",
					PayerActorId: testInternalActorId,
				},
				delay: ts.conf.ReVerifyAddressDelay(),
			},
			mockEvaluate: mockEvaluate{
				enable:      true,
				constraints: release.NewCommonConstraintData(typesPb.Feature_FEATURE_REMITTER_INFO_BACKFILL).WithActorId(testInternalActorId),
				res:         true,
			},
			args: args{
				internalSavingsAccountId: testAccountId,
				internalActorId:          testInternalActorId,
				parsedParticulars:        testParsedTxnDetailsForMetadata,
				txnDetails:               basicCreditTestTxnDetails,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				txnStatus:                paymentPb.TransactionStatus_SUCCESS,
				txnDetailedStatus: &paymentPb.TransactionDetailedStatus{
					DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
						{
							Api:   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
							State: paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
						},
					},
				},
				isLastAttempt:               true,
				attemptRemitterInfoBackfill: true,
			},
		},
		{
			name: "Should successfully process extended add funds payment when new add funds vpa is enabled",
			mockResolveInternalPiDetails: mockResolveInternalActorPiDetails{
				enable:                   true,
				internalSavingsAccountId: testAccountId,
				internalSavingsAcctNo:    testAccountNumber,
				isCardTxn:                false,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				internalPiId:             testInternalActorPidId,
				internalActorName:        testInternalActorName,
				source:                   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
				err:                      nil,
			},
			mockResolveOtherActorPiDetails: mockResolveOtherActorPiDetails{
				enable: true,
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol:         paymentPb.PaymentProtocol_UPI,
					OtherActorName:   genericActorName,
					PiIdentifier:     &paymentNotificationPb.ParsedTxnParticulars_PiId{PiId: pay.FederalPoolAccountPiId},
					Utr:              "EXTENDED:utr-2",
					UniqPartnerRefId: "S1234",
					ReqId:            pay.AddFundsReqIdPrefix + "123i432",
				},
				txnDetails:     basicDebitTestTxnDetails,
				actorId:        testInternalActorId,
				otherActorName: pay.DefaultFederalBankActorName,
				otherActorPiId: pay.FederalPoolAccountV1PiId,
			},
			mockResolveTimeline: mockResolveTimeline{
				enable:            true,
				internalActorId:   testInternalActorId,
				internalPiId:      testInternalActorPidId,
				internalActorName: testInternalActorName,
				otherPiId:         pay.FederalPoolAccountV1PiId,
				otherActorName:    pay.DefaultFederalBankActorName,
				fromActorId:       testInternalActorId,
				fromPiId:          testInternalActorPidId,
				toActorId:         testOtherActorId,
				toPiId:            pay.FederalPoolAccountV1PiId,
				isCredit:          false,
				err:               nil,
			},
			mockCreateOrder: mockCreateOrder{
				enable: true,
				req: &orderPb.Order{
					FromActorId: testInternalActorId,
					ToActorId:   testOtherActorId,
					Workflow:    orderPb.OrderWorkflow_NO_OP,
					Status:      orderPb.OrderStatus_PAID,
					Provenance:  orderPb.OrderProvenance_EXTERNAL,
					Amount:      basicCreditTestTxnDetails.GetAmount(),
				},
				want: &orderPb.Order{
					Id: "order-1",
				},
				err: nil,
			},
			mockGetPiById: mockGetPiById{
				enable: true,
				piId:   pay.FederalPoolAccountV1PiId,
				pi: &piPb.PaymentInstrument{
					Identifier: &piPb.PaymentInstrument_Upi{
						Upi: &piPb.Upi{
							IfscCode: "IFSC00000",
						},
					},
				},
			},
			mockCreateTxn: mockCreateTxn{
				enable: true,
				txn: &paymentPb.Transaction{
					PiFrom:            testInternalActorPidId,
					PiTo:              pay.FederalPoolAccountV1PiId,
					PartnerRefId:      "S1234",
					Utr:               convertToExtendedIdentifier(testUPIParsedTxnDetails.GetUtr()),
					Amount:            basicDebitTestTxnDetails.GetAmount(),
					Status:            paymentPb.TransactionStatus_SUCCESS,
					PaymentProtocol:   paymentPb.PaymentProtocol_UPI,
					Remarks:           basicDebitTestTxnDetails.GetRemarks(),
					Particulars:       basicDebitTestTxnDetails.GetParticular(),
					PartnerExecutedAt: testTs,
					PartnerBank:       commonvgpb.Vendor_FEDERAL_BANK,
					DebitedAt:         testTs,
					RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
						paymentPb.AccountingEntryType_DEBIT.String(): {
							Particulars: basicDebitTestTxnDetails.GetParticular(),
							ValueDate:   testValueTs,
						},
					},
					ParticularsDebit:  basicDebitTestTxnDetails.GetParticular(),
					PartnerRefIdDebit: "S1234",
					DetailedStatus: &paymentPb.TransactionDetailedStatus{
						DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
							{
								CreatedAt: timestamppb.Now(),
								Api:       paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
								State:     paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
							},
						},
					},
					DedupeId: &paymentPb.DedupeId{
						RequestId: "FBLEPIFI123i432",
						Utr:       "EXTENDED:utr-2",
						CbsId:     "S1234",
						TxnTime:   basicDebitTestTxnDetails.GetTimestamp(),
					},
					Metadata:  &paymentPb.Metadata{ParserTemplate: &paymentPb.ParserTemplate{}},
					Ownership: commontypes.Ownership_FEDERAL_BANK,
				},
				orderId: "order-1",
				reqInfo: &paymentPb.PaymentRequestInformation{
					UpiInfo: &paymentPb.PaymentRequestInformation_UPI{
						CustomerAccountInfo: &upi.CustomerAccountDetails{
							Ifsc: "IFSC00000",
						},
					},
				},
				want: &paymentPb.Transaction{
					Id: "txn-1",
				},
				err: nil,
			},
			mockPublish: mockPublish{
				enable: true,
				err:    nil,
			},
			mockEvaluate: mockEvaluate{
				enable:      true,
				constraints: release.NewCommonConstraintData(typesPb.Feature_FEATURE_REMITTER_INFO_BACKFILL).WithActorId(testInternalActorId),
				res:         true,
			},
			mockIsNewAddFundsVpaEnabledForActor: mockIsNewAddFundsVpaEnabledForActor{
				enable:  true,
				actorId: testInternalActorId,
				want:    false,
			},
			args: args{
				internalSavingsAccountId: testAccountId,
				internalActorId:          testInternalActorId,
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol:       paymentPb.PaymentProtocol_UPI,
					OtherActorName: genericActorName,
					PiIdentifier: &paymentNotificationPb.ParsedTxnParticulars_Vpa{
						Vpa: testVPA,
					},
					Utr:              "utr-2",
					UniqPartnerRefId: "S1234",
					ReqId:            pay.AddFundsReqIdPrefix + "123i432",
				},
				txnDetails:  basicDebitTestTxnDetails,
				partnerBank: commonvgpb.Vendor_FEDERAL_BANK,
				txnStatus:   paymentPb.TransactionStatus_SUCCESS,
				txnDetailedStatus: &paymentPb.TransactionDetailedStatus{
					DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
						{
							Api:   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
							State: paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
						},
					},
				},
				attemptRemitterInfoBackfill: true,
			},
			wantErr:     false,
			wantErrType: nil,
		},
		{
			name: "Should successfully create order with client request id for enach debits",
			mockResolveInternalPiDetails: mockResolveInternalActorPiDetails{
				enable:                   true,
				internalSavingsAccountId: testAccountId,
				internalSavingsAcctNo:    testAccountNumber,
				isCardTxn:                false,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				internalPiId:             testInternalActorPidId,
				internalActorName:        testInternalActorName,
				source:                   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
				err:                      nil,
			},
			mockResolveOtherActorPiDetails: mockResolveOtherActorPiDetails{
				enable:            true,
				parsedParticulars: testEnachParsedTxnDetails,
				txnDetails:        basicDebitTestTxnDetails,
				actorId:           testInternalActorId,
				otherActorName:    "test",
				otherActorPiId:    testOtherActorPiId,
			},
			mockResolveTimeline: mockResolveTimeline{
				enable:            true,
				internalActorId:   testInternalActorId,
				internalPiId:      testInternalActorPidId,
				internalActorName: testInternalActorName,
				otherPiId:         testOtherActorPiId,
				otherActorName:    "test",
				fromActorId:       testOtherActorId,
				fromPiId:          testOtherActorPiId,
				toActorId:         testInternalActorId,
				toPiId:            testInternalActorPidId,
				isCredit:          false,
				err:               nil,
			},
			mockCreateOrder: mockCreateOrder{
				enable: true,
				req: &orderPb.Order{
					FromActorId: testOtherActorId,
					ToActorId:   testInternalActorId,
					Workflow:    orderPb.OrderWorkflow_NO_OP,
					Status:      orderPb.OrderStatus_PAID,
					Provenance:  orderPb.OrderProvenance_EXTERNAL,
					Amount:      basicCreditTestTxnDetails.GetAmount(),
					ClientReqId: uuid.NewString(),
				},
				want: &orderPb.Order{
					Id: "order-1",
				},
				err: nil,
			},
			mockCreateTxn: mockCreateTxn{
				enable: true,
				txn: &paymentPb.Transaction{
					PiFrom:            testOtherActorPiId,
					PiTo:              testInternalActorPidId,
					PartnerRefId:      testEnachParsedTxnDetails.GetUniqPartnerRefId(),
					Utr:               testEnachParsedTxnDetails.GetUtr(),
					Amount:            basicDebitTestTxnDetails.GetAmount(),
					Status:            paymentPb.TransactionStatus_SUCCESS,
					PaymentProtocol:   paymentPb.PaymentProtocol_ENACH,
					Remarks:           basicDebitTestTxnDetails.GetRemarks(),
					Particulars:       basicDebitTestTxnDetails.GetParticular(),
					PartnerExecutedAt: testTs,
					PartnerBank:       commonvgpb.Vendor_FEDERAL_BANK,
					DebitedAt:         testTs,
					RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
						paymentPb.AccountingEntryType_DEBIT.String(): {
							Particulars: basicDebitTestTxnDetails.GetParticular(),
							ValueDate:   testValueTs,
						},
					},
					ParticularsDebit:  basicDebitTestTxnDetails.GetParticular(),
					PartnerRefIdDebit: testEnachParsedTxnDetails.GetUniqPartnerRefId(),
					DetailedStatus: &paymentPb.TransactionDetailedStatus{
						DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
							{
								CreatedAt: timestamppb.Now(),
								Api:       paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
								State:     paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
							},
						},
					},
					DedupeId: &paymentPb.DedupeId{
						Utr:     "S1234",
						CbsId:   "S1234",
						TxnTime: basicDebitTestTxnDetails.GetTimestamp(),
					},
					Metadata: &paymentPb.Metadata{
						ParserTemplate: &paymentPb.ParserTemplate{
							Id:      testEnachParsedTxnDetails.GetParserTemplate().GetId(),
							Version: testEnachParsedTxnDetails.GetParserTemplate().GetVersion(),
						},
					},
					Ownership: commontypes.Ownership_FEDERAL_BANK,
				},
				orderId: "order-1",
				want: &paymentPb.Transaction{
					Id: "txn-1",
				},
				err: nil,
			},
			mockPublish: mockPublish{
				enable: true,
				err:    nil,
			},
			mockIncomingCreditEventPublish: mockIncomingCreditEventPublish{
				enable: true,
			},
			mockEvaluate: mockEvaluate{
				enable:      true,
				constraints: release.NewCommonConstraintData(typesPb.Feature_FEATURE_REMITTER_INFO_BACKFILL).WithActorId(testInternalActorId),
				res:         true,
			},
			args: args{
				internalSavingsAccountId: testAccountId,
				internalActorId:          testInternalActorId,
				parsedParticulars:        testEnachParsedTxnDetails,
				txnDetails:               basicDebitTestTxnDetails,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				txnStatus:                paymentPb.TransactionStatus_SUCCESS,
				txnDetailedStatus: &paymentPb.TransactionDetailedStatus{
					DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
						{
							Api:   paymentPb.TransactionDetailedStatus_DetailedStatus_NOTIFICATION_CALLBACK,
							State: paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
						},
					},
				},
				isLastAttempt:               true,
				attemptRemitterInfoBackfill: true,
			},
		},
		{
			name: "Should omit DebitedAt field for FAILED debit card transaction",
			mockResolveInternalPiDetails: mockResolveInternalActorPiDetails{
				enable:                   true,
				internalSavingsAccountId: testAccountId,
				internalSavingsAcctNo:    testAccountNumber,
				isCardTxn:                true,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				internalPiId:             testInternalActorPidId,
				internalActorName:        testInternalActorName,
				source:                   paymentPb.TransactionDetailedStatus_DetailedStatus_CARD_SWITCH_NOTIFICATION,
				err:                      nil,
			},
			mockResolveOtherActorPiDetails: mockResolveOtherActorPiDetails{
				enable:            true,
				parsedParticulars: testCardParsedTxnDetails,
				txnDetails:        basicDebitTestTxnDetails,
				actorId:           testInternalActorId,
				otherActorName:    testOtherActorName,
				otherActorPiId:    testOtherActorPiId,
			},
			mockResolveTimeline: mockResolveTimeline{
				enable:            true,
				internalActorId:   testInternalActorId,
				internalPiId:      testInternalActorPidId,
				internalActorName: testInternalActorName,
				otherPiId:         testOtherActorPiId,
				otherActorName:    testOtherActorName,
				fromActorId:       testInternalActorId,
				fromPiId:          testInternalActorPidId,
				toActorId:         testOtherActorId,
				toPiId:            testOtherActorPiId,
				isCredit:          false,
				err:               nil,
			},
			mockCreateOrder: mockCreateOrder{
				enable: true,
				req: &orderPb.Order{
					FromActorId: testInternalActorId,
					ToActorId:   testOtherActorId,
					Workflow:    orderPb.OrderWorkflow_NO_OP,
					Status:      orderPb.OrderStatus_PAYMENT_FAILED, // Failed status
					Provenance:  orderPb.OrderProvenance_POS,
					Amount:      basicDebitTestTxnDetails.GetAmount(),
					Tags:        []orderPb.OrderTag{orderPb.OrderTag_MERCHANT},
				},
				want: &orderPb.Order{
					Id: "order-failed-card",
				},
				err: nil,
			},
			mockCreateTxn: mockCreateTxn{
				enable: true,
				txn: &paymentPb.Transaction{
					PiFrom:            testInternalActorPidId,
					PiTo:              testOtherActorPiId,
					PartnerRefId:      "S1234",
					Utr:               "S1234",
					Amount:            basicDebitTestTxnDetails.GetAmount(),
					Status:            paymentPb.TransactionStatus_FAILED, // Failed status
					PaymentProtocol:   paymentPb.PaymentProtocol_CARD,     // Card protocol
					Remarks:           basicDebitTestTxnDetails.GetRemarks(),
					Particulars:       basicDebitTestTxnDetails.GetParticular(),
					PartnerExecutedAt: testTs,
					PartnerBank:       commonvgpb.Vendor_FEDERAL_BANK,
					// DebitedAt should be omitted in the actual transaction created
					// even though we set it here initially
					DebitedAt:  nil,
					CreditedAt: nil,
					RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
						paymentPb.AccountingEntryType_DEBIT.String(): {
							Particulars: basicDebitTestTxnDetails.GetParticular(),
							ValueDate:   testValueTs,
						},
					},
					ParticularsDebit:  basicDebitTestTxnDetails.GetParticular(),
					PartnerRefIdDebit: "S1234",
					DetailedStatus: &paymentPb.TransactionDetailedStatus{
						DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
							{
								CreatedAt: timestamppb.Now(),
								Api:       paymentPb.TransactionDetailedStatus_DetailedStatus_CARD_SWITCH_NOTIFICATION,
								State:     paymentPb.TransactionDetailedStatus_DetailedStatus_FAILURE,
							},
						},
					},
					DedupeId: &paymentPb.DedupeId{
						Utr:     "S1234",
						CbsId:   "S1234",
						TxnTime: testTs,
					},
					Ownership: commontypes.Ownership_FEDERAL_BANK,
					Metadata: &paymentPb.Metadata{
						ParserTemplate: &paymentPb.ParserTemplate{},
					},
				},
				orderId: "order-failed-card",
				// Critical check: The DebitedAt field should be nil when the mock returns
				want: &paymentPb.Transaction{
					Id:        "txn-failed-card",
					DebitedAt: nil,
				},
				err: nil,
			},
			mockPublish: mockPublish{
				enable: true,
				err:    nil,
			},
			mockIncomingCreditEventPublish: mockIncomingCreditEventPublish{
				enable: false,
			},
			mockCheckNameIsMerchant: mockCheckNameIsMerchant{
				enable: true,
				name:   testOtherActorName,
				want:   false,
				err:    nil,
			},
			args: args{
				internalSavingsAccountId: testAccountId,
				internalActorId:          testInternalActorId,
				parsedParticulars:        testCardParsedTxnDetails,
				txnDetails:               basicDebitTestTxnDetails,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				txnStatus:                paymentPb.TransactionStatus_FAILED, // Failed status
				txnDetailedStatus: &paymentPb.TransactionDetailedStatus{
					DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
						{
							Api:   paymentPb.TransactionDetailedStatus_DetailedStatus_CARD_SWITCH_NOTIFICATION,
							State: paymentPb.TransactionDetailedStatus_DetailedStatus_FAILURE,
						},
					},
				},
				attemptRemitterInfoBackfill: false,
			},
			wantErr:     false,
			wantErrType: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockResolveInternalPiDetails.enable {
				mockPiProcessor.EXPECT().ResolveInternalActorPiDetails(context.Background(),
					tt.mockResolveInternalPiDetails.internalSavingsAccountId, tt.mockResolveInternalPiDetails.internalSavingsAcctNo,
					tt.mockResolveInternalPiDetails.isCardTxn, tt.mockResolveInternalPiDetails.partnerBank,
					tt.mockResolveInternalPiDetails.source, tt.mockResolveInternalPiDetails.maskedCardNumber).
					Return(tt.mockResolveInternalPiDetails.internalPiId, tt.mockResolveInternalPiDetails.internalActorName,
						tt.mockResolveInternalPiDetails.err)
			}
			if tt.mockCreateUpiPi.enable {
				mockPiProcessor.EXPECT().CreateUpiPi(
					gomock.Any(),
					tt.mockCreateUpiPi.name,
					tt.mockCreateUpiPi.vpa,
					tt.mockCreateUpiPi.mcc,
					tt.mockCreateUpiPi.merchantDetails,
					tt.mockCreateUpiPi.ownership,
					tt.mockCreateUpiPi.isVpaVerified,
				).Return(tt.mockCreateUpiPi.piId, tt.mockCreateUpiPi.err)
			}
			if tt.mockResolveOtherActorPiDetails.enable {
				mockPiProcessor.EXPECT().ResolveOtherActorPiDetails(
					context.Background(),
					tt.mockResolveOtherActorPiDetails.parsedParticulars,
					tt.mockResolveOtherActorPiDetails.txnDetails,
					tt.mockResolveOtherActorPiDetails.actorId,
					tt.mockResolveOtherActorPiDetails.ownership,
				).Return(
					tt.mockResolveOtherActorPiDetails.otherActorPiId,
					tt.mockResolveOtherActorPiDetails.otherActorName,
					tt.mockResolveOtherActorPiDetails.isMerchant,
					tt.mockResolveOtherActorPiDetails.err,
				)
			}
			if tt.mockResolveTimeline.enable {
				mockTimelineProcessor.EXPECT().ResolveTimeline(context.Background(), tt.mockResolveTimeline.internalActorId,
					tt.mockResolveTimeline.internalPiId, tt.mockResolveTimeline.internalActorName, tt.mockResolveTimeline.otherPiId,
					tt.mockResolveTimeline.otherActorName, tt.mockResolveTimeline.isCredit, timeline.Ownership_EPIFI_TECH).
					Return(tt.mockResolveTimeline.fromActorId, tt.mockResolveTimeline.fromPiId, tt.mockResolveTimeline.toActorId,
						tt.mockResolveTimeline.toPiId, tt.mockResolveTimeline.err)
			}
			if tt.mockCreateOrder.enable {
				mockOrderDao.EXPECT().Create(gomock.Any(), newCreateOrderArgMatcher(tt.mockCreateOrder.req), commontypes.Ownership_EPIFI_TECH).
					Return(tt.mockCreateOrder.want, tt.mockCreateOrder.err).AnyTimes()
			}
			if tt.mockGetPiById.enable {
				mockPiProcessor.EXPECT().GetPiById(gomock.Any(), tt.mockGetPiById.piId).
					Return(tt.mockGetPiById.pi, tt.mockGetPiById.err)
			}
			if tt.mockCreateTxn.enable {
				mockTxnDao.EXPECT().Create(gomock.Any(), newgetOrdersForActorArgMatcher(tt.mockCreateTxn.txn), tt.mockCreateTxn.reqInfo, tt.mockCreateTxn.orderId, commontypes.Ownership_EPIFI_TECH).
					Return(tt.mockCreateTxn.want, tt.mockCreateTxn.err)
			}
			if tt.mockPublish.enable {
				mockOrderEventPub.EXPECT().PublishWithAttributes(gomock.Any(),
					gomock.AssignableToTypeOf(&orderPb.OrderUpdate{}), gomock.AssignableToTypeOf([]queue.MessageAttribute{})).Return("id-1", tt.mockPublish.err)
			}
			if tt.mockIncomingCreditEventPublish.enable {
				mockTncEventPub.EXPECT().PublishWithDelay(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
			}
			if tt.mockVpaVerificationPublisher.enable {
				mockOrderVPAVerfPub.EXPECT().PublishWithDelay(gomock.Any(), tt.mockVpaVerificationPublisher.req, tt.mockVpaVerificationPublisher.delay).
					Return(tt.mockVpaVerificationPublisher.res, tt.mockVpaVerificationPublisher.err)
			}
			if tt.mockIsNewAddFundsVpaEnabledForActor.enable {
				mocksUpiProcessor.EXPECT().IsNewAddFundsVpaEnabledForActor(gomock.Any(), tt.mockIsNewAddFundsVpaEnabledForActor.actorId).
					Return(tt.mockIsNewAddFundsVpaEnabledForActor.want, tt.mockIsNewAddFundsVpaEnabledForActor.err)
			}
			for _, mockCreatePiCall := range tt.mockCreatePi {
				if mockCreatePiCall.enable {
					mockPiClient.EXPECT().CreatePi(gomock.Any(), mock.NewProtoMatcher(mockCreatePiCall.wantReq)).
						Return(mockCreatePiCall.returnRes, mockCreatePiCall.err).Times(1)
				}
			}

			if tt.mockEvaluate.enable {
				mockEval.EXPECT().Evaluate(context.Background(), tt.mockEvaluate.constraints).
					Return(tt.mockEvaluate.res, tt.mockEvaluate.err)
			}

			if tt.mockCheckNameIsMerchant.enable {
				mockMerchantProcessor.EXPECT().CheckNameIsMerchant(gomock.Any(), tt.mockCheckNameIsMerchant.name).
					Return(tt.mockCheckNameIsMerchant.want, nil).AnyTimes()
			}

			if tt.mockCreateAccountPi.enable {
				mockPiProcessor.EXPECT().CreateAccountPi(gomock.Any(), tt.mockCreateAccountPi.accountNumber, tt.mockCreateAccountPi.ifscCode, tt.mockCreateAccountPi.verifiedName, tt.mockCreateAccountPi.name, tt.mockCreateAccountPi.piType, tt.mockCreateAccountPi.accountType, gomock.Any(), gomock.Any()).Return(tt.mockCreateAccountPi.want, nil)
			}

			_, _, err := processor.ProcessOffAppPayment(context.Background(), tt.args.internalSavingsAccountId, tt.args.internalActorId, gomock.Any().String(), tt.args.txnDetails, tt.args.parsedParticulars, tt.args.partnerBank, tt.args.txnStatus, tt.args.txnDetailedStatus, tt.args.orderWorkflow,
				tt.args.vendorNotificationTimestamp, tt.args.isLastAttempt, tt.args.attemptRemitterInfoBackfill)

			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessOffAppPayment() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr && !errors.Is(err, tt.wantErrType) {
				t.Errorf("wanted: %v, got: %v", tt.wantErrType, err)
				return
			}
		})
	}
}

func TestProcessor_IsExtendedPiPayment(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockTxnDao, mockOrderDao, mockP2PInvestmentClient, mockPiProcessor, mockTimelineProcessor, mockPiClient, mockDepositClient, mockOrderEventPub, mockTncEventPub, mockEventsBroker, mocksUpiProcessor, mockOrderProcessor, mockOrderOrchPub, mockOrderNotifPub, mockOrderSearchPub, mockMerchantCl, mockOrderVPAVerfPub,
		mockTxnNotifPub, mockCelestialProcessor, mockIFTClient, mockMerchantResCl, mockParserCl, mockEval, mockEnachCl, mockRecurPayCl, mockTxnDetailedStatusPub, mockPayCl, mockMerchantProcessor := newInboundNotifDependencies(ctr)
	processor := inboundNotifProcessor.NewProcessor(mockDepositClient, mockTxnDao, mockOrderDao, mockOrderEventPub, mockTncEventPub, mockEventsBroker, mockTimelineProcessor, mockPiProcessor, mocksUpiProcessor, mockOrderProcessor, mockOrderOrchPub, mockOrderNotifPub, mockOrderSearchPub, mockMerchantCl,
		mockP2PInvestmentClient, mockOrderVPAVerfPub, mockTxnNotifPub, mockCelestialProcessor, mockIFTClient, mockMerchantResCl, mockParserCl, conf, mockEval, mockEnachCl, mockRecurPayCl, mockTxnDetailedStatusPub, mockPiClient, mockPayCl, mockMerchantProcessor)
	type args struct {
		txn      *paymentPb.Transaction
		isCredit bool
	}
	type mockGetPiById struct {
		enable bool
		piId   string
		pi     *piPb.PaymentInstrument
		err    error
	}
	var tests = []struct {
		name          string
		args          args
		mockGetPiById mockGetPiById
		want          bool
		wantErr       bool
	}{
		{
			name: "returns true as pi to for credit is not internal PI",
			args: args{
				txn: &paymentPb.Transaction{
					PiFrom: "pi-from",
					PiTo:   "pi-to",
				},
				isCredit: true,
			},
			mockGetPiById: mockGetPiById{
				enable: true,
				piId:   "pi-to",
				pi:     &piPb.PaymentInstrument{IssuerClassification: piPb.PaymentInstrumentIssuer_EXTERNAL},
				err:    nil,
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "returns false as pi from for debit is internal PI",
			args: args{
				txn: &paymentPb.Transaction{
					PiFrom: "pi-from",
					PiTo:   "pi-to",
				},
				isCredit: false,
			},
			mockGetPiById: mockGetPiById{
				enable: true,
				piId:   "pi-from",
				pi:     &piPb.PaymentInstrument{IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL},
				err:    nil,
			},
			want:    false,
			wantErr: false,
		},
		{
			name: "returns err due to error while fetching PI",
			args: args{
				txn: &paymentPb.Transaction{
					PiFrom: "pi-from",
					PiTo:   "pi-to",
				},
				isCredit: false,
			},
			mockGetPiById: mockGetPiById{
				enable: true,
				piId:   "pi-from",
				err:    errors.New("RPC ERR"),
			},
			want:    false,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetPiById.enable {
				mockPiProcessor.EXPECT().
					GetPiById(context.Background(), tt.mockGetPiById.piId).
					Return(tt.mockGetPiById.pi, tt.mockGetPiById.err)
			}
			got, err := processor.IsExtendedPiPayment(context.Background(), tt.args.txn, tt.args.isCredit)
			if (err != nil) != tt.wantErr {
				t.Errorf("IsExtendedPiPayment() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("IsExtendedPiPayment() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProcessor_GetUpdatedDetailsForExtendedPayment(t *testing.T) {
	var (
		testInternalActorNameFromPi = "Han Solo"
		testParsedUtr               = "utr-1"
	)

	internalIfsc, err := vendorPkg.GetIfscCodeForVendor(commonvgpb.Vendor_FEDERAL_BANK)
	assert.NoError(t, err)

	type mockGetPiById struct {
		enable bool
		piId   string
		pi     *piPb.PaymentInstrument
		err    error
	}
	type mockGetPiByVPA struct {
		enable bool
		vpa    string
		pi     *piPb.PaymentInstrument
		err    error
	}
	type args struct {
		txn           *paymentPb.Transaction
		isDebit       bool
		partnerBank   commonvgpb.Vendor
		parsedDetails *paymentNotificationPb.ParsedTxnParticulars
	}
	tests := []struct {
		name           string
		args           args
		mockGetPiById  mockGetPiById
		mockGetPiByVPA mockGetPiByVPA
		want           *paymentNotificationPb.ParsedTxnParticulars
	}{
		{
			name: "change pi identifier and cbs id to generic account for debit intra bank notification",
			args: args{
				txn: &paymentPb.Transaction{
					PiFrom: "pi-from",
					PiTo:   "pi-to",
				},
				isDebit:     true,
				partnerBank: commonvgpb.Vendor_FEDERAL_BANK,
				parsedDetails: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol:         paymentPb.PaymentProtocol_INTRA_BANK,
					OtherActorName:   pay.DefaultActorName,
					Utr:              testParsedUtr,
					UniqPartnerRefId: "123",
				},
			},
			mockGetPiById: mockGetPiById{
				enable: true,
				piId:   "pi-to",
				pi: &piPb.PaymentInstrument{
					VerifiedName: testInternalActorNameFromPi,
				},
				err: nil,
			},
			want: &paymentNotificationPb.ParsedTxnParticulars{
				Protocol:         paymentPb.PaymentProtocol_INTRA_BANK,
				OtherActorName:   testInternalActorNameFromPi,
				Utr:              fmt.Sprintf("EXTENDED:%s", testParsedUtr),
				PiIdentifier:     pay.GenericAccount(),
				UniqPartnerRefId: fmt.Sprintf("EXTENDED:%s", "123"),
			},
		},
		{
			name: "change pi identifier to generic account for credit notification with full account details",
			args: args{
				txn: &paymentPb.Transaction{
					PiFrom: "pi-from",
					PiTo:   "pi-to",
				},
				isDebit:     false,
				partnerBank: commonvgpb.Vendor_FEDERAL_BANK,
				parsedDetails: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol:       paymentPb.PaymentProtocol_IMPS,
					OtherActorName: pay.DefaultActorName,
					Utr:            testParsedUtr,
					PiIdentifier: &paymentNotificationPb.ParsedTxnParticulars_Account{
						Account: &paymentNotificationPb.ParsedTxnParticulars_AccountIdentifier{
							IfscCode: internalIfsc,
							PiType:   piPb.PaymentInstrumentType_BANK_ACCOUNT,
						},
					},
					UniqPartnerRefId: "123",
				},
			},
			mockGetPiById: mockGetPiById{
				enable: true,
				piId:   "pi-from",
				pi: &piPb.PaymentInstrument{
					VerifiedName: testInternalActorNameFromPi,
				},
				err: nil,
			},
			want: &paymentNotificationPb.ParsedTxnParticulars{
				Protocol:         paymentPb.PaymentProtocol_IMPS,
				OtherActorName:   testInternalActorNameFromPi,
				Utr:              fmt.Sprintf("EXTENDED:%s", testParsedUtr),
				PiIdentifier:     pay.GenericAccount(),
				UniqPartnerRefId: "123",
			},
		},
		{
			name: "change pi identifier to generic account for notification with internal issued VPA",
			args: args{
				txn: &paymentPb.Transaction{
					PiFrom: "pi-from",
					PiTo:   "pi-to",
				},
				isDebit:     true,
				partnerBank: commonvgpb.Vendor_FEDERAL_BANK,
				parsedDetails: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol:         paymentPb.PaymentProtocol_UPI,
					OtherActorName:   pay.DefaultActorName,
					Utr:              testParsedUtr,
					PiIdentifier:     &paymentNotificationPb.ParsedTxnParticulars_Vpa{Vpa: "xyz@fbl"},
					UniqPartnerRefId: "123",
				},
			},
			mockGetPiByVPA: mockGetPiByVPA{
				enable: true,
				vpa:    "xyz@fbl",
				pi:     &piPb.PaymentInstrument{IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL},
				err:    nil,
			},
			mockGetPiById: mockGetPiById{
				enable: true,
				piId:   "pi-to",
				pi: &piPb.PaymentInstrument{
					VerifiedName: testInternalActorNameFromPi,
				},
				err: nil,
			},
			want: &paymentNotificationPb.ParsedTxnParticulars{
				Protocol:         paymentPb.PaymentProtocol_UPI,
				OtherActorName:   testInternalActorNameFromPi,
				Utr:              fmt.Sprintf("EXTENDED:%s", testParsedUtr),
				PiIdentifier:     pay.GenericAccount(),
				UniqPartnerRefId: "123",
			},
		},
		{
			name: "don't change pi identifier to generic account for notification with external issued VPA",
			args: args{
				txn: &paymentPb.Transaction{
					PiFrom: "pi-from",
					PiTo:   "pi-to",
				},
				isDebit:     true,
				partnerBank: commonvgpb.Vendor_FEDERAL_BANK,
				parsedDetails: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol:       paymentPb.PaymentProtocol_UPI,
					OtherActorName: pay.DefaultActorName,
					Utr:            testParsedUtr,
					PiIdentifier: &paymentNotificationPb.ParsedTxnParticulars_Vpa{
						Vpa: "xyz@fedepsp",
					},
					UniqPartnerRefId: "123",
				},
			},
			mockGetPiByVPA: mockGetPiByVPA{
				enable: true,
				vpa:    "xyz@fedepsp",
				pi:     &piPb.PaymentInstrument{IssuerClassification: piPb.PaymentInstrumentIssuer_EXTERNAL},
				err:    nil,
			},
			mockGetPiById: mockGetPiById{
				enable: true,
				piId:   "pi-to",
				pi: &piPb.PaymentInstrument{
					VerifiedName: testInternalActorNameFromPi,
				},
				err: nil,
			},
			want: &paymentNotificationPb.ParsedTxnParticulars{
				Protocol:       paymentPb.PaymentProtocol_UPI,
				OtherActorName: testInternalActorNameFromPi,
				Utr:            fmt.Sprintf("EXTENDED:%s", testParsedUtr),
				PiIdentifier: &paymentNotificationPb.ParsedTxnParticulars_Vpa{
					Vpa: "xyz@fedepsp",
				},
				UniqPartnerRefId: "123",
			},
		},
		{
			name: "don't change pi identifier to generic account for notification with VPA and pi record not found",
			args: args{
				txn: &paymentPb.Transaction{
					PiFrom: "pi-from",
					PiTo:   "pi-to",
				},
				isDebit:     true,
				partnerBank: commonvgpb.Vendor_FEDERAL_BANK,
				parsedDetails: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol:       paymentPb.PaymentProtocol_UPI,
					OtherActorName: pay.DefaultActorName,
					Utr:            testParsedUtr,
					PiIdentifier: &paymentNotificationPb.ParsedTxnParticulars_Vpa{
						Vpa: "xyz@fedepsp",
					},
					UniqPartnerRefId: "123",
				},
			},
			mockGetPiByVPA: mockGetPiByVPA{
				enable: true,
				vpa:    "xyz@fedepsp",
				err:    piProcessor.ErrPiNotFound,
			},
			mockGetPiById: mockGetPiById{
				enable: true,
				piId:   "pi-to",
				pi: &piPb.PaymentInstrument{
					VerifiedName: testInternalActorNameFromPi,
				},
				err: nil,
			},
			want: &paymentNotificationPb.ParsedTxnParticulars{
				Protocol:       paymentPb.PaymentProtocol_UPI,
				OtherActorName: testInternalActorNameFromPi,
				Utr:            fmt.Sprintf("EXTENDED:%s", testParsedUtr),
				PiIdentifier: &paymentNotificationPb.ParsedTxnParticulars_Vpa{
					Vpa: "xyz@fedepsp",
				},
				UniqPartnerRefId: "123",
			},
		},
		{
			name: "change pi identifier to generic account for notification for VPA in case error while fetching PI",
			args: args{
				txn: &paymentPb.Transaction{
					PiFrom: "pi-from",
					PiTo:   "pi-to",
				},
				isDebit:     true,
				partnerBank: commonvgpb.Vendor_FEDERAL_BANK,
				parsedDetails: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol:       paymentPb.PaymentProtocol_UPI,
					OtherActorName: pay.DefaultActorName,
					Utr:            testParsedUtr,
					PiIdentifier: &paymentNotificationPb.ParsedTxnParticulars_Vpa{
						Vpa: "xyz@fedepsp",
					},
					UniqPartnerRefId: "123",
				},
			},
			mockGetPiByVPA: mockGetPiByVPA{
				enable: true,
				vpa:    "xyz@fedepsp",
				err:    errors.New("RPC failed"),
			},
			mockGetPiById: mockGetPiById{
				enable: true,
				piId:   "pi-to",
				pi: &piPb.PaymentInstrument{
					VerifiedName: testInternalActorNameFromPi,
				},
				err: nil,
			},
			want: &paymentNotificationPb.ParsedTxnParticulars{
				Protocol:         paymentPb.PaymentProtocol_UPI,
				OtherActorName:   testInternalActorNameFromPi,
				Utr:              fmt.Sprintf("EXTENDED:%s", testParsedUtr),
				PiIdentifier:     pay.GenericAccount(),
				UniqPartnerRefId: "123",
			},
		},
		{
			name: "return same name in case pi get call fails",
			args: args{
				txn: &paymentPb.Transaction{
					PiFrom: "pi-from",
					PiTo:   "pi-to",
				},
				isDebit:     true,
				partnerBank: commonvgpb.Vendor_FEDERAL_BANK,
				parsedDetails: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol:         paymentPb.PaymentProtocol_UPI,
					OtherActorName:   pay.DefaultActorName,
					Utr:              testParsedUtr,
					PiIdentifier:     &paymentNotificationPb.ParsedTxnParticulars_Vpa{Vpa: "xyz@fbl"},
					UniqPartnerRefId: "123",
				},
			},
			mockGetPiByVPA: mockGetPiByVPA{
				enable: true,
				vpa:    "xyz@fbl",
				pi:     &piPb.PaymentInstrument{IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL},
				err:    nil,
			},
			mockGetPiById: mockGetPiById{
				enable: true,
				piId:   "pi-to",
				err:    errors.New("RPC failed"),
			},
			want: &paymentNotificationPb.ParsedTxnParticulars{
				Protocol:         paymentPb.PaymentProtocol_UPI,
				OtherActorName:   pay.DefaultActorName,
				Utr:              fmt.Sprintf("EXTENDED:%s", testParsedUtr),
				PiIdentifier:     pay.GenericAccount(),
				UniqPartnerRefId: "123",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			mockTxnDao, mockOrderDao, mockP2PInvestmentClient, mockPiProcessor, mockTimelineProcessor, mockPiClient, mockDepositClient, mockOrderEventPub, mockTncEventPub, mockEventsBroker, mocksUpiProcessor, mockOrderProcessor, mockOrderOrchPub, mockOrderNotifPub, mockOrderSearchPub, mockMerchantCl, mockOrderVPAVerfPub,
				mockTxnNotifPub, mockCelestialProcessor, mockIFTClient, mockMerchantResCl, mockParserCl, mockEval, mockEnachCl, mockRecurPayCl, mockTxnDetailedStatusPub, mockPayCl, mockMerchantProcessor := newInboundNotifDependencies(ctr)
			processor := inboundNotifProcessor.NewProcessor(mockDepositClient, mockTxnDao, mockOrderDao, mockOrderEventPub, mockTncEventPub, mockEventsBroker, mockTimelineProcessor, mockPiProcessor, mocksUpiProcessor, mockOrderProcessor, mockOrderOrchPub, mockOrderNotifPub, mockOrderSearchPub, mockMerchantCl,
				mockP2PInvestmentClient, mockOrderVPAVerfPub, mockTxnNotifPub, mockCelestialProcessor, mockIFTClient, mockMerchantResCl, mockParserCl, conf, mockEval, mockEnachCl, mockRecurPayCl, mockTxnDetailedStatusPub, mockPiClient, mockPayCl, mockMerchantProcessor)
			if tt.mockGetPiById.enable {
				mockPiProcessor.EXPECT().
					GetPiById(context.Background(), tt.mockGetPiById.piId).
					Return(tt.mockGetPiById.pi, tt.mockGetPiById.err)
			}
			if tt.mockGetPiByVPA.enable {
				mockPiProcessor.EXPECT().
					GetPiByVPA(context.Background(), tt.mockGetPiByVPA.vpa).
					Return(tt.mockGetPiByVPA.pi, tt.mockGetPiByVPA.err)
			}
			got := processor.GetUpdatedDetailsForExtendedPayment(context.Background(), tt.args.txn, tt.args.isDebit, tt.args.parsedDetails, tt.args.partnerBank)

			if !proto2.Equal(tt.want, got) {
				t.Errorf("UpdateParsedDetailsForExtendedPayment got = %s want = %s", got.String(),
					tt.want.String())
				return
			}
		})
	}
}
