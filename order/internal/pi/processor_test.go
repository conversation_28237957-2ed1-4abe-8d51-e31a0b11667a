package pi_test

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/accounts"
	"github.com/epifi/gamma/api/merchant"
	mock_merchant "github.com/epifi/gamma/api/merchant/mocks"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	paymentNotificationPb "github.com/epifi/gamma/api/order/payment/notification"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	accountPiMocks "github.com/epifi/gamma/api/paymentinstrument/account_pi/mocks"
	piMocks "github.com/epifi/gamma/api/paymentinstrument/mocks"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/vendorgateway/merchantresolution"
	mocks2 "github.com/epifi/gamma/api/vendorgateway/merchantresolution/mocks"
	vgPaymentPb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/payment/mocks"
	piProcessor "github.com/epifi/gamma/order/internal/pi"
	mock_savings "github.com/epifi/gamma/order/internal/savings/mocks"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/crypto"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/mask"
	"github.com/epifi/gamma/pkg/pay"
)

func TestService_ResolveInternalActorPiDetails(t *testing.T) {
	env, err := cfg.GetEnvironment()
	require.NoError(t, err)

	logger.Init(env)

	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockAccountPiRelationClient := accountPiMocks.NewMockAccountPIRelationClient(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)
	mockSavingsProcessor := mock_savings.NewMockISavingsProcessor(ctr)
	helperSvc := piProcessor.NewProcessor(mockAccountPiRelationClient, mockPiClient, nil, nil, nil, nil, nil, nil, nil, mockSavingsProcessor, nil)

	type args struct {
		internalSavingsAccountId string
		internalSavingsAcctNo    string
		isCardTxn                bool
		partnerBank              commonvgpb.Vendor
		source                   paymentPb.TransactionDetailedStatus_DetailedStatus_API
		maskedCardNumber         string
	}
	type mockGetPiByAccountId struct {
		enable bool
		req    *accountPiPb.GetPiByAccountIdRequest
		res    *accountPiPb.GetPiByAccountIdResponse
		err    error
	}
	type mockGetPi struct {
		enable bool
		req    *piPb.GetPiRequest
		res    *piPb.GetPiResponse
		err    error
	}
	type mockGetIfscForGivenAccountAndVendor struct {
		enable        bool
		accountNumber string
		partnerBank   commonvgpb.Vendor
		ifsc          string
		err           error
	}
	type mockGetSavingsEssentials struct {
		enable bool
		req    *savingsPb.GetSavingsAccountEssentialsRequest
		res    *savingsPb.GetSavingsAccountEssentialsResponse
		err    error
	}
	tests := []struct {
		name                                string
		args                                args
		mockGetPiByAccountId                mockGetPiByAccountId
		mockGetPi                           mockGetPi
		mockGetSavingsEssentials            mockGetSavingsEssentials
		mockGetIfscForGivenAccountAndVendor mockGetIfscForGivenAccountAndVendor
		wantPiId                            string
		wantActorName                       string
		wantErr                             bool
		wantErrType                         error
	}{
		{
			name: "Should fetch details for savings account",
			mockGetIfscForGivenAccountAndVendor: mockGetIfscForGivenAccountAndVendor{
				enable:        true,
				accountNumber: "account-1",
				partnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
				ifsc:          "FDRL0001001",
			},
			mockGetPi: mockGetPi{
				enable: true,
				req: &piPb.GetPiRequest{
					Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
					Identifier: &piPb.GetPiRequest_AccountRequestParams_{
						AccountRequestParams: &piPb.GetPiRequest_AccountRequestParams{
							ActualAccountNumber: "account-1",
							IfscCode:            "FDRL0001001",
						},
					},
				},
				res: &piPb.GetPiResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Id:           "pi-1",
						VerifiedName: "actor-1-name",
					},
				},
				err: nil,
			},
			args: args{
				internalSavingsAccountId: "account-id-1",
				internalSavingsAcctNo:    "account-1",
				isCardTxn:                false,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
			},
			wantPiId:      "pi-1",
			wantActorName: "actor-1-name",
			wantErr:       false,
			wantErrType:   nil,
		},
		{
			name: "Should fetch details for a card transaction",
			mockGetIfscForGivenAccountAndVendor: mockGetIfscForGivenAccountAndVendor{
				enable:        true,
				accountNumber: "account-1",
				partnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
				ifsc:          "FDRL0001001",
			},
			mockGetPiByAccountId: mockGetPiByAccountId{
				enable: true,
				req: &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   "account-id-1",
					AccountType: accounts.Type_SAVINGS,
					PiTypes: []piPb.PaymentInstrumentType{
						piPb.PaymentInstrumentType_DEBIT_CARD,
						piPb.PaymentInstrumentType_CREDIT_CARD,
					},
				},
				res: &accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstruments: []*piPb.PaymentInstrument{
						{
							Id:                   "pi-1",
							Type:                 piPb.PaymentInstrumentType_DEBIT_CARD,
							State:                piPb.PaymentInstrumentState_VERIFIED,
							IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
							VerifiedName:         "actor-1-name",
						},
					},
				},
				err: nil,
			},
			args: args{
				internalSavingsAccountId: "account-id-1",
				internalSavingsAcctNo:    "account-1",
				isCardTxn:                true,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
			},
			wantPiId:      "pi-1",
			wantActorName: "actor-1-name",
			wantErr:       false,
			wantErrType:   nil,
		},
		{
			name: "Should fallback to account pi for a card transaction as card is inactive",
			mockGetIfscForGivenAccountAndVendor: mockGetIfscForGivenAccountAndVendor{
				enable:        true,
				accountNumber: "account-1",
				partnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
				ifsc:          "FDRL0001001",
			},
			mockGetPiByAccountId: mockGetPiByAccountId{
				enable: true,
				req: &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   "account-id-1",
					AccountType: accounts.Type_SAVINGS,
					PiTypes: []piPb.PaymentInstrumentType{
						piPb.PaymentInstrumentType_DEBIT_CARD,
						piPb.PaymentInstrumentType_CREDIT_CARD,
					},
				},
				res: &accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusRecordNotFound(),
				},
				err: nil,
			},
			mockGetPi: mockGetPi{
				enable: true,
				req: &piPb.GetPiRequest{
					Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
					Identifier: &piPb.GetPiRequest_AccountRequestParams_{
						AccountRequestParams: &piPb.GetPiRequest_AccountRequestParams{
							ActualAccountNumber: "account-1",
							IfscCode:            "FDRL0001001",
						},
					},
				},
				res: &piPb.GetPiResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Id:           "pi-1",
						VerifiedName: "actor-1-name",
					},
				},
				err: nil,
			},
			args: args{
				internalSavingsAccountId: "account-id-1",
				internalSavingsAcctNo:    "account-1",
				isCardTxn:                true,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
			},
			wantPiId:      "pi-1",
			wantActorName: "actor-1-name",
			wantErr:       false,
			wantErrType:   nil,
		},
		{
			name: "should fallback to active pi for a card transaction",
			mockGetIfscForGivenAccountAndVendor: mockGetIfscForGivenAccountAndVendor{
				enable:        true,
				accountNumber: "account-1",
				partnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
				ifsc:          "FDRL0001001",
			},
			mockGetPiByAccountId: mockGetPiByAccountId{
				enable: true,
				req: &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   "account-id-1",
					AccountType: accounts.Type_SAVINGS,
					PiTypes: []piPb.PaymentInstrumentType{
						piPb.PaymentInstrumentType_DEBIT_CARD,
						piPb.PaymentInstrumentType_CREDIT_CARD,
					},
				},
				res: &accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstruments: []*piPb.PaymentInstrument{
						{
							Id:                   "pi-1",
							Type:                 piPb.PaymentInstrumentType_DEBIT_CARD,
							State:                piPb.PaymentInstrumentState_PRE_CREATED,
							IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
							VerifiedName:         "actor-1-name",
							CreatedAt:            timestampPb.New(time.Now().Add(-10 * time.Second)),
						},
						{
							Id:                   "pi-2",
							Type:                 piPb.PaymentInstrumentType_DEBIT_CARD,
							State:                piPb.PaymentInstrumentState_VERIFIED,
							IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
							VerifiedName:         "actor-1-name",
							CreatedAt:            timestampPb.New(time.Now().Add(-20 * time.Second)),
						},
					},
				},
				err: nil,
			},
			args: args{
				internalSavingsAccountId: "account-id-1",
				internalSavingsAcctNo:    "account-1",
				isCardTxn:                true,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
			},
			wantPiId:      "pi-2",
			wantActorName: "actor-1-name",
			wantErr:       false,
			wantErrType:   nil,
		},
		{
			name: "should fallback to latest pi for a card transaction as card is inactive",
			mockGetIfscForGivenAccountAndVendor: mockGetIfscForGivenAccountAndVendor{
				enable:        true,
				accountNumber: "account-1",
				partnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
				ifsc:          "FDRL0001001",
			},
			mockGetPiByAccountId: mockGetPiByAccountId{
				enable: true,
				req: &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   "account-id-1",
					AccountType: accounts.Type_SAVINGS,
					PiTypes: []piPb.PaymentInstrumentType{
						piPb.PaymentInstrumentType_DEBIT_CARD,
						piPb.PaymentInstrumentType_CREDIT_CARD,
					},
				},
				res: &accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstruments: []*piPb.PaymentInstrument{
						{
							Id:                   "pi-1",
							Type:                 piPb.PaymentInstrumentType_DEBIT_CARD,
							State:                piPb.PaymentInstrumentState_PRE_CREATED,
							IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
							VerifiedName:         "actor-1-name",
							CreatedAt:            timestampPb.New(time.Now().Add(-10 * time.Second)),
						},
						{
							Id:                   "pi-2",
							Type:                 piPb.PaymentInstrumentType_DEBIT_CARD,
							State:                piPb.PaymentInstrumentState_BLOCKED,
							IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
							VerifiedName:         "actor-1-name",
							CreatedAt:            timestampPb.New(time.Now().Add(-20 * time.Second)),
						},
					},
				},
				err: nil,
			},
			args: args{
				internalSavingsAccountId: "account-id-1",
				internalSavingsAcctNo:    "account-1",
				isCardTxn:                true,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
			},
			wantPiId:      "pi-1",
			wantActorName: "actor-1-name",
			wantErr:       false,
			wantErrType:   nil,
		},
		{
			name: "should return card pi even if it's de-activated for decline data scenarios",
			args: args{
				internalSavingsAccountId: "account-id-1",
				internalSavingsAcctNo:    "account-1",
				isCardTxn:                true,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				source:                   paymentPb.TransactionDetailedStatus_DetailedStatus_CARD_DECLINE_DATA_DUMP,
				maskedCardNumber:         "1234********5678",
			},
			mockGetIfscForGivenAccountAndVendor: mockGetIfscForGivenAccountAndVendor{
				enable:        true,
				accountNumber: "account-1",
				partnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
				ifsc:          "FDRL0001001",
			},
			mockGetPiByAccountId: mockGetPiByAccountId{
				enable: true,
				req: &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   "account-id-1",
					AccountType: accounts.Type_SAVINGS,
					PiTypes: []piPb.PaymentInstrumentType{
						piPb.PaymentInstrumentType_DEBIT_CARD,
						piPb.PaymentInstrumentType_CREDIT_CARD,
					},
				},
				res: &accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstruments: []*piPb.PaymentInstrument{
						{
							Id:   "pi-1",
							Type: piPb.PaymentInstrumentType_DEBIT_CARD,
							Identifier: &piPb.PaymentInstrument_Card{
								Card: &piPb.Card{
									ActualCardNumber: "1234********9999",
								},
							},
							VerifiedName: "abc",
						},
						{
							Id:   "pi-2",
							Type: piPb.PaymentInstrumentType_DEBIT_CARD,
							Identifier: &piPb.PaymentInstrument_Card{
								Card: &piPb.Card{
									ActualCardNumber: "1234********5678",
								},
							},
							VerifiedName: "abc-2",
						},
					},
				},
				err: nil,
			},
			wantPiId:      "pi-2",
			wantActorName: "abc-2",
			wantErr:       false,
			wantErrType:   nil,
		},
		{
			name: "Should fallback to account pi for a card transaction as card PI not found",
			mockGetIfscForGivenAccountAndVendor: mockGetIfscForGivenAccountAndVendor{
				enable:        true,
				accountNumber: "account-1",
				partnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
				ifsc:          "FDRL0001001",
			},
			mockGetPiByAccountId: mockGetPiByAccountId{
				enable: true,
				req: &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   "account-id-1",
					AccountType: accounts.Type_SAVINGS,
					PiTypes: []piPb.PaymentInstrumentType{
						piPb.PaymentInstrumentType_DEBIT_CARD,
						piPb.PaymentInstrumentType_CREDIT_CARD,
					},
				},
				res: &accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusRecordNotFound(),
				},
				err: nil,
			},
			mockGetPi: mockGetPi{
				enable: true,
				req: &piPb.GetPiRequest{
					Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
					Identifier: &piPb.GetPiRequest_AccountRequestParams_{
						AccountRequestParams: &piPb.GetPiRequest_AccountRequestParams{
							ActualAccountNumber: "account-1",
							IfscCode:            "FDRL0001001",
						},
					},
				},
				res: &piPb.GetPiResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Id:           "pi-1",
						VerifiedName: "actor-1-name",
					},
				},
				err: nil,
			},
			args: args{
				internalSavingsAccountId: "account-id-1",
				internalSavingsAcctNo:    "account-1",
				isCardTxn:                true,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
			},
			wantPiId:      "pi-1",
			wantActorName: "actor-1-name",
			wantErr:       false,
			wantErrType:   nil,
		},
		{
			name: "Should return permanent failure as savings account PI not found",
			mockGetIfscForGivenAccountAndVendor: mockGetIfscForGivenAccountAndVendor{
				enable:        true,
				accountNumber: "account-1",
				partnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
				ifsc:          "FDRL0001001",
			},
			mockGetPi: mockGetPi{
				enable: true,
				req: &piPb.GetPiRequest{
					Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
					Identifier: &piPb.GetPiRequest_AccountRequestParams_{
						AccountRequestParams: &piPb.GetPiRequest_AccountRequestParams{
							ActualAccountNumber: "account-1",
							IfscCode:            "FDRL0001001",
						},
					},
				},
				res: &piPb.GetPiResponse{
					Status: rpc.StatusRecordNotFound(),
				},
				err: nil,
			},
			args: args{
				internalSavingsAccountId: "account-id-1",
				internalSavingsAcctNo:    "account-1",
				isCardTxn:                false,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
			},
			wantErr:     true,
			wantErrType: epifierrors.ErrPermanent,
		},
		{
			name: "Should return transient failure as failed to fetch savings account PI",
			mockGetIfscForGivenAccountAndVendor: mockGetIfscForGivenAccountAndVendor{
				enable:        true,
				accountNumber: "account-1",
				partnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
				ifsc:          "FDRL0001001",
			},
			mockGetPi: mockGetPi{
				enable: true,
				req: &piPb.GetPiRequest{
					Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
					Identifier: &piPb.GetPiRequest_AccountRequestParams_{
						AccountRequestParams: &piPb.GetPiRequest_AccountRequestParams{
							ActualAccountNumber: "account-1",
							IfscCode:            "FDRL0001001",
						},
					},
				},
				res: &piPb.GetPiResponse{
					Status: rpc.StatusInternal(),
				},
				err: nil,
			},
			args: args{
				internalSavingsAccountId: "account-id-1",
				internalSavingsAcctNo:    "account-1",
				isCardTxn:                false,
				partnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
			},
			wantErr:     true,
			wantErrType: epifierrors.ErrTransient,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			if tt.mockGetPi.enable {
				mockPiClient.EXPECT().GetPi(context.Background(), tt.mockGetPi.req).
					Return(tt.mockGetPi.res, tt.mockGetPi.err)
			}

			if tt.mockGetPiByAccountId.enable {
				mockAccountPiRelationClient.EXPECT().GetPiByAccountId(context.Background(), tt.mockGetPiByAccountId.req).
					Return(tt.mockGetPiByAccountId.res, tt.mockGetPiByAccountId.err)
			}

			if tt.mockGetIfscForGivenAccountAndVendor.enable {
				mockSavingsProcessor.EXPECT().GetIfscForAccountNumberAndVendor(context.Background(), tt.mockGetIfscForGivenAccountAndVendor.accountNumber, tt.mockGetIfscForGivenAccountAndVendor.partnerBank).
					Return(tt.mockGetIfscForGivenAccountAndVendor.ifsc, tt.mockGetIfscForGivenAccountAndVendor.err)
			}

			gotPiId, gotActorName, err := helperSvc.ResolveInternalActorPiDetails(
				context.Background(),
				tt.args.internalSavingsAccountId,
				tt.args.internalSavingsAcctNo,
				tt.args.isCardTxn,
				tt.args.partnerBank,
				tt.args.source,
				tt.args.maskedCardNumber,
			)
			if (err != nil) != tt.wantErr {
				t.Errorf("ResolveInternalActorPiDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr && !errors.Is(err, tt.wantErrType) {
				t.Errorf("wanted: %v, got: %v", tt.wantErrType, err)
				return
			}
			if gotPiId != tt.wantPiId {
				t.Errorf("ResolveInternalActorPiDetails() gotPiId = %v, wantPiId %v", gotPiId, tt.wantPiId)
			}
			if gotActorName != tt.wantActorName {
				t.Errorf("ResolveInternalActorPiDetails() gotActorName = %v, wantActorName %v", gotActorName, tt.wantActorName)
			}
		})
	}
}

func TestProcessor_GetPiById(t *testing.T) {
	var (
		testPi = &piPb.PaymentInstrument{Id: "id-1"}
	)
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockAccountPiRelationClient := accountPiMocks.NewMockAccountPIRelationClient(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)

	piProcessor := piProcessor.NewProcessor(mockAccountPiRelationClient, mockPiClient, nil, nil, nil, nil, nil, nil, nil, nil, nil)

	type mockGetPiById struct {
		enable bool
		piId   string
		res    *piPb.GetPiByIdResponse
		err    error
	}

	tests := []struct {
		name          string
		piId          string
		mockGetPiById mockGetPiById
		want          *piPb.PaymentInstrument
		wantErr       bool
		wantErrType   error
	}{
		{
			name: "get pi successfully",
			piId: "id-1",
			mockGetPiById: mockGetPiById{
				enable: true,
				piId:   "id-1",
				res: &piPb.GetPiByIdResponse{
					Status:            rpc.StatusOk(),
					PaymentInstrument: testPi,
				},
				err: nil,
			},
			want:    testPi,
			wantErr: false,
		},
		{
			name: "transient error due to unexpected error ",
			piId: "id-1",
			mockGetPiById: mockGetPiById{
				enable: true,
				piId:   "id-1",
				res: &piPb.GetPiByIdResponse{
					Status: rpc.StatusInternal(),
				},
				err: nil,
			},
			wantErr:     true,
			wantErrType: epifierrors.ErrTransient,
		},
		{
			name: "permanent error due to record not found error ",
			piId: "id-1",
			mockGetPiById: mockGetPiById{
				enable: true,
				piId:   "id-1",
				res: &piPb.GetPiByIdResponse{
					Status: rpc.StatusRecordNotFound(),
				},
				err: nil,
			},
			wantErr:     true,
			wantErrType: epifierrors.ErrPermanent,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetPiById.enable {
				mockPiClient.EXPECT().GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: tt.mockGetPiById.piId}).
					Return(tt.mockGetPiById.res, tt.mockGetPiById.err)
			}
			got, err := piProcessor.GetPiById(context.Background(), tt.piId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPiById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr && !errors.Is(err, tt.wantErrType) {
				t.Errorf("GetPiById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetPiById() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProcessor_CreateAaAccountPiRelation(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockAccountPiRelationClient := accountPiMocks.NewMockAccountPIRelationClient(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)

	processor := piProcessor.NewProcessor(mockAccountPiRelationClient, mockPiClient, nil, nil, nil, nil, nil, nil, nil, nil, nil)

	type mockCreate struct {
		enable bool
		req    *accountPiPb.CreateAaAccountPiRequest
		res    *accountPiPb.CreateAaAccountPiResponse
		err    error
	}

	tests := []struct {
		name        string
		accountType accounts.Type
		accountId   string
		piId        string
		actorId     string
		mockCreate  mockCreate
		wantErr     bool
	}{
		{
			name:        "create account pi relation",
			accountType: accounts.Type_SAVINGS,
			piId:        "pi-1",
			actorId:     "actor-1",
			accountId:   "account-1",
			mockCreate: mockCreate{
				enable: true,
				req: &accountPiPb.CreateAaAccountPiRequest{
					ActorId:     "actor-1",
					AccountId:   "account-1",
					AccountType: accounts.Type_SAVINGS,
					PiId:        "pi-1",
				},
				res: &accountPiPb.CreateAaAccountPiResponse{
					Status: rpc.StatusOk(),
				},
			},
		},
		{
			name:        "account pi relation already exists",
			accountType: accounts.Type_SAVINGS,
			piId:        "pi-1",
			actorId:     "actor-1",
			accountId:   "account-1",
			mockCreate: mockCreate{
				enable: true,
				req: &accountPiPb.CreateAaAccountPiRequest{
					ActorId:     "actor-1",
					AccountId:   "account-1",
					AccountType: accounts.Type_SAVINGS,
					PiId:        "pi-1",
				},
				res: &accountPiPb.CreateAaAccountPiResponse{
					Status: rpc.StatusAlreadyExists(),
				},
			},
		},
		{
			name:        "error creating account pi relation",
			accountType: accounts.Type_SAVINGS,
			piId:        "pi-1",
			actorId:     "actor-1",
			accountId:   "account-1",
			mockCreate: mockCreate{
				enable: true,
				req: &accountPiPb.CreateAaAccountPiRequest{
					ActorId:     "actor-1",
					AccountId:   "account-1",
					AccountType: accounts.Type_SAVINGS,
					PiId:        "pi-1",
				},
				res: &accountPiPb.CreateAaAccountPiResponse{
					Status: rpc.StatusInternal(),
				},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockCreate.enable {
				mockAccountPiRelationClient.EXPECT().CreateAaAccountPi(context.Background(), tt.mockCreate.req).
					Return(tt.mockCreate.res, tt.mockCreate.err)
			}

			err := processor.CreateAaAccountPiRelation(context.Background(), tt.accountType, tt.accountId, tt.piId, tt.actorId)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateAaAccountPiRelation() gotErr: %v, wantErr: %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestProcessor_createCardMerchantInfo(t *testing.T) {

	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockPiClient := piMocks.NewMockPiClient(ctr)
	mockMerchantClient := mock_merchant.NewMockMerchantServiceClient(ctr)
	processor := piProcessor.NewProcessor(nil, mockPiClient, mockMerchantClient, nil, nil, nil, nil, nil, nil, nil, nil)
	type args struct {
		ctx               context.Context
		parsedParticulars *paymentNotificationPb.ParsedTxnParticulars
		piId              string
		mocks             []interface{}
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "do not create card merchant info since pi identifier is generic pay account",
			args: args{
				ctx: context.Background(),
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					PiIdentifier: pay.GenericAccount(),
					Protocol:     paymentPb.PaymentProtocol_CARD,
					TxnCategory:  paymentNotificationPb.ParsedTxnParticulars_ECOMM,
				},
				piId: "test-pid",
			},
			wantErr: false,
		},
		{
			name: "do not create card merchant info when pi identifier is VPA and protocol is UPI",
			args: args{
				ctx: context.Background(),
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					PiIdentifier: &paymentNotificationPb.ParsedTxnParticulars_Vpa{Vpa: "test@upi"},
					Protocol:     paymentPb.PaymentProtocol_UPI,
				},
				piId: "test-pid",
			},
			wantErr: false,
		},
		{
			name: "do not create card merchant info since category is not pos or e-comm or ATM deposit or ATM withdrawal",
			args: args{
				ctx: context.Background(),
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol:     paymentPb.PaymentProtocol_CARD,
					PiIdentifier: pay.GenericAccount(),
					TxnCategory:  paymentNotificationPb.ParsedTxnParticulars_CASH,
				},
				piId: "test-pid",
			},
			wantErr: false,
		},
		{
			name: "do not create card merchant info since protocol is not card",
			args: args{
				ctx: context.Background(),
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol:     paymentPb.PaymentProtocol_IMPS,
					PiIdentifier: pay.GenericAccount(),
					TxnCategory:  paymentNotificationPb.ParsedTxnParticulars_ECOMM,
				},
				piId: "test-pid",
			},
			wantErr: false,
		},
		{
			name: "create card merchant info but merchant service call failed",
			args: args{
				ctx: context.Background(),
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol: paymentPb.PaymentProtocol_CARD,
					PiIdentifier: &paymentNotificationPb.ParsedTxnParticulars_Account{
						Account: &paymentNotificationPb.ParsedTxnParticulars_AccountIdentifier{
							AccountNumber: "*****************",
							IfscCode:      pay.DefaultIfscCardTxn,
							PiType:        piPb.PaymentInstrumentType_GENERIC,
							AccountType:   accounts.Type_SAVINGS,
						},
					},
					TxnCategory: paymentNotificationPb.ParsedTxnParticulars_ECOMM,
				},
				piId: "test-pid",
				mocks: []interface{}{
					mockMerchantClient.EXPECT().CreateCardMerchantInfo(gomock.Any(), gomock.Any()).
						Return(&merchant.CreateCardMerchantInfoResponse{Status: rpc.StatusInternal()}, nil),
				},
			},
			wantErr: true,
		},
		{
			name: "create card merchant info successfully",
			args: args{
				ctx: context.Background(),
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol: paymentPb.PaymentProtocol_CARD,
					PiIdentifier: &paymentNotificationPb.ParsedTxnParticulars_Account{
						Account: &paymentNotificationPb.ParsedTxnParticulars_AccountIdentifier{
							AccountNumber: "*****************",
							IfscCode:      pay.DefaultIfscCardTxn,
							PiType:        piPb.PaymentInstrumentType_GENERIC,
							AccountType:   accounts.Type_SAVINGS,
						},
					},
					TxnCategory: paymentNotificationPb.ParsedTxnParticulars_ECOMM,
				},
				piId: "test-pid",
				mocks: []interface{}{
					mockMerchantClient.EXPECT().CreateCardMerchantInfo(gomock.Any(), gomock.Any()).
						Return(&merchant.CreateCardMerchantInfoResponse{Status: rpc.StatusOk()}, nil),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := processor.CreateCardMerchantInfo(tt.args.ctx, tt.args.parsedParticulars, tt.args.piId); (err != nil) != tt.wantErr {
				t.Errorf("CreateCardMerchantInfo() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestProcessor_ResolveOtherActorPiDetails(t *testing.T) {
	ctr := gomock.NewController(t)
	mockPiClient := piMocks.NewMockPiClient(ctr)
	mockPaymentVgClient := mocks.NewMockPaymentClient(ctr)
	mockMerchantClient := mock_merchant.NewMockMerchantServiceClient(ctr)
	mockMerchantResolutionClient := mocks2.NewMockMerchantResolutionClient(ctr)
	defer ctr.Finish()
	lastFetchedTime1 := time.Now().Add(-50 * time.Second)
	transactionTimestamp1 := timestampPb.New(lastFetchedTime1)
	lastFetchedTime2 := time.Now().Add(-100 * time.Second)
	transactionTimestamp2 := timestampPb.New(lastFetchedTime2)
	processor := piProcessor.NewProcessor(nil, mockPiClient, mockMerchantClient, nil, mockPaymentVgClient, nil, nil, nil, conf, nil, mockMerchantResolutionClient)

	type mockGetRemitterDetails struct {
		enable bool
		req    *vgPaymentPb.GetRemitterDetailsRequest
		want   *vgPaymentPb.GetRemitterDetailsResponse
		err    error
	}
	type mockCreatePi struct {
		enable bool
		req    *piPb.CreatePiRequest
		want   *piPb.CreatePiResponse
		err    error
	}
	type mockUpdatePi struct {
		enable bool
		req    *piPb.UpdatePiRequest
		want   *piPb.UpdatePiResponse
		err    error
	}
	type mockMerchantResolution struct {
		enable bool
		req    *merchantresolution.MerchantResolutionRequest
		res    *merchantresolution.MerchantResolutionResponse
		err    error
	}
	type mockCreateMerchant struct {
		enable bool
		req    *merchant.CreateMerchantRequest
		res    *merchant.CreateMerchantResponse
		err    error
	}
	type args struct {
		ctx               context.Context
		parsedParticulars *paymentNotificationPb.ParsedTxnParticulars
		txnDetails        *paymentNotificationPb.TransactionDetails
		actorId           string
		ownership         piPb.Ownership
	}
	tests := []struct {
		name                   string
		args                   args
		wantPiId               string
		wantOtherActorName     string
		wantIsMerchantPi       bool
		wantErr                error
		mockGetRemitterDetails mockGetRemitterDetails
		mockCreatePi           mockCreatePi
		mockUpdatePi           mockUpdatePi
		mockCreateMerchant     mockCreateMerchant
		mockMerchantResolution mockMerchantResolution
	}{
		{
			name: "Should fetch the Remitter Info since payment protocol is NEFT",
			args: args{
				ctx: context.Background(),
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol: paymentPb.PaymentProtocol_NEFT,
					Utr:      "S81234",
					PiIdentifier: &paymentNotificationPb.ParsedTxnParticulars_Account{
						Account: &paymentNotificationPb.ParsedTxnParticulars_AccountIdentifier{
							AccountNumber: "*********",
							PiType:        piPb.PaymentInstrumentType_BANK_ACCOUNT,
							AccountType:   accounts.Type_CURRENT,
						},
					},
				},
				txnDetails: &paymentNotificationPb.TransactionDetails{
					Remarks:               "txn-1",
					Id:                    "S81234",
					Timestamp:             transactionTimestamp1,
					AccountNumber:         "account-1",
					NotificationEventType: paymentNotificationPb.TransactionDetails_CREDIT,
				},
				actorId:   "random-actor-id",
				ownership: piPb.Ownership_EPIFI_TECH,
			},
			wantPiId:           "remitter-pi-id",
			wantOtherActorName: "remitter-name",
			wantIsMerchantPi:   false,
			mockGetRemitterDetails: mockGetRemitterDetails{
				enable: true,
				req: &vgPaymentPb.GetRemitterDetailsRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					TxnDatetime: transactionTimestamp1,
					CbsTranId:   "S81234",
				},
				want: &vgPaymentPb.GetRemitterDetailsResponse{
					Status:            rpc.StatusOk(),
					RemitterName:      "remitter-name",
					RemitterAccountNo: "*********",
					RemitterIfscCode:  "remitter-ifsc",
				},
			},
			mockCreatePi: mockCreatePi{
				enable: true,
				req: &piPb.CreatePiRequest{
					Type:         piPb.PaymentInstrumentType_BANK_ACCOUNT,
					VerifiedName: "remitter-name",
					Identifier: &piPb.CreatePiRequest_Account{
						Account: &piPb.Account{
							ActualAccountNumber: "*********",
							IfscCode:            "remitter-ifsc",
							AccountType:         accounts.Type_CURRENT,
							Name:                "remitter-name",
							SecureAccountNumber: mask.MaskLastNDigits("*********", 3, ""),
						},
					},
					Ownership: piPb.Ownership_EPIFI_TECH,
					Capabilities: map[string]bool{
						piPb.Capability_INBOUND_TXN.String():  true,
						piPb.Capability_OUTBOUND_TXN.String(): true,
					},
				},
				want: &piPb.CreatePiResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Id:           "remitter-pi-id",
						VerifiedName: "remitter-name",
					},
				},
			},
		},
		{
			name: "Should fetch the Remitter Info since payment protocol is RTGS",
			args: args{
				ctx: context.Background(),
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol: paymentPb.PaymentProtocol_RTGS,
					Utr:      "S81234",
					PiIdentifier: &paymentNotificationPb.ParsedTxnParticulars_Account{
						Account: &paymentNotificationPb.ParsedTxnParticulars_AccountIdentifier{
							AccountNumber: "*********",
							PiType:        piPb.PaymentInstrumentType_BANK_ACCOUNT,
							AccountType:   accounts.Type_CURRENT,
						},
					},
				},
				txnDetails: &paymentNotificationPb.TransactionDetails{
					Remarks:               "txn-1",
					Id:                    "S81234",
					Timestamp:             transactionTimestamp1,
					AccountNumber:         "account-1",
					NotificationEventType: paymentNotificationPb.TransactionDetails_CREDIT,
				},
				actorId:   "random-actor-id",
				ownership: piPb.Ownership_EPIFI_TECH,
			},
			wantPiId:           "remitter-pi-id",
			wantOtherActorName: "remitter-name",
			wantIsMerchantPi:   false,
			mockGetRemitterDetails: mockGetRemitterDetails{
				enable: true,
				req: &vgPaymentPb.GetRemitterDetailsRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					TxnDatetime: transactionTimestamp1,
					CbsTranId:   "S81234",
				},
				want: &vgPaymentPb.GetRemitterDetailsResponse{
					Status:            rpc.StatusOk(),
					RemitterName:      "remitter-name",
					RemitterAccountNo: "*********",
					RemitterIfscCode:  "remitter-ifsc",
				},
			},
			mockCreatePi: mockCreatePi{
				enable: true,
				req: &piPb.CreatePiRequest{
					Type:         piPb.PaymentInstrumentType_BANK_ACCOUNT,
					VerifiedName: "remitter-name",
					Identifier: &piPb.CreatePiRequest_Account{
						Account: &piPb.Account{
							ActualAccountNumber: "*********",
							IfscCode:            "remitter-ifsc",
							AccountType:         accounts.Type_CURRENT,
							Name:                "remitter-name",
							SecureAccountNumber: mask.MaskLastNDigits("*********", 3, ""),
						},
					},
					Ownership: piPb.Ownership_EPIFI_TECH,
					Capabilities: map[string]bool{
						piPb.Capability_INBOUND_TXN.String():  true,
						piPb.Capability_OUTBOUND_TXN.String(): true,
					},
				},
				want: &piPb.CreatePiResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Id:           "remitter-pi-id",
						VerifiedName: "remitter-name",
					},
				},
			},
		},
		{
			name: "Transient Failure while fetching the remitter details",
			args: args{
				ctx: context.Background(),
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol: paymentPb.PaymentProtocol_NEFT,
					Utr:      "S81234",
					PiIdentifier: &paymentNotificationPb.ParsedTxnParticulars_Account{
						Account: &paymentNotificationPb.ParsedTxnParticulars_AccountIdentifier{
							AccountNumber: "*********",
							PiType:        piPb.PaymentInstrumentType_BANK_ACCOUNT,
							AccountType:   accounts.Type_CURRENT,
						},
					},
				},
				txnDetails: &paymentNotificationPb.TransactionDetails{
					Remarks:               "txn-1",
					Id:                    "S81234",
					Timestamp:             transactionTimestamp1,
					AccountNumber:         "account-1",
					NotificationEventType: paymentNotificationPb.TransactionDetails_CREDIT,
				},
				actorId:   "random-actor-id",
				ownership: piPb.Ownership_EPIFI_TECH,
			},
			mockGetRemitterDetails: mockGetRemitterDetails{
				enable: true,
				req: &vgPaymentPb.GetRemitterDetailsRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					TxnDatetime: transactionTimestamp1,
					CbsTranId:   "S81234",
				},
				want: &vgPaymentPb.GetRemitterDetailsResponse{
					Status: rpc.StatusInternal(),
				},
			},
			wantErr: epifierrors.ErrTransient,
		},
		{
			name: "No Record Error from GetRemitterDetails and last api call time <= threshold , should return transient failure",
			args: args{
				ctx: context.Background(),
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol: paymentPb.PaymentProtocol_NEFT,
					Utr:      "S81234",
					PiIdentifier: &paymentNotificationPb.ParsedTxnParticulars_Account{
						Account: &paymentNotificationPb.ParsedTxnParticulars_AccountIdentifier{
							AccountNumber: "*********",
							PiType:        piPb.PaymentInstrumentType_BANK_ACCOUNT,
							AccountType:   accounts.Type_CURRENT,
						},
					},
				},
				txnDetails: &paymentNotificationPb.TransactionDetails{
					Remarks:               "txn-1",
					Id:                    "S81234",
					Timestamp:             transactionTimestamp1,
					AccountNumber:         "account-1",
					NotificationEventType: paymentNotificationPb.TransactionDetails_CREDIT,
				},
				actorId:   "random-actor-id",
				ownership: piPb.Ownership_EPIFI_TECH,
			},
			wantPiId:           "remitter-pi-id",
			wantOtherActorName: "remitter-name",
			wantIsMerchantPi:   false,
			mockGetRemitterDetails: mockGetRemitterDetails{
				enable: true,
				req: &vgPaymentPb.GetRemitterDetailsRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					TxnDatetime: transactionTimestamp1,
					CbsTranId:   "S81234",
				},
				want: &vgPaymentPb.GetRemitterDetailsResponse{
					Status: rpc.StatusRecordNotFound(),
				},
			},
			wantErr: epifierrors.ErrTransient,
		},
		{
			name: "No Record Error from GetRemitterDetails and last api call time > threshold , should supress the error and create the pi",
			args: args{
				ctx: context.Background(),
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol: paymentPb.PaymentProtocol_NEFT,
					Utr:      "S81234",
					PiIdentifier: &paymentNotificationPb.ParsedTxnParticulars_Account{
						Account: &paymentNotificationPb.ParsedTxnParticulars_AccountIdentifier{
							AccountNumber: "*********",
							PiType:        piPb.PaymentInstrumentType_BANK_ACCOUNT,
							AccountType:   accounts.Type_CURRENT,
						},
					},
				},
				txnDetails: &paymentNotificationPb.TransactionDetails{
					Remarks:               "txn-1",
					Id:                    "S81234",
					Timestamp:             transactionTimestamp2,
					AccountNumber:         "account-1",
					NotificationEventType: paymentNotificationPb.TransactionDetails_CREDIT,
				},
				actorId:   "random-actor-id",
				ownership: piPb.Ownership_EPIFI_TECH,
			},
			wantPiId:           "pi-id",
			wantOtherActorName: "Others",
			wantIsMerchantPi:   false,
			mockGetRemitterDetails: mockGetRemitterDetails{
				enable: true,
				req: &vgPaymentPb.GetRemitterDetailsRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					TxnDatetime: transactionTimestamp2,
					CbsTranId:   "S81234",
				},
				want: &vgPaymentPb.GetRemitterDetailsResponse{
					Status: rpc.StatusRecordNotFound(),
				},
			},
			mockCreatePi: mockCreatePi{
				enable: true,
				req: &piPb.CreatePiRequest{
					Type:         piPb.PaymentInstrumentType_BANK_ACCOUNT,
					VerifiedName: "Others",
					Identifier: &piPb.CreatePiRequest_Account{
						Account: &piPb.Account{
							ActualAccountNumber: "*********",
							AccountType:         accounts.Type_CURRENT,
							Name:                "Others",
							SecureAccountNumber: mask.MaskLastNDigits("*********", 3, ""),
						},
					},
					Ownership: piPb.Ownership_EPIFI_TECH,
					Capabilities: map[string]bool{
						piPb.Capability_INBOUND_TXN.String():  true,
						piPb.Capability_OUTBOUND_TXN.String(): true,
					},
				},
				want: &piPb.CreatePiResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Id:           "pi-id",
						VerifiedName: "Others",
					},
				},
			},
		},
		{
			name: "should update the verified name in case other actor pi already exists in db with full account number",
			args: args{
				ctx: context.Background(),
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol: paymentPb.PaymentProtocol_NEFT,
					Utr:      "S81234",
					PiIdentifier: &paymentNotificationPb.ParsedTxnParticulars_Account{
						Account: &paymentNotificationPb.ParsedTxnParticulars_AccountIdentifier{
							AccountNumber: "*********",
							PiType:        piPb.PaymentInstrumentType_BANK_ACCOUNT,
							AccountType:   accounts.Type_CURRENT,
						},
					},
				},
				txnDetails: &paymentNotificationPb.TransactionDetails{
					Remarks:               "txn-1",
					Id:                    "S81234",
					Timestamp:             transactionTimestamp1,
					AccountNumber:         "account-1",
					NotificationEventType: paymentNotificationPb.TransactionDetails_CREDIT,
				},
				actorId:   "random-actor-id",
				ownership: piPb.Ownership_EPIFI_TECH,
			},
			wantPiId:           "remitter-pi-id",
			wantOtherActorName: "remitter-name",
			wantIsMerchantPi:   false,
			mockGetRemitterDetails: mockGetRemitterDetails{
				enable: true,
				req: &vgPaymentPb.GetRemitterDetailsRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					TxnDatetime: transactionTimestamp1,
					CbsTranId:   "S81234",
				},
				want: &vgPaymentPb.GetRemitterDetailsResponse{
					Status:            rpc.StatusOk(),
					RemitterName:      "remitter-name",
					RemitterAccountNo: "*********",
					RemitterIfscCode:  "remitter-ifsc",
				},
			},
			mockCreatePi: mockCreatePi{
				enable: true,
				req: &piPb.CreatePiRequest{
					Type:         piPb.PaymentInstrumentType_BANK_ACCOUNT,
					VerifiedName: "remitter-name",
					Identifier: &piPb.CreatePiRequest_Account{
						Account: &piPb.Account{
							ActualAccountNumber: "*********",
							IfscCode:            "remitter-ifsc",
							AccountType:         accounts.Type_CURRENT,
							Name:                "remitter-name",
							SecureAccountNumber: mask.MaskLastNDigits("*********", 3, ""),
						},
					},
					Ownership: piPb.Ownership_EPIFI_TECH,
					Capabilities: map[string]bool{
						piPb.Capability_INBOUND_TXN.String():  true,
						piPb.Capability_OUTBOUND_TXN.String(): true,
					},
				},
				want: &piPb.CreatePiResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Id: "remitter-pi-id",
						// Not returing the verified name , since the Pi was already there in the db and verified name was not populated
					},
				},
			},
			mockUpdatePi: mockUpdatePi{
				enable: true,
				req: &piPb.UpdatePiRequest{
					PaymentInstrument: &piPb.PaymentInstrument{
						VerifiedName: "remitter-name",
						Id:           "remitter-pi-id",
					},
					UpdateReason:    "Updating verified name as remitter name",
					UpdateFieldMask: []piPb.PaymentInstrumentFieldMask{piPb.PaymentInstrumentFieldMask_VERIFIED_NAME},
				},
				want: &piPb.UpdatePiResponse{
					Status: rpc.StatusOk(),
				},
			},
		},
		{
			name: "Should Ignore the Remitter Info Api since the transaction is not neft or rtgs",
			args: args{
				ctx: context.Background(),
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol: paymentPb.PaymentProtocol_UPI,
					Utr:      "S81234",
					PiIdentifier: &paymentNotificationPb.ParsedTxnParticulars_Account{
						Account: &paymentNotificationPb.ParsedTxnParticulars_AccountIdentifier{
							AccountNumber: "*********",
							PiType:        piPb.PaymentInstrumentType_BANK_ACCOUNT,
							AccountType:   accounts.Type_CURRENT,
						},
					},
				},
				txnDetails: &paymentNotificationPb.TransactionDetails{
					Remarks:               "txn-1",
					Id:                    "S81234",
					Timestamp:             transactionTimestamp1,
					AccountNumber:         "account-1",
					NotificationEventType: paymentNotificationPb.TransactionDetails_CREDIT,
				},
				actorId:   "random-actor-id",
				ownership: piPb.Ownership_EPIFI_TECH,
			},
			wantPiId:           "pi-id",
			wantOtherActorName: "Others",
			mockCreatePi: mockCreatePi{
				enable: true,
				req: &piPb.CreatePiRequest{
					Type:         piPb.PaymentInstrumentType_BANK_ACCOUNT,
					VerifiedName: "Others",
					Identifier: &piPb.CreatePiRequest_Account{
						Account: &piPb.Account{
							Name:                "Others",
							ActualAccountNumber: "*********",
							AccountType:         accounts.Type_CURRENT,
							SecureAccountNumber: mask.MaskLastNDigits("*********", 3, ""),
						},
					},
					Ownership: piPb.Ownership_EPIFI_TECH,
					Capabilities: map[string]bool{
						piPb.Capability_INBOUND_TXN.String():  true,
						piPb.Capability_OUTBOUND_TXN.String(): true,
					},
				},
				want: &piPb.CreatePiResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Id:           "pi-id",
						VerifiedName: "Others",
					},
				},
			},
		},
		{
			name: "Should Ignore the Remitter Info Api since the transaction is not of type credit",
			args: args{
				ctx: context.Background(),
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol: paymentPb.PaymentProtocol_RTGS,
					Utr:      "S81234",
					PiIdentifier: &paymentNotificationPb.ParsedTxnParticulars_Account{
						Account: &paymentNotificationPb.ParsedTxnParticulars_AccountIdentifier{
							AccountNumber: "123456-789",
							PiType:        piPb.PaymentInstrumentType_BANK_ACCOUNT,
							AccountType:   accounts.Type_CURRENT,
						},
					},
				},
				txnDetails: &paymentNotificationPb.TransactionDetails{
					Remarks:               "txn-1",
					Id:                    "S81234",
					Timestamp:             transactionTimestamp1,
					AccountNumber:         "account-1",
					NotificationEventType: paymentNotificationPb.TransactionDetails_DEBIT,
				},
				actorId:   "random-actor-id",
				ownership: piPb.Ownership_EPIFI_TECH,
			},
			wantPiId:           "pi-id",
			wantOtherActorName: "Others",
			mockCreatePi: mockCreatePi{
				enable: true,
				req: &piPb.CreatePiRequest{
					Type:         piPb.PaymentInstrumentType_BANK_ACCOUNT,
					VerifiedName: "Others",
					Identifier: &piPb.CreatePiRequest_Account{
						Account: &piPb.Account{
							Name:                "Others",
							ActualAccountNumber: "*********",
							AccountType:         accounts.Type_CURRENT,
							SecureAccountNumber: mask.MaskLastNDigits("*********", 3, ""),
						},
					},
					Ownership: piPb.Ownership_EPIFI_TECH,
					Capabilities: map[string]bool{
						piPb.Capability_INBOUND_TXN.String():  true,
						piPb.Capability_OUTBOUND_TXN.String(): true,
					},
				},
				want: &piPb.CreatePiResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Id:           "pi-id",
						VerifiedName: "Others",
					},
				},
			},
		},
		{
			name: "Transient Failure while fetching the remitter details (resource exhausted)",
			args: args{
				ctx: context.Background(),
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol: paymentPb.PaymentProtocol_NEFT,
					Utr:      "S81234",
					PiIdentifier: &paymentNotificationPb.ParsedTxnParticulars_Account{
						Account: &paymentNotificationPb.ParsedTxnParticulars_AccountIdentifier{
							AccountNumber: "*********",
							PiType:        piPb.PaymentInstrumentType_BANK_ACCOUNT,
							AccountType:   accounts.Type_CURRENT,
						},
					},
				},
				txnDetails: &paymentNotificationPb.TransactionDetails{
					Remarks:               "txn-1",
					Id:                    "S81234",
					Timestamp:             transactionTimestamp1,
					AccountNumber:         "account-1",
					NotificationEventType: paymentNotificationPb.TransactionDetails_CREDIT,
				},
				actorId:   "random-actor-id",
				ownership: piPb.Ownership_EPIFI_TECH,
			},
			mockGetRemitterDetails: mockGetRemitterDetails{
				enable: true,
				req: &vgPaymentPb.GetRemitterDetailsRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					TxnDatetime: transactionTimestamp1,
					CbsTranId:   "S81234",
				},
				want: &vgPaymentPb.GetRemitterDetailsResponse{
					Status: rpc.StatusResourceExhausted(),
				},
			},
			wantErr: epifierrors.ErrTransient,
		},
		{
			name: "Transient Failure while fetching the remitter details (deadline exceeded)",
			args: args{
				ctx: context.Background(),
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol: paymentPb.PaymentProtocol_NEFT,
					Utr:      "S81234",
					PiIdentifier: &paymentNotificationPb.ParsedTxnParticulars_Account{
						Account: &paymentNotificationPb.ParsedTxnParticulars_AccountIdentifier{
							AccountNumber: "random",
							PiType:        piPb.PaymentInstrumentType_BANK_ACCOUNT,
							AccountType:   accounts.Type_CURRENT,
						},
					},
				},
				txnDetails: &paymentNotificationPb.TransactionDetails{
					Remarks:               "txn-1",
					Id:                    "S81234",
					Timestamp:             transactionTimestamp1,
					AccountNumber:         "account-1",
					NotificationEventType: paymentNotificationPb.TransactionDetails_CREDIT,
				},
				actorId:   "random-actor-id",
				ownership: piPb.Ownership_EPIFI_TECH,
			},
			mockGetRemitterDetails: mockGetRemitterDetails{
				enable: true,
				req: &vgPaymentPb.GetRemitterDetailsRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					TxnDatetime: transactionTimestamp1,
					CbsTranId:   "S81234",
				},
				want: &vgPaymentPb.GetRemitterDetailsResponse{
					Status: rpc.StatusDeadlineExceeded(),
				},
			},
			wantErr: epifierrors.ErrTransient,
		},
		{
			name: "Transient Failure while fetching the remitter details (unknown status received from the vendor)",
			args: args{
				ctx: context.Background(),
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol: paymentPb.PaymentProtocol_NEFT,
					Utr:      "S81234",
					PiIdentifier: &paymentNotificationPb.ParsedTxnParticulars_Account{
						Account: &paymentNotificationPb.ParsedTxnParticulars_AccountIdentifier{
							AccountNumber: "*********",
							PiType:        piPb.PaymentInstrumentType_BANK_ACCOUNT,
							AccountType:   accounts.Type_CURRENT,
						},
					},
				},
				txnDetails: &paymentNotificationPb.TransactionDetails{
					Remarks:               "txn-1",
					Id:                    "S81234",
					Timestamp:             transactionTimestamp1,
					AccountNumber:         "account-1",
					NotificationEventType: paymentNotificationPb.TransactionDetails_CREDIT,
				},
				actorId:   "random-actor-id",
				ownership: piPb.Ownership_EPIFI_TECH,
			},
			mockGetRemitterDetails: mockGetRemitterDetails{
				enable: true,
				req: &vgPaymentPb.GetRemitterDetailsRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					TxnDatetime: transactionTimestamp1,
					CbsTranId:   "S81234",
				},
				want: &vgPaymentPb.GetRemitterDetailsResponse{
					Status: rpc.StatusUnknown(),
				},
			},
			wantErr: epifierrors.ErrTransient,
		},
		{
			name: "Should resolve other actor PI and create merchant for off-app enach transaction",
			args: args{
				ctx: context.Background(),
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol: paymentPb.PaymentProtocol_ENACH,
					PiIdentifier: &paymentNotificationPb.ParsedTxnParticulars_Account{
						Account: &paymentNotificationPb.ParsedTxnParticulars_AccountIdentifier{
							AccountNumber: "**********",
							IfscCode:      "IFSC0000000",
							AccountType:   accounts.Type_SAVINGS,
							PiType:        piPb.PaymentInstrumentType_GENERIC,
						},
					},
					EcsEnacheMandateDetails: &paymentNotificationPb.EcsEnachMandateDetails{
						MandateCategory: paymentNotificationPb.EcsEnachMandateDetails_ENACHE,
						Umrn:            "*********",
					},
					OtherActorName: "Grow",
				},
				txnDetails: &paymentNotificationPb.TransactionDetails{
					Remarks:               "txn-1",
					Id:                    "S81234",
					Timestamp:             transactionTimestamp1,
					AccountNumber:         "account-1",
					NotificationEventType: paymentNotificationPb.TransactionDetails_DEBIT,
				},
				actorId:   "random-actor-id",
				ownership: piPb.Ownership_EPIFI_TECH,
			},
			wantPiId:           "other-actor-pi-id",
			wantOtherActorName: "Grow Services",
			mockMerchantResolution: mockMerchantResolution{
				enable: true,
				req: &merchantresolution.MerchantResolutionRequest{
					Header:          &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_IN_HOUSE},
					RawMerchantName: "Grow",
					IsMerchantTxn:   true,
					IsFiTxn:         true,
				},
				res: &merchantresolution.MerchantResolutionResponse{
					Status:       rpc.StatusOk(),
					MerchantName: "Grow Services",
				},
			},
			mockCreatePi: mockCreatePi{
				enable: true,
				req: &piPb.CreatePiRequest{
					Type:         piPb.PaymentInstrumentType_GENERIC,
					VerifiedName: "Grow Services",
					Identifier: &piPb.CreatePiRequest_Account{
						Account: &piPb.Account{
							ActualAccountNumber: crypto.GetSHA1InBase32("Grow Services"),
							AccountType:         accounts.Type_SAVINGS,
							Name:                "Grow Services",
							IfscCode:            "IFSC0000000",
							SecureAccountNumber: mask.MaskLastNDigits(crypto.GetSHA1InBase32("Grow Services"), 3, ""),
						},
					},
					Capabilities: map[string]bool{
						piPb.Capability_INBOUND_TXN.String():  false,
						piPb.Capability_OUTBOUND_TXN.String(): false,
					},
					Ownership: piPb.Ownership_EPIFI_TECH,
				},
				want: &piPb.CreatePiResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Id:           "other-actor-pi-id",
						VerifiedName: "Grow Services",
					},
				},
			},
			mockCreateMerchant: mockCreateMerchant{
				enable: true,
				req: &merchant.CreateMerchantRequest{
					PiId:      "other-actor-pi-id",
					LegalName: "Grow Services",
					BrandName: "Grow Services",
				},
				res: &merchant.CreateMerchantResponse{
					Status: rpc.StatusOk(),
				},
			},
		},
		{
			name: "failed to fetch the sanatized other actor name, transient failure ",
			args: args{
				ctx: context.Background(),
				parsedParticulars: &paymentNotificationPb.ParsedTxnParticulars{
					Protocol: paymentPb.PaymentProtocol_ENACH,
					PiIdentifier: &paymentNotificationPb.ParsedTxnParticulars_Account{
						Account: &paymentNotificationPb.ParsedTxnParticulars_AccountIdentifier{
							AccountNumber: "**********",
							IfscCode:      "IFSC0000000",
							AccountType:   accounts.Type_SAVINGS,
							PiType:        piPb.PaymentInstrumentType_GENERIC,
						},
					},
					EcsEnacheMandateDetails: &paymentNotificationPb.EcsEnachMandateDetails{
						MandateCategory: paymentNotificationPb.EcsEnachMandateDetails_ENACHE,
						Umrn:            "*********",
					},
					OtherActorName: "Grow",
				},
				txnDetails: &paymentNotificationPb.TransactionDetails{
					Remarks:               "txn-1",
					Id:                    "S81234",
					Timestamp:             transactionTimestamp1,
					AccountNumber:         "account-1",
					NotificationEventType: paymentNotificationPb.TransactionDetails_DEBIT,
				},
				actorId:   "random-actor-id",
				ownership: piPb.Ownership_EPIFI_TECH,
			},
			wantErr: epifierrors.ErrTransient,
			mockMerchantResolution: mockMerchantResolution{
				enable: true,
				req: &merchantresolution.MerchantResolutionRequest{
					Header:          &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_IN_HOUSE},
					RawMerchantName: "Grow",
					IsMerchantTxn:   true,
					IsFiTxn:         true,
				},
				res: &merchantresolution.MerchantResolutionResponse{
					Status: rpc.StatusInternal(),
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockCreatePi.enable {
				mockPiClient.EXPECT().CreatePi(tt.args.ctx, tt.mockCreatePi.req).Return(tt.mockCreatePi.want, tt.mockCreatePi.err)
			}
			if tt.mockGetRemitterDetails.enable {
				mockPaymentVgClient.EXPECT().GetRemitterDetails(tt.args.ctx, tt.mockGetRemitterDetails.req).Return(tt.mockGetRemitterDetails.want, tt.mockGetRemitterDetails.err)
			}
			if tt.mockMerchantResolution.enable {
				mockMerchantResolutionClient.EXPECT().MerchantResolution(tt.args.ctx, tt.mockMerchantResolution.req).
					Return(tt.mockMerchantResolution.res, tt.mockMerchantResolution.err)
			}
			if tt.mockCreateMerchant.enable {
				mockMerchantClient.EXPECT().CreateMerchant(tt.args.ctx, tt.mockCreateMerchant.req).
					Return(tt.mockCreateMerchant.res, tt.mockCreateMerchant.err)
			}
			if tt.mockUpdatePi.enable {
				mockPiClient.EXPECT().UpdatePi(tt.args.ctx, tt.mockUpdatePi.req).Return(tt.mockUpdatePi.want, tt.mockUpdatePi.err)
			}
			piId, name, isMerchant, err := processor.ResolveOtherActorPiDetails(tt.args.ctx, tt.args.parsedParticulars, tt.args.txnDetails, tt.args.actorId, tt.args.ownership)

			if !errors.Is(err, tt.wantErr) {
				t.Errorf("ResolveOtherActorPiDetails got err: %v, want: %v", err, tt.wantErr)
				return
			}
			if err != nil {
				return
			}
			if piId != tt.wantPiId {
				t.Errorf("ResolveOtherActorPiDetails got piId: %s, want: %s", piId, tt.wantPiId)
			}
			if name != tt.wantOtherActorName {
				t.Errorf("ResolveOtherActorPiDetails got other actor name: %s, want: %s", name, tt.wantOtherActorName)
			}
			if isMerchant != tt.wantIsMerchantPi {
				t.Errorf("ResolveOtherActorPiDetails got isMerchant: %v, want: %v", isMerchant, tt.wantIsMerchantPi)
			}
		})

	}
}

func TestProcessor_UpdatePiVerifiedName(t *testing.T) {
	piId := "pi-id"
	verifiedRemitterName := "remitter-name"
	updatePiError := errors.New("Error while updating the verified name")
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockAccountPiRelationClient := accountPiMocks.NewMockAccountPIRelationClient(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)

	piProcessor := piProcessor.NewProcessor(mockAccountPiRelationClient, mockPiClient, nil, nil, nil, nil, nil, nil, nil, nil, nil)

	type mockUpdatePi struct {
		enable bool
		req    *piPb.UpdatePiRequest
		res    *piPb.UpdatePiResponse
		err    error
	}

	tests := []struct {
		name         string
		piId         string
		verifiedName string
		mockUpdatePi mockUpdatePi
		wantErr      bool
	}{
		{
			name:         "Updated the verified name successfully",
			piId:         piId,
			verifiedName: "remitter-name",
			mockUpdatePi: mockUpdatePi{
				enable: true,
				req: &piPb.UpdatePiRequest{
					PaymentInstrument: &piPb.PaymentInstrument{
						Id:           piId,
						VerifiedName: verifiedRemitterName,
					},
					UpdateReason:    "Updating verified name as remitter name",
					UpdateFieldMask: []piPb.PaymentInstrumentFieldMask{piPb.PaymentInstrumentFieldMask_VERIFIED_NAME},
				},
				res: &piPb.UpdatePiResponse{
					Status: rpc.StatusOk(),
				},
			},
		},
		{
			name:         "Error returned by UpdatePi rpc",
			piId:         piId,
			verifiedName: "remitter-name",
			mockUpdatePi: mockUpdatePi{
				enable: true,
				req: &piPb.UpdatePiRequest{
					PaymentInstrument: &piPb.PaymentInstrument{
						Id:           piId,
						VerifiedName: verifiedRemitterName,
					},
					UpdateReason:    "Updating verified name as remitter name",
					UpdateFieldMask: []piPb.PaymentInstrumentFieldMask{piPb.PaymentInstrumentFieldMask_VERIFIED_NAME},
				},
				err: updatePiError,
			},
			wantErr: true,
		},
		{
			name:         "UpdatPi rpc returns non-ok status",
			piId:         piId,
			verifiedName: "remitter-name",
			mockUpdatePi: mockUpdatePi{
				enable: true,
				req: &piPb.UpdatePiRequest{
					PaymentInstrument: &piPb.PaymentInstrument{
						Id:           piId,
						VerifiedName: verifiedRemitterName,
					},
					UpdateReason:    "Updating verified name as remitter name",
					UpdateFieldMask: []piPb.PaymentInstrumentFieldMask{piPb.PaymentInstrumentFieldMask_VERIFIED_NAME},
				},
				res: &piPb.UpdatePiResponse{
					Status: rpc.StatusInternal(),
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockUpdatePi.enable {
				mockPiClient.EXPECT().UpdatePi(context.Background(), tt.mockUpdatePi.req).Return(tt.mockUpdatePi.res, tt.mockUpdatePi.err)
			}
			err := piProcessor.UpdatePiVerifiedName(context.Background(), tt.piId, tt.verifiedName)
			if (err != nil) != tt.wantErr {
				t.Errorf("Got UpdatePiVerfiedName() Error : %v , want Error : %v", err, tt.wantErr)
			}
		})
	}
}
