OrderReceipt:
  # Different consolidated order stages that are to be displayed on the receipt
  ConsolidatedStages:
    PaymentSuccess:
      Description: "Successful"
      IsTimeStampEnabled: true
      TimeLayout: "Jan 2, 2006 at 03:04 PM"
      TimeStampStringFormat: "%s"
    PaymentInProgress:
      Description: "Pending"
      IsTimeStampEnabled: true
      TimeLayout: "Jan 2, 2006 at 03:04 PM"
      TimeStampStringFormat: "Last Updated At: %s"
    PaymentFailed:
      Description: "Failed"
      IsTimeStampEnabled: true
      TimeLayout: "Jan 2, 2006 at 03:04 PM"
      TimeStampStringFormat: "%s"
    CollectCancelled:
      Description: "Cancelled"
      IsTimeStampEnabled: true
      TimeLayout: "Jan 2, 2006 at 03:04 PM"
      TimeStampStringFormat: "%s"
    CollectDeclined:
      Description: "Declined"
      IsTimeStampEnabled: true
      TimeLayout: "Jan 2, 2006 at 03:04 PM"
      TimeStampStringFormat: "%s"
    PaymentInitiated:
      Description: "Initiated"
      IsTimeStampEnabled: false
    CollectExpired:
      Description: "Expired"
      IsTimeStampEnabled: true
      TimeLayout: "Jan 2, 2006 at 03:04 PM"
      TimeStampStringFormat: "%s"
    PaymentReversed:
      Description: "Reversed"
      IsTimeStampEnabled: true
      TimeLayout: "Jan 2, 2006 at 03:04 PM"
      TimeStampStringFormat: "%s"

  # Defines the stages of order for fund transfer to be displayed in the receipt
  # The sequence of order stages is same as defined here.
  # Any change in sequence will directly effect how the user sees the order receipts.
  # Please take a conscious call while changing the order
  FundTransferStages:
    DescriptionTimeLayout: "02 January, 2006"
    StageDescriptions:
      - Description: "Payment initiated"

      - Description: "Payment processed by partner bank"

      - Description: "Payment deposited at recipient's bank"

  FundTransferWithReversalStages:
    DescriptionTimeLayout: "02 January, 2006"
    StageDescriptions:
      - Description: "Payment initiated"

      - Description: "Payment processed by partner bank"

      - Description: "Payment reversed"
        SubDescription: "Refund has been processed on: %s"

  AddFundsStages:
    DescriptionTimeLayout: "02 January, 2006"
    StageDescriptions:
      - Description: "Payment initiated"

      - Description: "Payment received by partner bank"

      - Description: "Payment deposited in your account"

  AddFundsWithReversalStages:
    DescriptionTimeLayout: "02 January, 2006"
    StageDescriptions:
      - Description: "Payment initiated"

      - Description: "Payment received by partner bank"

      - Description: "Payment reversed"
        SubDescription: "Refund has been processed on: %s"

  ForexMarkupFeesStages:
    DescriptionTimeLayout: "02 January, 2006"
    StageDescriptions:
      - Description: "Forex fee charged"
      - Description: "Refund of forex fee initiated by partner bank"
      - Description: "Forex fee refunded"
        SubDescription: "Forex fee refunded on: %s"
  OrderReceiptDCAmcChargesInfoBanner:
    Title: "1% CASH BACK. NO EXTRA EFFORT!"
    SubTitle: "Your AMC just got rewarding. Click here to view more"
    BannerImage: "https://epifi-icons.pointz.in/card-images/1percent-gift-box.png"
    OnePercentBottomSheetInfo:
      Title : "Get 1% cashback* on every debit card spend"
      TopImage: "https://epifi-icons.pointz.in/card-images/1percent-gift-box-with-confetti.png"
      StepsImage: "https://epifi-icons.pointz.in/card-images/1percent-how-to-get-cashback.png"
      Subtitle : "While we will not be able to waive off your AMC, here’s a special offer to get your fees reduced."
      Note: "Note: If you have a physical Debit Card but don’t make payments, this charge still applies and cannot be waived"
      Tnc : "*T&C apply. Max cashback is ₹100/spend"
      DoKeepInMind : "DO KEEP IN MIND                                                                                      "
      DoKeepInMindPointers : '<span style="color:#6A6D70;">1. To avoid charges in the future, upgrade to the </span><b><span style="color:#00B899;">Salary</span></b> <span style="color:#6A6D70;">1or</span> <b><span style="color:#00B899;">Infinite Plan</span></b> <span style="color:#6A6D70;">1before the next card anniversary.</span></span><br/><span style="color:#6A6D70;">2. You can also upgrade to <b></span><span style="color:#00B899;">Plus Plan</span></b> <span style="color:#6A6D70;">1and spend more than ₹25k per year.</span><br/><span style="color:#6A6D70;">3. Card anniversary is annual. You can know your card anniversary by checking the <b>month of expiry</b> on your card.</span>'
      TermsAndConditions : "TERMS & CONDITIONS\n\n1. Offer is valid from till 2nd December 2024.\n2.Offer is valid till 90 days from your AMC Charging date\n3. To qualify for this offer, you can pay to any store or online with your Fi-Federal Debit Card\n4. You can earn a maximum of ₹100 under this offer\n5. Refunded or reversed payments will also not be considered for rewards\n6. Use your Fi-Federal Debit Card for the transaction. After a successful transaction, check the My Rewards section for your rewards\n7. By participating in the rewards program, users acknowledge and agree to the specific terms and conditions of the reward, in addition to the company’s overall terms and conditions\n8. Cashback will be credited to your Federal Savings Bank Account\n9. All decisions made by Fi, in consultation with its banking/lending partner (as applicable), regarding this reward—including reward calculation and transaction eligibility—are at the sole discretion of Fi and are final and binding\n10. Fi, along with its banking/lending partner (as applicable), reserves the right to change or discontinue the rewards at any time, with or without prior notice. Such changes will be final and binding, and neither Fi nor its banking/lending partner will be liable for any resulting loss or damage."

  # Defines the keys for different payment details that needs to be displayed in the receipt
  PaymentDetailKey:
    FromActorKey: "FROM"
    ToActorKey: "TO"
    ProtocolTransactionIdKey: "Transaction ID"
    FiOrderIdKey: "Fi Transaction ID"
    PaymentProtocolKey: "Transaction Mode"
    FromActorNameKey: "FROM"
    ToActorNameKey: "TO"
    TransactionRemark: "Transaction remark"
    InternationalPaymentMarkupKey: "MARKUP"
    InternationalPaymentConversionRateKey: "CONVERSION RATE"
    RemarksKey: "NOTES"

  DisputeDescription:
    Desc: "Dispute raised on %s"
    TimeLayout: "2 Jan, 03:04 PM"

  ReceiptHead:
    CreditTitle: "RECEIVED FROM"
    DebitTitle: "PAID TO"
    FixedDepositIconURL: "https://epifi-icons.pointz.in/deposit/fixed_deposit_png.png"
    SmartDepositIconURL: "https://epifi-icons.pointz.in/deposit/smart_deposit1_png.png"
    PayBGColourCode: "#DDFFE0"
    DepositBGColourCode: "#DDFFE0"

  # banner to be shown on the order receipt
  OrderReceiptProtocolLevelBanner:
    NeftTxnInfoBanner:
      InfoString: "This NEFT transaction can take upto 2 hours to get credited to the beneficiary account."
      # Time window for which OrderReceiptProtocolLevelBanner is displayed on screen when payment is success or failed.
      Window: "24h"
    ChequeTxnInfoBanner:
      InfoString: "Your balance will be updated within 24 hours. Please check the status then."
      # Time window for which OrderReceiptProtocolLevelBanner is displayed on screen when payment is success or failed.
      Window: "1h"

  #banner which gives user caution on txn receipt
  UserCautionInfo:
    ImageUrl: "https://epifi-icons.pointz.in/Txn_Receipt/icons/userCaution.png"
    ImageHeight: 20
    ImageWidth: 20
    Description:
      - DisplayValue: "Payments may take up to 3 working days to be reflected in your account. Check bank statements for the latest status of your transaction."
        FontColour: "#333333"
        FontStyle: "BUTTON_5"
    LeftImgTxtPadding: 12
    ContainerLeftPadding: 20
    ContainerRightPadding: 20
    ContainerTopPadding: 12
    ContainerBottomPadding: 12
    ContainerCornerRadius: 8
    ContainerBorderColour: "#E7E7E7"
    ContainerBgColour: "#FFFFFF"
    Deeplink:
      - Screen: ""

  #banner which gives user caution on txn receipt in case of atm cash withdrawal
  AtmWithdrawalUserCautionInfo:
    ImageUrl: "https://epifi-icons.pointz.in/Txn_Receipt/icons/userCaution.png"
    ImageHeight: 20
    ImageWidth: 20
    Description:
      - DisplayValue: "If you didn't get cash from the ATM, the deducted amount will be reversed in 5 working days. If not, just tap 'Get help' to raise a dispute. You'll receive updates about the dispute and when the money is credited."
        FontColour: "#333333"
        FontStyle: "BUTTON_5"
    LeftImgTxtPadding: 12
    ContainerLeftPadding: 20
    ContainerRightPadding: 20
    ContainerTopPadding: 12
    ContainerBottomPadding: 12
    ContainerCornerRadius: 8
    ContainerBorderColour: "#E7E7E7"
    ContainerBgColour: "#FFFFFF"
    Deeplink:
      - Screen: ""


  #banner which shows information related to Payment status on txn receipt
  PaymentStatusPendingBanner:
    InfoString: "Your payment has reached the receiver’s bank and will be transferred to them within 48 hours. If this fails for any reason, you will receive a refund."
    FontColour: "#555555"
    FontStyle: "BODY_XS"
    BgColour: "#EAD8A3"

  EcsEnachChargesBanner:
    BgColour: "#EAD8A3"
    Description:
      - DisplayValue: "Your ENACH/ECS transaction was declined. To avoid Return Charges, you need to maintain account balance OR cancel your ECS/NACH by clicking here"
        FontColour: "#555555"
        FontStyle: "BODY_XS"
    ContainerCornerRadius: 12
    ContainerLeftPadding: 20
    ContainerRightPadding: 20
    ContainerTopPadding: 12
    ContainerBottomPadding: 12
    ContainerBgColour: "#EAD8A3"

  OffAppEnachBanner:
    BgColour: "#EAD8A3"
    Description:
      - DisplayValue: "This recurring transaction was triggered as you have setup an ENACH mandate with the merchant. To manage all your active ENACH mandates, click here"
        FontColour: "#555555"
        FontStyle: "BODY_XS"
    ContainerCornerRadius: 12
    ContainerLeftPadding: 20
    ContainerRightPadding: 20
    ContainerTopPadding: 12
    ContainerBottomPadding: 12
    ContainerBgColour: "#EAD8A3"

  RemarksForEcsEnachCharges: "ECS/ENACH Return Charges"


  FaqInfo:
    ImageUrl: "https://epifi-icons.pointz.in/Txn_Receipt/icons/clipboard.png"
    ImageHeight: 20
    ImageWidth: 20
    Description:
      - DisplayValue: "FAQs"
        FontColour: "#00B899"
        FontStyle: "BUTTON_S"
    LeftImgTxtPadding: 4
    ContainerLeftPadding: 20
    ContainerRightPadding: 16
    ContainerTopPadding: 10
    ContainerBottomPadding: 10
    ContainerCornerRadius: 19
    ContainerBorderColour: "#E7E7E7"
    ContainerBgColour: "#FFFFFF"
    Deeplink:
      - Screen: "HELP_MAIN"

  GetHelpInfo:
    ImageUrl: "https://epifi-icons.pointz.in/Txn_Receipt/icons/annotation-warning.png"
    ImageHeight: 20
    ImageWidth: 20
    Description:
      - DisplayValue: "GET HELP"
        FontColour: "#00B899"
        FontStyle: "BUTTON_S"
    LeftImgTxtPadding: 4
    ContainerLeftPadding: 20
    ContainerRightPadding: 16
    ContainerTopPadding: 10
    ContainerBottomPadding: 10
    ContainerCornerRadius: 19
    ContainerBorderColour: "#E7E7E7"
    ContainerBgColour: "#FFFFFF"
    Deeplink:
      - Screen: "RAISE_DISPUTE"

  RaiseDisputeInfo:
    ImageUrl: "https://epifi-icons.pointz.in/Txn_Receipt/icons/annotation-warning.png"
    ImageHeight: 20
    ImageWidth: 20
    Description:
      - DisplayValue: "UPI HELP"
        FontColour: "#00B899"
        FontStyle: "BUTTON_S"
    LeftImgTxtPadding: 4
    ContainerLeftPadding: 20
    ContainerRightPadding: 16
    ContainerTopPadding: 10
    ContainerBottomPadding: 10
    ContainerCornerRadius: 19
    ContainerBorderColour: "#E7E7E7"
    ContainerBgColour: "#FFFFFF"
    Deeplink:
      - Screen: "RAISE_DISPUTE"

  ChatWithUsInfo:
    ImageUrl: "https://epifi-icons.pointz.in/Txn_Receipt/icons/message.png"
    ImageHeight: 20
    ImageWidth: 20
    Description:
      - DisplayValue: "Chat With Us"
        FontColour: "#00B899"
        FontStyle: "BUTTON_S"
    LeftImgTxtPadding: 4
    ContainerLeftPadding: 20
    ContainerRightPadding: 16
    ContainerTopPadding: 10
    ContainerBottomPadding: 10
    ContainerCornerRadius: 19
    ContainerBorderColour: "#E7E7E7"
    ContainerBgColour: "#FFFFFF"
    Deeplink:
      - Screen: "CHAT_WITH_US_SCREEN"

DepositCreationWorkflow:
  Fulfillment:
    RetryStrategy:
      Hybrid: # Exponential backoff is followed for first ~68 min post that regular interval is followed for next 7 days with interval of 2 hours.
        RetryStrategy1:
          ExponentialBackOff:
            BaseInterval: 3
            MaxAttempts: 12
            TimeUnit: "Second"
        RetryStrategy2:
          RegularInterval:
            Interval: 2
            MaxAttempts: 84
            TimeUnit: "Hour"
        MaxAttempts: 96
        CutOff: 12
    Method: "/deposit.Deposit/CreateOrClose"
    ServiceName: "deposit"
  InitialDelay: # initial delay for the first retry was added as request from federal
    Value: 0
    TimeUnit: "Second"
  IsAutoTriggered: true

UrnTransferWorkflow:
  Payment:
    RetryStrategy:
      ExponentialBackOff:
        BaseInterval: 1
        MaxAttempts: 9
        TimeUnit: "Second"
    Method: "/upi.UPI/GetOrCreateURNTransaction"
    ServiceName: "upi"
  InitialDelay:
    Value: 5
    TimeUnit: "Minute"
  IsAutoTriggered: true

PreCloseDepositWorkflow:
  Fulfillment:
    RetryStrategy:
      Hybrid: # Exponential backoff is followed for first ~68 min post that regular interval is followed for next 7 days with interval of 2 hours.
        RetryStrategy1:
          ExponentialBackOff:
            BaseInterval: 3
            MaxAttempts: 12
            TimeUnit: "Second"
        RetryStrategy2:
          RegularInterval:
            Interval: 2
            MaxAttempts: 84
            TimeUnit: "Hour"
        MaxAttempts: 96
        CutOff: 12
    Method: "/deposit.Deposit/CreateOrClose"
    ServiceName: "deposit"
  InitialDelay:
    Value: 0 # initial delay for the first retry was added as request from federal
    TimeUnit: "Second"
  IsAutoTriggered: true

P2PCollectWorkflow:
  CollectAmountLimit:
    CurrencyCode: "INR"
    Units: 2000
    Nanos: 0
  CollectExpirationDuration: "48h"

B2CFundTransferWorkflow:
  Payment:
    RetryStrategy:
      Hybrid: # Exponential backoff is followed for first ~68 min post that regular interval with jitter of 0.5 fraction is followed for next 5 days with interval of 2 hours.
        RetryStrategy1:
          ExponentialBackOff:
            BaseInterval: 2
            MaxAttempts: 12
            TimeUnit: "Second"
        RetryStrategy2:
          RegularIntervalWithJitter:
            Interval: 2
            MaxAttempts: 60
            Jitter: 0.5
            TimeUnit: "Hour"
        MaxAttempts: 72
        CutOff: 12
    Method: "/order.payment.Business/MakeB2CPayment"
    ServiceName: "order"
  IsAutoTriggered: true

RewardsCreateSdWorkflow:
  Payment:
    RetryStrategy:
      Hybrid: # Exponential backoff is followed for first ~68 min post that regular interval is followed for next 5 days with interval of 2 hours.
        RetryStrategy1:
          ExponentialBackOff:
            BaseInterval: 2
            MaxAttempts: 12
            TimeUnit: "Second"
        RetryStrategy2:
          RegularIntervalWithJitter:
            Interval: 2
            MaxAttempts: 60
            Jitter: 0.5
            TimeUnit: "Hour"
        MaxAttempts: 72
        CutOff: 12
    Method: "/order.payment.Business/RewardsCreateSDMakeB2CPaymentWrapper"
    ServiceName: "order"
  Fulfillment:
    RetryStrategy:
      Hybrid: # Exponential backoff is followed for first ~68 min post that regular interval is followed for next 7 days with interval of 2 hours.
        RetryStrategy1:
          ExponentialBackOff:
            BaseInterval: 2
            MaxAttempts: 12
            TimeUnit: "Second"
        RetryStrategy2:
          RegularInterval:
            Interval: 2
            MaxAttempts: 84
            TimeUnit: "Hour"
        MaxAttempts: 96
        CutOff: 12
    Method: "/deposit.Deposit/RewardsCreateSDCreateOrCloseWrapper"
    ServiceName: "deposit"
  IsAutoTriggered: true

RewardsAddFundsSdWorkflow:
  Payment:
    RetryStrategy:
      Hybrid: # Exponential backoff is followed for first ~68 min post that regular interval is followed for next 5 days with interval of 2 hours.
        RetryStrategy1:
          ExponentialBackOff:
            BaseInterval: 2
            MaxAttempts: 12
            TimeUnit: "Second"
        RetryStrategy2:
          RegularIntervalWithJitter:
            Interval: 2
            MaxAttempts: 60
            Jitter: 0.5
            TimeUnit: "Hour"
        MaxAttempts: 72
        CutOff: 12
    Method: "/order.payment.Business/RewardsAddFundsSDMakeB2CPaymentWrapper"
    ServiceName: "order"
  Fulfillment:
    RetryStrategy:
      Hybrid: # Exponential backoff is followed for first ~68 min post that regular interval is followed for next 7 days with interval of 2 hours.
        RetryStrategy1:
          ExponentialBackOff:
            BaseInterval: 2
            MaxAttempts: 12
            TimeUnit: "Second"
        RetryStrategy2: # TODO(kunal/harish): Discuss how long do we want to have retry after first 68 mins.
          RegularInterval:
            Interval: 2
            MaxAttempts: 84
            TimeUnit: "Hour"
        MaxAttempts: 96
        CutOff: 12
    Method: "/order.payment.Payment/RewardsSdAddFundsMakeDepositOwnFundTransferPayment"
    ServiceName: "order"
  IsAutoTriggered: true

AddFundsSdWorkflow:
  Payment:
    RetryStrategy:
      Hybrid: # Exponential backoff is followed for first ~68 min post that regular interval is followed for next 7 days with interval of 2 hours.
        RetryStrategy1:
          ExponentialBackOff:
            BaseInterval: 2
            MaxAttempts: 12
            TimeUnit: "Second"
        RetryStrategy2: # TODO(kunal/harish): Discuss how long do we want to have retry after first 68 mins.
          RegularInterval:
            Interval: 2
            MaxAttempts: 84
            TimeUnit: "Hour"
        MaxAttempts: 96
        CutOff: 12
    Method: "/order.payment.Payment/AddFundsSdMakeDepositOwnFundTransferPayment"
    ServiceName: "order"
  IsAutoTriggered: true

AddFundsWorkflow:
  Payment:
    RetryStrategy:
      Hybrid: # Exponential backoff is followed for first ~34 minutes post that regular interval is followed for next 2 days in interval of 1 hour
        RetryStrategy1:
          ExponentialBackOff:
            BaseInterval: 4
            MaxAttempts: 9
            TimeUnit: "Second"
        RetryStrategy2:
          RegularInterval:
            Interval: 1
            MaxAttempts: 48
            TimeUnit: "Hour"
        MaxAttempts: 57
        CutOff: 9
    Method: "/upi.UPI/GetOrCreateAddFundsTransaction"
    ServiceName: "upi"
  Settlement:
    RetryStrategy:
      Hybrid: # Exponential backoff is followed for first ~9 min post that regular interval is followed for next 3 days in interval of 1 hour
        RetryStrategy1:
          ExponentialBackOff:
            BaseInterval: 1
            MaxAttempts: 10
            TimeUnit: "Second"
        RetryStrategy2:
          RegularInterval:
            Interval: 1
            MaxAttempts: 72
            TimeUnit: "Hour"
        MaxAttempts: 82
        CutOff: 10
    Method: "/order.payment.Payment/ProcessAddFundTransaction"
    ServiceName: "order"
  IsAutoTriggered: true
  InitialDelay:
    Value: 20
    TimeUnit: "Second"

AddFundsCollectWorkflow:
  Collect:
    RetryStrategy: # regular intervals of 5 seconds for next 2 minute
      RegularInterval:
        Interval: 5
        MaxAttempts: 24
        TimeUnit: "Second"
    Method: "/order.payment.Payment/InitiateAddFundsCollect"
    ServiceName: "order"
  Payment:
    RetryStrategy:
      Hybrid: # Exponential backoff is followed for first ~18 min post that regular interval is followed for next 2 days in interval of 1 hour
        RetryStrategy1:
          ExponentialBackOff:
            BaseInterval: 2
            MaxAttempts: 10
            TimeUnit: "Second"
        RetryStrategy2:
          RegularInterval:
            Interval: 1
            MaxAttempts: 48
            TimeUnit: "Hour"
        MaxAttempts: 58
        CutOff: 10
    Method: "/upi.UPI/CheckAndUpdateAddFundsCollectPaymentStatus"
    ServiceName: "upi"
  Settlement:
    RetryStrategy:
      Hybrid: # Exponential backoff is followed for first ~9 min post that regular interval is followed for next 3 days in interval of 1 hour
        RetryStrategy1:
          ExponentialBackOff:
            BaseInterval: 1
            MaxAttempts: 10
            TimeUnit: "Second"
        RetryStrategy2:
          RegularInterval:
            Interval: 1
            MaxAttempts: 72
            TimeUnit: "Hour"
        MaxAttempts: 82
        CutOff: 10
    Method: "/order.payment.Payment/ProcessAddFundTransaction"
    ServiceName: "order"
  IsAutoTriggered: true
  InitialDelay:
    Value: 20
    TimeUnit: "Second"

P2PInvestmentWorkflow:
  Payment:
    RetryStrategy:
      Hybrid: # Exponential backoff is followed for first ~68 min post that regular interval is followed for next 5 days with interval of 2 hours.
        RetryStrategy1:
          ExponentialBackOff:
            BaseInterval: 2
            MaxAttempts: 12
            TimeUnit: "Second"
        RetryStrategy2:
          RegularInterval:
            Interval: 2
            MaxAttempts: 60
            TimeUnit: "Hour"
        MaxAttempts: 72
        CutOff: 12
    Method: "/p2pinvestment.P2pInvestment/PaymentWrapper"
    ServiceName: "p2pinvestment"
  Settlement:
    RetryStrategy:
      Hybrid: # Regular interval of 30 seconds for the first 10 minutes, post that regular interval is followed for next 20 days in interval of 2 hour
        RetryStrategy1:
          RegularInterval:
            Interval: 30
            MaxAttempts: 20
            TimeUnit: "Second"
        RetryStrategy2:
          RegularInterval:
            Interval: 2
            MaxAttempts: 240
            TimeUnit: "Hour"
        MaxAttempts: 260
        CutOff: 20
    Method: "/p2pinvestment.P2pInvestment/SettleInvestment"
    ServiceName: "p2pinvestment"
  IsAutoTriggered: false

P2PWithdrawalWorkflow:
  Fulfillment:
    RetryStrategy:
      Hybrid: # Regular interval of 30 seconds for the first 10 minutes, post that regular interval is followed for next 20 days with interval of 2 hours.
        RetryStrategy1:
          RegularInterval:
            Interval: 30
            MaxAttempts: 20
            TimeUnit: "Second"
        RetryStrategy2:
          RegularInterval:
            Interval: 2
            MaxAttempts: 240
            TimeUnit: "Hour"
        MaxAttempts: 260
        CutOff: 20
    Method: "/p2pinvestment.P2pInvestment/FulfillWithdrawal"
    ServiceName: "p2pinvestment"
  Payment:
    RetryStrategy:
      Hybrid: # Exponential backoff is followed for first ~68 min post that regular interval is followed for next 5 days with interval of 2 hours.
        RetryStrategy1:
          ExponentialBackOff:
            BaseInterval: 2
            MaxAttempts: 12
            TimeUnit: "Second"
        RetryStrategy2:
          RegularInterval:
            Interval: 2
            MaxAttempts: 60
            TimeUnit: "Hour"
        MaxAttempts: 72
        CutOff: 12
    Method: "/p2pinvestment.P2pInvestment/VerifyWithdrawal"
    ServiceName: "p2pinvestment"
  IsAutoTriggered: true

CollectVelocityLimit: 5

CollectVelocityWindow:
  Value: 24
  TimeUnit: "Hour"

CoolOffWindow:
  Value: 24
  TimeUnit: "Hour"

CoolOffAmountLimit:
  CurrencyCode: "INR"
  Units: 5000
  Nanos: 0

PaymentNotificationParams:
  HistoricPaymentSupportedTill: "2h"
  Credit:
    Title: "Money received"
    Body: "%s has sent you %s! Tap for details."
    IconAttr:
      IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"
  Debit:
    Title: "Money sent"
    Body: "Your transaction for %s has gone through! Tap to view."
    IconAttr:
      IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"
    TriggerAfter: "30s"
  ClosedDeposit:
    Disable: true
    Title: "%s closed"
    Body: "%s has been returned to your Federal Bank Savings Account on Fi."
    IconAttr:
      IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"
  MaturedDeposit:
    Title: "%s matured"
    Body: "%s has been deposited into your Federal Bank Savings Account on Fi."
    IconAttr:
      IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"
  CreditDeposit:
    Title: "Money sent to %s"
    Body: "We've transferred %s from your Savings Account into %s. Tap to view."
    IconAttr:
      IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"
    TriggerAfter: "10s"
  CreditUPI:
    Title: "Money received"
    Body: "%s has sent you %s! Tap for details"
    IconAttr:
      IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"
  DebitUPI:
    Title: "Money sent"
    Body: "You've made a successful transaction! Tap for details."
    IconAttr:
      IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"
    TriggerAfter: "12h"
  CollectReceived:
    Title: "Collect Request Received"
    Body: "%s has requested money from you on your Fi app. On approving the request, %s will be debited from your account"
    IconAttr:
      IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"
  CollectReceivedInApp:
    Body: "%s has sent a request for %s. Pay now."
    NotificationExpiry: "1h"
    NotificationType: "InApp"
    BgColour: "#CED2D6"
  DepositCreationFailed:
    Disable: true
    Title: "Failed to create %s"
    Body: "Sorry, couldn't create your deposit. Try again?"
    IconAttr:
      IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"
  DepositSdAddFundsFailed:
    Title: "Failed to add funds to %s"
    Body: "Sorry, we couldn't add funds into your Smart Deposit. Please, try again."
    IconAttr:
      IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"
  TransactionFailed:
    Title: "Transaction failed"
    Body: "Sorry, we couldn't send %s to %s. Tap to retry."
    IconAttr:
      IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"
    TriggerAfter: "30s"
  CollectRequestCancelled:
    NotificationType: "Background"
    NotificationExpiry: "1h"
  CreditInterest:
    Title: "Money received"
    Body: "Great News! You've earned %s as quarterly interest in your savings account %s"
    IconAttr:
      IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"
  TodChargesDebit:
    Title: "Temporary fee charge"
    Body: "Your account has been debited by %s. This amount is a temporary fee, and will be refunded to you in the next 48 hrs. Sorry about the inconvenience."
    IconAttr:
      IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"
  TransactionReversed:
    Title: "Payment reversed"
    Body: "Heads up! The %s transferred to %s is now back in your account."
    IconAttr:
      IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"
  EcsReturnCharges:
    Title: "🚨Return fee applied"
    Body: "Your auto-payment mandate (ECS/NACH) has failed. So,a Return fee of %s has been levied. To avoid this fee, cancel your ECS/NACH mandate here"
    IconAttr:
      IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"
  AtmDeclineFee:
    Title: "ATM Decline Fee deducted 🚧"
    Body: "We've deducted %s as an ATM Decline Fee. To avoid this, try to maintain sufficient balance."
    IconAttr:
      IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"
  DuplicateCardFee:
    Title: "Card replacement fee alert 🚨"
    Body: "Since this is your third card, we've deducted a %s replacement fee 💳"
    IconAttr:
      IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"
  AtmPenalty:
    Title: "ATM penalty amount sent ✅"
    Body: "As per RBI regulations, we've added %s to your account to make up for a delayed settlement."
    IconAttr:
      IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"
  IntATMcharges:
    Title: "International ATM fee deducted 🌎"
    Body: "As per our policy, there's a fee of %s for using your card abroad to withdraw cash."
    IconAttr:
      IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"
  OtherBankAtmTxnCharge:
    Title: "ATM Usage Fee 🏧"
    Body: "We've deducted an ATM Usage Fee of %s as you've exceeded 5 FREE txns at Other Bank ATMs."
    IconAttr:
      IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"
  EcsReturnChargesInApp:
    Title: "NACH Return fee 🚨"
    Body: "%s deducted from your a/c. To avoid such charges, maintain a/c balance for your scheduled auto-payments."
    IconAttr:
      IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    NotificationExpiry: "12h"
    NotificationType: "InApp"
  DcForexRefundTxn:
    Title: "Forex fee refund received 🎉"
    Body: "%s has been added to your a/c"
    NotificationType: "SystemTray"
  AddFundsSecondLegFailure:
    NotificationExpiry: "72h"
    Title: "Add Funds Failed – Refund in Process"
    Body: "%s payment failed. Refund will be issued within 2-3 days."
    NotificationType: "InApp"

PaymentSMSParams:
  SkipDebitCardDuplicateFees: true
  HistoricPaymentSupportedTill: "2h"
  Credit:
    Body: "Your a/c no. %s is credited Rs.%s on %s by %s (a/c no. %s). Thank you."
  Debit:
    Body: "Rs.%s has been debited from your a/c no %s on %s towards %s. To report fraud or raise dispute, call us on 080-********"
  CreditGenericPI:
    Body: "Your a/c no. %s is credited Rs.%s on %s by %s . Thank you."
  DebitGenericPI:
    Body: "Rs.%s has been debited from your a/c no %s on %s. To report fraud or raise dispute, call us on 080-********"
  CreditUPI:
    Body: "Your a/c no. %s is credited Rs.%s on %s by a/c linked to virtual address %s (UPI Ref %s). Thank you."
  DebitUPI:
    Body: "Rs.%s has been debited from your a/c no %s on %s towards %s. To report fraud or raise dispute, call us on 080-********"
  CollectReceived:
    Body: "%s has requested money from you on your Fi app. On approving the request, Rs %s will be debited from your account"
  TransactionFailed:
    Disable: true
    Body: "Your transaction has failed. Please retry."
    TriggerAfter: "5s"

IconUrls:
  FDIconUrl: "https://epifi-icons.pointz.in/deposit/fixed_deposit_png.png"
  SDIconUrl: "https://epifi-icons.pointz.in/deposit/smart_deposit1_png.png"
  ATMWithdrawalIconUrl: "https://epifi-icons.pointz.in/Images/debit_card/atm_withdrawal.png"
  FiBankIconUrl: "https://epifi-icons.pointz.in/fibank/icon/square-rounded.png"
  SuccessStatusIconUrl: "https://epifi-icons.pointz.in/Txn_Receipt/icons/Success.png"
  FailedStatusIconUrl: "https://epifi-icons.pointz.in/Txn_Receipt/icons/icons/Failed_status.png"
  PendingStatusIconUrl: "https://epifi-icons.pointz.in/Txn_Receipt/icons/Pending.png"

FundTransferWorkflow:
  LowValueTransactionUpperLimit:
    CurrencyCode: "INR"
    Units: 2000
    Nanos: 0

CollectShortCircuitWorkflow:
  LowValueTransactionUpperLimit:
    CurrencyCode: "INR"
    Units: 2000
    Nanos: 0
  CollectAmountLimit:
    CurrencyCode: "INR"
    Units: 2000
    Nanos: 0
  CollectExpirationDuration: "48h"

PaymentProtocolDecisionParams:
  INTRAParams:
    MinAmount: 500
    MaxAmount: 1000
    TotalTransactedAmountLimitInCoolDown: 10000
    ProfileUpdateCoolDownWindow:
      Value: 24
      TimeUnit: "Hour"
    ProtocolRestrictedWindow:
      IsEnabled: false
      Timezone: "Asia/Kolkata"
      DateTimeFormat: "2006-01-02:15:04:05"
      FromDateTime: "2021-04-18:00:00:00"
      ToDateTime: "2021-04-18:14:00:00"
    IntraCoolOffNotApplicablePis:
      - "paymentinstrument-creditcard-federal-pool-account-1"

  IMPSParams:
    MinAmount: 500
    MaxAmount: 3000
    TotalTransactedAmountLimitInCoolDown: 10000
    ProfileUpdateCoolDownWindow:
      Value: 24
      TimeUnit: "Hour"
    ProtocolRestrictedWindow:
      IsEnabled: false
      Timezone: "Asia/Kolkata"
      DateTimeFormat: "2006-01-02:15:04:05"
      FromDateTime: "2021-04-18:00:00:00"
      ToDateTime: "2021-04-18:14:00:00"
    LimitWindow:
      Value: 24
      TimeUnit: "Hour"
    TransactionCountLimit: 5

  NEFTParams:
    MinAmount: 3000
    MaxAmount: 200000
    TotalTransactedAmountLimitInCoolDown: 10000
    ProfileUpdateCoolDownWindow:
      Value: 24
      TimeUnit: "Hour"
    ProtocolRestrictedWindow:
      IsEnabled: false
      Timezone: "Asia/Kolkata"
      DateTimeFormat: "2006-01-02:15:04:05"
      FromDateTime: "2021-04-18:00:00:00"
      ToDateTime: "2021-04-18:14:00:00"
    ProtocolDailyDowntime:
      IsEnabled: true
      StartTime: "23:15"
      EndTime: "01:10"

  RTGSParams:
    MinAmount: 200000
    # TODO(raunak): To handle infinity amounts gracefully - maybe -1 ??
    MaxAmount: 1000000000
    TotalTransactedAmountLimitInCoolDown: 10000
    ProfileUpdateCoolDownWindow:
      Value: 24
      TimeUnit: "Hour"
    ProtocolRestrictedWindow:
      IsEnabled: false
      Timezone: "Asia/Kolkata"
      DateTimeFormat: "2006-01-02:15:04:05"
      FromDateTime: "2021-04-18:00:00:00"
      ToDateTime: "2021-04-18:14:00:00"

  UPIParams:
    UpiMinAmount: 1
    UpiMaxAmount: 500
    UpiTotalTransactedAmountLimit: 100000
    UpiTransactionCountLimit: 10
    UpiNewUserTotalTransactedAmountLimit: 5000
    UpiCoolDownPeriodTotalTransactedAmountLimit: 10000
    UpiPinResetTotalTransactedAmountLimit: 5000
    UpiLimitWindow:
      Value: 24
      TimeUnit: "Hour"
    UpiPinResetLimitWindow:
      Value: 12
      TimeUnit: "Hour"
    UpiProfileUpdateCoolDownWindow:
      ANDROID:
        Value: 24
        TimeUnit: "Hour"
      IOS:
        Value: 72
        TimeUnit: "Hour"
    UpiProfileUpdateCoolDownTriggerDurationLimit:
      Value: 24
      TimeUnit: "Hour"
    UpiProfileUpdateAfuSummariesFetchDuration:
      Value: 24
      TimeUnit: "Hour"
    UPIRestrictedWindow:
      IsEnabled: false
      Timezone: "Asia/Kolkata"
      DateTimeFormat: "2006-01-02:15:04:05"
      FromDateTime: "2021-04-18:00:00:00"
      ToDateTime: "2021-04-18:14:00:00"

  UpiPreferredPaymentProtocolAmountParams:
    Enable: true
    MinAmount: 1
    MaxAmount: 10000

  UserPaymentParams:
    TotalTransactionAmountLimit:
      AmountLimit: 1000000
      TimeWindow:
        Value: 24
        TimeUnit: "Hour"
    NewAccountTransactionAmountLimit:
      AmountLimit: 500000
      AccountAgeLimitThreshold: "2160h" # 90 days

  BeneficiaryCoolDownAmountLimit: 100000

  # Specific MCC or payment initiation mode or purpose code for which UPI txn limit is different than normal
  # Circular: https://www.npci.org.in/PDF/npci/upi/circular/2020/UPI%20OC%2082%20-%20Implementation%20of%20Rs%20%202%20Lakh%20limit%20per%20transaction%20for%20specific%20categories%20in%20UPI.pdf
  UPILimitExceptions:
    MCC:
      "6211":
        IsLimitOnlyForVerifiedMerchant: true
        Limits:
          MaxAmount: 1000
          MinAmount: 1
      "7322":
        IsLimitOnlyForVerifiedMerchant: true
        Limits:
          MaxAmount: 1000
          MinAmount: 1
      "5960":
        IsLimitOnlyForVerifiedMerchant: true
        Limits:
          MaxAmount: 1000
          MinAmount: 1
      "6300":
        IsLimitOnlyForVerifiedMerchant: true
        Limits:
          MaxAmount: 1000
          MinAmount: 1
      "6529":
        IsLimitOnlyForVerifiedMerchant: true
        Limits:
          MaxAmount: 1000
          MinAmount: 1
      "6540":
        Limits:
          MaxAmount: 5000
          MinAmount: 1
      "4812":
        Limits:
          MaxAmount: 5000
          MinAmount: 1
      "4814":
        Limits:
          MaxAmount: 5000
          MinAmount: 1
    P2PInitiationMode:
      "04":
        Limits:
          MaxAmount: 0
          MinAmount: 0
      "05":
        Limits:
          MaxAmount: 0
          MinAmount: 0
    P2MInitiationMode:
      "12":
        IsLimitOnlyForVerifiedMerchant: true
        Limits:
          MaxAmount: 1000
          MinAmount: 1
    PurposeCode:
      "15":
        IsLimitOnlyForVerifiedMerchant: true
        Limits:
          MaxAmount: 1000
          MinAmount: 1
      # defaultPurposeUpiLiteEnablement
      "41":
        IsLimitOnlyForVerifiedMerchant: false
        Limits:
          MaxAmount: 2000
          MinAmount: 1
      # defaultPurposeUpiLiteTopUp
      "42":
        IsLimitOnlyForVerifiedMerchant: false
        Limits:
          MaxAmount: 2000
          MinAmount: 1
      # defaultPurposeUpiLiteDisablement
      "43":
        IsLimitOnlyForVerifiedMerchant: false
        Limits:
          MaxAmount: 2000
          MinAmount: 1
      # upi lite payment
      "44":
        IsLimitOnlyForVerifiedMerchant: false
        Limits:
          MaxAmount: 500
          MinAmount: 1

  TotalTransactedAmountLimitInCoolDown: 10000
  ProfileUpdateCoolDownWindow:
    Value: 24
    TimeUnit: "Hour"

#csv file path
ImpsIfscCodesCsv: "./mappingCsv/impsIfscCodes.csv"
RtgsBlockListIfscCodesCsv: "./mappingCsv/rtgsBlockListIfscCodes.csv"

#json file path
PayFundTransferStatusCodeJson: "./mappingJson/fundTransferStatusCodes.json"
PayUpiStatusCodeJson: "./mappingJson/upiStatusCodes.json"
CardTxnStatusCodeJson: "./mappingJson/cardTxnResponseCodes.json"
EnachTransactionStatusCodeJson: "./mappingJson/enachTransactionStatusCodes.json"

Flags:
  TrimDebugMessageFromStatus: true
  SkipSmsForUpiDebitTransactions: true
  EnableBalanceV1Evaluation: true

RudderStack:
  Host: "https://rudder.pointz.in"
  Key: "1eGkhVch5jk2oJBF9lrTEN4I3zB"
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

# Urn payment limits. Here -1 denotes there is not upper limit on the txn amount
URNPaymentLimits:
  # QR code limits for P2P payments
  P2PQRCodeLimit:
    InsecureQRCodeLimit:
      DynamicQRLimit:
        CurrencyCode: "INR"
        Units: -1
        Nanos: 0
      StaticQRLimit:
        CurrencyCode: "INR"
        Units: -1
        Nanos: 0
    SecureQRCodeLimit:
      DynamicQRLimit:
        CurrencyCode: "INR"
        Units: -1
        Nanos: 0
      StaticQRLimit:
        CurrencyCode: "INR"
        Units: -1
        Nanos: 0
  # QR code limit for P2M payments for online merchant
  P2MOnlineQRCodeLimit:
    InsecureQRCodeLimit:
      DynamicQRLimit:
        CurrencyCode: "INR"
        Units: 2000
        Nanos: 0
      StaticQRLimit:
        CurrencyCode: "INR"
        Units: -1
        Nanos: 0
    SecureQRCodeLimit:
      DynamicQRLimit:
        CurrencyCode: "INR"
        Units: -1
        Nanos: 0
      StaticQRLimit:
        CurrencyCode: "INR"
        Units: -1
        Nanos: 0
  # QR code limit for P2M payments for offline merchant
  P2MOfflineQRCodeLimit:
    InsecureQRCodeLimit:
      DynamicQRLimit:
        CurrencyCode: "INR"
        Units: 2000
        Nanos: 0
      StaticQRLimit:
        CurrencyCode: "INR"
        Units: -1
        Nanos: 0
    SecureQRCodeLimit:
      DynamicQRLimit:
        CurrencyCode: "INR"
        Units: -1
        Nanos: 0
      StaticQRLimit:
        CurrencyCode: "INR"
        Units: -1
        Nanos: 0
  # limit for p2p qr share and pay
  P2PQRShareAndPayLimit:
    InsecureQRCodeLimit:
      DynamicQRLimit:
        CurrencyCode: "INR"
        Units: 2000
        Nanos: 0
      StaticQRLimit:
        CurrencyCode: "INR"
        Units: 2000
        Nanos: 0
    SecureQRCodeLimit:
      DynamicQRLimit:
        CurrencyCode: "INR"
        Units: 2000
        Nanos: 0
      StaticQRLimit:
        CurrencyCode: "INR"
        Units: 2000
        Nanos: 0
  # limit for p2m qr share and pay for online merchant
  P2MOnlineQRShareAndPayLimit:
    InsecureQRCodeLimit:
      VerifiedMerchant:
        DynamicQRLimit:
          CurrencyCode: "INR"
          Units: 2000
          Nanos: 0
        StaticQRLimit:
          CurrencyCode: "INR"
          Units: -1
          Nanos: 0
      NonVerifiedMerchant:
        DynamicQRLimit:
          CurrencyCode: "INR"
          Units: 2000
          Nanos: 0
        StaticQRLimit:
          CurrencyCode: "INR"
          Units: -1
          Nanos: 0
    SecureQRCodeLimit:
      VerifiedMerchant:
        DynamicQRLimit:
          CurrencyCode: "INR"
          Units: -1
          Nanos: 0
        StaticQRLimit:
          CurrencyCode: "INR"
          Units: -1
          Nanos: 0
      NonVerifiedMerchant:
        DynamicQRLimit:
          CurrencyCode: "INR"
          Units: -1
          Nanos: 0
        StaticQRLimit:
          CurrencyCode: "INR"
          Units: -1
          Nanos: 0
  # limit for p2m qr code for offline merchant
  P2MOfflineQRShareAndPayLimit:
    InsecureQRCodeLimit:
      VerifiedMerchant:
        DynamicQRLimit:
          CurrencyCode: "INR"
          Units: 2000
          Nanos: 0
        StaticQRLimit:
          CurrencyCode: "INR"
          Units: -1
          Nanos: 0
      NonVerifiedMerchant:
        DynamicQRLimit:
          CurrencyCode: "INR"
          Units: 2000
          Nanos: 0
        StaticQRLimit:
          CurrencyCode: "INR"
          Units: 2000
          Nanos: 0
    SecureQRCodeLimit:
      VerifiedMerchant:
        DynamicQRLimit:
          CurrencyCode: "INR"
          Units: -1
          Nanos: 0
        StaticQRLimit:
          CurrencyCode: "INR"
          Units: -1
          Nanos: 0
      NonVerifiedMerchant:
        DynamicQRLimit:
          CurrencyCode: "INR"
          Units: 2000
          Nanos: 0
        StaticQRLimit:
          CurrencyCode: "INR"
          Units: 2000
          Nanos: 0
  # P2P intent payment limits
  P2PIntentLimit:
    InsecureIntent:
      CurrencyCode: "INR"
      Units: 2000
      Nanos: 0
    SecureIntent:
      CurrencyCode: "INR"
      Units: 2000
      Nanos: 0
  # P2M intent payment limits for online merchant
  P2MOnlineIntentLimit:
    InsecureIntent:
      CurrencyCode: "INR"
      Units: 2000
      Nanos: 0
    SecureIntent:
      CurrencyCode: "INR"
      Units: -1
      Nanos: 0
  # P2M intent payment limits for offline merchant
  P2MOfflineIntentLimit:
    InsecureIntent:
      CurrencyCode: "INR"
      Units: 2000
      Nanos: 0
    SecureIntent:
      CurrencyCode: "INR"
      Units: -1
      Nanos: 0

PersonMCCCode: "0000"

OrderEventAmountCategories:
  - Name: "category_1"
    MinAmount: 1
    MaxAmount: 2000

  - Name: "category_2"
    MinAmount: 2001
    MaxAmount: 10000

  - Name: "category_3"
    MinAmount: 10001
    MaxAmount: 50000

  - Name: "category_4"
    MinAmount: 50001
    MaxAmount: 100000

RuleEngineParams:
  SalienceINTRA: 14
  SalienceIMPS: 9
  SalienceUPI: 8
  SalienceRTGS: 7
  SalienceNEFT: 6
  BumpedSalience: 20
  SecondHighestBumpedSalience: 13

AddFundsAlertParams:
  StartAfter: "7m"
  EndAfter: "15m"
  DistributedLockKeyPrefix: "order:addfunds:kibana:alert:"
  DistributedLockDuration: "96h"

# Time should be in HH:MM 24-hour format
# It is quick fix for CSIS time and will be removed when CSIS service is up.
CSIS:
  IsCsisEnable: false
  StartTime: "01:30"
  EndTime: "03:30"

# Time should be in HH:MM 24-hour format
# It is quick fix for BankDownTime and will be removed when CSIS service is up.
BankDownTime:
  IsBankDownTimeEnable: false
  StartTime: "20:45"
  EndTime: "12:00"

PartnerBankEODDelayMap:
  FEDERAL_BANK: "6h"

AddFundsAlertMailingParams:
  IsEnabled: true
  TimestampFormat: "January 2, 2006 15:04:05"
  FromAddress: "<EMAIL>"
  ToAddress: "<EMAIL>"
  FromName: "Epifi"
  ToName: "Federal"

SMSTypeToOptionVersionMap:
  GenericPiCredit:
    Default: "V2"
  GenericPiDebit:
    Default: "V2"
  UpiDebit:
    Default: "V2"
  UpiCredit:
    Default: "V2"
  GenericPiDebitUnclearBeneficiaryDetails:
    Default: "V1"
  CashWithdrawalAtmFallback:
    Default: "V1"
  PosDebitFallback:
    Default: "V1"
  NeftDebitFallback:
    Default: "V1"
  NeftCreditFallback:
    Default: "V1"
  RtgsDebitFallback:
    Default: "V1"
  InterestPaidInSbFallback:
    Default: "V1"
  TodChargesDebit:
    Default: "V1"
  TransactionReversed:
    Default: "V1"
  EcsReturnCharges:
    Default: "V1"
  AtmDeclineFee:
    Default: "V1"
  DuplicateCardFee:
    Default: "V1"
  AtmPenalty:
    Default: "V1"
  IntATMcharges:
    Default: "V1"
  OtherBankAtmTxnCharge:
    Default: "V1"

DynamicVPAParamsMap:
  FEDERAL_BANK:
    AccountNumber: "**************"
    Name: "Fi - Federal Bank"
    Ifsc: "FDRL0005555"
    AccountReferenceNumber: "DPyyFKvsMTvhOXvq5sL+5JZbm38ofcW692RcDTwor2w="
    DynamicVPAString: "addfi.%s@fede"
    MerchantDetails:
      MerchantID: "MerchantID"
      MerchantStoreID: "MerchantStoreID"
      MerchantTerminalId: "MerchantTerminalId"
      Genre: 1
      BrandName: "Fi - Federal Bank"
      LegalName: "Fi - Federal Bank"
      FranchiseName: "Fi - Federal Bank"
      OwnerShipType: 4
      OnboardingType: 1
      MerchantType: 2
      SubCode: "5399"
      Mcc: "5399"

DynamicVPAV1ParamsMap:
  FEDERAL_BANK:
    AccountNumber: "**************"
    Name: "Fi - Federal Bank"
    Ifsc: "FDRL0005555"
    AccountReferenceNumber: "DPyyFKvsMTvhOXvq5sL+5JZbm38ofcW692RcDTwor2w="
    DynamicVPAString: "addfi.5555@fifederal"
    MerchantDetails:
      MerchantID: "MerchantID"
      MerchantStoreID: "MerchantStoreID"
      MerchantTerminalId: "MerchantTerminalId"
      Genre: 1
      BrandName: "Fi - Federal Bank"
      LegalName: "Fi - Federal Bank"
      FranchiseName: "Fi - Federal Bank"
      OwnerShipType: 4
      OnboardingType: 1
      MerchantType: 2
      SubCode: "7409"
      Mcc: "7409"

Logging:
  EnableLoggingToFile: true
  LogPath: "/var/log/order/info.log"
  MaxSizeInMBs: 1024
  MaxBackups: 1

CampaignParams:
  Add_Funds:
    Disable: false
    CampaignNotifications:
      - TriggerAfter: "1h"
        Title: "Congrats! Your Federal Bank Savings Account on Fi is ready ✅"
        Body: "Add ₹5000 or more into your account & earn up to ₹250 in a Smart deposit."
        NotificationType: "SystemTray"
        NotificationExpiry: "1h"
        ValidFrom: 11
        ValidTill: 21
        Disable: false

      - TriggerAfter: "20h"
        Title: "Add funds into your Fi account & win big 🎁"
        Body: "Earn up to ₹250 in a Smart Deposit by transferring ₹5000 or more into your account now!"
        NotificationType: "SystemTray"
        NotificationExpiry: "1h"
        ValidFrom: 11
        ValidTill: 21
        Disable: false

      - TriggerAfter: "48h"
        Title: "It pays to save  (No, really!)"
        Body: "Start saving with Fi by adding ₹5,000 into your account & earn up to ₹250. As you maximise your savings, we amplify your rewards."
        NotificationType: "SystemTray"
        NotificationExpiry: "1h"
        ValidFrom: 11
        ValidTill: 21
        Disable: false

RecurringPaymentCreationWorkflow:
  Fulfillment:
    RetryStrategy:
      Hybrid: # Exponential backoff is followed for first ~68 min post that regular interval is followed for next 7 days with interval of 2 hours.
        RetryStrategy1:
          ExponentialBackOff:
            BaseInterval: 3
            MaxAttempts: 12
            TimeUnit: "Second"
        RetryStrategy2:
          RegularInterval:
            Interval: 2
            MaxAttempts: 84
            TimeUnit: "Hour"
        MaxAttempts: 96
        CutOff: 12
    Method: "/recurringpayment.RecurringPaymentService/ProcessRecurringPaymentCreation"
    ServiceName: "order"
  IsAutoTriggered: false

RecurringPaymentExecutionWorkflow:
  Payment:
    RetryStrategy:
      Hybrid: # Exponential backoff is followed for first ~68 min post that regular interval is followed for next 5 days with interval of 2 hours.
        RetryStrategy1:
          ExponentialBackOff:
            BaseInterval: 3
            MaxAttempts: 12
            TimeUnit: "Second"
        RetryStrategy2:
          RegularInterval:
            Interval: 2
            MaxAttempts: 60
            TimeUnit: "Hour"
        MaxAttempts: 72
        CutOff: 12
    Method: "/recurringpayment.RecurringPaymentService/ProcessRecurringPaymentExecution"
    ServiceName: "order"
  IsAutoTriggered: true

RecurringPaymentModifyWorkflow:
  Fulfillment:
    RetryStrategy:
      Hybrid: # Exponential backoff is followed for first ~68 min post that regular interval is followed for next 7 days with interval of 2 hours.
        RetryStrategy1:
          ExponentialBackOff:
            BaseInterval: 3
            MaxAttempts: 12
            TimeUnit: "Second"
        RetryStrategy2:
          RegularInterval:
            Interval: 2
            MaxAttempts: 84
            TimeUnit: "Hour"
        MaxAttempts: 96
        CutOff: 12
    Method: "/recurringpayment.RecurringPaymentService/ProcessRecurringPaymentModify"
    ServiceName: "recurringpayment"
  IsAutoTriggered: false

RecurringPaymentRevokeWorkflow:
  Fulfillment:
    RetryStrategy:
      Hybrid: # Exponential backoff is followed for first ~68 min post that regular interval is followed for next 7 days with interval of 2 hours.
        RetryStrategy1:
          ExponentialBackOff:
            BaseInterval: 3
            MaxAttempts: 12
            TimeUnit: "Second"
        RetryStrategy2:
          RegularInterval:
            Interval: 2
            MaxAttempts: 84
            TimeUnit: "Hour"
        MaxAttempts: 96
        CutOff: 12
    Method: "/recurringpayment.RecurringPaymentService/ProcessRecurringPaymentRevoke"
    ServiceName: "recurringpayment"
  IsAutoTriggered: false

MutualFundsInvestmentPostPaid:
  Fulfillment:
    RetryStrategy:
      Hybrid: # Exponential backoff is followed for first ~68 min post that regular interval is followed for next 12 hours with interval of 20 mins.
        RetryStrategy1:
          ExponentialBackOff:
            BaseInterval: 2
            MaxAttempts: 12
            TimeUnit: "Second"
        RetryStrategy2:
          RegularInterval:
            Interval: 20
            MaxAttempts: 36
            TimeUnit: "Minute"
        MaxAttempts: 48
        CutOff: 12
    Method: "/api.investment.mutualfund.order.OrderManager/SendOrderToVendor"
    ServiceName: "investment"
  Payment:
    RetryStrategy:
      Hybrid: # Exponential backoff is followed for first ~68 minutes post that regular interval is followed for next 36 hours with interval of 30 mins.
        RetryStrategy1:
          ExponentialBackOff:
            BaseInterval: 2
            MaxAttempts: 12
            TimeUnit: "Second"
        RetryStrategy2:
          RegularInterval:
            Interval: 30
            MaxAttempts: 72
            TimeUnit: "Minute"
        MaxAttempts: 84
        CutOff: 12
    Method: "/api.investment.mutualfund.payment_handler.PaymentHandler/ExecutePayment"
    ServiceName: "investment"
  IsAutoTriggered: true

MutualFundsRedemption:
  Fulfillment:
    RetryStrategy:
      Hybrid: # Exponential backoff is followed for first ~68 min post that regular interval is followed for next 12 hours with interval of 20 mins.
        RetryStrategy1:
          ExponentialBackOff:
            BaseInterval: 2
            MaxAttempts: 12
            TimeUnit: "Second"
        RetryStrategy2:
          RegularInterval:
            Interval: 20
            MaxAttempts: 36
            TimeUnit: "Minute"
        MaxAttempts: 48
        CutOff: 12
    Method: "/api.investment.mutualfund.order.OrderManager/SendOrderToVendor"
    ServiceName: "investment"
  IsAutoTriggered: true

ConnectedAccountUserGroupParams:
  IsAARestricted: false

PaymentEnquiryParams:
  NotFoundMaxRetryDurationVendorMap:
    "FEDERAL_BANK":
      IntraBank: "3m"
      UPI: "5m"
      NEFT: "3m"
      RTGS: "3m"
      IMPS: "5m"
  InProgressToSuccessMap: # time duration and error codes for in progress response status will be move to success
    "FEDERAL_BANK":
      FiErrorCodes:
        - "FI304"
      PaymentProtocolToDurationMap:
        "NEFT": "120m"
        "RTGS": "120m"
      PaymentProtocolToDeemedEnquiryDurationMap:
        UPI:
          P2P: 6m
          P2M: 6m
        IMPS:
          P2P: 6m
          P2M: 6m
      OffAppPaymentProtocolToDeadlineExceededDuration:
        UPI: 5m
      OffAppPaymentProtocolToNotFoundDuration:
        UPI: 5m
  PaymentProcessingSLAVendorMap:
    "FEDERAL_BANK":
      IntraProcessingTime: "20s"
      RtgsProcessingTime: "10s"
      ImpsProcessingTime: "30s"
      UpiProcessingTime: "30s"
      NeftProcessingTime: "30m"

ExecuteRecurringPaymentWithAuthWorkflow:
  Payment:
    RetryStrategy:
      Hybrid: # Exponential backoff is followed for first ~68 min post that regular interval is followed for next 5 days with interval of 2 hours.
        RetryStrategy1:
          ExponentialBackOff:
            BaseInterval: 3
            MaxAttempts: 12
            TimeUnit: "Second"
        RetryStrategy2:
          RegularInterval:
            Interval: 2
            MaxAttempts: 60
            TimeUnit: "Hour"
        MaxAttempts: 72
        CutOff: 12
    Method: "/recurringpayment.RecurringPaymentService/ProcessRecurringPaymentExecution"
    ServiceName: "order"
  IsAutoTriggered: false

ExecuteRecurringPaymentWithNoAuthWorkflow:
  Payment:
    RetryStrategy:
      Hybrid: # Exponential backoff is followed for first ~68 min post that regular interval is followed for next 5 days with interval of 2 hours.
        RetryStrategy1:
          ExponentialBackOff:
            BaseInterval: 3
            MaxAttempts: 12
            TimeUnit: "Second"
        RetryStrategy2:
          RegularInterval:
            Interval: 2
            MaxAttempts: 60
            TimeUnit: "Hour"
        MaxAttempts: 72
        CutOff: 12
    Method: "/recurringpayment.RecurringPaymentService/ProcessRecurringPaymentExecution"
    ServiceName: "order"
  IsAutoTriggered: false

RecurringPaymentPauseUnpauseWorkflow:
  Fulfillment:
    RetryStrategy:
      Hybrid: # Exponential backoff is followed for first ~68 min post that regular interval is followed for next 7 days with interval of 2 hours.
        RetryStrategy1:
          ExponentialBackOff:
            BaseInterval: 3
            MaxAttempts: 12
            TimeUnit: "Second"
        RetryStrategy2:
          RegularInterval:
            Interval: 2
            MaxAttempts: 84
            TimeUnit: "Hour"
        MaxAttempts: 96
        CutOff: 12
    Method: "/recurringpayment.RecurringPaymentService/ProcessPauseUnpause"
    ServiceName: "recurringpayment"
  IsAutoTriggered: false

AaParams:
  # 1 day
  PurgingBatchWindow: "24h"
  # defines the time at which data actual data purging starts in HH:MM format where HH is in 24 hrs format
  DataPurgingStartTime: "00:00"
  # defines the time at which data actual data purging end in HH:MM format where HH is in 24 hrs format
  DataPurgingEndTime: "23:59"
  # key prefix to be used to uniquely isolate aa data sync lock prefix
  DataSyncLockPrefix: "AA:SYNC_LOCK:"
  # lease duration to be for new data sync lock
  NewDataSyncLeaseDuration: "10s"
  # lease duration to be for data purging lock
  DataPurgingLeaseDuration: "10s"
  # db connection alias for connecting to connected_account
  PgdbConnAlias: "connected_account_pgdb"

NameCheckParamsForAddFunds:
  IsNameCheckRestricted: true

DefaultExpiryDuration: 10

# Parameters for reconciliation
ReconParams:
  TimeLimitForExhaustedAttempt: 1 # Time limit before attempting reconciliation again (hours)

HideAccountBalanceInNotification: false

Tracing:
  Enable: false

# number retries for utr collision in case of upi transactions
UpiUtrCollisionRetryCount: 3

# feature flags to control enable/disable features
FeatureFlags:
  DedupeByDedupeIdEnable: true
  EnableRemitterInfo:
    IsRestricted: false
  PercentageRollOutForTimelineOptimisation:
    RolloutPercentage: 100
  EnableOptimisationForGetTotalTransactionCountParams:
    IsFeatureRestricted: false
  EnableFallbackForGetTotalTransactionCountToCrdb:
    IsFeatureRestricted: false
  EnableOptimisationForGetTotalTransactionAmountParams:
    IsFeatureRestricted: false
  EnableFallbackForGetTotalTransactionAmountToCrdb:
    IsFeatureRestricted: false
  EnableTotalAmountUserCheck:
    IsFeatureRestricted: false
  AllowedUserGroupForSuccessfulOrderWithTxnFetchOptimization:
  EnableHandleAddFundsWithoutFirstLegTxn: false
  EnableBypassCooldownForWhitelistedUser:
    IsFeatureRestricted: true
  IncludePartnerRefIdToDedupeTxnInRecon: false
  EnableAllTransactionForSelectedOrderStatesParams:
    IsFeatureRestricted: false
  EnableRecentActivityForDifferentTxns:
    IsFeatureRestricted: true
    AllowedUserGroups: [ ]
  DisableCommsForP2PFundTransfer: true
  EnableDbOptimisationForAllTransactionsFlow:
    IsFeatureRestricted: false
  EnablePGDBDaoForAA: true
  EnableOnAppEnachExecutionInboundNotifProcessor: true
  BypassTPAPCoolDown:
    IsFeatureRestricted: false
  EnableAllTransactionsForNoOpFailures:
    IsFeatureRestricted: false
  EnableOffAppUpiFlow: true
  RedirectToAmountScreenFromCollectPN: true
  EnableDcAmcChargesCashbackBanner: true

Events:
  IncomingCreditMaxPublishDelay: "2m"

RemitterInfoSyncDelayThreshold: "0s"

# DelayRangeForBalanceV1 will be used to choose a random delay time between MinimumDelay and MaximumDelay and
# use this delay while invoking balance V1 api
DelayRangeForBalanceV1:
  MinimumDelay: "15s"
  MaximumDelay: "100s"

ReVerifyAddressDelay: "30m"

B2CInitialEnquiryDelay: "10s"

VerifyVpaDeadlineForInboundNotification: "15s"

InboundNotificationParams:
  BalanceRefreshDelay: "10s"


ErrorRespCodesForPermanentFailure:
  FundTransferCallBackErrRespCodesForVendorMap:
    "FEDERAL_BANK":
      FundTransferCallBackErrRespCodes:
        - "119" #account is closed
  B2CFundTransferPermanentFailureRespCodesToVendorMap:
    "FEDERAL_BANK":
      RespCodes:
        - "119" #account is closed
        - "12" #invalid account
        - "YD" #DO NOT HONOUR (BENEFICIARY)

DataReplicationParams:
  IsEnabled: false
  TargetDBName: "federal"
  SourceTables:
    - "transactions"

RecentOrderEligibleDuration: "240h"

ReconRestrictedWindow:
  IsEnabled: true
  Timezone: "Asia/Kolkata"
  DateTimeFormat: "2006-01-02:15:04:05"
  FromDateTime: "2024-03-27:13:30:00"
  ToDateTime: "2024-03-27:19:30:00"

FeatureReleaseConfig:
  FeatureConstraints:
    TXN_RECEIPT_SCREEN_V1:
      AppVersionConstraintConfig:
        MinAndroidVersion: 256
        MinIOSVersion: 357
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1
    HEALTH_ENGINE_FOR_PAYMENTS:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FETCH_ORDER_WITH_TRANSACTIONS_USING_IN_MEMORY_JOIN:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
    QUICK_RECAT:
      AppVersionConstraintConfig:
        MinAndroidVersion: 100
        MinIOSVersion: 100
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1
    FEATURE_REMITTER_INFO_BACKFILL:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1
    IMPS_DEEMED_HANDLING:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups: [ ]
    HEALTH_ENGINE_FOR_INTRA_PAYMENTS:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    HEALTH_ENGINE_FOR_NEFT_PAYMENTS:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    HEALTH_ENGINE_FOR_IMPS_PAYMENTS:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    HEALTH_ENGINE_FOR_RTGS_PAYMENTS:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    SORT_RECENT_ACTIVITIES_ON_CREATED_AT:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    BENEFICIARY_COOL_DOWN_RULE:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_OFF_APP_UPI_VIA_TEMPORAL:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 37 # PAY_EXPERIMENTAL
    FEATURE_OFF_APP_UPI_PREEMPT:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 37 # PAY_EXPERIMENTAL
    REPORT_FRAUD_ORDER_RECEIPT:
      AppVersionConstraintConfig:
        MinAndroidVersion: 10000
        MinIOSVersion: 10000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups: [ ]
    FEATURE_AMOUNT_SCREEN_IN_COLLECT_PN:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
    FEATURE_ENABLE_ACCOUNT_OPERATIONAL_STATUS_CHECK:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_ASSOCIATED_TRANSACTION_ORDER_RECEIPT:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
    FEATURE_DAILY_TOTAL_TRANSACTION_AMOUNT_LIMIT_ON_APP:
        AppVersionConstraintConfig:
            MinAndroidVersion: 0
            MinIOSVersion: 0
        StickyPercentageConstraintConfig:
            RolloutPercentage: 100
    FEATURE_NUGGET_TRANSACTION_CHATBOT:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_OC215A_SKIP_VALIDATE_ADDRESS:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 37 # PAY_EXPERIMENTAL

MccsWithOnlyAllowedIntentPayments:
  ANDROID:
    - 5816

B2CPaymentParams:
  ProcessB2cPaymentLock: "PROCESS_B2C_PAYMENT_LOCK"
  # lease duration to be for new data sync lock
  ProcessB2cPaymentLockLeaseDuration: "2s"


IsPushingPaymentsMetricsToHealthEngineEnabled: true

IsEmailNotificationEnable: true
AmountThresholdForNotification:
  CurrencyCode: "INR"
  Units: 10000
  Nanos: 0

InternationalQrInfoPrefix: "VALIDATE_INTERNATIONAL_QR"
DelimiterForRedisKey: ":"

AaEnrichedTxnUpdateBatchSize: 1000

ForexRefundInfo:
  RefundInProgressLayout: "₹%.2f\nRefund will be processed by %s."
  RefundUnderReviewLayout: "₹%.2f\nRefund will be processed by %s."
  RefundCompletedLayout: "₹%.2f (Refunded on %s)"
  RefundCompletedWithoutInfo: "Refunded"
  IncomingRefundLayout: "For an international transaction of ₹%.2f on %s"
  IncomingRefundLayoutWithoutInfo: "For an international transaction"

# it will decide whether to compare the new statement data(GetAccountStatementData) and old statement data (GetAccountStatement)
IsStatementDataComparison: false

# MaximumPauseTimeForRemitterInfoWorkflow is in minutes
MaximumPauseTimeForRemitterInfoWorkflow: 1

QuestSdk:
  Disable: true

IncidentManagerParams:
  DebitedTransactionThresholdBreachDuration: 5m

SecureLoggingV2:
  EnableSecureLog: true
  DefaultFileLoggingParams:
    LogPath: "/var/log/order/secure.log"
    MaxSizeInMBs: 100
    MaxBackups: 10

CommsConfig:
  # ExcludeUsersForEmail will be used to exclude some user from getting email
  # If the value corresponding to the user id is marked as true then that user will be excluded from getting email
  ExcludeUsersForEmail:
    "4272db89-e5f3-4a09-9cc3-3c4710c3e896": true

RewardsInfo:
  DpandaFiStoreOrderHistoryTargetUrl: "fimoney.staging.dpanda.io/user_id=%s"
  PoshvineGiftCardStoreOrderHistoryTargetUrl: "sandbox-fi-money.poshvine.com/order-history?pageType=GiftCardBooking"

TimeoutForGetOperationalStatus: "2s"
PinotQueryTimeout: "5s"

# This configuration outlines different transaction scenarios and their corresponding story details.
# Priority depends on the ordering of different cases.
# Priority can be given to more specific cases of transaction attributes over more generic ones.
TransactionAttributesBasedStoryDetailsCombinations:
  - IsEnabled: true
    IsMoneyDebited: true
    CurrentTransactionStatus: "FAILED"
    DetailedApiStatus: "FAILURE"
    StatusCodePayer: "UPI1204"
    StatusCodePayee: "UPI825"
    StoryDetails:
      Description: "Money deducted but transaction Failed?"
      Title: "Money deducted but transaction Failed?"
      Url: "https://storifyme.com/stories/g-prateek-gauba-22176/91980/preview"
      Id: "PAYMENT_FAILED_UPI1204_UPI825"
      LeftIconUrl: "https://epifi-icons.pointz.in/failedtxn"
  - IsEnabled: true
    IsMoneyDebited: true
    CurrentTransactionStatus: "FAILED"
    DetailedApiStatus: "FAILURE"
    StatusCodePayer: "UPI171"
    StatusCodePayee: "UPI784"
    StoryDetails:
      Description: "Money deducted but transaction Failed?"
      Title: "Money deducted but transaction Failed?"
      Url: "https://storifyme.com/stories/g-prateek-gauba-22176/91980/preview"
      Id: "PAYMENT_FAILED_UPI171_UPI784"
      LeftIconUrl: "https://epifi-icons.pointz.in/failedtxn"
  - IsEnabled: true
    IsMoneyDebited: true
    CurrentTransactionStatus: "FAILED"
    DetailedApiStatus: "FAILURE"
    StatusCodePayer: "UPI784"
    StatusCodePayee: "UPI784"
    StoryDetails:
      Description: "Money deducted but transaction Failed?"
      Title: "Money deducted but transaction Failed?"
      Url: "https://storifyme.com/stories/g-prateek-gauba-22176/91980/preview"
      Id: "PAYMENT_FAILED_UPI784_UPI784"
      LeftIconUrl: "https://epifi-icons.pointz.in/failedtxn"
  - IsEnabled: true
    IsMoneyDebited: true
    CurrentTransactionStatus: "FAILED"
    DetailedApiStatus: "FAILURE"
    StatusCodePayer: "UPI_SUCCESS"
    StatusCodePayee: "UPI_SUCCESS"
    StoryDetails:
      Description: "Money deducted but transaction Failed?"
      Title: "Money deducted but transaction Failed?"
      Url: "https://storifyme.com/stories/g-prateek-gauba-22176/91980/preview"
      Id: "PAYMENT_FAILED_UPI_SUCCESS_UPI_SUCCESS"
      LeftIconUrl: "https://epifi-icons.pointz.in/failedtxn"
  - IsEnabled: true
    IsMoneyDebited: true
    CurrentTransactionStatus: "IN_PROGRESS"
    DetailedApiStatus: "DEEMED"
    StatusCodePayer: "*"
    StatusCodePayee: "*"
    StoryDetails:
      Description: "Money not credited to beneficiary?"
      Title: "Money not credited to beneficiary?"
      Url: "https://storifyme.com/stories/g-prateek-gauba-22176/31055/preview"
      Id: "PAYMENT_DEEMED"
      LeftIconUrl: "https://epifi-icons.pointz.in/deemedtxn"

EnableEntitySegregation: true

RupayCCRestrictedMCCs:
  - "0000"
  - "4829"
  - "5413"
  - "6010"
  - "6011"
  - "6012"
  - "6051"
  - "6211"
  - "7322"
  - "7407"
  - "7408"
  - "7409"
  - "7800"
  - "7802"
  - "7995"

AWS:
  Region: "ap-south-1"
