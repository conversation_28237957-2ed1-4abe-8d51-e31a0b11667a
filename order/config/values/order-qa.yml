Application:
  Environment: "qa"
  Name: "order"
  IsSeparateEnquiryPerProtocolEnabled: true


Server:
  Ports:
    GrpcPort: 8091
    GrpcSecurePort: 9513
    HttpPort: 9999
    HttpPProfPort: 9990

EpifiDb:
  DbType: "CRDB"
  AppName: "order"
  StatementTimeout: 1s
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "qa/cockroach/ca.crt"
  SSLClientCert: "qa/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "qa/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 40
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true
    EnableMultiDBSupport: true
    DBResolverList:
      - Alias: "connected_account_pgdb"
        DbDsn:
          DbType: "PGDB"
          AppName: "order"
          StatementTimeout: 1m
          Name: "connected_account"
          EnableDebug: true
          SSLMode: "disable"
          SecretName: "qa/rds/epifimetis/connected_account_dev_user"


EpifiWealthDB:
  DbType: "CRDB"
  AppName: "order"
  Username: "epifi_wealth_dev_user"
  Password: ""
  Name: "epifi_wealth"
  EnableDebug: false
  SSLMode: "verify-full"
  SSLRootCert: "qa/cockroach/ca.crt"
  SSLClientCert: "qa/cockroach/client.epifi_wealth_dev_user.crt"
  SSLClientKey: "qa/cockroach/client.epifi_wealth_dev_user.key"
  MaxOpenConn: 20
  MaxIdleConn: 5
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true
    EnableMultiDBSupport: true
    DBResolverList:
      - Alias: "connected_account_pgdb"
        DbDsn:
          DbType: "PGDB"
          AppName: "order"
          StatementTimeout: 1m
          Name: "connected_account"
          EnableDebug: true
          SSLMode: "disable"
          SecretName: "qa/rds/epifimetis/connected_account_dev_user"

PaymentOrchestrationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-payment-orchestration-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

IntraBankEnquirySubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-payment-intrabank-enquiry-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "order:intrabank"

UPIEnquirySubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-payment-upi-enquiry-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "order:upi"

IMPSEnquirySubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-payment-imps-enquiry-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "order:imps"

NEFTEnquirySubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-payment-neft-enquiry-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "order:neft"

RTGSEnquirySubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-payment-rtgs-enquiry-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "order:rtgs"

OrderOrchestrationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-order-orchestration-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

PaymentCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-payment-callback-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "order"

InboundTxnSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-inbound-txn-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

OrderUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-order-update-order-notification-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

OrderCollectNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-order-collect-notification-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

OrderNotificationFallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-order-notification-fallback-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

OrderWorkflowProcessingSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-order-workflow-processing-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 15
      TimeUnit: "Second"

DisputeEventProcessingSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-cx-dispute-events-external-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 9
      TimeUnit: "Second"

SavingsLedgerReconSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-order-savings-ledger-recon-queue"
  # Exponential backoff till 4 min, to add tolerance against transient failure due to network failure while fetching statement
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

AATxnSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-order-aa-txn-queue"
  # Exponential backoff till 2 min
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 7
      TimeUnit: "Second"

AAFirstDataPullTxnSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-order-aa-first-data-pull-txn-queue"
  # Exponential backoff till 2 min
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 7
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 60
        Period: 1s
    Namespace: "order-aa"

PurgeAaDataSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-order-aa-data-purge-queue"
  # Exponential backoff till 2 min
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 7
      TimeUnit: "Second"

AaTxnPurgeSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-aa-txn-purge-queue"
  # Exponential backoff till 2 min
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 7
      TimeUnit: "Second"

AaDataPurgeOrchestrationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-order-aa-data-purging-orchestration-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 9
      TimeUnit: "Second"

OrderOrchestrationPublisher:
  QueueName: "qa-order-orchestration-queue"

PaymentOrchestrationPublisher:
  QueueName: "qa-payment-orchestration-queue"

OrderUpdateEventPublisher:
  TopicName: "qa-order-update-topic"

OrderMerchantMergeEventPublisher:
  TopicName: "qa-batch-order-update-topic"

AATxnPublisher:
  TopicName: "qa-order-aa-txn-topic"

TxnDetailedStatusUpdateSnsPublisher:
  TopicName: "qa-txn-detailed-status-update-topic"

WorkflowProcessingPublisher:
  QueueName: "qa-order-workflow-processing-queue"

SavingsLedgerReconPublisher:
  QueueName: "qa-order-savings-ledger-recon-queue"

OrderNotificationPublisher:
  QueueName: "qa-order-update-order-notification-consumer-queue"

OrderCollectNotificationPublisher:
  QueueName: "qa-order-collect-notification-consumer-queue"

OrderNotificationFallbackPublisher:
  QueueName: "qa-order-notification-fallback-consumer-queue"

OrderSearchPublisher:
  QueueName: "qa-order-update-indexing-consumer-queue"

IntraBankEnquiryPublisher:
  QueueName: "qa-payment-intrabank-enquiry-queue"

UPIEnquiryPublisher:
  QueueName: "qa-payment-upi-enquiry-queue"

IMPSEnquiryPublisher:
  QueueName: "qa-payment-imps-enquiry-queue"

NEFTEnquiryPublisher:
  QueueName: "qa-payment-neft-enquiry-queue"

RTGSEnquiryPublisher:
  QueueName: "qa-payment-rtgs-enquiry-queue"

AaAccountPiPurgePublisher:
  QueueName: "qa-aa-account-pi-purge-queue"

ActorPiRelationPurgePublisher:
  QueueName: "qa-actor-pi-purge-queue"

AaDataPurgeOrchestrationPublisher:
  QueueName: "qa-order-aa-data-purging-orchestration-queue"

EventsCompletedTnCPublisher:
  QueueName: "qa-event-completed-tnc-queue"

SignalWorkflowPublisher:
  QueueName: "qa-celestial-signal-workflow-queue"

TxnNotificationPublisher:
  QueueName: "qa-transaction-notification-queue"

InPaymentOrderUpdatePublisher:
  QueueName: "qa-order-in-payment-order-update-queue"

ProcrastinatorWorkflowPublisher:
  QueueName: "qa-celestial-initiate-procrastinator-workflow-queue"

AWS:
  Region: "ap-south-1"

Flags:
  TrimDebugMessageFromStatus: false

CustomRuleDEParams:
  IsCustomRuleDERestricted: false

Secrets:
  Ids:
    RudderWriteKey: "qa/rudder/internal-writekey"
    GoogleCloudProfilingServiceAccountKey: "qa/gcloud/profiling-service-account-key"
    EventSalt: "qa/rudder/salt"
    EpifiFederalPgpPrivateKey: "qa/pgp/pgp-epifi-key-fed-api"
    FederalPgpPublicKey: "qa/pgp/pgp-federal-dummy-key-for-sim"
    EpifiFederalPgpPassphrase: "qa/pgp/pgp-epifi-key-passphrase-fed-api"
    EncryptorData: "qa/order/aes/txn-monitoring-aes-encryption-data"

PaymentProtocolDecisionParams:
  INTRAParams:
    MinAmount: 150
    MaxAmount: 1000
    TotalTransactedAmountLimitInCoolDown: ********
    ProfileUpdateCoolDownWindow:
      Value: 5
      TimeUnit: "Minute"
    ProtocolRestrictedWindow:
      IsEnabled: false
      Timezone: "Asia/Kolkata"
      DateTimeFormat: "2006-01-02:15:04:05"
      FromDateTime: "2021-04-18:00:00:00"
      ToDateTime: "2021-04-18:14:00:00"

  IMPSParams:
    MinAmount: 1001
    MaxAmount: 200000
    TotalTransactedAmountLimitInCoolDown: ********
    ProfileUpdateCoolDownWindow:
      Value: 24
      TimeUnit: "Hour"
    ProtocolRestrictedWindow:
      IsEnabled: false
      Timezone: "Asia/Kolkata"
      DateTimeFormat: "2006-01-02:15:04:05"
      FromDateTime: "2021-04-18:00:00:00"
      ToDateTime: "2021-04-18:14:00:00"
    LimitWindow:
      Value: 5
      TimeUnit: "Minute"
    TransactionCountLimit: 5

  NEFTParams:
    MinAmount: 200001
    MaxAmount: 300000
    TotalTransactedAmountLimitInCoolDown: ********
    ProfileUpdateCoolDownWindow:
      Value: 24
      TimeUnit: "Hour"
    ProtocolRestrictedWindow:
      IsEnabled: false
      Timezone: "Asia/Kolkata"
      DateTimeFormat: "2006-01-02:15:04:05"
      FromDateTime: "2021-04-18:00:00:00"
      ToDateTime: "2021-04-18:14:00:00"

  RTGSParams:
    MinAmount: 300001
    # TODO(raunak): To handle infinity amounts gracefully - maybe -1 ??
    MaxAmount: 1500000
    TotalTransactedAmountLimitInCoolDown: ********
    ProfileUpdateCoolDownWindow:
      Value: 4
      TimeUnit: "Minute"
    ProtocolRestrictedWindow:
      IsEnabled: false
      Timezone: "Asia/Kolkata"
      DateTimeFormat: "2006-01-02:15:04:05"
      FromDateTime: "2021-04-18:00:00:00"
      ToDateTime: "2021-04-18:14:00:00"

  UPIParams:
    UpiMinAmount: 1
    UpiMaxAmount: 100000
    UpiTotalTransactedAmountLimit: 1000000
    UpiTransactionCountLimit: 10
    UpiNewUserTotalTransactedAmountLimit: 5000
    UpiCoolDownPeriodTotalTransactedAmountLimit: 10000
    UpiPinResetTotalTransactedAmountLimit: 5000
    UpiLimitWindow:
      Value: 24
      TimeUnit: "Hour"
    UpiPinResetLimitWindow:
      Value: 12
      TimeUnit: "Hour"
    UpiProfileUpdateCoolDownWindow:
      ANDROID:
        Value: 24
        TimeUnit: "Hour"
      IOS:
        Value: 24
        TimeUnit: "Hour"
    UpiProfileUpdateCoolDownTriggerDurationLimit:
      Value: 24
      TimeUnit: "Hour"
    UpiProfileUpdateAfuSummariesFetchDuration:
      Value: 24
      TimeUnit: "Hour"
    UPIRestrictedWindow:
      IsEnabled: false
      Timezone: "Asia/Kolkata"
      DateTimeFormat: "2006-01-02:15:04:05"
      FromDateTime: "2021-04-18:00:00:00"
      ToDateTime: "2021-04-18:14:00:00"

  UserPaymentParams:
    TotalTransactionAmountLimit:
      AmountLimit: 3000000
      TimeWindow:
        Value: 24
        TimeUnit: "Hour"
    NewAccountTransactionAmountLimit:
      AmountLimit: 500000
      AccountAgeLimitThreshold: "24h"

  # Specific MCC or payment initiation mode or purpose code for which UPI txn limit is different than normal
  # Circular: https://www.npci.org.in/PDF/npci/upi/circular/2020/UPI%20OC%2082%20-%20Implementation%20of%20Rs%20%202%20Lakh%20limit%20per%20transaction%20for%20specific%20categories%20in%20UPI.pdf
  UPILimitExceptions:
    MCC:
      "9311":
        IsLimitOnlyForVerifiedMerchant: true
        Limits:
          MaxAmount: 500000
          MinAmount: 1
      "6211":
        IsLimitOnlyForVerifiedMerchant: true
        Limits:
          MaxAmount: 500000
          MinAmount: 1
      "7322":
        IsLimitOnlyForVerifiedMerchant: true
        Limits:
          MaxAmount: 200000
          MinAmount: 1

  TotalTransactedAmountLimitInCoolDown: 10000
  ProfileUpdateCoolDownWindow:
    Value: 24
    TimeUnit: "Hour"

  UpiPreferredPaymentProtocolAmountParams:
    Enable: false
    MinAmount: 1
    MaxAmount: 10000

AddFundsWorkflow:
  InitialDelay:
    Value: 5
    TimeUnit: "Second"

AddFundsCollectWorkflow:
  InitialDelay:
    Value: 5
    TimeUnit: "Second"

# Time should be in HH:MM 24-hour format
# It is quick fix for CSIS time and will be removed when CSIS service is up.
CSIS:
  IsCsisEnable: false
  StartTime: "00:30"
  EndTime: "00:59"

# Time should be in HH:MM 24-hour format
# It is quick fix for BankDownTime and will be removed when CSIS service is up.
BankDownTime:
  IsBankDownTimeEnable: false
  StartTime: "23:45"
  EndTime: "23:59"

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.qa-common-cache-redis.rxzivf.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    # on qa we are using common redis cluster with a different db
    DB: 3

ConnectedAccountUserGroupParams:
  IsAARestricted: false
  AllowedUserGroupForAA:
    - 1 # INTERNAL
    - 2 # FNF
    - 7 # Connected Account

PaymentNotificationParams:
  SkipFITOrders: true
  SkipMFOrders: true

OrderReceipt:
  # banner to be shown on the order receipt
  OrderReceiptProtocolLevelBanner:
    NeftTxnInfoBanner:
      InfoString: "This NEFT transaction can take upto 2 hours to get credited to the beneficiary account."
      # Time window for which OrderReceiptProtocolLevelBanner is displayed on screen when payment is success or failed.
      Window: "1h"

PaymentSMSParams:
  SkipFITOrders: true

RuleEngineParams:
  SalienceINTRA: 22
  SalienceIMPS: 27
  SalienceUPI: 7
  SalienceRTGS: 25
  SalienceNEFT: 21
  BumpedSalience: 20
  SecondHighestBumpedSalience: 13

TxnNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-transaction-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

DeclineCardTransactionsProcessingSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-card-txns-decline-data-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

DeclineDataAwsSftpBucket:
  AwsBucket: "epifi-qa-federal-debit-card-data"
  AclString: "private"
  SrcFolder: "in_data"
  DstFolder: "processed"

SavingsLedgerReconCacheConfig:
  IsCachingEnabled: true
  SavingsLedgerReconPrefix: "savings_ledger_recon_account_Id"
  CacheTTl: "2m"
  SavingsLedgerRedisOptions:
    IsSecureRedis: true
    Options:
      Addr: "master.qa-common-cache-redis.rxzivf.aps1.cache.amazonaws.com:6379"
      Password: "" ## empty string for no password
      DB: 14

OrderCacheConfig:
  IsCachingEnabled: true
  UseCaseToCacheConfigMap:
    # CacheConfig for RecentActivityCache used in GetRecentActivityOrdersFromCache for storing recent pay activity
    # (RecentPayActivityFromActorIdPrefix, RecentPayActivityToActorIdPrefix are cache prefixes used here).
    - RecentActivityCache:
        IsCachingEnabled: true
        CacheTTL: "5m"
    # CacheConfig for OrderCache used in GetById for storing order
    # (OrderKeyPrefix is cache prefix used here).
    - OrderCache:
        IsCachingEnabled: true
        CacheTTL: "2m"
  RedisOptions:
    IsSecureRedis: true
    Options:
      Addr: "master.qa-common-cache-redis.rxzivf.aps1.cache.amazonaws.com:6379"
      Password: "" ## empty string for no password
      DB: 14
      ClientName: order

TransactionCacheConfig:
  IsCachingEnabled: true
  UseCaseToCacheConfigMap:
    # CacheConfig for TransactionCache used in GetById for storing transaction
    # (TransactionKeyPrefix is cache prefix used here).
    - TransactionCache:
        IsCachingEnabled: false
        CacheTTL: "5m"


Tracing:
  Enable: false

P2PInvestmentWorkflow:
  Payment:
    RetryStrategy:
      Hybrid: # Exponential backoff is followed for first ~68 min post that regular interval is followed for next 5 days with interval of 2 hours.
        RetryStrategy1:
          ExponentialBackOff:
            BaseInterval: 2
            MaxAttempts: 12
            TimeUnit: "Second"
        RetryStrategy2:
          RegularInterval:
            Interval: 2
            MaxAttempts: 60
            TimeUnit: "Hour"
        MaxAttempts: 72
        CutOff: 12
    Method: "/p2pinvestment.P2pInvestment/PaymentWrapper"
    ServiceName: "p2pinvestment"
  Settlement:
    RetryStrategy:
      Hybrid: # Regular interval of 1 min for the first 2 hours, post that regular interval is followed for next 1 days with interval of 1 hour.
        RetryStrategy1:
          RegularInterval:
            Interval: 1
            MaxAttempts: 120
            TimeUnit: "Minute"
        RetryStrategy2:
          RegularInterval:
            Interval: 1
            MaxAttempts: 24
            TimeUnit: "Hour"
        MaxAttempts: 144
        CutOff: 120
    Method: "/p2pinvestment.P2pInvestment/SettleInvestment"
    ServiceName: "p2pinvestment"
  IsAutoTriggered: false

P2PWithdrawalWorkflow:
  Fulfillment:
    RetryStrategy:
      Hybrid: # Regular interval of 1 min for the first 2 hours, post that regular interval is followed for next 1 days with interval of 1 hour.
        RetryStrategy1:
          RegularInterval:
            Interval: 1
            MaxAttempts: 120
            TimeUnit: "Minute"
        RetryStrategy2:
          RegularInterval:
            Interval: 1
            MaxAttempts: 24
            TimeUnit: "Hour"
        MaxAttempts: 144
        CutOff: 120
    Method: "/p2pinvestment.P2pInvestment/FulfillWithdrawal"
    ServiceName: "p2pinvestment"
  Payment:
    RetryStrategy:
      Hybrid: # Regular interval of 1 min for the first 2 hours, post that regular interval is followed for next 1 days with interval of 1 hour.
        RetryStrategy1:
          RegularInterval:
            Interval: 1
            MaxAttempts: 120
            TimeUnit: "Minute"
        RetryStrategy2:
          RegularInterval:
            Interval: 1
            MaxAttempts: 24
            TimeUnit: "Hour"
        MaxAttempts: 144
        CutOff: 120
    Method: "/p2pinvestment.P2pInvestment/VerifyWithdrawal"
    ServiceName: "p2pinvestment"
  IsAutoTriggered: true

InboundUpiTxnSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-inbound-upi-txn-queue"
  RetryStrategy:
    Hybrid:
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 10
          MaxAttempts: 5
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 30
          MaxAttempts: 5
          TimeUnit: "Minute"
      MaxAttempts: 10
      CutOff: 12
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 30
        Period: 1s
    Namespace: "order-consumer"

FeatureFlags:
  EnableAllTransactionForSelectedOrderStatesParams: # feature flag to enable pending, failed and reversed txns in all txn page
    IsFeatureRestricted: false
  EnableRecentActivityForDifferentTxns: # feature flag to enable pending, failed and reversed txns in recent activity tab in home screen
    IsFeatureRestricted: false
  EnableB2CCallbackSignalToCelestial: true
  EnablePGDBDaoForAA: true
  EnableBypassCooldownForWhitelistedUser:
    IsFeatureRestricted: true
    AllowedUserGroups:
      - 20 # BYPASS_COOLDOWN_QA
  EnableHandleAddFundsWithoutFirstLegTxn: true
  EnableOnAppEnachExecutionInboundNotifProcessor: true
  EnableParameterizedTimelineEvents:
    IsFeatureRestricted: false

Events:
  IncomingCreditMaxPublishDelay: "2m"

OrderVpaVerificationPublisher:
  QueueName: "qa-unverified-vpa-queue"

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

DataReplicationParams:
  IsEnabled: false
  TargetDBName: federal
  SourceTables:
    - "transactions"

FeatureReleaseConfig:
  FeatureConstraints:
    TXN_RECEIPT_SCREEN_V1:
      AppVersionConstraintConfig:
        MinAndroidVersion: 242
        MinIOSVersion: 1369
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    HEALTH_ENGINE_FOR_PAYMENTS:
      AppVersionConstraintConfig:
        MinAndroidVersion: 249
        MinIOSVersion: 1414
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    QUICK_RECAT:
      AppVersionConstraintConfig:
        MinAndroidVersion: 296
        MinIOSVersion: 1670
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1
    HEALTH_ENGINE_FOR_INTRA_PAYMENTS:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    HEALTH_ENGINE_FOR_NEFT_PAYMENTS:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    HEALTH_ENGINE_FOR_IMPS_PAYMENTS:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    HEALTH_ENGINE_FOR_RTGS_PAYMENTS:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    BENEFICIARY_COOL_DOWN_RULE:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    REPORT_FRAUD_ORDER_RECEIPT:
      AppVersionConstraintConfig:
        MinAndroidVersion: 334
        MinIOSVersion: 2180
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups: [ ]
    FEATURE_AMOUNT_SCREEN_IN_COLLECT_PN:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 2328
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
    FEATURE_ENABLE_ACCOUNT_OPERATIONAL_STATUS_CHECK:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_ASSOCIATED_TRANSACTION_ORDER_RECEIPT:
      AppVersionConstraintConfig:
        MinAndroidVersion: 388
        MinIOSVersion: 2460
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
    FEATURE_DAILY_TOTAL_TRANSACTION_AMOUNT_LIMIT_ON_APP:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
    FEATURE_NUGGET_TRANSACTION_CHATBOT:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
    FEATURE_OC215A_SKIP_VALIDATE_ADDRESS:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 37 # PAY_EXPERIMENTAL

DeemedTransactionUPIEnquiryPublisher:
  QueueName: "qa-deemed-payment-upi-enquiry-queue"

DeemedTransactionUpiEnquirySubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-deemed-payment-upi-enquiry-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 15
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "order:upi"

NonTpapPspHandles: ["fede"]

QuestRedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.qa-common-cache-redis.rxzivf.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 10
  ClientName: order-quest-sdk
  HystrixCommand:
    CommandName: "quest_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 500
      ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

ReportFraudConfig:
  TransactionProtocolToMaxDaysAvailableForReportFraud:
    CARD: 1.0
    IMPS: 1.0
    UPI: 1.0

EnableEntitySegregation: true
