Application:
  Environment: "staging"
  Name: "order"
  IsSeparateEnquiryPerProtocolEnabled: true

Server:
  Ports:
    GrpcPort: 8091
    GrpcSecurePort: 9513
    HttpPort: 9999
    HttpPProfPort: 9990

EpifiDb:
  DbType: "CRDB"
  AppName: "order"
  StatementTimeout: 1s
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "staging/cockroach/ca.crt"
  SSLClientCert: "staging/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "staging/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV1Conf:
    LogMode: "DETAILED_LOG"
    LogLevel: "INFO"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true
    EnableMultiDBSupport: true
    DBResolverList:
      - Alias: "connected_account_pgdb"
        DbDsn:
          DbType: "PGDB"
          AppName: "order"
          StatementTimeout: 1m
          Name: "connected_account"
          EnableDebug: true
          SSLMode: "disable"
          SecretName: "staging/rds/postgres14"

EpifiWealthDB:
  DbType: "CRDB"
  AppName: "order"
  StatementTimeout: 1s
  Username: "epifi_wealth_dev_user"
  Password: ""
  Name: "epifi_wealth"
  EnableDebug: false
  SSLMode: "verify-full"
  SSLRootCert: "staging/cockroach/ca.crt"
  SSLClientCert: "staging/cockroach/client.epifi_wealth_dev_user.crt"
  SSLClientKey: "staging/cockroach/client.epifi_wealth_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true
    EnableMultiDBSupport: true
    DBResolverList:
      - Alias: "connected_account_pgdb"
        DbDsn:
          DbType: "PGDB"
          AppName: "order"
          StatementTimeout: 1m
          Name: "connected_account"
          EnableDebug: true
          SSLMode: "disable"
          SecretName: "staging/rds/postgres14"

PaymentOrchestrationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-payment-orchestration-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

IntraBankEnquirySubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-payment-intrabank-enquiry-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

UPIEnquirySubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-payment-upi-enquiry-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

IMPSEnquirySubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-payment-imps-enquiry-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

NEFTEnquirySubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-payment-neft-enquiry-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

RTGSEnquirySubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-payment-rtgs-enquiry-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

OrderOrchestrationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-order-orchestration-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

PaymentCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-payment-callback-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

InboundTxnSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-inbound-txn-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

OrderUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-order-update-order-notification-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

OrderCollectNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-order-collect-notification-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

OrderNotificationFallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-order-notification-fallback-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

OrderWorkflowProcessingSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-order-workflow-processing-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 15
      TimeUnit: "Second"

DisputeEventProcessingSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-cx-dispute-events-external-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 9
      TimeUnit: "Second"

SavingsLedgerReconSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-order-savings-ledger-recon-queue"
  # Exponential backoff till 2 min, to add tolerance against transient failure due to network failure while fetching statement
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 7
      TimeUnit: "Second"

AATxnSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-order-aa-txn-queue"
  # Exponential backoff till 2 min
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 7
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 60
        Period: 1s
    Namespace: "order-aa"


PurgeAaDataSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-order-aa-data-purge-queue"
  # Exponential backoff till 2 min
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 7
      TimeUnit: "Second"

AaTxnPurgeSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-aa-txn-purge-queue"
  # Exponential backoff till 2 min
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 7
      TimeUnit: "Second"

AaDataPurgeOrchestrationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-order-aa-data-purging-orchestration-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 9
      TimeUnit: "Second"

InboundUpiTxnSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-inbound-upi-txn-queue"
  RetryStrategy:
    Hybrid:
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 10
          MaxAttempts: 5
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 30
          MaxAttempts: 5
          TimeUnit: "Minute"
      MaxAttempts: 10
      CutOff: 12
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 30
        Period: 1s
    Namespace: "order-consumer"

AAFirstDataPullTxnSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-order-aa-first-data-pull-txn-queue"
  # Exponential backoff till 2 min
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 7
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 60
        Period: 1s
    Namespace: "order-aa"

OrderOrchestrationPublisher:
  QueueName: "staging-order-orchestration-queue"

PaymentOrchestrationPublisher:
  QueueName: "staging-payment-orchestration-queue"

OrderUpdateEventPublisher:
  TopicName: "staging-order-update-topic"

OrderMerchantMergeEventPublisher:
  TopicName: "staging-batch-order-update-topic"

AATxnPublisher:
  TopicName: "staging-order-aa-txn-topic"

TxnDetailedStatusUpdateSnsPublisher:
  TopicName: "staging-txn-detailed-status-update-topic"

WorkflowProcessingPublisher:
  QueueName: "staging-order-workflow-processing-queue"

SavingsLedgerReconPublisher:
  QueueName: "staging-order-savings-ledger-recon-queue"

OrderNotificationPublisher:
  QueueName: "staging-order-update-order-notification-consumer-queue"

OrderCollectNotificationPublisher:
  QueueName: "staging-order-collect-notification-consumer-queue"

OrderNotificationFallbackPublisher:
  QueueName: "staging-order-notification-fallback-consumer-queue"

OrderSearchPublisher:
  QueueName: "staging-order-update-indexing-consumer-queue"

IntraBankEnquiryPublisher:
  QueueName: "staging-payment-intrabank-enquiry-queue"

UPIEnquiryPublisher:
  QueueName: "staging-payment-upi-enquiry-queue"

IMPSEnquiryPublisher:
  QueueName: "staging-payment-imps-enquiry-queue"

NEFTEnquiryPublisher:
  QueueName: "staging-payment-neft-enquiry-queue"

RTGSEnquiryPublisher:
  QueueName: "staging-payment-rtgs-enquiry-queue"

AaAccountPiPurgePublisher:
  QueueName: "staging-aa-account-pi-purge-queue"

ActorPiRelationPurgePublisher:
  QueueName: "staging-actor-pi-purge-queue"

AaDataPurgeOrchestrationPublisher:
  QueueName: "staging-order-aa-data-purging-orchestration-queue"

EventsCompletedTnCPublisher:
  QueueName: "staging-event-completed-tnc-queue"

SignalWorkflowPublisher:
  QueueName: "staging-celestial-signal-workflow-queue"

TxnNotificationPublisher:
  QueueName: "staging-transaction-notification-queue"

InPaymentOrderUpdatePublisher:
  QueueName: "staging-order-in-payment-order-update-queue"
AWS:
  Region: "ap-south-1"

Flags:
  TrimDebugMessageFromStatus: false

CustomRuleDEParams:
  IsCustomRuleDERestricted: false

PaymentProtocolDecisionParams:
  INTRAParams:
    MinAmount: 1
    MaxAmount: 1000
    TotalTransactedAmountLimitInCoolDown: 10000
    ProfileUpdateCoolDownWindow:
      Value: 24
      TimeUnit: "Hour"
    ProtocolRestrictedWindow:
      IsEnabled: false
      Timezone: "Asia/Kolkata"
      DateTimeFormat: "2006-01-02:15:04:05"
      FromDateTime: "2021-04-18:00:00:00"
      ToDateTime: "2021-04-18:14:00:00"

  IMPSParams:
    MinAmount: 1001
    MaxAmount: 20000
    TotalTransactedAmountLimitInCoolDown: 2000000
    ProfileUpdateCoolDownWindow:
      Value: 24
      TimeUnit: "Hour"
    ProtocolRestrictedWindow:
      IsEnabled: false
      Timezone: "Asia/Kolkata"
      DateTimeFormat: "2006-01-02:15:04:05"
      FromDateTime: "2021-04-18:00:00:00"
      ToDateTime: "2021-04-18:14:00:00"

  NEFTParams:
    MinAmount: 1
    MaxAmount: 200000
    TotalTransactedAmountLimitInCoolDown: 10000
    ProfileUpdateCoolDownWindow:
      Value: 24
      TimeUnit: "Hour"
    ProtocolRestrictedWindow:
      IsEnabled: false
      Timezone: "Asia/Kolkata"
      DateTimeFormat: "2006-01-02:15:04:05"
      FromDateTime: "2021-04-18:00:00:00"
      ToDateTime: "2021-04-18:14:00:00"

  RTGSParams:
    MinAmount: 200000
    # TODO(raunak): To handle infinity amounts gracefully - maybe -1 ??
    MaxAmount: 1000000000
    TotalTransactedAmountLimitInCoolDown: 10000
    ProfileUpdateCoolDownWindow:
      Value: 24
      TimeUnit: "Hour"
    ProtocolRestrictedWindow:
      IsEnabled: false
      Timezone: "Asia/Kolkata"
      DateTimeFormat: "2006-01-02:15:04:05"
      FromDateTime: "2021-04-18:00:00:00"
      ToDateTime: "2021-04-18:14:00:00"

  UPIParams:
    UpiMinAmount: 1
    UpiMaxAmount: 100000
    UpiTotalTransactedAmountLimit: 1000000
    UpiTransactionCountLimit: 10
    UpiNewUserTotalTransactedAmountLimit: 5000
    UpiCoolDownPeriodTotalTransactedAmountLimit: 10000
    UpiPinResetTotalTransactedAmountLimit: 5000
    UpiLimitWindow:
      Value: 24
      TimeUnit: "Hour"
    UpiPinResetLimitWindow:
      Value: 12
      TimeUnit: "Hour"
    UpiProfileUpdateCoolDownWindow:
      ANDROID:
        Value: 24
        TimeUnit: "Hour"
      IOS:
        Value: 24
        TimeUnit: "Hour"
    UpiProfileUpdateCoolDownTriggerDurationLimit:
      Value: 24
      TimeUnit: "Hour"
    UpiProfileUpdateAfuSummariesFetchDuration:
      Value: 24
      TimeUnit: "Hour"
    UPIRestrictedWindow:
      IsEnabled: false
      Timezone: "Asia/Kolkata"
      DateTimeFormat: "2006-01-02:15:04:05"
      FromDateTime: "2021-04-18:00:00:00"
      ToDateTime: "2021-04-18:14:00:00"

  UserPaymentParams:
    TotalTransactionAmountLimit:
      AmountLimit: 1000000
      TimeWindow:
        Value: 24
        TimeUnit: "Hour"
    NewAccountTransactionAmountLimit:
      AmountLimit: 500000
      AccountAgeLimitThreshold: "2h" # 90 days

  # Specific MCC or payment initiation mode or purpose code for which UPI txn limit is different than normal
  # Circular: https://www.npci.org.in/PDF/npci/upi/circular/2020/UPI%20OC%2082%20-%20Implementation%20of%20Rs%20%202%20Lakh%20limit%20per%20transaction%20for%20specific%20categories%20in%20UPI.pdf
  UPILimitExceptions:
    MCC:
      "9311":
        IsLimitOnlyForVerifiedMerchant: true
        Limits:
          MaxAmount: 500000
          MinAmount: 1
      "6211":
        IsLimitOnlyForVerifiedMerchant: true
        Limits:
          MaxAmount: 500000
          MinAmount: 1
      "7322":
        IsLimitOnlyForVerifiedMerchant: true
        Limits:
          MaxAmount: 200000
          MinAmount: 1

  BeneficiaryCoolDownAmountLimit: 10000

  TotalTransactedAmountLimitInCoolDown: 10000
  ProfileUpdateCoolDownWindow:
    Value: 24
    TimeUnit: "Hour"

Secrets:
  Ids:
    RudderWriteKey: "staging/rudder/internal-writekey"
    GoogleCloudProfilingServiceAccountKey: "staging/gcloud/profiling-service-account-key"
    EventSalt: "staging/rudder/salt"
    EpifiFederalPgpPrivateKey: "staging/pgp/pgp-epifi-key-fed-api"
    FederalPgpPublicKey: "staging/pgp/pgp-federal-dummy-key-for-sim"
    EpifiFederalPgpPassphrase: "staging/pgp/pgp-epifi-key-passphrase-fed-api"
    EncryptorData: "staging/order/aes/txn-monitoring-aes-encryption-data"

AddFundsWorkflow:
  InitialDelay:
    Value: 5
    TimeUnit: "Second"

AddFundsCollectWorkflow:
  InitialDelay:
    Value: 5
    TimeUnit: "Second"

# Time should be in HH:MM 24-hour format
# It is quick fix for CSIS time and will be removed when CSIS service is up.
CSIS:
  IsCsisEnable: false
  StartTime: "23:30"
  EndTime: "01:30"

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    # on staging we are using common redis cluster with a different db
    DB: 3

PaymentNotificationParams:
  SkipFITOrders: false
  SkipMFOrders: false

PaymentSMSParams:
  SkipFITOrders: true

ConnectedAccountUserGroupParams:
  IsAARestricted: true
  AllowedUserGroupForAA:
    - 1 # INTERNAL
    - 2 # FNF
    - 7 # Connected Account

TxnNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-transaction-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 5
      MaxAttempts: 9
      TimeUnit: "Second"

DeclineCardTransactionsProcessingSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-card-txns-decline-data-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

DeclineDataAwsSftpBucket:
  AwsBucket: "epifi-staging-federal-debit-card-data"
  AclString: "private"
  SrcFolder: "in_data"
  DstFolder: "processed"

SavingsLedgerReconCacheConfig:
  IsCachingEnabled: true
  SavingsLedgerReconPrefix: "savings_ledger_recon_account_Id"
  CacheTTl: "2m"
  SavingsLedgerRedisOptions:
    IsSecureRedis: true
    Options:
      Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
      Password: "" ## empty string for no password
      DB: 14

OrderCacheConfig:
  IsCachingEnabled: true
  UseCaseToCacheConfigMap:
    # CacheConfig for RecentActivityCache used in GetRecentActivityOrdersFromCache for storing recent pay activity
    # (RecentPayActivityFromActorIdPrefix, RecentPayActivityToActorIdPrefix are cache prefixes used here).
    - RecentActivityCache:
        IsCachingEnabled: true
        CacheTTL: "5m"
    # CacheConfig for OrderCache used in GetById for storing order
    # (OrderKeyPrefix is cache prefix used here).
    - OrderCache:
        IsCachingEnabled: true
        CacheTTL: "2m"
  RedisOptions:
    IsSecureRedis: true
    Options:
      Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
      Password: "" ## empty string for no password
      DB: 14
      ClientName: order

TransactionCacheConfig:
  IsCachingEnabled: true
  UseCaseToCacheConfigMap:
    # CacheConfig for TransactionCache used in GetById for storing transaction
    # (TransactionKeyPrefix is cache prefix used here).
    - TransactionCache:
        IsCachingEnabled: false
        CacheTTL: "5m"

Tracing:
  Enable: false

FeatureFlags:
  EnableB2CCallbackSignalToCelestial: true
  EnableOnAppEnachExecutionInboundNotifProcessor: true
  EnableBypassCooldownForWhitelistedUser:
    IsFeatureRestricted: true
    AllowedUserGroups:
      - 20 # BYPASS_COOLDOWN_QA
  EnableAllTransactionForSelectedOrderStatesParams:  # feature flag to enable pending, failed and reversed txns in all txn page
    IsFeatureRestricted: false
  EnableRecentActivityForDifferentTxns: # feature flag to enable pending, failed and reversed txns in recent activity tab in home screen
    IsFeatureRestricted: false
  EnablePGDBDaoForAA: true
  EnableParameterizedTimelineEvents:
    IsFeatureRestricted: false

Events:
  IncomingCreditMaxPublishDelay: "2m"

OrderVpaVerificationPublisher:
  QueueName: "staging-unverified-vpa-queue"

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

DataReplicationParams:
  IsEnabled: false
  TargetDBName: "federal"
  SourceTables:
    - "transactions"

DeemedTransactionUPIEnquiryPublisher:
  QueueName: "staging-deemed-payment-upi-enquiry-queue"

ProcrastinatorWorkflowPublisher:
  QueueName: "staging-celestial-initiate-procrastinator-workflow-queue"

DeemedTransactionUpiEnquirySubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-deemed-payment-upi-enquiry-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 15
      TimeUnit: "Second"

NonTpapPspHandles: ["fede"]

FeatureReleaseConfig:
  FeatureConstraints:
    TXN_RECEIPT_SCREEN_V1:
      AppVersionConstraintConfig:
        MinAndroidVersion: 220
        MinIOSVersion: 220
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1
    HEALTH_ENGINE_FOR_INTRA_PAYMENTS:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    HEALTH_ENGINE_FOR_NEFT_PAYMENTS:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    HEALTH_ENGINE_FOR_IMPS_PAYMENTS:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    HEALTH_ENGINE_FOR_RTGS_PAYMENTS:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    BENEFICIARY_COOL_DOWN_RULE:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    REPORT_FRAUD_ORDER_RECEIPT:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_ENABLE_ACCOUNT_OPERATIONAL_STATUS_CHECK:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_ASSOCIATED_TRANSACTION_ORDER_RECEIPT:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
    FEATURE_DAILY_TOTAL_TRANSACTION_AMOUNT_LIMIT_ON_APP:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
    FEATURE_NUGGET_TRANSACTION_CHATBOT:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
    FEATURE_OC215A_SKIP_VALIDATE_ADDRESS:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 37 # PAY_EXPERIMENTAL

QuestSdk:
  Disable: false

QuestRedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 10
  ClientName: order-quest-sdk
  HystrixCommand:
    CommandName: "quest_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 500
      ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

ReportFraudConfig:
  TransactionProtocolToMaxDaysAvailableForReportFraud:
    CARD: 1.0
    IMPS: 1.0
    UPI: 1.0
