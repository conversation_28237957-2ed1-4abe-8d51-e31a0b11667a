package datafetcher

import (
	"context"
	"time"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"
	catalogPb "github.com/epifi/gamma/api/securities/catalog"

	mfPb "github.com/epifi/gamma/api/investment/mutualfund"
	mfCatalogPb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	investmentPkg "github.com/epifi/gamma/pkg/investment"

	"github.com/pkg/errors"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/epifigrpc"
	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	enumsPb "github.com/epifi/gamma/api/insights/networth/enums"
)

// WeeklyChangeDataFetcher fetches 7-day asset changes and adds minimal metadata.
// Asset types: Mutual Funds (required) and Indian Securities (best-effort).
// If MF data is missing => NOT_AVAILABLE. Window: last 7 days (inclusive).
type WeeklyChangeDataFetcher struct {
	networthClient         networthPb.NetWorthClient
	mfCatalogManagerClient mfCatalogPb.CatalogManagerClient
	catalogClient          catalogPb.SecuritiesCatalogClient
}

// NewWeeklyChangeDataFetcher constructs a new fetcher with the required clients.
func NewWeeklyChangeDataFetcher(
	networthClient networthPb.NetWorthClient,
	mfCatalogManagerClient mfCatalogPb.CatalogManagerClient,
	catalogClient catalogPb.SecuritiesCatalogClient,
) *WeeklyChangeDataFetcher {
	return &WeeklyChangeDataFetcher{
		networthClient:         networthClient,
		mfCatalogManagerClient: mfCatalogManagerClient,
		catalogClient:          catalogClient,
	}
}

// GetRawDetails returns 7-day day-change plus metadata.
// MF is required for availability; Indian stocks are best-effort enrichment.
func (f *WeeklyChangeDataFetcher) GetRawDetails(ctx context.Context, req *GetRawDetailsRequest) (*analyserVariablePb.RawDetails, error) {
	assetDayChangeResp, getAssetsDayChangeErr := f.networthClient.GetAssetsDayChange(ctx, &networthPb.GetAssetsDayChangeRequest{
		ActorId: req.ActorId,
		// Last 7 calendar days window for the weekly report
		InitialDate: timestampPb.New(time.Now().AddDate(0, 0, -7)),
		FinalDate:   timestampPb.Now(),
		AssetTypes:  []enumsPb.AssetType{enumsPb.AssetType_ASSET_TYPE_MUTUAL_FUND, enumsPb.AssetType_ASSET_TYPE_INDIAN_SECURITIES},
	})
	if rpcErr := epifigrpc.RPCError(assetDayChangeResp, getAssetsDayChangeErr); rpcErr != nil {
		return nil, errors.Wrap(rpcErr, "error fetching assets day change for weekly change report")
	}
	// Collecting mutual fund metadata (mandatory for availability)
	mutualFundChangeResp, isMutualFundAvailable := assetDayChangeResp.GetAssetTypeToDayChangeResponseMap()[enumsPb.AssetType_ASSET_TYPE_MUTUAL_FUND.String()]
	if !isMutualFundAvailable {
		return &analyserVariablePb.RawDetails{
			RawDetailsName:       analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_WEEKLY_CHANGE,
			RawDetailsDataStatus: analyserVariablePb.RawDetailsDataStatus_RAW_DETAILS_DATA_STATUS_NOT_AVAILABLE,
		}, nil
	}
	allMfAssetIds := make([]string, 0)
	for _, assetChange := range mutualFundChangeResp.GetAssetsValueChange() {
		allMfAssetIds = append(allMfAssetIds, assetChange.GetAssetId())
	}
	if len(allMfAssetIds) == 0 {
		return &analyserVariablePb.RawDetails{
			RawDetailsName:       analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_WEEKLY_CHANGE,
			RawDetailsDataStatus: analyserVariablePb.RawDetailsDataStatus_RAW_DETAILS_DATA_STATUS_NOT_AVAILABLE,
		}, nil
	}
	metadataMap := make(map[string]*analyserVariablePb.SecurityMetadata)
	var err error
	metadataMap, err = f.getMutualFundLogosAndNames(ctx, allMfAssetIds, metadataMap)
	if err != nil {
		return nil, err
	}

	// Collecting Indian stocks metadata (optional best-effort enrichment)
	indianStocksChangeResp, isIndianStocksAvailable := assetDayChangeResp.GetAssetTypeToDayChangeResponseMap()[enumsPb.AssetType_ASSET_TYPE_INDIAN_SECURITIES.String()]
	allIndianStocksAssetIds := make([]string, 0)
	if isIndianStocksAvailable {
		for _, assetChange := range indianStocksChangeResp.GetAssetsValueChange() {
			allIndianStocksAssetIds = append(allIndianStocksAssetIds, assetChange.GetAssetId())
		}
	}
	// adding indian stocks metadata only if there are any assets
	if len(allIndianStocksAssetIds) > 0 {
		metadataMap = f.getIndianStocksLogosAndNames(ctx, allIndianStocksAssetIds, metadataMap)
	}

	return &analyserVariablePb.RawDetails{
		RawDetailsName: analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_WEEKLY_CHANGE,
		Variable: &analyserVariablePb.RawDetails_AssetsDayChange{
			AssetsDayChange: &analyserVariablePb.AssetsDayChangeWrapper{
				AssetResponseMap:    assetDayChangeResp.GetAssetTypeToDayChangeResponseMap(),
				SecurityMetadataMap: metadataMap,
			},
		},
		RawDetailsDataStatus: analyserVariablePb.RawDetailsDataStatus_RAW_DETAILS_DATA_STATUS_AVAILABLE,
	}, nil
}

// getMutualFundLogosAndNames enriches the metadata map with Logo URL and display name for the
// provided mutual fund IDs. AMC icons are resolved via a static map in pkg/investment.
//
// Returns the updated metadata map or an error if the catalog RPC fails.
func (f *WeeklyChangeDataFetcher) getMutualFundLogosAndNames(ctx context.Context, assetIds []string, mfMetadataMap map[string]*analyserVariablePb.SecurityMetadata) (map[string]*analyserVariablePb.SecurityMetadata, error) {
	res, err := f.mfCatalogManagerClient.GetMutualFunds(ctx, &mfCatalogPb.GetMutualFundsRequest{
		FundIdentifier: mfPb.MutualFundIdentifier_MUTUAL_FUND_IDENTIFIER_ID,
		Ids:            assetIds,
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		return nil, errors.Wrapf(rpcErr, "error fetching mutual fund details for ids %v", assetIds)
	}
	for _, mfDetails := range res.GetMutualFunds() {
		mfMetadataMap[mfDetails.GetId()] = &analyserVariablePb.SecurityMetadata{
			LogoUrl:      investmentPkg.IconsForAmc[mfDetails.GetAmc()],
			SecurityName: mfDetails.GetNameData().GetLongName(),
		}
	}

	return mfMetadataMap, nil
}

// getIndianStocksLogosAndNames enriches metadata for Indian securities.
// Non-RecordNotFound errors are logged and ignored; RecordNotFound is a no-op.
func (f *WeeklyChangeDataFetcher) getIndianStocksLogosAndNames(ctx context.Context, allAssetIds []string, securityMetadataMap map[string]*analyserVariablePb.SecurityMetadata) map[string]*analyserVariablePb.SecurityMetadata {
	securitiesResp, getSecuritiesErr := f.catalogClient.GetSecurityListings(ctx, &catalogPb.GetSecurityListingsRequest{
		Identifiers: &catalogPb.GetSecurityListingsRequest_ExternalIds_{
			ExternalIds: &catalogPb.GetSecurityListingsRequest_ExternalIds{
				ExternalIds: allAssetIds,
			},
		},
		SecurityFields: []catalogPb.SecurityFieldMask{
			catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_LOGO_URL,
			catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_NAME,
		},
		SecurityListingFields: []catalogPb.SecurityListingFieldMask{
			catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_SECURITY_ID,
		},
	})
	if rpcErr := epifigrpc.RPCError(securitiesResp, getSecuritiesErr); rpcErr != nil && !securitiesResp.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error fetching bulk security metadata", zap.Error(rpcErr))
		return securityMetadataMap // Incase of error from securities RPC, return the map as received
	}

	if securitiesResp.GetStatus().IsRecordNotFound() {
		return securityMetadataMap // No securities found, return the map as received
	}

	for _, securityAndSecurityListing := range securitiesResp.GetSecurityAndSecurityListings() {
		securityMetadataMap[securityAndSecurityListing.GetSecurityListing().GetExternalId()] = &analyserVariablePb.SecurityMetadata{
			LogoUrl:      securityAndSecurityListing.GetSecurity().GetLogoUrl(),
			SecurityName: securityAndSecurityListing.GetSecurity().GetSecurityName(),
		}
	}
	return securityMetadataMap
}
