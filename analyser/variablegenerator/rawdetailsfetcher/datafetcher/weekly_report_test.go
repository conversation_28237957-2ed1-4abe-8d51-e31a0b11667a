package datafetcher_test

import (
	"context"
	"errors"
	"testing"

	mockCatalogPb "github.com/epifi/gamma/api/securities/catalog/mocks"

	"github.com/go-test/deep"
	"github.com/golang/mock/gomock"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/analyser/variablegenerator/rawdetailsfetcher/datafetcher"
	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	enumsPb "github.com/epifi/gamma/api/insights/networth/enums"
	mockNetworthPb "github.com/epifi/gamma/api/insights/networth/mocks"
	mfPb "github.com/epifi/gamma/api/investment/mutualfund"
	mfCatalogPb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	mockCatalogManagerPb "github.com/epifi/gamma/api/investment/mutualfund/catalog/mocks"
)

type mockMFWeeklyChangeFields struct {
	mockNetworthClient       *mockNetworthPb.MockNetWorthClient
	mockCatalogManagerClient *mockCatalogManagerPb.MockCatalogManagerClient
	mockCatalogClient        *mockCatalogPb.MockSecuritiesCatalogClient
}

func initMFWeeklyChangeMocks(ctrl *gomock.Controller) *mockMFWeeklyChangeFields {
	return &mockMFWeeklyChangeFields{
		mockNetworthClient:       mockNetworthPb.NewMockNetWorthClient(ctrl),
		mockCatalogManagerClient: mockCatalogManagerPb.NewMockCatalogManagerClient(ctrl),
	}
}

func TestMFWeeklyChangeDataFetcher_GetRawDetails(t *testing.T) {
	testActorId := "test-actor-id"
	testAssetId := "asset-id-1"

	tests := []struct {
		name    string
		before  func(m *mockMFWeeklyChangeFields)
		want    *analyserVariablePb.RawDetails
		wantErr bool
	}{
		{
			name: "Successfully get MF weekly change raw details",
			before: func(m *mockMFWeeklyChangeFields) {
				assetChange := &networthPb.AssetValueChange{
					AssetId: testAssetId,
				}
				dayChangeResp := &networthPb.AssetTypeDayChangeResponse{
					AssetsValueChange: []*networthPb.AssetValueChange{assetChange},
				}
				m.mockNetworthClient.EXPECT().GetAssetsDayChange(gomock.Any(), gomock.Any()).Return(&networthPb.GetAssetsDayChangeResponse{
					Status: rpcPb.StatusOk(),
					AssetTypeToDayChangeResponseMap: map[string]*networthPb.AssetTypeDayChangeResponse{
						enumsPb.AssetType_ASSET_TYPE_MUTUAL_FUND.String(): dayChangeResp,
					},
				}, nil)
				m.mockCatalogManagerClient.EXPECT().GetMutualFunds(gomock.Any(), gomock.Any()).Return(
					&mfCatalogPb.GetMutualFundsResponse{
						Status: rpcPb.StatusOk(),
						MutualFunds: map[string]*mfPb.MutualFund{
							testAssetId: {
								Id: testAssetId,
								NameData: &mfPb.FundNameMetadata{
									LongName: "Test MF",
								},
								Amc: mfPb.Amc_ICICI_PRUDENTIAL,
							},
						},
					}, nil,
				)
			},
			want: &analyserVariablePb.RawDetails{
				RawDetailsName: analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_WEEKLY_CHANGE,
				Variable: &analyserVariablePb.RawDetails_AssetsDayChange{
					AssetsDayChange: &analyserVariablePb.AssetsDayChangeWrapper{
						AssetResponseMap: map[string]*networthPb.AssetTypeDayChangeResponse{
							enumsPb.AssetType_ASSET_TYPE_MUTUAL_FUND.String(): {
								AssetsValueChange: []*networthPb.AssetValueChange{{AssetId: testAssetId}},
							},
						},
						SecurityMetadataMap: map[string]*analyserVariablePb.SecurityMetadata{
							testAssetId: {
								SecurityName: "Test MF",
								LogoUrl:      "https://epifi-icons.pointz.in/amc_logos/ipru_logo.png",
							},
						},
					},
				},
				RawDetailsDataStatus: analyserVariablePb.RawDetailsDataStatus_RAW_DETAILS_DATA_STATUS_AVAILABLE,
			},
		},
		{
			name: "Assets with empty change list - returns NOT_AVAILABLE status",
			before: func(m *mockMFWeeklyChangeFields) {
				dayChangeResp := &networthPb.AssetTypeDayChangeResponse{
					AssetsValueChange: []*networthPb.AssetValueChange{},
				}
				m.mockNetworthClient.EXPECT().GetAssetsDayChange(gomock.Any(), gomock.Any()).Return(&networthPb.GetAssetsDayChangeResponse{
					Status: rpcPb.StatusOk(),
					AssetTypeToDayChangeResponseMap: map[string]*networthPb.AssetTypeDayChangeResponse{
						enumsPb.AssetType_ASSET_TYPE_MUTUAL_FUND.String(): dayChangeResp,
					},
				}, nil)
			},
			want: &analyserVariablePb.RawDetails{
				RawDetailsName:       analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_WEEKLY_CHANGE,
				RawDetailsDataStatus: analyserVariablePb.RawDetailsDataStatus_RAW_DETAILS_DATA_STATUS_NOT_AVAILABLE,
			},
		},
		{
			name: "Error while fetching assets day change",
			before: func(m *mockMFWeeklyChangeFields) {
				m.mockNetworthClient.EXPECT().GetAssetsDayChange(gomock.Any(), gomock.Any()).Return(&networthPb.GetAssetsDayChangeResponse{
					Status: rpcPb.StatusInternal(),
				}, errors.New("networth service error"))
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mocks := initMFWeeklyChangeMocks(ctrl)
			if tt.before != nil {
				tt.before(mocks)
			}

			fetcher := datafetcher.NewWeeklyChangeDataFetcher(mocks.mockNetworthClient, mocks.mockCatalogManagerClient, mocks.mockCatalogClient)
			got, err := fetcher.GetRawDetails(context.Background(), &datafetcher.GetRawDetailsRequest{
				ActorId: testActorId,
			})

			if (err != nil) != tt.wantErr {
				t.Errorf("GetRawDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr && got != nil {
				if diff := deep.Equal(got, tt.want); len(diff) > 0 {
					t.Errorf("GetRawDetails() diff = %v", diff)
				}
			}
		})
	}
}
