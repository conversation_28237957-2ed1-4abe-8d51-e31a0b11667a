package rawdetailsfetcher

import (
	"context"
	"fmt"

	"github.com/google/wire"

	"github.com/epifi/gamma/analyser/variablegenerator/rawdetailsfetcher/datafetcher"
	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"
	insightsPkg "github.com/epifi/gamma/insights/pkg"
)

var RawDetailsFactoryWireSet = wire.NewSet(NewRawDetailsFactory, wire.Bind(new(IRawDetailsFactory), new(*RawDetailsFactory)),
	datafetcher.NewUserDeclaredMonthlyIncomeDataFetcher,
	datafetcher.NewMfHistoryPortfolioDataFetcher,
	datafetcher.NewUserDeclaredCompanyDetailsDataFetcher,
	datafetcher.NewUserDeclaredDobDataFetcher,
	insightsPkg.InsightsDobProcessorWireSet,
	datafetcher.NewMfHPortfolioAnalyticsDataFetcher,
	datafetcher.NewPortfolioSummaryDataFetcher,
	datafetcher.NewMfLowTrackingErrorIndexFundDataFetcher,
	datafetcher.NewNetworthDetailsFetcher,
	datafetcher.NewMfAssetCategoryDetailsDataFetcher,
	datafetcher.NewMfInvestmentActivitiesDataFetcher,
	datafetcher.NewAssetsDayChangeDataFetcher,
	datafetcher.NewWeeklyChangeDataFetcher,
)

//go:generate mockgen -source=./factory.go -destination=mocks/mock.go -package=mocks
type IRawDetailsFactory interface {
	GetRawDetails(ctx context.Context, req *datafetcher.GetRawDetailsRequest) (*analyserVariablePb.RawDetails, error)
}

type RawDetailsFactory struct {
	rawDetailsProcessorMap map[analyserVariablePb.RawDetailsName]datafetcher.IRawDetailsDataFetcher
}

func NewRawDetailsFactory(userDeclaredMonthlyIncomeDataFetcher *datafetcher.UserDeclaredMonthlyIncomeDataFetcher,
	mfPortfolioHistory *datafetcher.MfHistoryPortfolioDataFetcher,
	userDeclaredCompanyDetailsDataFetcher *datafetcher.UserDeclaredCompanyDetailsDataFetcher,
	userDeclaredDobDataFetcher *datafetcher.UserDeclaredDobDataFetcher,
	mfPortfolioAnalyticsDataFetcher *datafetcher.MfPortfolioAnalyticsDataFetcher,
	portfolioSummaryDataFetcher *datafetcher.PortfolioSummaryDataFetcher,
	mfLowTrackingErrorIndexFundDataFetcher *datafetcher.MfLowTrackingErrorIndexFundDataFetcher,
	networthDetailsFetcher *datafetcher.NetworthDetailsFetcher,
	mfAssetCategoryDetailsDataFetcher *datafetcher.MfAssetCategoryDetailsDataFetcher,
	mfInvestmentActivitiesDataFetcher *datafetcher.MfInvestmentActivitiesDataFetcher,
	assetsDayChangeDataFetcher *datafetcher.AssetsDayChangeDataFetcher,
	weeklyChangeDataFetcher *datafetcher.WeeklyChangeDataFetcher,
) *RawDetailsFactory {
	rawDetailsProcessorMap := make(map[analyserVariablePb.RawDetailsName]datafetcher.IRawDetailsDataFetcher)
	rawDetailsProcessorMap[analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_USER_DECLARED_MONTHLY_INCOME] = userDeclaredMonthlyIncomeDataFetcher
	rawDetailsProcessorMap[analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_MF_PORTFOLIO_HISTORY] = mfPortfolioHistory
	rawDetailsProcessorMap[analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_USER_DECLARED_COMPANY_DETAILS] = userDeclaredCompanyDetailsDataFetcher
	rawDetailsProcessorMap[analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_USER_DOB] = userDeclaredDobDataFetcher
	rawDetailsProcessorMap[analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_MF_PORTFOLIO_ANALYTICS] = mfPortfolioAnalyticsDataFetcher
	rawDetailsProcessorMap[analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_PORTFOLIO_SUMMARY] = portfolioSummaryDataFetcher
	rawDetailsProcessorMap[analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_MF_LOW_TRACKING_ERROR_INDEX_FUND] = mfLowTrackingErrorIndexFundDataFetcher
	rawDetailsProcessorMap[analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_NETWORTH_DETAILS] = networthDetailsFetcher
	rawDetailsProcessorMap[analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_MF_ASSET_CATEGORY_DETAILS] = mfAssetCategoryDetailsDataFetcher
	rawDetailsProcessorMap[analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_MF_INVESTMENT_ACTIVITIES] = mfInvestmentActivitiesDataFetcher
	rawDetailsProcessorMap[analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_ASSETS_DAY_CHANGE] = assetsDayChangeDataFetcher
	rawDetailsProcessorMap[analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_WEEKLY_CHANGE] = weeklyChangeDataFetcher
	return &RawDetailsFactory{
		rawDetailsProcessorMap: rawDetailsProcessorMap,
	}
}

func (r *RawDetailsFactory) GetRawDetails(ctx context.Context, req *datafetcher.GetRawDetailsRequest) (*analyserVariablePb.RawDetails, error) {
	rawDetailsProcessor, ok := r.rawDetailsProcessorMap[req.RawDetailsName]
	if !ok {
		return nil, fmt.Errorf("unable to find raw details processor %v", req.RawDetailsName)
	}
	return rawDetailsProcessor.GetRawDetails(ctx, req)
}
