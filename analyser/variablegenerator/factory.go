package variablegenerator

import (
	"context"
	"fmt"

	"github.com/google/wire"

	variableprocessor2 "github.com/epifi/gamma/analyser/variablegenerator/variableprocessor"
	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"
)

var AnalysisVariableFactoryWireSet = wire.NewSet(NewAnalysisVariableFactory, wire.Bind(new(IAnalysisVariableFactory), new(*AnalysisVariableFactory)), variableprocessor2.NewUserDeclaredMonthlySalaryVariableProcessor,
	variableprocessor2.NewMfMonthlyInvestmentStatisticsVariableProcessor, variableprocessor2.NewUserDeclaredCompanyDetailsVariableProcessor, variableprocessor2.NewUserDeclaredDobVariableProcessor,
	variableprocessor2.NewMfFundsBreakdownVariableGenerator, variableprocessor2.NewPortfolioSummaryVariableGenerator, variableprocessor2.NewMfPortfolioPerformanceVariableProcessor,
	variableprocessor2.NewMfInvestmentActivitiesVariableProcessor, variableprocessor2.NewMfAssetCategoryDetailsVariableProcessor, variableprocessor2.NewNetworthDetailsVariableProcessor,
	variableprocessor2.NewIndianStocksDistributionVariableGenerator, variableprocessor2.NewMFWeeklyReportVariableGenerator, variableprocessor2.NewGoldDistributionVariableGenerator,
	variableprocessor2.NewIndianStocksWeeklyReportVariableGenerator,
)

//go:generate mockgen -source=./factory.go -destination=mocks/mock_factory.go -package=mocks
type IAnalysisVariableFactory interface {
	GenerateAnalysisVariable(ctx context.Context, req *variableprocessor2.GenerateAnalysisVariableRequest) (*analyserVariablePb.AnalysisVariable, error)
}

type AnalysisVariableFactory struct {
	analysisVariableProcessor map[analyserVariablePb.AnalysisVariableName]variableprocessor2.IAnalysisVariableProcessor
}

func NewAnalysisVariableFactory(userDeclaredMonthly *variableprocessor2.UserDeclaredMonthlyIncomeVariableProcessor, mfPortfolioHistory *variableprocessor2.MfMonthlyInvestmentStatisticsVariableProcessor,
	userDeclCompanyDetails *variableprocessor2.UserDeclaredCompanyDetailsVariableProcessor, userDeclaredDob *variableprocessor2.UserDeclaredDobVariableProcessor, mfSchemeAnalytics *variableprocessor2.MfFundsBreakdownVariableGenerator,
	portfolioSummary *variableprocessor2.PortfolioSummaryVariableGenerator, mfPortfolioPerformance *variableprocessor2.MfPortfolioPerformanceVariableProcessor,
	mfInvestmentActivities *variableprocessor2.MfInvestmentActivitiesVariableProcessor, mfAssetCategoryDetails *variableprocessor2.MfAssetCategoryDetailsVariableProcessor,
	networthDetails *variableprocessor2.NetworthDetailsVariableProcessor, indianStocksGenerator *variableprocessor2.IndianStocksDistributionVariableGenerator,
	mfWeeklyReportGenerator *variableprocessor2.MFWeeklyReportVariableGenerator, goldDistributionGenerator *variableprocessor2.GoldDistributionVariableGenerator,
	indianStocksWeeklyReportGenerator *variableprocessor2.IndianStocksWeeklyReportVariableGenerator,
) *AnalysisVariableFactory {
	analysisVariableProcessorMap := make(map[analyserVariablePb.AnalysisVariableName]variableprocessor2.IAnalysisVariableProcessor, 0)
	analysisVariableProcessorMap[analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_USER_DECLARED_MONTHLY_INCOME] = userDeclaredMonthly
	analysisVariableProcessorMap[analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_MONTHLY_INVESTMENT_STATISTICS] = mfPortfolioHistory
	analysisVariableProcessorMap[analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_USER_DECLARED_COMPANY_DETAILS] = userDeclCompanyDetails
	analysisVariableProcessorMap[analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_USER_DOB] = userDeclaredDob
	analysisVariableProcessorMap[analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS] = mfSchemeAnalytics
	analysisVariableProcessorMap[analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_PORTFOLIO_SUMMARY] = portfolioSummary
	analysisVariableProcessorMap[analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_DAILY_MARKET_COMPARISON_WITH_PORTFOLIO] = mfPortfolioPerformance
	analysisVariableProcessorMap[analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_INVESTMENT_ACTIVITIES] = mfInvestmentActivities
	analysisVariableProcessorMap[analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_ASSET_CATEGORY_DETAILS] = mfAssetCategoryDetails
	analysisVariableProcessorMap[analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_NETWORTH_DETAILS] = networthDetails
	analysisVariableProcessorMap[analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_ASSETS_DISTRIBUTION] = indianStocksGenerator
	analysisVariableProcessorMap[analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_WEEKLY_DISTRIBUTION] = mfWeeklyReportGenerator
	analysisVariableProcessorMap[analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_GOLD_ASSETS_DISTRIBUTION] = goldDistributionGenerator
	analysisVariableProcessorMap[analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_WEEKLY_DISTRIBUTION] = indianStocksWeeklyReportGenerator
	return &AnalysisVariableFactory{
		analysisVariableProcessor: analysisVariableProcessorMap,
	}
}

func (f *AnalysisVariableFactory) GenerateAnalysisVariable(ctx context.Context, req *variableprocessor2.GenerateAnalysisVariableRequest) (*analyserVariablePb.AnalysisVariable, error) {
	processor, ok := f.analysisVariableProcessor[req.AnalysisVariable]
	if !ok {
		return nil, fmt.Errorf("unhandled analysis variable %v", req.AnalysisVariable)
	}
	return processor.GenerateAnalysisVariable(ctx, req)
}
