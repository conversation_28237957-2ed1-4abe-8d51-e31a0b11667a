package variableprocessor

import (
	"context"
	"testing"

	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	enumsPb "github.com/epifi/gamma/api/insights/networth/enums"
)

func TestIndianStocksWeeklyReportVariableGenerator_GenerateAnalysisVariable(t *testing.T) {
	tests := []struct {
		name    string
		req     *GenerateAnalysisVariableRequest
		want    *analyserVariablePb.AnalysisVariable
		wantErr bool
	}{
		{
			name: "Happy path: returns Indian Stocks weekly distribution",
			req: &GenerateAnalysisVariableRequest{
				RawDetailsMap: map[analyserVariablePb.RawDetailsName]*analyserVariablePb.RawDetails{
					analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_WEEKLY_CHANGE: {
						Variable: &analyserVariablePb.RawDetails_AssetsDayChange{
							AssetsDayChange: &analyserVariablePb.AssetsDayChangeWrapper{
								AssetResponseMap: map[string]*networthPb.AssetTypeDayChangeResponse{
									enumsPb.AssetType_ASSET_TYPE_INDIAN_SECURITIES.String(): {
										// Add fields as needed for test
									},
								},
								SecurityMetadataMap: map[string]*analyserVariablePb.SecurityMetadata{},
							},
						},
					},
				},
			},
			want: &analyserVariablePb.AnalysisVariable{
				AnalysisVariableName: analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_WEEKLY_DISTRIBUTION,
				Variable: &analyserVariablePb.AnalysisVariable_IndianStocksWeeklyDistribution{
					IndianStocksWeeklyDistribution: &analyserVariablePb.AssetTypeDayChangeResponseWrapper{
						DayChangeResponse:   &networthPb.AssetTypeDayChangeResponse{},
						SecurityMetadataMap: map[string]*analyserVariablePb.SecurityMetadata{},
					},
				},
				AnalysisVariableState: analyserVariablePb.AnalysisVariableState_ANALYSIS_VARIABLE_STATE_AVAILABLE,
			},
		},
		{
			name: "Missing INDIAN_STOCKS_WEEKLY_CHANGE raw details returns error",
			req: &GenerateAnalysisVariableRequest{
				RawDetailsMap: map[analyserVariablePb.RawDetailsName]*analyserVariablePb.RawDetails{},
			},
			wantErr: true,
		},
		{
			name: "Missing indian stocks entry in asset response map returns data missing state",
			req: &GenerateAnalysisVariableRequest{
				RawDetailsMap: map[analyserVariablePb.RawDetailsName]*analyserVariablePb.RawDetails{
					analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_WEEKLY_CHANGE: {
						Variable: &analyserVariablePb.RawDetails_AssetsDayChange{
							AssetsDayChange: &analyserVariablePb.AssetsDayChangeWrapper{
								AssetResponseMap:    map[string]*networthPb.AssetTypeDayChangeResponse{},
								SecurityMetadataMap: map[string]*analyserVariablePb.SecurityMetadata{},
							},
						},
					},
				},
			},
			want: &analyserVariablePb.AnalysisVariable{
				AnalysisVariableName:  analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_WEEKLY_DISTRIBUTION,
				AnalysisVariableState: analyserVariablePb.AnalysisVariableState_ANALYSIS_VARIABLE_STATE_DATA_MISSING,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			g := NewIndianStocksWeeklyReportVariableGenerator()
			got, err := g.GenerateAnalysisVariable(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GenerateAnalysisVariable() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("GenerateAnalysisVariable() got and want are different. \ngot :%v\nwant:%v\ndiff:%v", got, tt.want, diff)
			}
		})
	}
}
