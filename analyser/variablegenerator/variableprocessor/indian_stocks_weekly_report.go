// nolint:dupl
package variableprocessor

import (
	"context"
	"fmt"

	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"
	enumsPb "github.com/epifi/gamma/api/insights/networth/enums"
)

type IndianStocksWeeklyReportVariableGenerator struct{}

func NewIndianStocksWeeklyReportVariableGenerator() *IndianStocksWeeklyReportVariableGenerator {
	return &IndianStocksWeeklyReportVariableGenerator{}
}

func (p *IndianStocksWeeklyReportVariableGenerator) GenerateAnalysisVariable(ctx context.Context, req *GenerateAnalysisVariableRequest) (*analyserVariablePb.AnalysisVariable, error) {
	assetsDayChanges, ok := req.RawDetailsMap[analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_WEEKLY_CHANGE]
	if !ok {
		return nil, fmt.Errorf("unable to find raw %v", analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_WEEKLY_CHANGE)
	}
	indianStocksWeeklyChangeResp, ok := assetsDayChanges.GetAssetsDayChange().GetAssetResponseMap()[enumsPb.AssetType_ASSET_TYPE_INDIAN_SECURITIES.String()]
	if !ok {
		return &analyserVariablePb.AnalysisVariable{
			AnalysisVariableName:  analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_WEEKLY_DISTRIBUTION,
			AnalysisVariableState: analyserVariablePb.AnalysisVariableState_ANALYSIS_VARIABLE_STATE_DATA_MISSING,
		}, nil
	}
	return &analyserVariablePb.AnalysisVariable{
		AnalysisVariableName: analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_WEEKLY_DISTRIBUTION,
		Variable: &analyserVariablePb.AnalysisVariable_IndianStocksWeeklyDistribution{
			IndianStocksWeeklyDistribution: &analyserVariablePb.AssetTypeDayChangeResponseWrapper{
				DayChangeResponse:   indianStocksWeeklyChangeResp,
				SecurityMetadataMap: assetsDayChanges.GetAssetsDayChange().GetSecurityMetadataMap(),
			},
		},
		AnalysisVariableState: analyserVariablePb.AnalysisVariableState_ANALYSIS_VARIABLE_STATE_AVAILABLE,
	}, nil
}
