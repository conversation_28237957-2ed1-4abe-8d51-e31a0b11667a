// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"context"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/kinesis"
	kinesis2 "github.com/epifi/be-common/pkg/aws/kinesis"
	config2 "github.com/epifi/be-common/pkg/aws/v2/config"
	"github.com/epifi/be-common/pkg/cache"
	types2 "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/lock"
	"github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/pkg/stream/hash/murmur2"
	"github.com/epifi/gamma/analyser/config"
	"github.com/epifi/gamma/analyser/config/genconf"
	"github.com/epifi/gamma/analyser/consumer"
	"github.com/epifi/gamma/analyser/dao"
	"github.com/epifi/gamma/analyser/dao/hybrid"
	"github.com/epifi/gamma/analyser/dao/poller"
	"github.com/epifi/gamma/analyser/dataprovider"
	"github.com/epifi/gamma/analyser/developer"
	"github.com/epifi/gamma/analyser/developer/processor"
	"github.com/epifi/gamma/analyser/dynamicelements"
	aa2 "github.com/epifi/gamma/analyser/internal/aa"
	"github.com/epifi/gamma/analyser/internal/category"
	merchant2 "github.com/epifi/gamma/analyser/internal/merchant"
	"github.com/epifi/gamma/analyser/investment"
	"github.com/epifi/gamma/analyser/investment/calc"
	consumer2 "github.com/epifi/gamma/analyser/investment/consumer"
	processor2 "github.com/epifi/gamma/analyser/investment/consumer/processor"
	dao2 "github.com/epifi/gamma/analyser/investment/dao"
	"github.com/epifi/gamma/analyser/investment/datacollector"
	factory2 "github.com/epifi/gamma/analyser/investment/datacollector/asset_details/factory"
	"github.com/epifi/gamma/analyser/investment/datacollector/nav/factory"
	"github.com/epifi/gamma/analyser/investment/processor/mutualfund"
	cache2 "github.com/epifi/gamma/analyser/investment/processor/mutualfund/cache"
	"github.com/epifi/gamma/analyser/investment/processor/mutualfund/mfcentral"
	"github.com/epifi/gamma/analyser/investment/processor/mutualfund/smallcase"
	"github.com/epifi/gamma/analyser/txnaggregates"
	aa3 "github.com/epifi/gamma/analyser/txnaggregates/aa"
	"github.com/epifi/gamma/analyser/txnaggregates/transaction"
	"github.com/epifi/gamma/analyser/variablegenerator"
	"github.com/epifi/gamma/analyser/variablegenerator/rawdetailsfetcher"
	"github.com/epifi/gamma/analyser/variablegenerator/rawdetailsfetcher/datafetcher"
	"github.com/epifi/gamma/analyser/variablegenerator/variableprocessor"
	"github.com/epifi/gamma/analyser/wire/types"
	"github.com/epifi/gamma/api/actor"
	investment2 "github.com/epifi/gamma/api/analyser/investment"
	"github.com/epifi/gamma/api/categorizer"
	"github.com/epifi/gamma/api/insights/epf"
	"github.com/epifi/gamma/api/insights/networth"
	"github.com/epifi/gamma/api/insights/user_declaration"
	"github.com/epifi/gamma/api/investment/mutualfund/catalog"
	"github.com/epifi/gamma/api/investment/mutualfund/external"
	"github.com/epifi/gamma/api/merchant"
	"github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/order/aa"
	"github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/api/paymentinstrument/account_pi"
	"github.com/epifi/gamma/api/preapprovedloan"
	catalog2 "github.com/epifi/gamma/api/securities/catalog"
	"github.com/epifi/gamma/api/tiering"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/vendorgateway/wealth/mutualfund/analytics"
	"github.com/epifi/gamma/insights/pkg"
	"github.com/epifi/gamma/pkg/pinot"
)

// Injectors from wire.go:

func InitialiseAnalyserConsumerService(ctx context.Context, analyserConf *config.Config, dynconf *genconf.Config, actorClient actor.ActorClient, piClient paymentinstrument.PiClient, merchantClient merchant.MerchantServiceClient, accountPiClient account_pi.AccountPIRelationClient, orderClient order.OrderServiceClient, paymentClient payment.PaymentClient, categorizerClient categorizer.TxnCategorizerClient, aaClient aa.AccountAggregatorClient, tieringClient tiering.TieringClient) (*consumer.Service, error) {
	defaultTime := datetime.NewDefaultTime()
	processor := category.NewCategoryProcessor(categorizerClient)
	aaProcessor := aa2.NewAccountAggregatorProcessor(aaClient)
	awsConfig, err := awsV2ConfigProvider(ctx, analyserConf)
	if err != nil {
		return nil, err
	}
	client := kinesisClientProvider(awsConfig)
	transactionsProducer, err := transactionsProducerProvider(ctx, analyserConf, client)
	if err != nil {
		return nil, err
	}
	aaTransactionsProducer, err := aaTransactionsProducerProvider(ctx, analyserConf, client)
	if err != nil {
		return nil, err
	}
	transactionsUpdateProducer, err := transactionsUpdateProducerProvider(ctx, analyserConf, client)
	if err != nil {
		return nil, err
	}
	aaTransactionsUpdateProducer, err := aaTransactionsUpdateProducerProvider(ctx, analyserConf, client)
	if err != nil {
		return nil, err
	}
	service := consumer.NewService(dynconf, defaultTime, actorClient, piClient, merchantClient, accountPiClient, orderClient, paymentClient, categorizerClient, processor, aaProcessor, transactionsProducer, aaTransactionsProducer, transactionsUpdateProducer, aaTransactionsUpdateProducer, tieringClient)
	return service, nil
}

func InitialiseTxnAggregatesService(ctx context.Context, analyserConf *config.Config, categorizerClient categorizer.TxnCategorizerClient, merchantClient merchant.MerchantServiceClient, dynConf *genconf.Config, redisClient types.AnalyserRedisStore) (*txnaggregates.Service, error) {
	string2 := AnalyserConfigAppEnvProvider(analyserConf)
	client, err := pinotClientProvider(analyserConf, string2)
	if err != nil {
		return nil, err
	}
	pinotPollingConfig := pinotPollerConfigProvider(dynConf)
	cacheStorage := redisCacheClientProvider(redisClient)
	client2 := types.AnalyserRedisStoreRedisClientProvider(redisClient)
	clock := lock.NewRealClockProvider()
	uuidGenerator := idgen.NewUuidGenerator()
	redisV9LockManager := lock.NewRedisV9LockManager(client2, clock, uuidGenerator)
	updatedAtPollerImpl := poller.NewUpdatedAtPoller(pinotPollingConfig, client, cacheStorage, redisV9LockManager)
	analyserTransactionDaoPinot := dao.NewAnalyserTransactionDao(ctx, client, updatedAtPollerImpl)
	analyserTransactionUpdateDaoPinot := dao.NewAnalyserTransactionUpdateDao(client)
	analyserTransactionHybridDaoPinot := hybrid.NewAnalyserTransactionHybridDao(analyserTransactionDaoPinot, analyserTransactionUpdateDaoPinot)
	processor := category.NewCategoryProcessor(categorizerClient)
	transactionProcessor := transaction.NewAnalyserTransactionsProcessor(analyserTransactionDaoPinot, analyserTransactionHybridDaoPinot, processor)
	analyserAATransactionDaoPinot := dao.NewAnalyserAATransactionDao(ctx, client, updatedAtPollerImpl)
	analyserAATransactionUpdateDaoPinot := dao.NewAnalyserAATransactionUpdateDao(client)
	analyserAATransactionHybridDaoPinot := hybrid.NewAnalyserAATransactionHybridDao(analyserAATransactionDaoPinot, analyserAATransactionUpdateDaoPinot)
	aaProcessor := aa3.NewAATransactionsProcessor(analyserAATransactionDaoPinot, analyserAATransactionHybridDaoPinot, processor)
	merchantProcessor := merchant2.NewMerchantProcessor(merchantClient)
	service := txnaggregates.NewService(dynConf, transactionProcessor, aaProcessor, categorizerClient, merchantProcessor, analyserTransactionDaoPinot)
	return service, nil
}

func InitialiseAnalyserDevService(ctx context.Context, dynConf *genconf.Config, analyserConf *config.Config, db types2.EpifiWealthAnalyticsPGDB, redisClient types.AnalyserRedisStore) (*developer.AnalyserDevService, error) {
	string2 := AnalyserConfigAppEnvProvider(analyserConf)
	client, err := pinotClientProvider(analyserConf, string2)
	if err != nil {
		return nil, err
	}
	pinotPollingConfig := pinotPollerConfigProvider(dynConf)
	cacheStorage := redisCacheClientProvider(redisClient)
	client2 := types.AnalyserRedisStoreRedisClientProvider(redisClient)
	clock := lock.NewRealClockProvider()
	uuidGenerator := idgen.NewUuidGenerator()
	redisV9LockManager := lock.NewRedisV9LockManager(client2, clock, uuidGenerator)
	updatedAtPollerImpl := poller.NewUpdatedAtPoller(pinotPollingConfig, client, cacheStorage, redisV9LockManager)
	analyserTransactionDaoPinot := dao.NewAnalyserTransactionDao(ctx, client, updatedAtPollerImpl)
	analyserAATransactionDaoPinot := dao.NewAnalyserAATransactionDao(ctx, client, updatedAtPollerImpl)
	analyserTransactionUpdateDaoPinot := dao.NewAnalyserTransactionUpdateDao(client)
	analyserAATransactionUpdateDaoPinot := dao.NewAnalyserAATransactionUpdateDao(client)
	commonProcessor := processor.NewCommonProcessor(analyserTransactionDaoPinot, analyserAATransactionDaoPinot, analyserTransactionUpdateDaoPinot, analyserAATransactionUpdateDaoPinot)
	analysisTaskDaoPgdb := dao2.NewAnalysisTaskDaoPgdb(db)
	analysisTaskProcessor := processor.NewAnalysisTaskProcessor(analysisTaskDaoPgdb)
	mfPortfolioHistoryDaoPGDB := dao2.NewMFPortfolioHistoryDaoPGDB(db)
	mfPortfolioHistoryProcessor := processor.NewMfPortfolioHistoryProcessor(mfPortfolioHistoryDaoPGDB)
	devFactory := developer.NewDevFactory(commonProcessor, analysisTaskProcessor, mfPortfolioHistoryProcessor)
	analyserDevService := developer.NewAnalyserDevService(devFactory)
	return analyserDevService, nil
}

func InitialiseInvestmentAnalyticsService(db types2.EpifiWealthAnalyticsPGDB, investmentAnalyticsTaskPublisher types.InvestmentAnalysisTaskPublisher, mfExternalOrdersClient external.MFExternalOrdersClient, mfAnalytics analytics.MFAnalyticsClient, redisClient types.AnalyserRedisStore, highPriorityInvestmentAnalyticsTaskPublisher types.HighPriorityInvestmentAnalysisTaskPublisher) *investment.Service {
	defaultTime := datetime.NewDefaultTime()
	analysisTaskDaoPgdb := dao2.NewAnalysisTaskDaoPgdb(db)
	txnExecutor := getTxnExecutor(db)
	mfDataCollectorImpl := datacollector.NewMFDataCollectorImpl(mfExternalOrdersClient)
	converter := mfcentral.NewMFCentralFormatConverter()
	smallCase := smallcase.NewSmallCaseProcessor(mfDataCollectorImpl, mfAnalytics, converter, defaultTime)
	mfPortfolioHistoryDaoPGDB := dao2.NewMFPortfolioHistoryDaoPGDB(db)
	mfPortfolioHistoryFetcherImpl := investment.NewMFPortfolioHistoryFetcher(mfPortfolioHistoryDaoPGDB)
	client := types.AnalyserRedisStoreRedisClientProvider(redisClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	mfSchemeAnalyticsCacheImpl := cache2.NewMfSchemeAnalyticsCacheImpl(redisCacheStorage)
	service := investment.NewInvestmentAnalyticsService(defaultTime, analysisTaskDaoPgdb, investmentAnalyticsTaskPublisher, highPriorityInvestmentAnalyticsTaskPublisher, txnExecutor, smallCase, mfPortfolioHistoryFetcherImpl, mfSchemeAnalyticsCacheImpl, mfPortfolioHistoryDaoPGDB)
	return service
}

func InitialiseAnalyserInvestmentConsumerService(gconf *genconf.Config, db types2.EpifiWealthAnalyticsPGDB, investmentAnalyticsClient investment2.InvestmentAnalyticsClient, mfExternalOrdersClient external.MFExternalOrdersClient, mfCatalogManagerClient catalog.CatalogManagerClient, commsClient types.AnalyserCommsClientWithInterceptors, userClient user.UsersClient, redisClient types.AnalyserRedisStore) *consumer2.Service {
	defaultTime := datetime.NewDefaultTime()
	mfDataCollectorImpl := datacollector.NewMFDataCollectorImpl(mfExternalOrdersClient)
	mfPortfolioHistoryDaoPGDB := dao2.NewMFPortfolioHistoryDaoPGDB(db)
	analysisTaskDaoPgdb := dao2.NewAnalysisTaskDaoPgdb(db)
	navCollectorFactoryImpl := factory.NewNavCollectorFactory(mfCatalogManagerClient)
	assetDetailsCollectorFactoryImpl := factory2.NewAssetDetailsCollectorFactory(mfCatalogManagerClient)
	historyAggregates := calc.NewHistoryAggregates(gconf, navCollectorFactoryImpl, assetDetailsCollectorFactoryImpl, investmentAnalyticsClient)
	txnExecutor := getTxnExecutor(db)
	mfTaskProcessorFactoryImpl := mutualfund.NewMFTaskProcessorFactory(mfPortfolioHistoryDaoPGDB, analysisTaskDaoPgdb, historyAggregates, assetDetailsCollectorFactoryImpl, defaultTime, txnExecutor)
	commsCommsClient := types.CommsClientProvider(commsClient)
	notificationSenderImpl := mutualfund.NewNotificationSenderImpl(commsCommsClient, userClient, defaultTime)
	mutualfundProcessor := mutualfund.NewMutualFundProcessor(mfDataCollectorImpl, mfTaskProcessorFactoryImpl, notificationSenderImpl)
	processorImpl := processor2.NewProcessor(gconf, defaultTime, investmentAnalyticsClient, mutualfundProcessor, analysisTaskDaoPgdb)
	client := types.AnalyserRedisStoreRedisClientProvider(redisClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	mfSchemeAnalyticsCacheImpl := cache2.NewMfSchemeAnalyticsCacheImpl(redisCacheStorage)
	service := consumer2.NewService(processorImpl, analysisTaskDaoPgdb, mfSchemeAnalyticsCacheImpl)
	return service
}

func InitialiseDynamicElementsService(preapprovedloanClient preapprovedloan.PreApprovedLoanClient) *dynamicelements.Service {
	service := dynamicelements.NewDynamicElementsService(preapprovedloanClient)
	return service
}

func InitialiseVariableGeneratorService(userDeclarationClient user_declaration.ServiceClient, investmentAnalysticsClient investment2.InvestmentAnalyticsClient, userClient user.UsersClient, catalogManagerClient catalog.CatalogManagerClient, networthClient networth.NetWorthClient, catalogClient catalog2.SecuritiesCatalogClient, epfClient epf.EpfClient) *variablegenerator.Service {
	userDeclaredMonthlyIncomeDataFetcher := datafetcher.NewUserDeclaredMonthlyIncomeDataFetcher(userDeclarationClient)
	defaultTime := datetime.NewDefaultTime()
	mfHistoryPortfolioDataFetcher := datafetcher.NewMfHistoryPortfolioDataFetcher(investmentAnalysticsClient, defaultTime)
	userDeclaredCompanyDetailsDataFetcher := datafetcher.NewUserDeclaredCompanyDetailsDataFetcher(userDeclarationClient)
	dobProcessor := pkg.NewDobProcessor(userClient)
	userDeclaredDobDataFetcher := datafetcher.NewUserDeclaredDobDataFetcher(dobProcessor)
	mutualFundCatalogProvider := dataprovider.NewMutualFundCatalogProvider(catalogManagerClient)
	mfPortfolioAnalyticsDataFetcher := datafetcher.NewMfHPortfolioAnalyticsDataFetcher(investmentAnalysticsClient, catalogManagerClient, mutualFundCatalogProvider)
	portfolioSummaryDataFetcher := datafetcher.NewPortfolioSummaryDataFetcher(networthClient)
	mfLowTrackingErrorIndexFundDataFetcher := datafetcher.NewMfLowTrackingErrorIndexFundDataFetcher(catalogManagerClient)
	networthDetailsFetcher := datafetcher.NewNetworthDetailsFetcher(networthClient)
	mfAssetCategoryDetailsDataFetcher := datafetcher.NewMfAssetCategoryDetailsDataFetcher(investmentAnalysticsClient, catalogManagerClient, mutualFundCatalogProvider)
	mfInvestmentActivitiesDataFetcher := datafetcher.NewMfInvestmentActivitiesDataFetcher(investmentAnalysticsClient, defaultTime)
	assetsDayChangeDataFetcher := datafetcher.NewAssetsDayChangeDataFetcher(networthClient, catalogClient)
	weeklyChangeDataFetcher := datafetcher.NewWeeklyChangeDataFetcher(networthClient, catalogManagerClient, catalogClient)
	rawDetailsFactory := rawdetailsfetcher.NewRawDetailsFactory(userDeclaredMonthlyIncomeDataFetcher, mfHistoryPortfolioDataFetcher, userDeclaredCompanyDetailsDataFetcher, userDeclaredDobDataFetcher, mfPortfolioAnalyticsDataFetcher, portfolioSummaryDataFetcher, mfLowTrackingErrorIndexFundDataFetcher, networthDetailsFetcher, mfAssetCategoryDetailsDataFetcher, mfInvestmentActivitiesDataFetcher, assetsDayChangeDataFetcher, weeklyChangeDataFetcher)
	rawDetailsFetcher := rawdetailsfetcher.NewRawDetailsFetcher(rawDetailsFactory)
	userDeclaredMonthlyIncomeVariableProcessor := variableprocessor.NewUserDeclaredMonthlySalaryVariableProcessor()
	mfMonthlyInvestmentStatisticsVariableProcessor := variableprocessor.NewMfMonthlyInvestmentStatisticsVariableProcessor()
	userDeclaredCompanyDetailsVariableProcessor := variableprocessor.NewUserDeclaredCompanyDetailsVariableProcessor(epfClient)
	userDeclaredDobVariableProcessor := variableprocessor.NewUserDeclaredDobVariableProcessor()
	mfFundsBreakdownVariableGenerator := variableprocessor.NewMfFundsBreakdownVariableGenerator()
	portfolioSummaryVariableGenerator := variableprocessor.NewPortfolioSummaryVariableGenerator()
	mfPortfolioPerformanceVariableProcessor := variableprocessor.NewMfPortfolioPerformanceVariableProcessor()
	mfInvestmentActivitiesVariableProcessor := variableprocessor.NewMfInvestmentActivitiesVariableProcessor()
	mfAssetCategoryDetailsVariableProcessor := variableprocessor.NewMfAssetCategoryDetailsVariableProcessor()
	networthDetailsVariableProcessor := variableprocessor.NewNetworthDetailsVariableProcessor()
	indianStocksDistributionVariableGenerator := variableprocessor.NewIndianStocksDistributionVariableGenerator()
	mfWeeklyReportVariableGenerator := variableprocessor.NewMFWeeklyReportVariableGenerator()
	goldDistributionVariableGenerator := variableprocessor.NewGoldDistributionVariableGenerator()
	indianStocksWeeklyReportVariableGenerator := variableprocessor.NewIndianStocksWeeklyReportVariableGenerator()
	analysisVariableFactory := variablegenerator.NewAnalysisVariableFactory(userDeclaredMonthlyIncomeVariableProcessor, mfMonthlyInvestmentStatisticsVariableProcessor, userDeclaredCompanyDetailsVariableProcessor, userDeclaredDobVariableProcessor, mfFundsBreakdownVariableGenerator, portfolioSummaryVariableGenerator, mfPortfolioPerformanceVariableProcessor, mfInvestmentActivitiesVariableProcessor, mfAssetCategoryDetailsVariableProcessor, networthDetailsVariableProcessor, indianStocksDistributionVariableGenerator, mfWeeklyReportVariableGenerator, goldDistributionVariableGenerator, indianStocksWeeklyReportVariableGenerator)
	service := variablegenerator.NewService(rawDetailsFetcher, analysisVariableFactory)
	return service
}

// wire.go:

func AnalyserConfigAppEnvProvider(analyserConf *config.Config) string {
	return analyserConf.Application.Environment
}

func pinotClientProvider(analyserConf *config.Config, env string) (pinot.Client, error) {
	return pinot.NewPinotClient(analyserConf.PinotConfig, env)
}

func awsV2ConfigProvider(ctx context.Context, analyserConf *config.Config) (aws.Config, error) {
	return config2.NewAWSConfig(ctx, analyserConf.Aws.Region, analyserConf.Tracing.Enable)
}

func kinesisClientProvider(awsV2Config aws.Config) *kinesis.Client {
	return kinesis2.NewKinesisClient(&awsV2Config)
}

func transactionsProducerProvider(ctx context.Context, analyserConf *config.Config, kinesisClient *kinesis.Client) (types.TransactionsProducer, error) {
	return kinesis2.NewKinesisProducerWithCustomHash(ctx, analyserConf.TransactionsProducer, kinesisClient, murmur2.GetPartition)
}

func aaTransactionsProducerProvider(ctx context.Context, analyserConf *config.Config, kinesisClient *kinesis.Client) (types.AaTransactionsProducer, error) {
	return kinesis2.NewKinesisProducerWithCustomHash(ctx, analyserConf.AATransactionsProducer, kinesisClient, murmur2.GetPartition)
}

func transactionsUpdateProducerProvider(ctx context.Context, analyserConf *config.Config, kinesisClient *kinesis.Client) (types.TransactionsUpdateProducer, error) {
	return kinesis2.NewKinesisProducerWithCustomHash(ctx, analyserConf.TransactionsUpdateProducer, kinesisClient, murmur2.GetPartition)
}

func aaTransactionsUpdateProducerProvider(ctx context.Context, analyserConf *config.Config, kinesisClient *kinesis.Client) (types.AaTransactionsUpdateProducer, error) {
	return kinesis2.NewKinesisProducerWithCustomHash(ctx, analyserConf.AATransactionsUpdateProducer, kinesisClient, murmur2.GetPartition)
}

func getTxnExecutor(db types2.EpifiWealthAnalyticsPGDB) storagev2.TxnExecutor {
	return storagev2.NewGormTxnExecutor(types2.EpifiWealthAnalyticsPGDBGormDBProvider(db))
}

func pinotPollerConfigProvider(cfg *genconf.Config) *genconf.PinotPollingConfig {
	return cfg.PinotPollingConfig()
}

func redisCacheClientProvider(redisClient types.AnalyserRedisStore) cache.CacheStorage {
	return cache.NewRedisCacheStorage(redisClient)
}
