// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"context"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	sqs2 "github.com/epifi/be-common/pkg/aws/v2/sqs"
	"github.com/epifi/be-common/pkg/aws/v2/wire"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cfg/genconf"
	types2 "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epifitemporal/client"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/queue"
	"github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/pkg/storage/v2/usecase"
	"github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/celestial"
	"github.com/epifi/gamma/celestial/activity"
	"github.com/epifi/gamma/celestial/activity/procrastinator"
	"github.com/epifi/gamma/celestial/activity/unpauseworkflows"
	"github.com/epifi/gamma/celestial/activity/v2"
	genconf2 "github.com/epifi/gamma/celestial/config/genconf"
	"github.com/epifi/gamma/celestial/consumer"
	"github.com/epifi/gamma/celestial/dao"
	"github.com/epifi/gamma/celestial/developer"
	"github.com/epifi/gamma/celestial/developer/processor"
	celestial2 "github.com/epifi/gamma/celestial/internal/celestial"
	"github.com/epifi/gamma/celestial/internal/temporal"
	"github.com/epifi/gamma/celestial/types"
	types3 "github.com/epifi/gamma/celestial/wire/types"
	"go.temporal.io/api/workflowservice/v1"
	"gorm.io/gorm"
)

// Injectors from wire.go:

func InitializeService(ctx context.Context, pgdbConns *genconf.PgdbConns, conf *genconf2.Config, awsConf aws.Config, dbConnProvider *usecase.DBResourceProvider[*gorm.DB], txnExecutorProvider *usecase.DBResourceProvider[storagev2.IdempotentTxnExecutor]) *celestial.Service {
	clientFactory := client.GetClientFactory()
	initiateWorkflowExtendedPublisher := initiateWorkflowPublisherProvider(ctx, awsConf, conf)
	signalWorkflowExtendedPublisher := signalWorkflowPublisherProvider(ctx, awsConf, conf)
	workflowHistoryDaoCRDB := dao.NewWorkflowHistoryDao(dbConnProvider)
	dbResourceProvider := cloneDbResourceProvider(dbConnProvider)
	workflowHistoryDaoPGDB := dao.NewWorkflowHistoryDaoPgdb(pgdbConns, dbResourceProvider)
	workflowHistoryDao := dao.NewWorkflowHistoryDaoProvider(pgdbConns, workflowHistoryDaoCRDB, workflowHistoryDaoPGDB)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	workflowRequestDaoCRDB := dao.NewWorkflowRequestDao(dbConnProvider, domainIdGenerator)
	workflowRequestDaoPGDB := dao.NewWorkflowRequestDaoPgdb(pgdbConns, dbResourceProvider, domainIdGenerator)
	workflowRequestDao := dao.NewWorkflowRequestDaoProvider(pgdbConns, workflowRequestDaoCRDB, workflowRequestDaoPGDB)
	processor := celestial2.NewCelestialProcessor(workflowRequestDao, txnExecutorProvider)
	celestialCodecAesKey := getTemporalCodecAesKey(conf)
	temporalProcessor := temporal.NewTemporalProcessor(workflowRequestDao, clientFactory, celestialCodecAesKey)
	service := celestial.NewService(clientFactory, initiateWorkflowExtendedPublisher, signalWorkflowExtendedPublisher, workflowHistoryDao, workflowRequestDao, processor, temporalProcessor, txnExecutorProvider, celestialCodecAesKey)
	return service
}

func InitializeActivityProcessor(conf *genconf.PgdbConns, dbConnProvider *usecase.DBResourceProvider[*gorm.DB], txnExecutor storagev2.IdempotentTxnExecutor, workflowUpdatePublisher types.WorkflowUpdatePublisher, commsClient comms.CommsClient) *activity.Processor {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	workflowRequestDaoCRDB := dao.NewWorkflowRequestDao(dbConnProvider, domainIdGenerator)
	dbResourceProvider := cloneDbResourceProvider(dbConnProvider)
	workflowRequestDaoPGDB := dao.NewWorkflowRequestDaoPgdb(conf, dbResourceProvider, domainIdGenerator)
	workflowRequestDao := dao.NewWorkflowRequestDaoProvider(conf, workflowRequestDaoCRDB, workflowRequestDaoPGDB)
	workflowHistoryDaoCRDB := dao.NewWorkflowHistoryDao(dbConnProvider)
	workflowHistoryDaoPGDB := dao.NewWorkflowHistoryDaoPgdb(conf, dbResourceProvider)
	workflowHistoryDao := dao.NewWorkflowHistoryDaoProvider(conf, workflowHistoryDaoCRDB, workflowHistoryDaoPGDB)
	clientFactory := client.GetClientFactory()
	processor := activity.NewProcessor(workflowRequestDao, workflowHistoryDao, workflowUpdatePublisher, commsClient, clientFactory, txnExecutor)
	return processor
}

func InitializeActivityProcessorV2(conf *genconf.PgdbConns, dbConnProvider *usecase.DBResourceProvider[*gorm.DB], txnExecutorProvider *usecase.DBResourceProvider[storagev2.IdempotentTxnExecutor], workflowUpdatePublisher types.WorkflowUpdatePublisher, commsClient comms.CommsClient, codecAesKey types2.CelestialCodecAesKey) *activityV2.Processor {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	workflowRequestDaoCRDB := dao.NewWorkflowRequestDao(dbConnProvider, domainIdGenerator)
	dbResourceProvider := cloneDbResourceProvider(dbConnProvider)
	workflowRequestDaoPGDB := dao.NewWorkflowRequestDaoPgdb(conf, dbResourceProvider, domainIdGenerator)
	workflowRequestDao := dao.NewWorkflowRequestDaoProvider(conf, workflowRequestDaoCRDB, workflowRequestDaoPGDB)
	workflowHistoryDaoCRDB := dao.NewWorkflowHistoryDao(dbConnProvider)
	workflowHistoryDaoPGDB := dao.NewWorkflowHistoryDaoPgdb(conf, dbResourceProvider)
	workflowHistoryDao := dao.NewWorkflowHistoryDaoProvider(conf, workflowHistoryDaoCRDB, workflowHistoryDaoPGDB)
	clientFactory := client.GetClientFactory()
	processor := celestial2.NewCelestialProcessor(workflowRequestDao, txnExecutorProvider)
	activityV2Processor := activityV2.NewProcessor(workflowRequestDao, workflowHistoryDao, workflowUpdatePublisher, commsClient, clientFactory, txnExecutorProvider, processor, codecAesKey)
	return activityV2Processor
}

func InitializeConsumerService(conf *genconf.PgdbConns, genconf3 *genconf2.Config, dbConnProvider *usecase.DBResourceProvider[*gorm.DB]) *consumer.Service {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	workflowRequestDaoCRDB := dao.NewWorkflowRequestDao(dbConnProvider, domainIdGenerator)
	dbResourceProvider := cloneDbResourceProvider(dbConnProvider)
	workflowRequestDaoPGDB := dao.NewWorkflowRequestDaoPgdb(conf, dbResourceProvider, domainIdGenerator)
	workflowRequestDao := dao.NewWorkflowRequestDaoProvider(conf, workflowRequestDaoCRDB, workflowRequestDaoPGDB)
	clientFactory := client.GetClientFactory()
	celestialCodecAesKey := getTemporalCodecAesKey(genconf3)
	processor := temporal.NewTemporalProcessor(workflowRequestDao, clientFactory, celestialCodecAesKey)
	service := consumer.NewService(workflowRequestDao, clientFactory, processor, celestialCodecAesKey)
	return service
}

func InitializeProcrastinatorActivityProcessor(sqsClient *sqs.Client, codecAesKey types2.CelestialCodecAesKey) *procrastinator.Processor {
	clientFactory := client.GetClientFactory()
	processor := procrastinator.NewProcessor(sqsClient, codecAesKey, clientFactory)
	return processor
}

func InitializeDevService(conf *genconf.PgdbConns, dbConnProvider *usecase.DBResourceProvider[*gorm.DB]) *developer.CelestialDevService {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	workflowRequestDaoCRDB := dao.NewWorkflowRequestDao(dbConnProvider, domainIdGenerator)
	dbResourceProvider := cloneDbResourceProvider(dbConnProvider)
	workflowRequestDaoPGDB := dao.NewWorkflowRequestDaoPgdb(conf, dbResourceProvider, domainIdGenerator)
	workflowRequestDao := dao.NewWorkflowRequestDaoProvider(conf, workflowRequestDaoCRDB, workflowRequestDaoPGDB)
	workflowRequestProcessor := processor.NewWorkflowRequestProcessor(workflowRequestDao)
	workflowHistoryDaoCRDB := dao.NewWorkflowHistoryDao(dbConnProvider)
	workflowHistoryDaoPGDB := dao.NewWorkflowHistoryDaoPgdb(conf, dbResourceProvider)
	workflowHistoryDao := dao.NewWorkflowHistoryDaoProvider(conf, workflowHistoryDaoCRDB, workflowHistoryDaoPGDB)
	workflowHistoryProcessor := processor.NewWorkflowHistoryProcessor(workflowHistoryDao)
	devFactory := developer.NewDevFactory(workflowRequestProcessor, workflowHistoryProcessor)
	celestialDevService := developer.NewCelestialDevService(devFactory)
	return celestialDevService
}

func InitializeUnpauseWorkflowsProcessor(temporalServiceClient workflowservice.WorkflowServiceClient) *unpauseworkflows.Processor {
	unpauseworkflowsProcessor := unpauseworkflows.NewProcessor(temporalServiceClient)
	return unpauseworkflowsProcessor
}

// wire.go:

func newExtendedPublisher(ctx context.Context, conf *cfg.ExtendedSqsPublisher, awsConf aws.Config) queue.ExtendedPublisher {
	return wire.InitializeExtendedPublisherV1(ctx, conf, awsConf, queue.NewDefaultMessage(), sqs2.ServiceName(cfg.CELESTIAL_SERVICE))
}

func initiateWorkflowPublisherProvider(ctx context.Context, awsConf aws.Config, conf *genconf2.Config) types3.InitiateWorkflowExtendedPublisher {
	return newExtendedPublisher(ctx, conf.InitiateWorkflowPublisher(), awsConf)
}

func signalWorkflowPublisherProvider(ctx context.Context, awsConf aws.Config, conf *genconf2.Config) types3.SignalWorkflowExtendedPublisher {
	return newExtendedPublisher(ctx, conf.SignalWorkflowPublisher(), awsConf)
}

func cloneDbResourceProvider(dbConnProvider *usecase.DBResourceProvider[*gorm.DB]) usecase.DBResourceProvider[*gorm.DB] {
	return *dbConnProvider
}

func cloneTxnExecutorProvider(txnExecutorProvider usecase.IDBResourceProvider[storagev2.IdempotentTxnExecutor]) *usecase.DBResourceProvider[storagev2.IdempotentTxnExecutor] {
	return txnExecutorProvider.(*usecase.DBResourceProvider[storagev2.IdempotentTxnExecutor])
}

func getTemporalCodecAesKey(conf *genconf2.Config) types2.CelestialCodecAesKey {
	return types2.CelestialCodecAesKey(conf.TemporalCodecConfig().TemporalCodecAesKeyValue)
}

func DbResourceProviderWithUseCaseProvider(dbConnProvider *usecase.DBResourceProvider[storagev2.IdempotentTxnExecutor]) usecase.IDBResourceProvider[storagev2.IdempotentTxnExecutor] {
	return dbConnProvider
}

func DbProviderWithUseCaseProvider(dbConnProvider *usecase.DBResourceProvider[*gorm.DB]) usecase.IDBResourceProvider[*gorm.DB] {
	return dbConnProvider
}

func IDBResourceProviderProvider(dbConnProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor]) storagev2.IDbResourceProvider[storagev2.IdempotentTxnExecutor] {
	return dbConnProvider
}
