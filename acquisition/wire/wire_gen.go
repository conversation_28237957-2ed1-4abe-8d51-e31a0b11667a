// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/gamma/acquisition/config/genconf"
	"github.com/epifi/gamma/acquisition/crossattach"
	"github.com/epifi/gamma/acquisition/crossattach/cohort"
	"github.com/epifi/gamma/acquisition/crossattach/userattributes"
	"github.com/epifi/gamma/acquisition/crossattach/userattributes/dao"
	"github.com/epifi/gamma/acquisition/wire/types"
	"github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/insights/networth"
	"github.com/epifi/gamma/api/preapprovedloan/lendability"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/userintel"
)

// Injectors from wire.go:

func InitializeCrossAttachService(gconf *genconf.Config, userAttributesCacheStorage types.CrossAttachUserAttributesCacheStorage, onbClient onboarding.OnboardingClient, lendabilityClient lendability.LendabilityClient, networthClient networth.NetWorthClient, usersClient user.UsersClient, userIntelClient userintel.UserIntelServiceClient, caClient connected_account.ConnectedAccountClient, eventBroker events.Broker) *crossattach.Service {
	crossAttachConfig := CrossAttachConfProvider(gconf)
	crossAttachCacheConfig := CrossAttachCacheConfProvider(crossAttachConfig)
	userAttributesCache := dao.NewUserAttributesCache(userAttributesCacheStorage, crossAttachCacheConfig)
	lendabilityDetailsGetter := userattributes.NewLendabilityDetailsGetter(lendabilityClient)
	screenerCheckResultGetter := userattributes.NewScreenerCheckResultGetter(userIntelClient, usersClient)
	onboardingDetailsGetter := userattributes.NewOnboardingDetailsGetter(onbClient)
	netWorthAssetValuesGetter := userattributes.NewNetWorthAssetValuesGetter(networthClient)
	latestAssetConnectedTimestampGetter := userattributes.NewLatestAssetConnectedTimestampGetter(networthClient, caClient)
	userAttributesHelperImpl := userattributes.NewUserAttributesHelperImpl(userAttributesCache, lendabilityDetailsGetter, screenerCheckResultGetter, onboardingDetailsGetter, netWorthAssetValuesGetter, latestAssetConnectedTimestampGetter)
	cohortWbNotConnected := cohort.NewCohortWbNotConnected(userAttributesHelperImpl)
	cohortWbConnected := cohort.NewCohortWbConnected(userAttributesHelperImpl)
	cohortWbConnectedAssetValueBelowThreshold := cohort.NewCohortWbConnectedAssetValueBelowThreshold(userAttributesHelperImpl, crossAttachConfig)
	cohortUpiTpap := cohort.NewCohortUpiTpap(userAttributesHelperImpl)
	cohortPLDropOff := cohort.NewCohortPLDropOff(userAttributesHelperImpl)
	cohortSAExpired := cohort.NewCohortSAExpired(userAttributesHelperImpl)
	cohortSAForcedDropOff := cohort.NewCohortSAForcedDropOff(userAttributesHelperImpl)
	service := crossattach.NewService(userAttributesHelperImpl, userAttributesCache, eventBroker, cohortWbNotConnected, cohortWbConnected, cohortWbConnectedAssetValueBelowThreshold, cohortUpiTpap, cohortPLDropOff, cohortSAExpired, cohortSAForcedDropOff)
	return service
}

// wire.go:

func CrossAttachConfProvider(acquisitionConf *genconf.Config) *genconf.CrossAttachConfig {
	return acquisitionConf.CrossAttachConfig()
}

func CrossAttachCacheConfProvider(crossAttachConf *genconf.CrossAttachConfig) *genconf.CrossAttachCacheConfig {
	return crossAttachConf.CrossAttachCacheConfig()
}
