// nolint
package cohort

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/acquisition/crossattach/userattributes"
	crossAttachPb "github.com/epifi/gamma/api/acquisition/crossattach"
	"github.com/epifi/gamma/api/user/onboarding"
)

type CohortPLDropOff struct {
	userAttributesHelper userattributes.UserAttributesHelper
}

func NewCohortPLDropOff(userAttributesHelper userattributes.UserAttributesHelper) *CohortPLDropOff {
	return &CohortPLDropOff{
		userAttributesHelper: userAttributesHelper,
	}
}

func (s *CohortPLDropOff) IsMember(ctx context.Context, req *IsMemberRequest) (*IsMemberResponse, error) {
	getUserAttributeRes, err := s.userAttributesHelper.GetUserAttribute(ctx, &userattributes.GetUserAttributeRequest{
		ActorId:       req.GetActorId(),
		UserAttribute: crossAttachPb.UserAttribute_USER_ATTRIBUTE_ONBOARDING_FEATURE_DETAILS,
	})
	if err != nil {
		logger.Error(ctx, "failed to get user attribute", zap.Error(err))
		return nil, err
	}
	intentMetadataRes, err := s.userAttributesHelper.GetUserAttribute(ctx, &userattributes.GetUserAttributeRequest{
		ActorId:       req.GetActorId(),
		UserAttribute: crossAttachPb.UserAttribute_USER_ATTRIBUTE_ONBOARDING_INTENT_SELECTION_METADATA,
	})
	if err != nil {
		logger.Error(ctx, "failed to get user attribute", zap.Error(err))
		return nil, err
	}
	if intentMetadataRes.GetUserAttributeValue().GetOnbIntentSelectionMetadata().GetSelection() == onboarding.OnboardingIntent_ONBOARDING_INTENT_PERSONAL_LOANS &&
		getUserAttributeRes.GetUserAttributeValue().GetOnbFeatureDetails().GetFeatureInfo()[onboarding.Feature_FEATURE_PL.String()].GetFeatureStatus() == onboarding.FeatureStatus_FEATURE_STATUS_ONBOARDING_IN_PROGRESS {
		logger.Info(ctx, "PL onboarding drop off user")
		return &IsMemberResponse{
			IsMember: true,
		}, nil
	}

	return &IsMemberResponse{
		IsMember: false,
	}, nil
}

func (s *CohortPLDropOff) CanUpsell(ctx context.Context, req *CanUpsellRequest) (*CanUpsellResponse, error) {
	getUserAttributeRes, err := s.userAttributesHelper.GetUserAttribute(ctx, &userattributes.GetUserAttributeRequest{
		ActorId:       req.GetActorId(),
		UserAttribute: crossAttachPb.UserAttribute_USER_ATTRIBUTE_HOME_LANDED_TIMESTAMP,
	})
	if err != nil {
		logger.Error(ctx, "failed to get user attribute", zap.Error(err))
		return nil, err
	}
	homeLandedTimestamp := getUserAttributeRes.GetUserAttributeValue().GetHomeLandedTimestamp()
	if homeLandedTimestamp == nil {
		logger.Error(ctx, "empty home landed timestamp")
		return nil, fmt.Errorf("empty home landed timestamp")
	}
	upsellAfter := 7 * 24 * time.Hour
	if time.Since(homeLandedTimestamp.AsTime()) < upsellAfter {
		logger.Info(ctx, "cannot upsell as landed on home within 3 days")
		return &CanUpsellResponse{
			CanUpsell:   false,
			UpsellAfter: homeLandedTimestamp.AsTime().Add(upsellAfter).Sub(time.Now()),
		}, nil
	}
	return &CanUpsellResponse{
		CanUpsell:   true,
		UpsellAfter: 0,
	}, nil
}
