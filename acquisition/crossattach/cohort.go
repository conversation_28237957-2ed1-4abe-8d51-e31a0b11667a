package crossattach

const (
	/*
		Relevant design for defining the below 4 cohort types
		https://whimsical.com/cross-attach-MVcd2FNoUC4kUhL8ngpd3b
	*/
	// Cohorts with prefix COHORT_TYPE_WB indicates user who did wealth builder onboarding
	COHORT_TYPE_WB_NOT_CONNECTED                         = "COHORT_TYPE_WB_NOT_CONNECTED"
	COHORT_TYPE_WB_CONNECTED                             = "COHORT_TYPE_WB_CONNECTED"
	COHORT_TYPE_WB_CONNECTED_ASSET_VALUE_BELOW_THRESHOLD = "COHORT_TYPE_WB_CONNECTED_ASSET_VALUE_BELOW_THRESHOLD"
	COHORT_TYPE_UPI_TPAP                                 = "COHORT_TYPE_UPI_TPAP"
	COHORT_TYPE_SA_EXPIRED                               = "COHORT_TYPE_SA_EXPIRED"
	COHORT_TYPE_SA_FORCED_DROP_OFF                       = "COHORT_TYPE_SA_FORCED_DROP_OFF"
	COHORT_TYPE_PL_DROP_OFF                              = "COHORT_TYPE_PL_DROP_OFF"
)

var (
	cohortTypeListByPriority = []string{
		COHORT_TYPE_WB_CONNECTED_ASSET_VALUE_BELOW_THRESHOLD,
		COHORT_TYPE_WB_CONNECTED,
		COHORT_TYPE_WB_NOT_CONNECTED,
		COHORT_TYPE_SA_EXPIRED,
		COHORT_TYPE_SA_FORCED_DROP_OFF,
		COHORT_TYPE_PL_DROP_OFF,
		COHORT_TYPE_UPI_TPAP,
	}
)
