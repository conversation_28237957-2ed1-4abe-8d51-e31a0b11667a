Application:
  Environment: "staging"
  Name: "auth"
  AndroidClientSignature: "vEESbjXDMpQ"
  OAuthAndroidClientID: "432746002179-4q86u70ot3opldm79u9qnd82icd3b64h.apps.googleusercontent.com"
  OAuthIOSClientID: "411450025013-t1n09gmk2n6cf2dplh69mmsrktkgmlqj.apps.googleusercontent.com"
  UseFFMPEGToExtractFrame: true
  UseFFMPEGToCompressVideo: true
  UserAccessTokenSigningMethod:
    # Keeping it equal to 60 minutes same as the validity of OAuth id token returned by Google
    Duration: 3600
    # Ideally supposed to be around 5-15 minutes. Keeping it 30 minutes for the developer convenience
    InactivityTimer: 900
    Algo: "HS256"
    IsRetiredKeyPresent: true
  UserRefreshTokenSigningMethod:
    # 30 days => 60 * 60 * 24 * 30
    Duration: 2592000
    # No need of inactivity timer on refresh token as that's enforced on access token
    InactivityTimer: 2592000
    Algo: "HS384"
    IsRetiredKeyPresent: true
  WaitlistUserAccessTokenSigningMethod:
    Duration: 3600
    InactivityTimer: 1800
    Algo: "HS256"
    IsRetiredKeyPresent: false
  AppInsightsAccessTokenSigningMethod:
    Duration: 900
    InactivityTimer: 900
    Algo: "HS256"
    IsRetiredKeyPresent: false
  ChatbotAccessTokenSigningMethod:
    Duration: 2592000
    InactivityTimer: 2592000
    Algo: "HS256"
    IsRetiredKeyPresent: false
  WebLiteAccessTokenSigningMethod:
    Duration: 3600
    InactivityTimer: 3600
    Algo: "HS256"
    IsRetiredKeyPresent: false
  GenieRefreshTokenSigningMethod:
    # 1 day => 60 * 60 * 24
    Duration: 86400
    InactivityTimer: 86400
    Algo: "HS384"
    IsRetiredKeyPresent: true
  GenieAccessTokenSigningMethod:
    # Keeping it equal to 15 minutes
    Duration: 900
    # Keeping it 15 minutes.
    InactivityTimer: 900
    Algo: "HS256"
    IsRetiredKeyPresent: true
  WebCABTRefreshTokenSigningMethod:
    # 1 day => 60 * 60 * 24
    Duration: 86400
    InactivityTimer: 86400
    Algo: "HS384"
    IsRetiredKeyPresent: true
  WebCABTAccessTokenSigningMethod:
    # Keeping it equal to 15 minutes
    Duration: 900
    # Keeping it 15 minutes.
    InactivityTimer: 900
    Algo: "HS256"
    IsRetiredKeyPresent: true
  RiskOutcallWebformAccessTokenSigningMethod:
    # Keeping it equal to 30 minutes
    Duration: 1800
    # Keeping it 30 minutes.
    InactivityTimer: 1800
    Algo: "HS256"
    IsRetiredKeyPresent: true
  NetworthMcpAccessTokenSigningMethod:
    Duration: 1800
    InactivityTimer: 1800
    Algo: "HS256"
    IsRetiredKeyPresent: false
  NuggetChatbotAccessTokenSigningMethod:
    # Keeping it equal to 30 minutes
    Duration: 1800
    InactivityTimer: 1800
    Algo: "HS256"
    IsRetiredKeyPresent: false
  SMSConfig:
    PhoneNumbers:
      - 9743994779
      - 9743994780
      - 9743994781
    DeviceRegistrationPrefix: "EPIFY "
  EnableHybridOAuthVerifier: true
  NPCIDeviceBindingLimitPerDay: 25
  SkipClientIDCheck: true
  OAuthVerifierType: 2 #Hybrid
  AppleOAuthIOSClientID: "com.epifi.fi.staging"
  AppleClientSecretValidityInMinutes: "10m"
  IsSecureRedis: true

Server:
  Ports:
    GrpcPort: 8086
    GrpcSecurePort: 9503
    HttpPort: 9999
    HttpPProfPort: 9990

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
    DB: 12
  HystrixCommand:
    CommandName: "auth_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 500
      ExecutionTimeout: 500ms
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

KarzaLivenessSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-check-liveness-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

LivenessStatusPublisher:
  QueueName: "staging-liveness-status-queue"

LivenessManualReviewPublisher:
  TopicName: "staging-liveness-manual-review-topic"

LivenessSummaryCompletedEventPublisher:
  TopicName: "staging-liveness-summary-completed-event-topic"

LivenessStatusSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-liveness-status-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 9
      TimeUnit: "Second"

Aws:
  Endpoint: "localhost:4576"
  Region: "ap-south-1"
  S3:
    LivenessBucketName: "epifi-staging-liveness"

OtpConfig:
  OtpResendInterval: 30s
  GenerateOtpLimits:
    - Duration: 5m
      MaxAttempts: 20
  SkipOTPValidationForNumbers:
    - "************"
    - "************"
    - "************"
    - "************"
    - "************" # This number is reserved for Android benchmark tests

BankConfig:
  CardPinLength: 4
  SecurePinLength: 4
  BankLogoUrl: "https://epifi-icons.pointz.in/fibank/icon/96.png"
  BankName: "Federal Bank"
  VendorOtpLength: 6

DeviceIntegrityConfig:
  AndroidAppPackageNames:
    - "com.epifi.paisa.staging"
  AndroidApkCertificateDigestSha256:
    - "0kYoXaKxm/apSWnciRJqY3N3YBDSyUvuoUOKKVkgCo4="
  DeviceIntegrityNonceValidityInSecs: 300
  CheckApkPackageName: true
  CheckCertificateHash: true
  AppleAppAttestRootCertificate: |
    -----BEGIN CERTIFICATE-----
    MIICITCCAaegAwIBAgIQC/O+DvHN0uD7jG5yH2IXmDAKBggqhkjOPQQDAzBSMSYw
    JAYDVQQDDB1BcHBsZSBBcHAgQXR0ZXN0YXRpb24gUm9vdCBDQTETMBEGA1UECgwK
    QXBwbGUgSW5jLjETMBEGA1UECAwKQ2FsaWZvcm5pYTAeFw0yMDAzMTgxODMyNTNa
    Fw00NTAzMTUwMDAwMDBaMFIxJjAkBgNVBAMMHUFwcGxlIEFwcCBBdHRlc3RhdGlv
    biBSb290IENBMRMwEQYDVQQKDApBcHBsZSBJbmMuMRMwEQYDVQQIDApDYWxpZm9y
    bmlhMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAERTHhmLW07ATaFQIEVwTtT4dyctdh
    NbJhFs/Ii2FdCgAHGbpphY3+d8qjuDngIN3WVhQUBHAoMeQ/cLiP1sOUtgjqK9au
    Yen1mMEvRq9Sk3Jm5X8U62H+xTD3FE9TgS41o0IwQDAPBgNVHRMBAf8EBTADAQH/
    MB0GA1UdDgQWBBSskRBTM72+aEH/pwyp5frq5eWKoTAOBgNVHQ8BAf8EBAMCAQYw
    CgYIKoZIzj0EAwMDaAAwZQIwQgFGnByvsiVbpTKwSga0kP0e8EeDS4+sQmTvb7vn
    53O5+FRXgeLhpJ06ysC5PrOyAjEAp5U4xDgEgllF7En3VcE3iexZZtKeYnpqtijV
    oyFraWVIyd/dganmrduC1bmTBGwD
    -----END CERTIFICATE-----
  AppleCredCertOidExtensionSeq: [ 1, 2, 840, 113635, 100, 8, 2 ]
  IosAppIdentifiers:
    - "com.epifi.fi.staging"
  AllowSafetynetCertChainVerificationWithModifiedCurrTime: true
  CurrTimeOverrideForSafetynetCertVerification: "-48h"
  BypassTokenIdValidationForPhoneNumbers:
    - ************
    - ************
    - ************ # This number is reserved for Android benchmark tests
  DeviceAttestationV2Cfg:
    DisableFeature: false
    MinAndroidVersion: 180
    MinIosVersion: 10000
    FallbackToEnableFeature: false
  ExpiryTimeForDeviceIntegrity: 1h5m
  MockSafetynetTokenResult:
    SAFETYNET_CTS_PROFILE_MATCH_TEST_FAILED: "deviceId-1"
    SAFETYNET_BASIC_INTEGRITY_TEST_FAILED: "deviceId-2"
    ERROR_EXTRACTING_ATTESTATION_PAYLOAD: "deviceId-3"
    SAFETYNET_ATTESTATION_CREATION_FAILURE: "deviceId-4"

AFU:
  CredentialsOrder:
    UPDATE_PHONE_NUM:
      - [ LIVENESS_FM_VALIDATION ] # LEVEL 0
      - [ ATM_PIN_VALIDATION ] # LEVEL 1
    UPDATE_EMAIL:
      - [ LIVENESS_FM_VALIDATION ] # LEVEL 0
    UPDATE_PHONE_NUM_EMAIL:
      - [ LIVENESS_FM_VALIDATION ] # LEVEL 0
      - [ ATM_PIN_VALIDATION ] # LEVEL 1
    UPDATE_DEVICE_PHONE_NUM:
      - [ LIVENESS_FM_VALIDATION ]
      - [ ATM_PIN_VALIDATION ]
    UPDATE_DEVICE_EMAIL:
      - [ LIVENESS_FM_VALIDATION ]
      - [ ATM_PIN_VALIDATION ]
    UPDATE_EMAIL_SIM:
      - [ LIVENESS_FM_VALIDATION ]
    UPDATE_DEVICE:
      - [ LIVENESS_FM_VALIDATION ]
    UPDATE_SIM: [ ]
  VendorUpdateProducerDelayInSecs: 1
  ReRegInitIncrementalDelay: 12s
  BypassCredentialVerificationForPhoneNumbers:
    - ************
    - ************
    - ************
    - ************
    - ************ # This number is reserved for Android benchmark tests
  LivenessManualReviewExpiryTime: 4m
  MaxRecordsDepthForAFUTroubleshooter: 25
  AuthFactorUpdateCacheConfig:
    IsCachingEnabled: true
    AuthFactorUpdatePrefix: "AFU_"
  MinVersionForAFURetry:
    MinAndroidVersion: 10
    MinIosVersion: 10
    FallbackToEnableFeature: true

RudderStack:
  Host: "http://internal-data-stage-alb-373638850.ap-south-1.elb.amazonaws.com:7001"
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

Flags:
  TrimDebugMessageFromStatus: false
  SkipIOSIdTokenExpiryCheck: false
  EnableLivenessManualReviewInAfu: true
  EnableCheckForAccessRevoke: false
  DisableLivenessAttemptChangeFeed: false
  EnableSMSAckListener: true

AFUVendorUpdatePublisher:
  QueueName: "staging-afu-vendor-update-queue"

AFUVendorUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-afu-vendor-update-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~5 min post that regular interval is followed for next 3hr20mins
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 5
          MaxAttempts: 7
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 5
          MaxAttempts: 40
          TimeUnit: "Minute"
      MaxAttempts: 47
      CutOff: 6

AFUManualReviewNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-afu-manual-review-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

ProcessPinAttemptsExceededEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-auth-pin-attempts-exceeded-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

BiometricEventPublisher:
  QueueName: "staging-biometrics-details-queue"

AuthFactorUpdatePublisher:
  TopicName: "staging-auth-factor-update"

DeviceReregCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-device-rereg-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Second"

DeviceRegSMSAckSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-device-reg-sms-ack-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Second"


ReturnOtpTokenOnError: true

DeviceLocationCacheConfig:
  IsCachingEnabled: true
  CacheTTl: "2m"
  DeviceLocationPrefix: "device_location_"

Tracing:
  Enable: true

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

DeviceRegistration:
  SMSAckUrl: "https://vnotificationgw.staging.pointz.in/openbanking/auth/federal/user-device/registration-sms-update"

Keycloak:
  RealmName: "Onboarding"
  BaseURL: "https://keycloak.pointz.in/"

AuthSecrets:
  SecretsKey: "staging/auth/secrets"

TokenStoresCacheConfig:
  UseTokenStoresDaoV2: true

AuthTokenCreationPublisher:
  TopicName: "staging-auth-token-creation-topic"

NetworthMcpConfig:
  LoginUrl: "https://web.staging.pointz.in/wealth-mcp-login?token="
