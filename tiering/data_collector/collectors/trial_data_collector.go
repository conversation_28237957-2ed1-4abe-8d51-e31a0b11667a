package collectors

import (
	"context"
	"errors"

	"github.com/epifi/be-common/pkg/epifierrors"
	tieringPb "github.com/epifi/gamma/api/tiering"
	"github.com/epifi/gamma/tiering/config/genconf"
	"github.com/epifi/gamma/tiering/dao"
	"github.com/epifi/gamma/tiering/data_collector/options"
)

type TrialDataCollector struct {
	gconf             *genconf.Config
	actorTierInfosDao dao.ActorTierInfoDao
}

func NewTrialDataCollector(
	gconf *genconf.Config,
	actorTierInfosDao dao.ActorTierInfoDao,
) *TrialDataCollector {
	return &TrialDataCollector{
		gconf:             gconf,
		actorTierInfosDao: actorTierInfosDao,
	}
}

func (t *TrialDataCollector) CollectData(ctx context.Context, actorId string, opts ...options.DataCollectorOptions) (*tieringPb.CollectedData, error) {
	actorTierInfo, err := t.actorTierInfosDao.Get(ctx, actorId, tieringPb.ActorTierInfoFieldMask_ACTOR_TIER_INFO_FIELD_MASK_TRIAL_DETAILS)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return nil, err
	}

	if actorTierInfo.GetTrialDetails() == nil {
		return nil, nil
	}

	return &tieringPb.CollectedData{
		Data: &tieringPb.CollectedData_TrialData{
			TrialData: &tieringPb.TrialData{
				TrialDetails: actorTierInfo.GetTrialDetails(),
			},
		},
	}, nil
}
