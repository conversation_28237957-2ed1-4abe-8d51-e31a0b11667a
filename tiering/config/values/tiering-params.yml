Flags:
  TrimDebugMessageFromStatus: true

Tracing:
  Enable: false

TieringFeatureRelease:
  IsFeatureEnabled: false
  RolloutPercentage: 0
  AllowedGroups: [ ]

ToShowUpgradeNotifForSalary: true
ToShowUpgradeNotifForSalaryLite: false
ToShowUpgradeNotifForSalaryBasic: true

NotificationConfigMap:
  - NOTIFICATION_TYPE_UPGRADE:
      IsEnabled: false
      ExpireAfter: 168h # 7 days
      AutoDismissOnHomeAfterInSeconds: 60
  - NOTIFICATION_TYPE_DOWNGRADE:
      IsEnabled: false
      ExpireAfter: 720h # 30 days
      AutoDismissOnHomeAfterInSeconds: 60
  - NOTIFICATION_TYPE_GRACE:
      IsEnabled: false
      AutoDismissOnHomeAfterInSeconds: 60

ActorTierInfoCacheConfig:
  IsCachingEnabled: false
  Prefix: "TIERING:ATI:"
  CacheTTl: "48h" # 2 days

EligibleTierMovementCacheConfig:
  IsCachingEnabled: false
  Prefix: "TIERING:ETM:"
  CacheTTl: "1h"

TierMovementHistoryCacheConfig:
  IsCachingEnabled: false
  Prefix: "TIERING:TMH:"
  CacheTTl: "1h"

CxMovementHistoryLimit: 50

EnableGoodUserSilentGraceSegmentation: true
EnableRewardsAbuserSegmentation: false

# Feature flag to control whether to use tiering_abuser table for abuser checks
# When enabled, uses table-based approach instead of segment-based approach
EnableTieringAbuserDbCheck: false

SegmentIds:
  GoodUser: "test-segment"
  RewardsAbuser: "AWS_test-segment"
  HigherTiersCashbackRewardInEligibleUser: "AWS_test-segment"

TierToIneligibleActorSegmentIdMap:
  - TIER_FI_PLUS: "AWS_test-segment"
  - TIER_FI_INFINITE: "AWS_test-segment"
  - TIER_FI_AA_SALARY_BAND_3: "AWS_test-segment"

SilentGraceTierSegmentsMap:
  - TIER_ONE_HUNDRED: "test-segment-plus"
  - TIER_ONE_THOUSAND: "test-segment-infinite"

MinAndroidVersion: 1
MinIosVersion: 1

EnableHomeTopBarBanner: false
EnablePromoWidget: false

GraceWindowDuration: 168h # 7 days
DowngradeWindowDuration: 120h # 5 days
GraceInitialWindowDuration: 0h # 0 days

PinotConfig:
  AppName: "tiering-eod-balance"

TierEODBalanceServiceConfigMap:
  - TIER_TEN:
      Enable: false
  - TIER_ONE_HUNDRED:
      Enable: false
      EODBalanceAggregateDuration: 1080h # 45 days
      MinAggregationDays: 30
      AverageBalanceThreshold: 10000
  - TIER_ONE_THOUSAND:
      Enable: false
      EODBalanceAggregateDuration: 1080h # 45 days
      MinAggregationDays: 30
      AverageBalanceThreshold: 10000
  - TIER_ONE_THOUSAND_FIVE_HUNDRED:
      Enable: false
  - TIER_TWO_THOUSAND:
      Enable: false
  - TIER_ONE_THOUSAND_TWO_NINETY:
      Enable: true
      EODBalanceAggregateDuration: 1080h # 45 days
      MinAggregationDays: 30
      AverageBalanceThreshold: 80000

AutoUpgradeConfig:
  DisableProvenanceCheck: false
  DisableSalaryTierCheck: false
  DisableFirstTierMovementCheck: false
  DisableAutoUpgradeCutoffCheck: false
  DisableUserSpecificChecks: false
  DisableHigherTierCheck: false
  DisableAverageBalanceCheck: false
  AvgBalanceDurationBuffer: 24h
  DisableBeenInTieringCheck: false
  DisableToTierConditionMap:
    Standard: true
    Plus: false
    Infinite: false
    SalaryLite: true
    Salary: true
    SalaryBasic: true
  AutoUpgradeAvgBalanceConditionMap:
    - TIER_ONE_HUNDRED:
        AverageBalanceConditionMap:
          - "168h": 5000 # 7 days
          - "720h": 2000 # 30 days
    - TIER_ONE_THOUSAND:
        AverageBalanceConditionMap:
          - "168h": 30000 # 7 days
          - "720h": 10000 # 30 days

TierMovementHistoriesDbMaxPageSize: 25
EnableTierDowngrades: true

IsReactivationPromoWidgetEnabled: true

FeatureReleaseConfig:
  FeatureConstraints:
    - FEATURE_REGULAR_TIER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_TIERING_MULTIPLE_WAYS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 100000
          MinIOSVersion: 100000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
    - FEATURE_TIERING_PITCH_THROUGH_PROMO_WIDGET:
        AppVersionConstraintConfig:
          MinAndroidVersion: 100000
          MinIOSVersion: 100000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0

RegularTierLaunchDate: "2024-10-01T00:00:00+05:30"
ShouldRoundGraceToNextDay: true

TieringBalanceBasedPitchParams:
  BalanceForPitchingInfiniteForBasic: 25000
  BalanceForPitchingPrimeForBasic: 50000

#Trials are planned to be run in phases
#we will have start date and end dates for displaying the entry points
#Trial will end for all the users on TrialEndDate irrespective of the date on which user opts in for the trial
#we will give users a trial period of at-least 15 days + 7 days of grace.
#hence TrialEndDate should be equal to TrialEntryPointEndDate + 22 days
TieringTrialConfig:
  TrialEntryPointStartDate: "2025-08-01T00:00:00+05:30"
  TrialEntryPointEndDate: "2025-08-28T00:00:00+05:30"
  TrialEndDate: "2025-09-19T00:00:00+05:30"
  MinimumTrialDuration: 528h # 22 days
  PlusTrialSegments:
    - "AWS_trial-test-segment-plus"
  InfiniteTrialSegments:
    - "AWS_trial-test-segment-infinite"
  PrimeTrialSegments:
    - "AWS_trial-test-segment-prime"
