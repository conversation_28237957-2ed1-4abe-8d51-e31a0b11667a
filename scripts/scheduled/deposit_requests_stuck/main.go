// nolint:govet
package main

import (
	"context"
	"encoding/csv"
	"fmt"
	"os"
	"time"

	"github.com/pkg/errors"
	"github.com/slack-go/slack"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	depositPb "github.com/epifi/gamma/api/deposit"
	"github.com/epifi/gamma/deposit/dao/model"
	"github.com/epifi/gamma/scripts/scheduled/deposit_requests_stuck/config"
)

func uploadDepositRequestsToSlack(ctx context.Context, depositRequests []*model.DepositRequest, slackClient *slack.Client, channelID string) error {
	if len(depositRequests) == 0 {
		message := "No deposit requests which are stuck"
		_, _, err := slackClient.PostMessage(channelID, slack.MsgOptionText(message, false))
		return err
	}

	// Create temporary CSV file
	fileName := "deposit_requests_stuck.csv"
	file, err := os.Create(fileName)
	if err != nil {
		logger.Error(ctx, "failed to create file", zap.Error(err))
		return err
	}

	wr := csv.NewWriter(file)
	defer func() {
		if err1 := file.Close(); err1 != nil {
			logger.Error(ctx, fmt.Sprintf("error closing writer: %v", err1))
		}
		wr.Flush()
		_ = os.Remove(fileName)
	}()

	// Write CSV headers
	headers := []string{
		"Deposit Request ID",
		"Actor ID",
		"Type",
		"Client Request ID",
		"Created At",
		"Vendor Request ID",
		"State",
		"Last Attempt",
		"Deposit Account ID",
		"Partner Bank",
		"Detailed Status",
	}
	if err := wr.Write(headers); err != nil {
		return errors.Wrap(err, "failed to write CSV headers")
	}

	// Write deposit request data
	for _, request := range depositRequests {

		detailedStatus := ""
		if request.DetailedStatus != nil && request.DetailedStatus.GetDetailedStatusList() != nil {
			detailedStatusList := request.DetailedStatus.GetDetailedStatusList()
			if len(detailedStatusList) > 0 {
				detailedStatus = fmt.Sprintf("%v", detailedStatusList[len(detailedStatusList)-1])
			}
		}

		record := []string{
			request.Id,
			request.ActorId,
			request.Type.String(),
			request.ClientRequestId,
			request.CreatedAt.Format(time.RFC3339),
			request.RequestId,
			request.State.String(),
			fmt.Sprintf("%v", request.LastAttempt),
			request.DepositAccountId.String,
			request.PartnerBank.String(),
			detailedStatus,
		}
		if err := wr.Write(record); err != nil {
			return errors.Wrap(err, "failed to write CSV record")
		}
	}
	wr.Flush()

	if err := wr.Error(); err != nil {
		return errors.Wrap(err, "error flushing CSV writer")
	}

	// Upload file to Slack
	currentTime := time.Now()
	params := slack.FileUploadParameters{
		File:           fileName,
		Filename:       fmt.Sprintf("deposit_requests_stuck_%v.csv", currentTime.In(datetime.IST).Format("2006-01-02")),
		InitialComment: fmt.Sprintf("Deposit Requests Stuck Report - Total: %d", len(depositRequests)),
		Channels:       []string{channelID},
	}

	_, err = slackClient.UploadFile(params)
	if err != nil {
		return errors.Wrap(err, "failed to upload file to Slack")
	}

	return nil
}

// nolint:funlen
func main() {
	// Get environment
	envName, err := cfg.GetEnvironment()
	if err != nil {
		panic(fmt.Errorf("failed to get environment: %w", err))
	}

	// Setup logger
	logger.Init(envName)
	defer func() { _ = logger.Log.Sync() }()

	// Load deposit configuration
	depositConf, err := config.Load()
	if err != nil {
		panic(err)
	}

	// Initialize Slack client if secrets are configured
	var slackClient *slack.Client
	if depositConf.Secrets != nil && depositConf.Secrets.Ids != nil {
		slackClient = slack.New(depositConf.Secrets.Ids.SlackBotOauthToken, slack.OptionDebug(true))
	}

	// Connect to Epifi CRDB
	dbConn, err := storagev2.NewCRDBWithConfig(depositConf.EpifiDb, depositConf.Tracing.Enable)
	if err != nil {
		panic(err)
	}
	sqlDb, err := dbConn.DB()
	if err != nil {
		logger.Panic("failed to get sql DB", zap.Error(err))
	}
	storagev2.InitDefaultCRDBTransactionExecutor(dbConn)
	defer func() { _ = sqlDb.Close() }()
	db := gormctxv2.FromContextOrDefault(context.Background(), dbConn)

	// Runs at 12am everyday
	year, month, day := time.Now().Date()
	todayStartTime := time.Date(year, month, day, 0, 0, 0, 0, time.Local)
	depositRequestEndTime := todayStartTime.Add(-12 * time.Hour)

	depositRequests := make([]*model.DepositRequest, 0)

	// If this runs on say 25th May 00:00 UTC it'll report deposit requests whose creation date is < 24th May 00:00 UTC.
	if err := db.Where("state in (?) AND created_at <= ? AND type != ?", []depositPb.RequestState{
		depositPb.RequestState_REQUEST_IN_PROGRESS, depositPb.RequestState_REQUEST_INITIATED, depositPb.RequestState_REQUEST_UNKNOWN,
	}, depositRequestEndTime, depositPb.RequestType_MATURITY_ACTION).
		Order("created_at ASC").
		Find(&depositRequests).Error; err != nil {
		logger.ErrorNoCtx("failed to fetch stuck pre-closure deposit requests",
			zap.Error(err),
		)
		return
	}
	// Send report to Slack if client is initialized
	const slackChannelID = "C04S8KRHHD5"
	if slackClient != nil {
		ctx := context.Background()
		logger.InfoNoCtx(fmt.Sprintf("Attempting to send Slack message to channel: %s", slackChannelID))
		if err := uploadDepositRequestsToSlack(ctx, depositRequests, slackClient, slackChannelID); err != nil {
			logger.ErrorNoCtx("failed to upload report to slack", zap.Error(err))
		} else {
			logger.InfoNoCtx("Successfully sent Slack message")
		}
	}

	failDepositRequestQuery := "UPDATE deposit_requests SET state = 'REQUEST_FAILED' where id in (%s) and state = 'REQUEST_IN_PROGRESS' and last_attempt = true and type = 'PRECLOSE';"
	depositRequestsIdList := ""

	failOrderQuery := "UPDATE orders SET status='FULFILLMENT_FAILED' where external_id in (%s) and status = 'MANUAL_INTERVENTION'  and workflow = 'PRECLOSE_DEPOSIT';"
	externalIdList := ""
	logger.InfoNoCtx(fmt.Sprintf("Total deposit requests stuck: %d", len(depositRequests)))
	if len(depositRequests) > 0 {
		logger.InfoNoCtx("deposit requests stuck for more than 24 hours: \n")
		for _, depositRequest := range depositRequests {
			if depositRequest.Type == depositPb.RequestType_PRECLOSE && depositRequest.CreatedAt.After(time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC)) &&
				depositRequest.LastAttempt && depositRequest.State == depositPb.RequestState_REQUEST_IN_PROGRESS {
				depositRequestsIdList += "'" + depositRequest.Id + "',"
				externalIdList += "'" + depositRequest.ClientRequestId + "',"
			}
			logger.InfoNoCtx(fmt.Sprintf("depositRequestId: %s, actorID: %s, depositRequestType: %s, ClientRequestId: %s, createdAt: %s, vendorReqId: %s\n", depositRequest.Id, depositRequest.ActorId, depositRequest.Type, depositRequest.ClientRequestId, depositRequest.CreatedAt, depositRequest.RequestId))
		}
	} else {
		logger.InfoNoCtx("No deposit requests which are stuck")
	}

	if len(depositRequestsIdList) > 0 {
		finalDRQuery := fmt.Sprintf(failDepositRequestQuery, depositRequestsIdList[:len(depositRequestsIdList)-1])
		if err = db.Exec(finalDRQuery).Error; err != nil {
			logger.InfoNoCtx("failed to update deposit request state", zap.Error(err))
		} else {
			logger.InfoNoCtx("successfully failed deposit requests")
		}
	}

	if len(externalIdList) > 0 {
		finalOrderQuery := fmt.Sprintf(failOrderQuery, externalIdList[:len(externalIdList)-1])
		if err = db.Exec(finalOrderQuery).Error; err != nil {
			logger.InfoNoCtx("failed to update order state", zap.Error(err))
		} else {
			logger.InfoNoCtx("successfully failed orders")
		}
	}
}
