Application:
  Environment: "test"
  Name: "risk-fix-form-questionnaire-mappings"

FRMPgdb:
  AppName: "risk"
  DbType: "PGDB"
  StatementTimeout: 5s
  Name: "frm_pgdb"
  Host: "localhost"
  Port: 5432
  Username: "postgres"
  Password: "test"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 20
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

IsFRMPgdbEnabled: true

AWS:
  Region: "ap-south-1"

Secrets:
  Ids:
    FRMPgdbUsernamePassword: "postgres:test"
