Application:
  Environment: "prod"
  Name: "risk-fix-form-questionnaire-mappings"

FRMPgdb:
  AppName: "risk"
  DbType: "PGDB"
  StatementTimeout: 5s
  Name: "frm_pgdb"
  EnableDebug: false
  SSLMode: "require"
  MaxOpenConn: 20
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "ERROR"
    SlowQueryLogThreshold: 500ms
    UseInsecureLog: false

IsFRMPgdbEnabled: true

AWS:
  Region: "ap-south-1"

Secrets:
  Ids:
    FRMPgdbUsernamePassword: "prod/rds/postgres/frm_pgdb_user"
