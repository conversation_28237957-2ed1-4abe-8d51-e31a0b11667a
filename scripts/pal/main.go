package main

import (
	"context"
	"database/sql"
	"flag"
	"fmt"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/rudderlabs/analytics-go"
	"go.uber.org/zap"
	gormv2 "gorm.io/gorm"

	celestialPb "github.com/epifi/be-common/api/celestial"
	commonTypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	awsconfpkg "github.com/epifi/be-common/pkg/aws/v2/config"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/aws/v2/sqs"
	"github.com/epifi/be-common/pkg/cfg"
	pkgcmdcfg "github.com/epifi/be-common/pkg/cmd/config"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/crypto/cryptormap"
	"github.com/epifi/be-common/pkg/crypto/pgp"
	"github.com/epifi/be-common/pkg/epifigrpc"
	epifitemporalClient "github.com/epifi/be-common/pkg/epifitemporal/client"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	"github.com/epifi/be-common/pkg/storage"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	conf3 "github.com/epifi/be-common/pkg/vendorapi/config"
	genconf3 "github.com/epifi/be-common/pkg/vendorapi/config/genconf"

	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	actorPb "github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/auth/developer"
	livenessPb "github.com/epifi/gamma/api/auth/liveness"
	livenessDevPb "github.com/epifi/gamma/api/auth/liveness/developer"
	orchPb "github.com/epifi/gamma/api/auth/orchestrator"
	bankCustPb "github.com/epifi/gamma/api/bankcust"
	brePb "github.com/epifi/gamma/api/bre"
	"github.com/epifi/gamma/api/collection"
	commsPb "github.com/epifi/gamma/api/comms"
	analyticsCa "github.com/epifi/gamma/api/connected_account/analytics"
	"github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/creditreportv2"
	docsPb "github.com/epifi/gamma/api/docs"
	creditreportFepb "github.com/epifi/gamma/api/frontend/credit_report"
	fePalPb "github.com/epifi/gamma/api/frontend/preapprovedloan"
	mfexternalorderpb "github.com/epifi/gamma/api/investment/mutualfund/external"
	kycPb "github.com/epifi/gamma/api/kyc"
	vkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	"github.com/epifi/gamma/api/nudge"
	orderpb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/pay"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	"github.com/epifi/gamma/api/preapprovedloan"
	palCx "github.com/epifi/gamma/api/preapprovedloan/cx"
	savingsPb "github.com/epifi/gamma/api/savings"
	userPb "github.com/epifi/gamma/api/user"
	userDevPb "github.com/epifi/gamma/api/user/developer"
	"github.com/epifi/gamma/api/user/obfuscator"
	beOnbPb "github.com/epifi/gamma/api/user/onboarding"
	ekycPb "github.com/epifi/gamma/api/vendorgateway/ekyc"
	"github.com/epifi/gamma/api/vendorgateway/lending/collections/credgenics"
	"github.com/epifi/gamma/api/vendorgateway/lending/digitap"
	"github.com/epifi/gamma/api/vendorgateway/lending/lms/finflux"
	palVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan"
	vgIdfcPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/idfc"
	ldcVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden"
	palLlVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/liquiloans"
	moneyviewVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/moneyview"
	"github.com/epifi/gamma/api/vendorgateway/lending/securedloans/fiftyfin"
	accountVgPb "github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
	panVgPb "github.com/epifi/gamma/api/vendorgateway/pan"
	"github.com/epifi/gamma/pkg/persistentqueue"
	palConfig "github.com/epifi/gamma/preapprovedloan/config"
	commonGenConf "github.com/epifi/gamma/preapprovedloan/config/common/genconf"
	worker "github.com/epifi/gamma/preapprovedloan/config/worker"
	palWorkerGConf "github.com/epifi/gamma/preapprovedloan/config/worker/genconf"
	"github.com/epifi/gamma/scripts/pal/config"
	"github.com/epifi/gamma/scripts/pal/job"
	"github.com/epifi/gamma/scripts/pal/wire"
	"github.com/epifi/gamma/vendormapping/dao"
)

// jobName and jobArguments to be given as input
// refer the particular job.go file for the sample input format

// Example: (JobName should be enum)
// "JobName", "RESOLVE_LOEC"
// "Arguments", `{"filePath":"","isDecryptionNeeded":false,"case":1,"batchID":"LL_PA_PL_ONBOARDED_LP1","loanScheme":""}`

var (
	jobName   = flag.String("JobName", "", "refer job_names.go for the jobName input")
	arguments = flag.String("Arguments", "", "refer the particular job file for the sample input format")
	filePath1 = flag.String("FilePath1", "", "file path when a file is provided as input to the job")
)

// nolint:funlen
func main() {
	flag.Parse()
	env, err := cfg.GetEnvironment()
	if err != nil {
		panic(err)
	}

	logger.Init(env)
	defer func() { _ = logger.Log.Sync() }()

	conf, err := config.Load()
	if err != nil {
		logger.Panic("failed to load config", zap.Error(err))
		return
	}

	creditReportCommonConf := &commonGenConf.CreditReportConfig{}
	_ = creditReportCommonConf.SetUseCreditReportV2(false, false, nil)

	var dbConn *gormv2.DB

	if conf.PgdbMigrationFlag {
		dbConn, err = storagev2.NewPostgresDBWithConfig(conf.FederalPgDb, false)
		if err != nil {
			logger.Panic("unable to connect to db", zap.Error(err))
		}
	} else {
		dbConn, err = storagev2.NewCRDBWithConfig(conf.FederalDb, false)
		if err != nil {
			logger.Panic("unable to connect to db", zap.Error(err))
		}
	}
	sqlDb, err := dbConn.DB()
	if err != nil {
		logger.Panic("failed to get sql DB", zap.Error(err))
	}
	logger.InfoNoCtx(fmt.Sprintf("Successfully connected to DB: %s on %s:%d with debug mode: %t",
		conf.FederalDb.GetName(), conf.FederalDb.Host, conf.FederalDb.Port, conf.FederalDb.EnableDebug),
		zap.String("service", conf.Application.Name))
	defer func() { _ = sqlDb.Close() }()

	// This is only being used by credit report service, and used for credit report flattening job. This is not being migrated now
	epifiDb, err := storagev2.NewCRDBWithConfig(conf.EpifiDb, false)
	if err != nil {
		logger.Panic("Failed to load Epifi DB", zap.Error(err))
		return
	}

	var (
		dbConnProvider      *storagev2.DBResourceProvider[*gormv2.DB]
		txnExecutorProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor]
		dbConnTeardown      func()

		dbConnProvider2 *storagev2.DBResourceProvider[*gormv2.DB]
		dbConnTeardown2 func()
	)

	if conf.PgdbMigrationFlag {
		dbConnProvider, txnExecutorProvider, dbConnTeardown, err = storagev2.NewDBResourceProvider(conf.PgDbConfigMap.GetOwnershipToDbConfigMap(), false)
		if err != nil {
			logger.Panic("failed to get common db resource provider", zap.Error(err))
		}
	} else {
		dbConnProvider, txnExecutorProvider, dbConnTeardown, err = storagev2.NewCRDBResourceProvider(conf.DbConfigMap.GetOwnershipToDbConfigMap(), false)
		if err != nil {
			logger.Panic("failed to get db resource provider", zap.Error(err))
		}
	}

	dbConnProvider2, _, dbConnTeardown2, err = storagev2.NewCRDBResourceProvider(conf.DbConfigMap.GetOwnershipToDbConfigMap(), false)
	if err != nil {
		logger.Panic("failed to get db resource provider", zap.Error(err))
	}

	defer dbConnTeardown()
	defer dbConnTeardown2()

	// Connect to FeatureEngineering PGDB (GORM)
	pgdbConn, err := storagev2.NewPostgresDBWithConfig(conf.FeatureEngineeringDb, false)
	var pgsqlDb *sql.DB
	if err != nil {
		if !cfg.IsTestEnv(conf.Application.Environment) {
			logger.Panic("unable to connect to pgdb for PG", zap.Error(err))
		}
	} else {
		pgsqlDb, err = pgdbConn.DB()
		if err != nil {
			logger.Panic("failed to get sql DB", zap.Error(err))
		} else {
			logger.InfoNoCtx(fmt.Sprintf("Successfully connected to PGDB DB: %s on %s:%d with debug mode: %t",
				conf.FeatureEngineeringDb.GetName(), conf.FeatureEngineeringDb.Host, conf.FeatureEngineeringDb.Port, conf.FeatureEngineeringDb.EnableDebug),
				zap.String("service", conf.Application.Name))
		}
	}
	defer func() {
		if pgsqlDb != nil {
			_ = pgsqlDb.Close()
		}
	}()

	connectedAccountPGDB, err := storagev2.NewPostgresDBWithConfig(conf.PgDbConfigMap.GetOwnershipToDbConfigMap()[commonTypes.Ownership_CONNECTED_ACCOUNT_WEALTH], true)
	if err != nil {
		logger.Panic("failed to initialise ca PGDB", zap.Error(err))
	}
	connectedAccountPGDBSqlDb, err := connectedAccountPGDB.DB()
	if err != nil {
		logger.Panic("failed to get sql database ca PGDB", zap.Error(err))
	}
	defer func() { _ = connectedAccountPGDBSqlDb.Close() }()

	var pgdbTxnExecutor types.PGDBTxnExecutor = storagev2.NewGormTxnExecutor(pgdbConn)

	awsConf, err := awsconfpkg.NewAWSConfig(context.Background(), conf.Aws.Region, false)
	if err != nil {
		logger.Panic("failed to initialise aws", zap.Error(err))
	}
	s3Client := s3.NewClient(awsConf, conf.Aws.S3.EligibleUsersBucketName)
	dataDevS3Client := s3.NewClient(awsConf, conf.RawDataDevBucketName)
	palS3Client := s3.NewClient(awsConf, conf.CredgenicsNACHPaymentsPostingConf.PreApprovedLoanBucketName)
	dsaReportS3Client := s3.NewClient(awsConf, conf.Aws.S3.DSAReportS3BucketName)

	actorConn := epifigrpc.NewConnByService(cfg.ACTOR_SERVICE)
	defer epifigrpc.CloseConn(actorConn)
	actorClient := actorPb.NewActorClient(actorConn)

	userConn := epifigrpc.NewConnByService(cfg.USER_SERVICE)
	defer epifigrpc.CloseConn(userConn)
	userClient := userPb.NewUsersClient(userConn)

	bankCustConn := epifigrpc.NewConnByService(cfg.BANK_CUSTOMER_SERVICE)
	defer epifigrpc.CloseConn(bankCustConn)
	bankCustClient := bankCustPb.NewBankCustomerServiceClient(bankCustConn)

	vgConn := epifigrpc.NewConnByService(cfg.VENDOR_GATEWAY_SERVICE)
	defer epifigrpc.CloseConn(vgConn)
	palVgClient := palVgPb.NewPreApprovedLoanClient(vgConn)
	accountVgClient := accountVgPb.NewAccountsClient(vgConn)
	palLlVgClient := palLlVgPb.NewLiquiloansClient(vgConn)
	ekycVgClient := ekycPb.NewEKYCClient(vgConn)
	vgIdfcClient := vgIdfcPb.NewIdfcClient(vgConn)
	digitapServiceClient := digitap.NewDigitapServiceClient(vgConn)

	authConn := epifigrpc.NewConnByService(cfg.AUTH_SERVICE)
	defer epifigrpc.CloseConn(authConn)
	livenessConn := epifigrpc.NewConnByService(cfg.LIVENESS_SERVICE)
	defer epifigrpc.CloseConn(livenessConn)
	livenessClient := livenessPb.NewLivenessClient(livenessConn)
	authClient := authPb.NewAuthClient(authConn)
	orchClient := orchPb.NewOrchestratorClient(authConn)
	devAuthClient := developer.NewDevAuthClient(authConn)
	devLivenessClient := livenessDevPb.NewDevLivenessClient(authConn)

	commsConn := epifigrpc.NewConnByService(cfg.COMMS_SERVICE)
	defer epifigrpc.CloseConn(commsConn)
	commsClient := commsPb.NewCommsClient(commsConn)

	savingsConn := epifigrpc.NewConnByService(cfg.SAVINGS_SERVICE)
	defer epifigrpc.CloseConn(savingsConn)
	savingsClient := savingsPb.NewSavingsClient(savingsConn)

	kycConn := epifigrpc.NewConnByService(cfg.KYC_SERVICE)
	defer epifigrpc.CloseConn(kycConn)
	vkycClient := vkycPb.NewVKYCClient(kycConn)
	kycClient := kycPb.NewKycClient(kycConn)

	pQueue := persistentqueue.NewPersistentQueue(dbConn)

	paymentConn := epifigrpc.NewConnByService(cfg.ORDER_SERVICE)
	defer epifigrpc.CloseConn(paymentConn)
	paymentClient := paymentPb.NewPaymentClient(paymentConn)

	orderConn := epifigrpc.NewConnByService(cfg.ORDER_SERVICE)
	defer epifigrpc.CloseConn(orderConn)
	orderClient := orderpb.NewOrderServiceClient(orderConn)

	palConn := epifigrpc.NewConnByService(cfg.PRE_APPROVED_LOAN_SERVICE)
	defer epifigrpc.CloseConn(palConn)
	palClient := preapprovedloan.NewPreApprovedLoanClient(palConn)
	palCxClient := palCx.NewCxClient(palConn)

	piConn := epifigrpc.NewConnByService(cfg.PAYMENT_INSTRUMENT_SERVICE)
	defer epifigrpc.CloseConn(piConn)
	piClient := piPb.NewPiClient(piConn)

	accountPiConn := epifigrpc.NewConnByService(cfg.PAYMENT_INSTRUMENT_SERVICE)
	defer epifigrpc.CloseConn(accountPiConn)
	accountPiClient := accountPiPb.NewAccountPIRelationClient(accountPiConn)

	onboardingConn := epifigrpc.NewConnByService(cfg.USER_SERVICE)
	defer epifigrpc.CloseConn(onboardingConn)
	onboardingClient := beOnbPb.NewOnboardingClient(onboardingConn)

	obfuscatorClient := obfuscator.NewObfuscatorClient(userConn)

	accountConn := epifigrpc.NewConnByService(cfg.ACCOUNTS_SERVICE)
	defer epifigrpc.CloseConn(accountConn)
	accountBalanceClient := accountBalancePb.NewBalanceClient(accountConn)

	collectionConn := epifigrpc.NewConnByService(cfg.COLLECTION_SERVICE)
	defer epifigrpc.CloseConn(collectionConn)
	collectionClient := collection.NewCollectionClient(collectionConn)

	celestialConn := epifigrpc.NewConnByService(cfg.CELESTIAL_SERVICE)
	defer epifigrpc.CloseConn(celestialConn)
	celestialClient := celestialPb.NewCelestialClient(celestialConn)

	docsConn := epifigrpc.NewConnByService(cfg.DOCS_SERVICE)
	docsClient := docsPb.NewDocsClient(docsConn)
	defer epifigrpc.CloseConn(docsConn)

	nudgeConn := epifigrpc.NewConnByService(cfg.NUDGE_SERVICE)
	nudgeClient := nudge.NewNudgeServiceClient(nudgeConn)
	defer epifigrpc.CloseConn(nudgeConn)

	payConn := epifigrpc.NewConnByService(cfg.PAY_SERVICE)
	payClient := pay.NewPayClient(payConn)
	defer epifigrpc.CloseConn(payConn)

	panVgClient := panVgPb.NewPANClient(vgConn)

	// load rudder-stack broker for sending events
	rudderClient, err := analytics.NewWithConfig(
		conf.Secrets.Ids[palConfig.RudderWriteKey], "http://"+cfg.GetServerEndPoint(cfg.RUDDER_SERVER).URL(),
		analytics.Config{
			Callback:  events.NewCallback(),
			Interval:  conf.RudderStack.IntervalInSec * time.Second, // nolint: durationcheck
			BatchSize: conf.RudderStack.BatchSize,
			Verbose:   conf.RudderStack.Verbose,
		},
	)
	if err != nil {
		panic(err)
	}
	rudderStackBroker := events.NewRudderStackBroker(rudderClient)
	defer rudderStackBroker.Close()

	epifiPgdbConn, err := storagev2.NewPostgresDBWithConfig(conf.EpifiPgdb, false)
	if err != nil {
		logger.Panic("failed to load postgres db: %v\n", zap.Error(err))
		return
	}

	sqlPgdb, _ := epifiPgdbConn.DB()
	defer func() { _ = sqlPgdb.Close() }()
	vendorMappingDao := dao.NewVendorMappingDao(epifiPgdbConn, nil)

	// Initialize AuthDb connection from PgDbConfigMap
	var authPgdbConn *gormv2.DB
	var sqlAuthDb *sql.DB
	if !cfg.IsProdEnv(conf.Application.Environment) {
		authPgdbConn, err = storagev2.NewPostgresDBWithConfig(conf.PgDbConfigMap["AuthDb"], false)
		if err != nil {
			logger.Panic("failed to connect to AuthDb: %v\n", zap.Error(err))
			return
		}
		sqlAuthDb, _ = authPgdbConn.DB()
		defer func() { _ = sqlAuthDb.Close() }()
	}

	cryptorStoreMap, err := initCryptors(conf)
	if err != nil {
		panic(err)
	}

	investmentConn := epifigrpc.NewConnByService(cfg.INVESTMENT_SERVICE)
	defer epifigrpc.CloseConn(investmentConn)
	mfExternalCleint := mfexternalorderpb.NewMFExternalOrdersClient(investmentConn)

	temporalClient, err := epifitemporalClient.NewWorkflowClient(conf.PalTemporalNamespace, true, conf.Secrets.Ids[pkgcmdcfg.TemporalCodecAesKey])
	if err != nil {
		logger.Panic("failed to initialise workflow client", zap.Error(err))
	}
	defer temporalClient.Close()

	ffVgClient := fiftyfin.NewFiftyFinClient(vgConn)
	// Create a gRPC client
	moneyViewVgPbclient := moneyviewVgPb.NewMoneyviewClient(vgConn)
	ldcVgClient := ldcVgPb.NewLendenClient(vgConn)

	credgenicsVgClient := credgenics.NewCredgenicsClient(vgConn)

	userCon := epifigrpc.NewConnByService(cfg.USER_SERVICE)
	defer epifigrpc.CloseConn(userCon)
	devUserClient := userDevPb.NewDevUserClient(userCon)
	consentClient := consent.NewConsentClient(userConn)

	creditReportPublisher := initSQSPublisher(context.Background(), conf.CreditReportFlattenPublisher, awsConf)

	finfluxClient := finflux.NewFinfluxClient(vgConn)

	redisStore := storage.NewRedisClientFromConfig(conf.RedisStore, false)
	defer func() {
		_ = redisStore.Close()
	}()
	centralgrowthConn := epifigrpc.NewServerConn(cfg.VENDOR_NOTIFI_SERVER, cfg.CENTRAL_GROWTH_SERVER)
	defer epifigrpc.CloseConn(centralgrowthConn)
	lendingConn := epifigrpc.NewConnByServer(cfg.LENDING_SERVER)
	analyticsClient := analyticsCa.NewAnalyticsClient(centralgrowthConn)
	creditReportManagerClient := creditreportv2.NewCreditReportManagerClient(lendingConn)

	breConn := epifigrpc.NewConnByService(cfg.BRE_SERVICE)
	defer epifigrpc.CloseConn(breConn)
	breClient := brePb.NewBreClient(breConn)

	workerConf := &worker.Config{
		Application: &cfg.TemporalWorkerApplication{
			Environment: conf.Application.Environment,
		},
		RawBucketScienapticBreDataFilePath: conf.RawBucketScienapticBreDataFilePath,
	}
	palWorkerGenConf := &palWorkerGConf.Config{}
	palWorkerGenConf.Init("")
	if err := palWorkerGenConf.Set(workerConf, false, nil); err != nil {
		logger.Panic("failed to set palWorkerGenConf", zap.Error(err))
	}

	vendorApiGenConf, _ := genconf3.NewConfig()
	vendorApiGenConf.Init()
	err = vendorApiGenConf.Set(&conf3.Config{
		HttpClientConfig: &cfg.HttpClient{
			Transport: struct {
				DialContext struct {
					Timeout   time.Duration
					KeepAlive time.Duration
				}
				TLSHandshakeTimeout time.Duration
				MaxIdleConns        int
				IdleConnTimeout     time.Duration
				MaxIdleConnsPerHost int
				MaxConnsPerHost     int
			}{
				DialContext: struct {
					Timeout   time.Duration
					KeepAlive time.Duration
				}{
					Timeout:   30 * time.Second,
					KeepAlive: 30 * time.Second,
				},
				TLSHandshakeTimeout: 10 * time.Second,
				MaxIdleConns:        100,
				IdleConnTimeout:     90 * time.Second,
				MaxConnsPerHost:     10,
				MaxIdleConnsPerHost: 10,
			},
		},
	}, false, nil)
	if err != nil {
		logger.Panic("failed to set palWorkerGenConf", zap.Error(err))
	}
	feConn := epifigrpc.NewConnByService(cfg.FRONTEND_SERVICE)
	defer epifigrpc.CloseConn(feConn)
	fePalClient := fePalPb.NewPreApprovedLoanClient(feConn)
	creditReportFeClient := creditreportFepb.NewCreditReportClient(feConn)

	jobRegistry := wire.InitialiseJobRegistry(dbConn, dbConnProvider, epifiDb, pgdbConn, pgdbTxnExecutor, palVgClient, accountVgClient, userClient, actorClient, bankCustClient, authClient, commsClient, savingsClient, s3Client, rudderStackBroker, conf, txnExecutorProvider, celestialClient,
		livenessClient, vkycClient, kycClient, nil, nil, orderClient, payClient, piClient, accountPiClient, paymentClient, nil, palLlVgClient, nil, orchClient, pQueue, nil, nil, fePalClient, creditReportFeClient, authPgdbConn, awsConf,
		vgIdfcClient, palClient, nil, creditReportManagerClient, onboardingClient, obfuscatorClient, accountBalanceClient, collectionClient,
		docsClient, panVgClient, ffVgClient, nil, nil, nil, dbConnProvider2, creditReportCommonConf, dataDevS3Client,
		cryptorStoreMap, mfExternalCleint, nil, ekycVgClient, moneyViewVgPbclient, nudgeClient, vendorMappingDao, creditReportPublisher, temporalClient, credgenicsVgClient, redisStore, palS3Client, palCxClient, consentClient, devAuthClient, devLivenessClient, devUserClient, nil,
		finfluxClient, nil, ldcVgClient, analyticsClient, breClient, palWorkerGenConf, connectedAccountPGDB, nil, digitapServiceClient, vendorApiGenConf, dsaReportS3Client)

	jobType := job.JobNames[*jobName]

	if err := jobRegistry.RunJob(jobType, *arguments, *filePath1); err != nil {
		panic(err)
	}

	return
}

func initCryptors(conf *config.Config) (*cryptormap.InMemoryCryptorStore, error) {
	cryptorStore := &cryptormap.InMemoryCryptorStore{}
	if conf.ExperianPgpKeySet != nil {
		// create a pgp cryptor to be used for experian communication encryption
		experianPgpCryptor := pgp.New(conf.ExperianPgpKeySet.VendorPublicKey,
			conf.ExperianPgpKeySet.PrivateKey, conf.ExperianPgpKeySet.PassPhrase)
		if experianPgpCryptor == nil {
			return nil, fmt.Errorf("failed to create experian PGP cryptor")
		}
		logger.InfoNoCtx("experianPgpCryptor created")
		cryptorStore.AddCryptor(commonvgpb.Vendor_EXPERIAN, commonvgpb.CryptorType_PGP, experianPgpCryptor)
		logger.InfoNoCtx("added experianPgpCryptor to cryptorStore")
	} else {
		logger.InfoNoCtx("nil ExperianPgpKeySet")
	}

	return cryptorStore, nil
}

func initSQSPublisher(ctx context.Context, conf *cfg.SqsPublisher, awsConf aws.Config) queue.Publisher {
	sqsClient := sqs.InitSQSClient(awsConf)
	cibilReportPublisher, err := sqs.NewPublisherWithConfig(ctx, conf, sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Panic(fmt.Sprintf("failed to initialize credit report flatten queue: %s", err))
	}
	return cibilReportPublisher
}
