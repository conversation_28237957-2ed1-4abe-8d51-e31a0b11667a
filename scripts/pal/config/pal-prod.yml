Application:
  Environment: "prod"
  Name: "pal"
  Namespace: "prod-pre-approved-loan"

EpifiDb:
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: false
  SSLMode: "verify-full"
  SSLRootCert: "prod/cockroach/ca.crt"
  SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 1
  MaxIdleConn: 1
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

FeatureEngineeringDb:
  StatementTimeout: 60s
  Name: "feature_engineering"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  MaxOpenConn: 50
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  SecretName: "prod/rds/epifimetis/feature_engineering_dev_user"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

FederalPgDb:
  DbType: "PGDB"
  AppName: "preapprovedloan"
  StatementTimeout: 10s
  Name: "loans_federal"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  SecretName: "prod/rds/epifimetis/loans_federal_dev_user"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

FederalDb:
  Username: "federal_dev_user"
  Password: ""
  Name: "federal"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/cockroach/ca.crt"
  SSLClientCert: "prod/cockroach/client.federal_dev_user.crt"
  SSLClientKey: "prod/cockroach/client.federal_dev_user.key"
  MaxOpenConn: 1
  MaxIdleConn: 1
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "ERROR"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

PgDbConfigMap:
  FEDERAL_BANK:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 10s
    Name: "loans_federal"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_federal_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  LIQUILOANS_PL:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 10s
    Name: "loans_liquiloans"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_liquiloans_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  IDFC_PL:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 10s
    Name: "loans_idfc"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_idfc_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  FIFTYFIN_LAMF:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 10s
    Name: "loans_fiftyfin"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_fiftyfin_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  MONEYVIEW_PL:
    DbType: "PGDB"
    AppName: "preapprovedloan-script"
    StatementTimeout: 10s
    Name: "loans_moneyview"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_moneyview_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  LOANS_ABFL:
    DbType: "PGDB"
    AppName: "preapprovedloan-script"
    StatementTimeout: 5s
    Name: "loans_abfl"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_abfl_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  LOANS_LENDEN:
    DbType: "PGDB"
    AppName: "preapprovedloan-script"
    StatementTimeout: 5s
    Name: "loans_lenden"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_lenden_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  LOANS_STOCK_GUARDIAN_LSP:
    DbType: "PGDB"
    AppName: "preapprovedloan-script"
    StatementTimeout: 5s
    Name: "loans_stock_guardian_lsp"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_stock_guardian_lsp_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  EPIFI_TECH_V2:
    DbType: "PGDB"
    AppName: "preapprovedloan-script"
    StatementTimeout: 5s
    Name: "loans_epifi_tech"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_epifi_tech_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  CONNECTED_ACCOUNT_WEALTH:
    DbType: "PGDB"
    AppName: "connectedaccount"
    StatementTimeout: 10s
    Name: "connected_account"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 20
    MaxIdleConn: 5
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/connected-account"
    GormV2:
      LogLevelGormV2: "SILENT"
      UseInsecureLog: false


DbConfigMap:
  FEDERAL_BANK:
    DBType: "CRDB"
    Username: "federal_dev_user"
    Password: ""
    Name: "federal"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.federal_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.federal_dev_user.key"
    MaxOpenConn: 1
    MaxIdleConn: 1
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  LIQUILOANS_PL:
    DBType: "CRDB"
    Username: "pl_liquiloans_dev_user"
    Password: ""
    Name: "pl_liquiloans"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.pl_liquiloans_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.pl_liquiloans_dev_user.key"
    MaxOpenConn: 1
    MaxIdleConn: 1
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  IDFC_PL:
    DBType: "CRDB"
    Username: "pl_idfc_dev_user"
    Password: ""
    Name: "pl_idfc"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.pl_idfc_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.pl_idfc_dev_user.key"
    MaxOpenConn: 1
    MaxIdleConn: 1
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  FIFTYFIN_LAMF:
    DBType: "CRDB"
    Username: "loans_fiftyfin_crdb_dev_user"
    Password: ""
    Name: "loans_fiftyfin_crdb"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.loans_fiftyfin_crdb_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.loans_fiftyfin_crdb_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "pre-approved-loan-worker"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms

EpifiPgdb:
  DbType: "PGDB"
  AppName: "vendormapping"
  StatementTimeout: 2m
  Name: "vendormapping"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  MaxOpenConn: 20
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  SecretName: "prod/rds/epifimetis/vendormapping"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

AWS:
  Region: "ap-south-1"
  S3:
    EligibleUsersBucketName: "epifi-prod-preapprovedloan"
    PreApprovedLoanBucketName: "epifi-prod-preapprovedloan"
    DSAReportS3BucketName: "epifi-prod-lending-dsa"

RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

Secrets:
  Ids:
    RudderWriteKey: "prod/rudder/internal-writekey"
    # Experian
    EpifiExperianPgpKeySet: "prod/pgp/epifi-experian-key-set"
    TemporalCodecAesKey: "prod/temporal/codec-encryption-key"
    LentraSecrets: "prod/vendorgateway/lentra-secrets"
    PreApprovedLoanSecrets: "prod/vendorgateway/lending-preapprovedloans-secrets"
    EpiFiFederalClientSslCert: "prod/vg-vgpci/tls-client-cert-for-federal-2024"
    EpiFiFederalClientSslKey: "prod/vg-vgpci/tls-client-priv-key-for-federal-2024"

PgdbMigrationFlag: true

RawBucketScienapticBreDataFilePath: "vendor/bre_scienaptic/bre_data/%s/%s-.csv"

RawDataDevBucketName: "epifi-raw"

CreditReportFlattenPublisher:
  QueueName: "prod-lending-credit-report-flattening-queue"

PalTemporalNamespace: "prod-pre-approved-loan"

ITRFileConf:
  ITRS3Bucket: "epifi-prod-itr-intimation"
  S3PrefixPath: "ITR_INT_PDF/"
  OutputS3Bucket: "epifi-prod-preapprovedloan-liquiloans-download"
  OutputS3PfxPath: "prod-preapprovedloan-liqlns-d/ITR-Docs/"

RedisStore:
  IsSecureRedis: true
  Options:
    Addr: "redis-10246.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:10246"
    Password: ""
  AuthDetails:
    SecretPath: "prod/redis/cards/prefixaccess"
    Environment: "prod"
    Region: "ap-south-1"

CredgenicsNACHPaymentsPostingConf:
  PreApprovedLoanBucketName: "epifi-prod-preapprovedloan"

CreditReportPurgeConfig:
  SoftDeletionMinAge: "4152h" # 173 days
  HardDeletionMinDuration: "1m"
  # This configuration ensures deletion occurs 20 days before the consent expires, providing a buffer in case the job fails and also ensuring that the 15 days backup will be under the consent period.
  CibilSoftDeletionMinAge: "-480h"
  CibilHardDeletionMinDuration: "1m"

DSAIdToEmailMap:
  loantap:
    - "<EMAIL>"
    - "<EMAIL>"
    - "<EMAIL>"
    - "<EMAIL>"
    - "<EMAIL>"
    - "<EMAIL>"
  mymoneymantra:
    - "<EMAIL>"
    - "<EMAIL>"
  mymoneymantrab2c:
    - "<EMAIL>"
    - "<EMAIL>"
    - "<EMAIL>"
    - "<EMAIL>"
  cashkuber:
    - "<EMAIL>"
    - "<EMAIL>"
  keshvacredit:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_cpadvisor_PL:
    - "<EMAIL>"
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_universaldsa_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_dsconsultants_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_pnsassociates_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_samayfinance_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_dineshkokadiya_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_vaishalikokadiya_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_chitracapital_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_atlantagroup_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_astarsolutions_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_ananyaenterprises_PL:
    - "<EMAIL>"
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_sathyamassociates_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_mdfcfinance_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_androfinancesolution_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_blrfinancialservices_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_mmfincorp_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_manikantaenterprises_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_quickindiainsuranceandloanservices_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_fastxloans_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_amgothenterprises_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_upliftadvisors_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_planetmoney_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_finance4u_PL:
    - "<EMAIL>"
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_rvfinserv_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_atharvamarketingandfinancialservices_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_bhorshafintech_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_whitepearlfinance_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_loancubedsa_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_omkarenterprises_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_amaninternational_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_shyamsundaryaligar_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_chaitanyarealty_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_vivekenterprises_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_saleemkhan_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  fintifi:
    - "<EMAIL>"
    - "<EMAIL>"
    - "<EMAIL>"
    - "<EMAIL>"
  switchmyloan:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_sastapaisa_PL:
    - "<EMAIL>"
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_kapilafinserv_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_mahavirhomeloans_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_yesgenesis_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_swarajtheeya_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_chiragpatel_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_finfreesolutions_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_ezeeloans_PL:
    - "<EMAIL>"
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_shivalkardattaram_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_capitalrise_PL:
    - "<EMAIL>"
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_easyloanfinancialservices_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_speedcashandspotcash_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_loanplus_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_hamzaconsultancy_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_nitingarg_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_guruhomeloans_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_proloancapital_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_dreambusinesssolution_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_rsfinancialsolutions_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_anmolfinance_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_mayankjaintaxnfinance_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_loanpointservices_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_fingate_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_srfinancialservices_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_shravicapitalservices_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_monakhuranacorporateadvisors_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_srilalithaassociates_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_jkfinservices_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_maulifinancialservices_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_skmfinance_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_divyaservices_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_brightcityassociates_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_goodwillcapital_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_sandeepkceasyfinancialservices_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_goverdhanlalkumawat_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_jaipurfunds_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_sdreamsolution_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_durgaramanandsingh_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_mthdigitalservices_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_sharadaloanservices_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_hyderabadloanz_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_mattayafinancialservices_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_someshvaidya_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_sripanthsarkar_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_laavanyaassociates_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_tsungroup_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_capitalonefinancialservices_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_kgfinvest_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_msudhakar_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_bhagwatiinfra_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_aegmacapitalservices_PL:
    - "<EMAIL>"
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_trutrustloans_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_assethousefinance_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_srivenfinancialservices_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_dreamkingconsultancy_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_eloanss_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_connectxglobalsolutions_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_grandcapitalfinancialservices_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_megafinance_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_vishwanathar_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_kabiluddinlaskar_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_omspsglobaltech_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_snnfinancialsolutions_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_pramaandigitaltechnologies_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_sybridglobalmanagement_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_srilasyaenterprises_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_sadulamahesh_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_kettekishorekumar_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_sonimoneyworld_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_shriramwealthplus_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_rlkassociates_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_princeenterprises_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_akconsultancy_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_palaniappans_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_ghanshyambhaibhammar_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_moneyking_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_ssvassociates_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_moneyyug_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_moneyplantinc_PLL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_expertfinserve_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_saanvifinancialservices_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_capitalcorps_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_loandoctor_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_apnafinanceservices_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_singaporefinancialservices_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_shreesiddhivinayakenterprises_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_gowthamsuryanarayana_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_gatijfinserv_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_aarambhfinserv_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_sbfinancialservices_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_pkfinancialservices_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_shrimahalaxmifinancialsolution_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_shivshaktifinancialagencies_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_moryafinanceandinsurance_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_fandcfinancecompany_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_mrmanjunathreddy_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_maniramshah_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_pankajverma_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_uttamkumar_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_solarflarefinancialservices_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_dalpatsinghpanwar_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_pitambaraenterprises_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_loankendra_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_dhruvafinancials_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_interloandistributionservices_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_pocketsolutionfinancialservice_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_quickfinancesolution_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_maabaglamukhifinance_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_milindmeshram_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_fortunewheels_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_ramji_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_sreefinserv_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_shubhamenterprise_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_kbnenterprise_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_k.g.nconsultancy_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_shahrukhkhan_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_biswajitroy_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_vipulvaja_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_rituraj_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_rakeshkumarsingh_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_shivshaktifinancialservices_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_shilpenmodi_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_deeeshafinandedutechnologies_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_marutifinancialservice_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_sanjaysinghverma_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_gopalkrishnaswain_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_sandeepkumar_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_rajnikhichi_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_janagammahesh_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_anupkumarsaini_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_naimjindani_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_tarunsahu_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_nilima_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_siddarthenterprises_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_amanvermaenterprises_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_zeeshanalikhan_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_prasantakumarnaik_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_ravindraraikwar_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_gromo_PL:
    - "<EMAIL>"
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_jkmfinanceservice_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_neoloansfin_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_justdigital_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_durgafinance:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_loanyaamifinserv_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_rajatthakral_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_awadheshsingh_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_creditbestfinancialsolutions_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_vinayakenterprises_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_camenterprises_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_bhoopendrakumar_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_vivekkumarchoubey_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_krishnatraders_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_maheshwarifintech_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_dhanbarshawealthadvisors_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_j&tfinances_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_mahadeventerprise_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_sanjitdebbarma_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_shreeshyamenterprises_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_royfincoropprivatelimited_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_pravindayaramdeshmukh_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_muniralamshaikh_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_shreesaimarketing_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_shribalajienterprises_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_amitdas_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_vikaskumarrathaur_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_mittapallyvenugopal_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_nileshkushwaha_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_dipankarsarkar_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_sumandasgupta_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_jsfinancial_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_icapitaservicespvtltd_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_ranonfinancialservices_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_abhishekkumarsrivastava_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_dharmeshpahadiya_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_surendraprasadmoharana_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_sbrandloanservice_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_sunilkumars_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_abhishekkumar_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_propitiousbusinessadvisoryprivatelimited_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_iquantustechtribe(opc)privatelimited_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_himanshuramu_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_amarjeetkumarthakur_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_surekhadevidasbhise_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_sumitkumar_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_devgautam_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_wealthcraftssolution_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_salauddinshamsuddinshemle_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_brijeshpratapsingh_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_nirmalkumarsinha_PL:
    - "<EMAIL>"
    - "<EMAIL>"

