Application:
  Environment: "qa"
  Name: "pal"
  Namespace: "qa-pre-approved-loan"

EpifiDb:
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLCertPath: "./crdb/qa/"
  SSLRootCert: "qa/cockroach/ca.crt"
  SSLClientCert: "qa/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "qa/cockroach/client.epifi_dev_user.key"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

FeatureEngineeringDb:
  StatementTimeout: 5s
  Name: "feature_engineering"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  SecretName: "qa/rds/epifimetis/feature_engineering_dev_user"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

FederalPgDb:
  DbType: "PGDB"
  AppName: "preapprovedloan"
  StatementTimeout: 5s
  Name: "loans_federal"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  SecretName: "qa/rds/epifimetis/loans_federal_dev_user"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

FederalDb:
  Username: "federal_dev_user"
  Password: ""
  Name: "federal"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "qa/cockroach/ca.crt"
  SSLClientCert: "qa/cockroach/client.federal_dev_user.crt"
  SSLClientKey: "qa/cockroach/client.federal_dev_user.key"
  MaxOpenConn: 1
  MaxIdleConn: 1
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

PgDbConfigMap:
  FEDERAL_BANK:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 5s
    Name: "loans_federal"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "qa/rds/epifimetis/loans_federal_dev_user"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  LIQUILOANS_PL:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 5s
    Name: "loans_liquiloans"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "qa/rds/epifimetis/loans_liquiloans_dev_user"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  IDFC_PL:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 5s
    Name: "loans_idfc"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "qa/rds/epifimetis/loans_idfc_dev_user"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  FIFTYFIN_LAMF:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 5s
    Name: "loans_fiftyfin"
    EnableDebug: true
    SSLMode: "disable"
    #    Enable once ssl secret permissions are added
    #    SSLMode: "verify-full"
    #    SSLRootCert: "qa/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "qa/rds/epifimetis/loans_fiftyfin_dev_user"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  MONEYVIEW_PL:
    DbType: "PGDB"
    AppName: "preapprovedloan-script"
    StatementTimeout: 5s
    Name: "loans_moneyview"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 3
    MaxIdleConn: 3
    MaxConnTtl: "30m"
    SecretName: "qa/rds/epifimetis/loans_moneyview_dev_user"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  LOANS_ABFL:
    DbType: "PGDB"
    AppName: "preapprovedloan-script"
    StatementTimeout: 5s
    Name: "loans_abfl"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "qa/rds/epifimetis/loans_abfl_dev_user"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  LOANS_LENDEN:
    DbType: "PGDB"
    AppName: "preapprovedloan-script"
    StatementTimeout: 5s
    Name: "loans_lenden"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "qa/rds/epifimetis/loans_lenden_dev_user"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  LOANS_STOCK_GUARDIAN_LSP:
    DbType: "PGDB"
    AppName: "preapprovedloan-script"
    StatementTimeout: 5s
    Name: "loans_stock_guardian_lsp"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "qa/rds/epifimetis/loans_stock_guardian_lsp_dev_user"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  EPIFI_TECH_V2:
    DbType: "PGDB"
    AppName: "preapprovedloan-script"
    StatementTimeout: 5s
    Name: "loans_epifi_tech"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "qa/rds/epifimetis/loans_epifi_tech_dev_user"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  CONNECTED_ACCOUNT_WEALTH:
    DbType: "PGDB"
    AppName: "connectedaccount"
    StatementTimeout: 5s
    Name: "connected_account"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "qa/rds/epifimetis/connected_account_dev_user"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  AuthDb:
    AppName: "auth"
    StatementTimeout: 5s
    Name: "auth"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 10
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    DbType: "PGDB"
    SecretName: "qa/rds/postgres/auth"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

DbConfigMap:
  FEDERAL_BANK:
    DBType: "CRDB"
    Username: "federal_dev_user"
    Password: ""
    Name: "federal"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "qa/cockroach/ca.crt"
    SSLClientCert: "qa/cockroach/client.federal_dev_user.crt"
    SSLClientKey: "qa/cockroach/client.federal_dev_user.key"
    MaxOpenConn: 1
    MaxIdleConn: 1
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  LIQUILOANS_PL:
    DBType: "CRDB"
    Username: "pl_liquiloans_dev_user"
    Password: ""
    Name: "pl_liquiloans"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "qa/cockroach/ca.crt"
    SSLClientCert: "qa/cockroach/client.pl_liquiloans_dev_user.crt"
    SSLClientKey: "qa/cockroach/client.pl_liquiloans_dev_user.key"
    MaxOpenConn: 1
    MaxIdleConn: 1
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  IDFC_PL:
    DBType: "CRDB"
    Username: "pl_idfc_dev_user"
    Password: ""
    Name: "pl_idfc"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "qa/cockroach/ca.crt"
    SSLClientCert: "qa/cockroach/client.pl_idfc_dev_user.crt"
    SSLClientKey: "qa/cockroach/client.pl_idfc_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  FIFTYFIN_LAMF:
    DBType: "CRDB"
    Username: "loans_fiftyfin_crdb_dev_user"
    Password: ""
    Name: "loans_fiftyfin_crdb"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "qa/cockroach/ca.crt"
    SSLClientCert: "qa/cockroach/client.loans_fiftyfin_crdb_dev_user.crt"
    SSLClientKey: "qa/cockroach/client.loans_fiftyfin_crdb_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "pre-approved-loan-worker"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

EpifiPgdb:
  DbType: "PGDB"
  AppName: "preapprovedloan"
  StatementTimeout: 2m
  Name: "vendormapping"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 20
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  SecretName: "qa/rds/postgres14"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

AWS:
  Region: "ap-south-1"
  S3:
    EligibleUsersBucketName: "epifi-qa-preapprovedloan"
    PreApprovedLoanBucketName: "epifi-qa-preapprovedloan"
    DSAReportS3BucketName: "epifi-qa-lending-dsa"


RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

Secrets:
  Ids:
    RudderWriteKey: "qa/rudder/internal-writekey"
    TemporalCodecAesKey: "qa/temporal/codec-encryption-key"


PgdbMigrationFlag: true

RawBucketScienapticBreDataFilePath: "qa/data/vendor/bre_scienaptic/bre_data/%s/%s-.csv"

RawDataDevBucketName: "epifi-raw-dev"

CreditReportFlattenPublisher:
  QueueName: "qa-lending-credit-report-flattening-queue"

PalTemporalNamespace: "qa-pre-approved-loan"

ITRFileConf:
  ITRS3Bucket: "epifi-qa-itr-intimation"
  S3PrefixPath: "ITR_INT_PDF/"
  OutputS3Bucket: "epifi-qa-preapprovedloan"
  OutputS3PfxPath: "itr-intimation/ITR-Docs/"

RedisStore:
  IsSecureRedis: true
  Options:
    Addr: "master.qa-common-cache-redis.rxzivf.aps1.cache.amazonaws.com:6379"
    Password: ""
    DB: 12
    PoolSize: 20 # this is set to twice the size of default connection pool size
    MinIdleConns: 10 # this allows min. number of conn to be readily available

CredgenicsNACHPaymentsPostingConf:
  PreApprovedLoanBucketName: "epifi-qa-preapprovedloan"
