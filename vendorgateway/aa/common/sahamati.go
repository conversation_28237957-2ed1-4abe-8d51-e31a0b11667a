package common

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/golang/protobuf/jsonpb"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/crypto/jws"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"
	"github.com/epifi/gamma/api/vendorgateway/aa"
	sahamati "github.com/epifi/gamma/api/vendors/aa"
	"github.com/epifi/gamma/pkg/connectedaccount"
)

// GenerateAccessTokenReq provides functionality for adapting to Sahamati's generate token API.
type GenerateAccessTokenReq struct {
	Method       string
	Req          *aa.GenerateAccessTokenRequest
	Url          string
	ClientId     string
	ClientSecret string
}

func (g GenerateAccessTokenReq) HTTPMethod() string {
	return g.Method
}

func (g GenerateAccessTokenReq) URL() string {
	return g.Url
}

func (g GenerateAccessTokenReq) GetResponse() vendorapi.Response {
	return GenerateAccessTokenResp{}
}

func (g GenerateAccessTokenReq) Add(req *http.Request) *http.Request {
	return req
}

func (g GenerateAccessTokenReq) Marshal() ([]byte, error) {
	body := fmt.Sprintf("id=%s&secret=%s", g.ClientId, g.ClientSecret)
	return []byte(body), nil
}

func (g GenerateAccessTokenReq) ContentTypeString() string {
	return "application/x-www-form-urlencoded"
}

type GenerateAccessTokenResp struct {
}

func (g GenerateAccessTokenResp) Unmarshal(b []byte) (proto.Message, error) {
	m := sahamati.GenerateAccessTokenResponse{}
	umErr := protojson.UnmarshalOptions{DiscardUnknown: true, AllowPartial: true}.Unmarshal(b, &m)
	if umErr != nil {
		logger.ErrorNoCtx("error while unmarshalling", zap.Error(umErr))
		return &aa.GenerateAccessTokenResponse{Status: rpc.StatusInternalWithDebugMsg("error in unmarshal response")}, umErr
	}
	accessToken := m.GetAccessToken()
	if m.GetAccessTokenV2() != "" {
		accessToken = m.GetAccessTokenV2()
	}
	expiresIn := m.GetExpiresIn()
	if m.GetExpiresInV2() != 0 {
		expiresIn = m.GetExpiresInV2()
	}
	resp := &aa.GenerateAccessTokenResponse{
		Status:           rpc.StatusOk(),
		AccessToken:      accessToken,
		ExpiresIn:        expiresIn,
		RefreshExpiresIn: m.GetRefreshExpiresIn(),
		IdToken:          m.GetIdToken(),
		NotBeforePolicy:  m.GetNotBeforePolicy(),
		Scope:            m.GetScope(),
	}
	return resp, nil
}

//nolint:dupl
func (g GenerateAccessTokenResp) HandleHttpError(ctx context.Context, httpStatus int, b []byte) (proto.Message, error) {
	res := sahamati.GenerateAccessTokenResponse{}
	umErr := protojson.UnmarshalOptions{DiscardUnknown: true, AllowPartial: true}.Unmarshal(b, &res)
	if umErr != nil {
		logger.Error(ctx, "error while unmarshalling", zap.Error(umErr))
		return &aa.GenerateAccessTokenResponse{Status: rpc.StatusInternalWithDebugMsg("error in unmarshal response")}, umErr
	}
	return &aa.GenerateAccessTokenResponse{Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("StatusCode %v Error %v ErrorDesc %v", httpStatus, res.GetError(), res.GetErrorDescription()))}, nil
}

// FetchCrEntityDetailsReq provides functionality for adapting to Sahamati's entity details fetch API.
type FetchCrEntityDetailsReq struct {
	Method      string
	Req         *aa.FetchCrEntityDetailsRequest
	Url         string
	AccessToken string
}

func (g FetchCrEntityDetailsReq) HTTPMethod() string {
	return g.Method
}

func (g FetchCrEntityDetailsReq) URL() string {
	return g.Url
}

func (g FetchCrEntityDetailsReq) GetResponse() vendorapi.Response {
	return FetchCrEntityDetailsResp{EntityId: g.Req.GetEntityId()}
}

func (g FetchCrEntityDetailsReq) Add(req *http.Request) *http.Request {
	basicHeader := fmt.Sprintf("Bearer %v", g.AccessToken)
	req.Header.Add("Authorization", basicHeader)
	return req
}

func (g FetchCrEntityDetailsReq) Marshal() ([]byte, error) {
	return nil, nil
}

type FetchCrEntityDetailsResp struct {
	EntityId string
}

//nolint:funlen
func (g FetchCrEntityDetailsResp) Unmarshal(b []byte) (proto.Message, error) {
	response := &aa.FetchCrEntityDetailsResponse{}
	var responseList []*sahamati.FetchCrEntityDetailsResponse
	unmarshaler := jsonpb.Unmarshaler{
		AllowUnknownFields: true,
	}
	// jsonpb unmarshal function doesn't support list at top level(it has to be of type message)
	// so had to unmarshal each object separately and append in the list manually
	jsonDecoder := json.NewDecoder(bytes.NewReader(b))
	// read open bracket
	_, err := jsonDecoder.Token()
	if err != nil {
		response.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return response, nil
	}
	// Unmarshal each object separately and append to list
	for jsonDecoder.More() {
		crEntityResponse := &sahamati.FetchCrEntityDetailsResponse{}
		errUm := unmarshaler.UnmarshalNext(jsonDecoder, crEntityResponse)
		if errUm != nil {
			logger.ErrorNoCtx("error while unmarshalling fetch cr entity response", zap.Error(errUm))
			response.Status = rpc.StatusInternalWithDebugMsg(errUm.Error())
			return response, errUm
		}
		responseList = append(responseList, crEntityResponse)
	}
	if len(responseList) == 0 {
		response.Status = rpc.StatusInternalWithDebugMsg("cr entity response list empty")
		return response, nil
	}
	var crEntityResponse *sahamati.FetchCrEntityDetailsResponse
	var errCrEntity error
	if g.EntityId != "" {
		crEntityResponse, errCrEntity = getCrDetailsByEntityId(g.EntityId, responseList)
	} else {
		crEntityResponse, errCrEntity = getOneMoneyCr(responseList)
	}
	if errCrEntity != nil {
		logger.ErrorNoCtx("error while filtering onemoney cr details", zap.Error(errCrEntity))
		response.Status = rpc.StatusInternalWithDebugMsg(errCrEntity.Error())
		return response, errCrEntity
	}

	err = checkIfValidCrEntityResponse(crEntityResponse, g.EntityId)
	if err != nil {
		logger.ErrorNoCtx("invalid cr entity response from central registry", zap.Error(err))
		response.Status = rpc.StatusInternalWithDebugMsg("invalid cr entity response from central registry")
		return response, errors.Wrap(err, "invalid cr entity response from central registry")
	}

	requester := &aa.Requester{Id: crEntityResponse.GetRequester().GetId(), Name: crEntityResponse.GetRequester().GetName()}
	entityInfo := &aa.EntityInfo{
		Name:          crEntityResponse.GetEntityInfo().GetName(),
		Id:            crEntityResponse.GetEntityInfo().GetId(),
		Code:          crEntityResponse.GetEntityInfo().GetCode(),
		EntityHandle:  crEntityResponse.GetEntityInfo().GetEntityHandle(),
		Identifiers:   getIdentifiers(crEntityResponse),
		BaseUrl:       crEntityResponse.GetEntityInfo().GetBaseUrl(),
		WebviewUrl:    crEntityResponse.GetEntityInfo().GetWebviewUrl(),
		FiTypes:       crEntityResponse.GetEntityInfo().GetFiTypes(),
		Certificate:   getCertificate(crEntityResponse),
		TokenInfo:     getTokenInfo(crEntityResponse),
		Gsp:           crEntityResponse.GetEntityInfo().GetGsp(),
		Signature:     getSignature(crEntityResponse),
		InboundPorts:  crEntityResponse.GetEntityInfo().GetInboundPorts(),
		OutboundPorts: crEntityResponse.GetEntityInfo().GetOutboundPorts(),
		Ips:           crEntityResponse.GetEntityInfo().GetIps(),
		CredentialsPk: crEntityResponse.GetEntityInfo().GetCredentialsPk(),
	}
	response.Requester = requester
	response.EntityInfo = entityInfo
	response.Timestamp = crEntityResponse.GetTimestamp()
	response.Ver = crEntityResponse.GetVer()
	response.TxnId = crEntityResponse.GetTxnId()
	response.Status = rpc.StatusOk()
	return response, nil
}

//nolint:dupl
func (g FetchCrEntityDetailsResp) HandleHttpError(ctx context.Context, httpStatus int, b []byte) (proto.Message, error) {
	res := sahamati.FetchCrEntityDetailsResponse{}
	// unmarshalling empty array of bytes always returns error
	// change it to a readable encoding to get a more accurate debug msg
	if len(b) == 0 {
		b = []byte(`{}`)
	}

	umErr := protojson.UnmarshalOptions{DiscardUnknown: true, AllowPartial: true}.Unmarshal(b, &res)
	if umErr != nil {
		logger.Error(ctx, "error while unmarshalling", zap.Error(umErr))
		return &aa.FetchCrEntityDetailsResponse{Status: rpc.StatusInternalWithDebugMsg("error in unmarshal response")}, umErr
	}
	return &aa.FetchCrEntityDetailsResponse{
		Status: vendorapi.GetRpcStatusFromHttpCodeWithDebugMsg(
			ctx,
			httpStatus,
			fmt.Sprintf("StatusCode %v Error %v Message %v", httpStatus, res.GetError(), res.GetMessage()),
		),
	}, fmt.Errorf("StatusCode %v Error %v Message %v", httpStatus, res.GetError(), res.GetMessage())
}

func getIdentifiers(response *sahamati.FetchCrEntityDetailsResponse) []*aa.Identifier {
	var identifiers []*aa.Identifier
	for _, v := range response.GetEntityInfo().GetIdentifiers() {
		identifiers = append(identifiers, &aa.Identifier{Type: v.GetType(), Category: v.GetCategory()})
	}
	return identifiers
}

func getTokenInfo(response *sahamati.FetchCrEntityDetailsResponse) *aa.TokenInfo {
	return &aa.TokenInfo{
		Url:      response.GetEntityInfo().GetTokenInfo().GetUrl(),
		Maxcalls: response.GetEntityInfo().GetTokenInfo().GetMaxcalls(),
		Desc:     response.GetEntityInfo().GetTokenInfo().GetDesc(),
	}
}

func getSignature(response *sahamati.FetchCrEntityDetailsResponse) *aa.Signature {
	return &aa.Signature{
		SignValue: response.GetEntityInfo().GetSignature().GetSignValue(),
	}
}

func getCertificate(response *sahamati.FetchCrEntityDetailsResponse) *aa.Certificate {
	return &aa.Certificate{
		Alg: response.GetEntityInfo().GetCertificate().GetAlg(),
		E:   response.GetEntityInfo().GetCertificate().GetE(),
		Kid: response.GetEntityInfo().GetCertificate().GetKid(),
		Kty: response.GetEntityInfo().GetCertificate().GetKty(),
		N:   response.GetEntityInfo().GetCertificate().GetN(),
		Use: response.GetEntityInfo().GetCertificate().GetUse(),
	}
}

func getOneMoneyCr(response []*sahamati.FetchCrEntityDetailsResponse) (*sahamati.FetchCrEntityDetailsResponse, error) {
	for _, v := range response {
		if v.GetEntityInfo().GetName() == connectedaccount.OneMoneyAA {
			return v, nil
		}
	}
	return nil, fmt.Errorf("one money cr details not found")
}

func getCrDetailsByEntityId(entityId string, crEntityDetailsResponses []*sahamati.FetchCrEntityDetailsResponse) (*sahamati.FetchCrEntityDetailsResponse, error) {
	for _, v := range crEntityDetailsResponses {
		if v.GetEntityInfo().GetId() == entityId {
			return v, nil
		}
	}
	return nil, fmt.Errorf("cr details not found by entity id")
}

func checkIfValidCrEntityResponse(response *sahamati.FetchCrEntityDetailsResponse, entityId string) error {
	if response.GetEntityInfo() == nil {
		return errors.New("entity info missing in cr entity details")
	}

	entityInfo := response.GetEntityInfo()
	if entityInfo.GetId() != entityId {
		return errors.New("entityId mismatch in cr entity details")
	}
	if entityInfo.GetEntityHandle() == "" {
		return errors.New("entity handle missing in cr entity details")
	}
	if entityInfo.GetBaseUrl() == "" {
		return errors.New("base url missing in cr entity details")
	}
	// validate certificate
	if entityInfo.GetCertificate() == nil {
		return errors.New("certificate missing in cr entity details")
	} else {
		publicKeyJwk := entityInfo.GetCertificate()
		by, marshalErr := json.Marshal(publicKeyJwk)
		if marshalErr != nil {
			return errors.Wrap(marshalErr, fmt.Sprintf("error marshalling %s public key cert", entityId))
		}
		_, loadKeyErr := jws.LoadRSAPublicKeyFromJWS(string(by))
		if loadKeyErr != nil {
			return errors.Wrap(loadKeyErr, fmt.Sprintf("error loading %s public key from jws", entityId))
		}
	}

	return nil
}
