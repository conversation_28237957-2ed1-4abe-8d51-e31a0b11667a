package provider

import (
	"context"

	moneyPb "google.golang.org/genproto/googleapis/type/money"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/nulltypes"

	"github.com/epifi/gamma/api/connected_account/external"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palEnumFePb "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
)

//go:generate mockgen -source=provider.go -destination=./mocks/provider.go -package=mocks
type IDeeplinkProvider interface {
	GetLoanLandingInfo(lh *palEnumFePb.LoanHeader) *deeplinkPb.Deeplink
	GetStartLivenessScreen(lh *palEnumFePb.LoanHeader, currentStepExecution *palPb.LoanStepExecution) *deeplinkPb.Deeplink
	GetLivenessManualReviewStatusScreen(lh *palEnumFePb.LoanHeader) *deeplinkPb.Deeplink
	GetLivenessFailedScreen(lh *palEnumFePb.LoanHeader) *deeplinkPb.Deeplink
	GetLoanApplicationStatusPollScreenDeepLink(lh *palEnumFePb.LoanHeader, requestId string) *deeplinkPb.Deeplink
	GetInitiateESignScreen(lh *palEnumFePb.LoanHeader, loanRequestId string, documentUrl string) *deeplinkPb.Deeplink
	GetAuthPollScreen(lh *palEnumFePb.LoanHeader, clientReqId string) *deeplinkPb.Deeplink
	GetAddressConfirmationScreen(lh *palEnumFePb.LoanHeader, loanReqId string, customerName string) *deeplinkPb.Deeplink
	GetPlUpdatedRateScreenDeeplink(lh *palEnumFePb.LoanHeader, lr *palPb.LoanRequest) *deeplinkPb.Deeplink
	GetInformationDialogDeeplink(lh *palEnumFePb.LoanHeader) *deeplinkPb.Deeplink
	GetFailedCkycCheckDeeplink(lh *palEnumFePb.LoanHeader) *deeplinkPb.Deeplink
	GetLoanApplicationStatusScreenDeepLink(ctx context.Context, lh *palEnumFePb.LoanHeader, loanReq *palPb.LoanRequest) (*deeplinkPb.Deeplink, error)
	GetApplicationStatusPollScreenDeepLink(lh *palEnumFePb.LoanHeader, requestId string) *deeplinkPb.Deeplink
	GetLoanDashboardScreenDeepLink(lh *palEnumFePb.LoanHeader) *deeplinkPb.Deeplink
	GetLoanApplicationConfirmationViaOtpScreen(lh *palEnumFePb.LoanHeader, loanRequestId string, lseId string) *deeplinkPb.Deeplink
	GetPollScreenDeepLink(lh *palEnumFePb.LoanHeader, lse *palPb.LoanStepExecution) (*deeplinkPb.Deeplink, error)
	GetLoanApplicationConfirmationViaOtpAttemptsExhaustedScreenDeepLink(lh *palEnumFePb.LoanHeader) *deeplinkPb.Deeplink
	GetApplicationStatusPollScreenWithCustomMsgDeepLink(lh *palEnumFePb.LoanHeader, requestId string, pollingText string, fontColor string, iconUrl string) (*deeplinkPb.Deeplink, error)
	ClientCallbackScreenDeeplinkESign(lh *palEnumFePb.LoanHeader, lrId string) (*deeplinkPb.Deeplink, error)
	GetLoanActivityStatusPollScreenDeepLink(lh *palEnumFePb.LoanHeader, refId string) *deeplinkPb.Deeplink
	GetLoanHeader() *palEnumFePb.LoanHeader
	GetCreditReportFetchScreen(ctx context.Context, lh *palEnumFePb.LoanHeader, params *FetchCreditReportScreenParams) (*deeplinkPb.Deeplink, error)
	GetPanAndDobScreen(lh *palEnumFePb.LoanHeader, requestId string) (*deeplinkPb.Deeplink, error)
	GetNameAndGenderScreenDeeplink(lh *palEnumFePb.LoanHeader, refId string, name string) *deeplinkPb.Deeplink
	GetBankingDetailsScreenDeeplink(lh *palEnumFePb.LoanHeader, requestId string, lseId string, searchType palEnumFePb.SearchIfscType, faqScreenType preapprovedloans.BankingDetailsBottomSheetType, accountNumberSuffix string) (*deeplinkPb.Deeplink, error)
	GetEmploymentDetailsDeeplink(lh *palEnumFePb.LoanHeader, loanRequestId string) *deeplinkPb.Deeplink
	GetVkycPendingScreen(lh *palEnumFePb.LoanHeader, lrId string) *deeplinkPb.Deeplink
	GetEligibilitySuccessScreen(ctx context.Context, lh *palEnumFePb.LoanHeader, offerAmount *moneyPb.Money, interestRate float64, offerId string, loanOffer *palPb.LoanOffer) *deeplinkPb.Deeplink
	GetLoanOfferDetails(lh *palEnumFePb.LoanHeader, offerId string, showLoanOfferV2Screen bool) *deeplinkPb.Deeplink
	GetVkycFailedScreen(lh *palEnumFePb.LoanHeader) *deeplinkPb.Deeplink
	GetVkycExpiredScreen(lh *palEnumFePb.LoanHeader) *deeplinkPb.Deeplink
	GetAadhaarIntroAndEnterDetailsScreenDeeplink(lh *palEnumFePb.LoanHeader, refId string) *deeplinkPb.Deeplink
	GetConfirmPfAccountScreen(lh *palEnumFePb.LoanHeader, lrId string, lseId string, number *commontypes.PhoneNumber, email string, pan string) *deeplinkPb.Deeplink
	GetMandateIntroScreenDeeplink(lh *palEnumFePb.LoanHeader, loanRequestId string, maskedAccountNumber, entryUrl string, retryBackoff uint32, platform commontypes.Platform) (*deeplinkPb.Deeplink, error)
	GetSelfieScreen(lh *palEnumFePb.LoanHeader, lrId string) *deeplinkPb.Deeplink
	GetReviewScreenDeeplink(lh *palEnumFePb.LoanHeader, name *commontypes.Name, pan string, address *types.PostalAddress, ckycId string, dob string, loanReqId string) (*deeplinkPb.Deeplink, error)
	GetReferenceScreenDeeplink(ctx context.Context, lh *palEnumFePb.LoanHeader, lrId string) (*deeplinkPb.Deeplink, error)
	GetLoansApplicationStatusPollDeeplink(ctx context.Context, lh *palEnumFePb.LoanHeader, actorId string, loanReqId string, params *ApplicationStatusPollDeeplinkParams) (*deeplinkPb.Deeplink, error)
	GetApplicationStatusPollScreenDeepLinkWithCustomPollingConfig(lh *palEnumFePb.LoanHeader, requestId string, retryDelayMs int32, retryDurationMs int32) *deeplinkPb.Deeplink
	GetIncomeVerificationIntroDelayScreen(lh *palEnumFePb.LoanHeader, lr string) (*deeplinkPb.Deeplink, error)
	GetIncomeVerificationResultErrorScreen(lh *palEnumFePb.LoanHeader, lr string, connectedAccounts []*external.AccountDetails) (*deeplinkPb.Deeplink, error)
	GetIncomeVerificationResultSuccessScreen(lh *palEnumFePb.LoanHeader, lrId string, connectedAccounts []*external.AccountDetails, continueDl *deeplinkPb.Deeplink, incomeDataSource palPb.IncomeDataSource) (*deeplinkPb.Deeplink, error)
	GetLoansMandateInitiateScreenV2(ctx context.Context, lh *palEnumFePb.LoanHeader, loanRequestId string, lseId string, params *LoansMandateInitiateScreenV2Params) (*deeplinkPb.Deeplink, error)
	GetLoansPennyDropInitiateScreenV2(ctx context.Context, lh *palEnumFePb.LoanHeader, loanRequestId string, lseId string, nextActionDl *deeplinkPb.Deeplink) (*deeplinkPb.Deeplink, error)
	GetLoansAlternateAccountsScreen(ctx context.Context, lh *palEnumFePb.LoanHeader, loanRequestId string, lseId string, params *LoansAlternateAccountsScreenParams) (*deeplinkPb.Deeplink, error)
	GetLoansMandateSetupScreen(ctx context.Context, lh *palEnumFePb.LoanHeader, actorId string, loanRequestId string, lseId string, params *LoansMandateSetupScreenParams) (*deeplinkPb.Deeplink, error)
	GetLoansDebitCardDetailsScreen(ctx context.Context, actorId string, lh *palEnumFePb.LoanHeader, nextCtaDeeplink *deeplinkPb.Deeplink) (*deeplinkPb.Deeplink, error)
	// GetLoansMandateDigioSdkScreen to init DIGIO SDK on Android client side
	// this has to be only used when user is using fi account to set up mandate and want to fetch dc details before setting up mandate
	// this screen option has to be used or passed in continue_cta in LoansDebitCardDetailsScreenOptions
	// For non-fi account or iOS client please use LoansMandateSetupScreenOptions as a thumb rule
	// this is done only for Android client to decrease screen navigation complexity
	GetLoansMandateDigioSdkScreen(ctx context.Context, actorId string, lh *palEnumFePb.LoanHeader, lseId string, lrId string, params *DigioSdkParams) (*deeplinkPb.Deeplink, error)
	GetMandateFailureScreen(ctx context.Context, lrId string, lseId string, tryAgainDl *deeplinkPb.Deeplink, failedAccountDetails *palPb.MandateData_BankingDetails_AccountDetails) (*deeplinkPb.Deeplink, error)
	GetLoanStatusTimerPollDeeplink(ctx context.Context, lh *palEnumFePb.LoanHeader, actorId string, loanReqId string, params *LoanStatusPollTimerDeeplinkParams) (*deeplinkPb.Deeplink, error)
	// GetNonFiAccountMandateFlowScreen is the intro screen for the flow where user is restricted to set up mandate on its fi-federal bank account, and
	// we show the necessary messages to the user on this screen as why fi account is not eligible for mandate setup. Such use cases can be catered with this deeplink
	// For now, we are not allowing min and re-kyc due users to set up the mandate on fi-federal bank accounts, this can be extended to other use cases as well.
	GetNonFiAccountMandateFlowScreen(ctx context.Context, lh *palEnumFePb.LoanHeader, lrId string, lseId string, searchType palEnumFePb.SearchIfscType, ineligibilityReason palPb.MandateData_FiAccountIneligibleForMandateReason) (*deeplinkPb.Deeplink, error)
	GetContactabilityIntroScreen(ctx context.Context, lh *palEnumFePb.LoanHeader, lseId string) (*deeplinkPb.Deeplink, error)
	GetAlternateContactVerificationViaOtpScreen(ctx context.Context, lh *palEnumFePb.LoanHeader, lse *palPb.LoanStepExecution) (*deeplinkPb.Deeplink, error)
	GetAlternateContactSuccessScreen(ctx context.Context, lh *palEnumFePb.LoanHeader) (*deeplinkPb.Deeplink, error)
	GetPhoneNumberScreenForAlternateContactFlow(lseId string, lh *palEnumFePb.LoanHeader) *deeplinkPb.Deeplink
	GetAlternateContactCoolOffScreen(ctx context.Context, lh *palEnumFePb.LoanHeader) (*deeplinkPb.Deeplink, error)
	GetVkycFailureScreen(rejectReason string, nextAction *deeplinkPb.Deeplink, ctaText string) (*deeplinkPb.Deeplink, error)
	GetVkycDeeplinkWithWebview(loanRequestId string, vkycUrl string, platform commontypes.Platform) (*deeplinkPb.Deeplink, error)
	GetIntroScreenForWebViewDeeplink(lh *palEnumFePb.LoanHeader, loanRequestId string, entryUrl string, retryBackoff uint32, platform commontypes.Platform) (*deeplinkPb.Deeplink, error)
	GetLoanOfferOnClickDeeplink(ctx context.Context, lo *palPb.LoanOffer, showLoanOfferScreenV2 bool) *deeplinkPb.Deeplink
	GetAddressVerificationIntroScreen(lh *palEnumFePb.LoanHeader, loanRequestId string) *deeplinkPb.Deeplink
	GetMandateSuccessScreen(ctx context.Context, lh *palEnumFePb.LoanHeader) (*deeplinkPb.Deeplink, error)
	GetManualReviewWaitScreen(ctx context.Context, req *GetManualReviewWaitScreenRequest) *deeplinkPb.Deeplink
	GetKycReviewScreenDeeplinkV2(ctx context.Context, req *GetReviewScreenDeeplinkV2Request) (*deeplinkPb.Deeplink, error)
	GetPreBreLoanConsentScreen(lh *palEnumFePb.LoanHeader, loanRequestId string) (*deeplinkPb.Deeplink, error)
	GetPreBreLoanOfferDetailsScreen(lh *palEnumFePb.LoanHeader, loanRequestId string) (*deeplinkPb.Deeplink, error)
	GetPreBreLoansOfferIntroScreen(lh *palEnumFePb.LoanHeader, loanRequestId string) (*deeplinkPb.Deeplink, error)
	GetApplicationPollingScreenV2(ctx context.Context, req *GetApplicationPollingScreenV2Request) (*deeplinkPb.Deeplink, error)
	GetKycIntroScreenDeeplink(ctx context.Context, lh *palEnumFePb.LoanHeader, loanRequestId, lseId, entryUrl string, retryBackoff uint32, showKycFailureBanner bool, platform commontypes.Platform) (*deeplinkPb.Deeplink, error)
	GetPwaScreenDeeplinkWithWebview(ctx context.Context, req *PwaScreenDeeplinkRequest) (*deeplinkPb.Deeplink, error)
	GetPaymentTerminalScreenDeeplink(ctx context.Context, req *PaymentTerminalScreenDlReq) (*deeplinkPb.Deeplink, error)
}

type PaymentTerminalScreenDlReq struct {
	PaymentStatus palPb.LoanPaymentRequestStatus
	PaymentType   palPb.LoanPaymentRequestType
	LoanHeader    *palEnumFePb.LoanHeader
	LoanAccountId string
}

type PwaScreenDeeplinkRequest struct {
	LoanRequestId string
	EntryUrl      string
	LoanHeader    *palEnumFePb.LoanHeader
	RetryBackOff  uint32
	NudgeData     *preapprovedloans.LoansWebviewWithStatusPollScreen_InstructionView
}

type ReviewScreenBottomCard struct {
	Title    string
	Subtitle string
}

type GetManualReviewWaitScreenRequest struct {
	LoanHeader     *palEnumFePb.LoanHeader
	LoanRequestId  string
	CenterImageUrl string
	Title          string
	Subtitle       string
	BottomCard     *ReviewScreenBottomCard
}

type GetApplicationPollingScreenV2Request struct {
	LoanHeader     *palEnumFePb.LoanHeader
	LoanReqId      string
	PollingIconUrl string
	Title          string
	SubTitle       string
	HideLoader     bool
	BottomCtaText  string
}

type GetReviewScreenDeeplinkV2Request struct {
	LoanHeader            *palEnumFePb.LoanHeader
	LoanReqId             string
	Name                  *commontypes.Name
	Dob                   string
	Pan                   string
	PermanentAddress      *types.PostalAddress
	CorrespondenceAddress *types.PostalAddress
	OVDAddress            *types.PostalAddress
	CkycId                string
	KycTYpe               palPb.KycType
	KycDocumentNumber     string
}

type LoanStatusPollTimerDeeplinkParams struct {
	Title              nulltypes.NullString
	SubTitle           nulltypes.NullString
	RetryAttemptNumber int32
	RetryDurationInMs  int32
	RetryBackOffInMs   int32
	MaxTime            int32
	HideLoader         bool
	Banners            []*Banner
	TimeoutTitle       nulltypes.NullString
	TimeoutSubtitle    nulltypes.NullString
	TimeoutButtonText  nulltypes.NullString
	TimeoutImageUrl    nulltypes.NullString
}

type Banner struct {
	Title    string
	Body     string
	ImageUrl string
}

type ApplicationStatusPollDeeplinkParams struct {
	Icon                nulltypes.NullString
	Title               nulltypes.NullString
	SubTitle            nulltypes.NullString
	RetryDurationInMs   int32
	RetryBackOffInMs    int32
	GetNextActionInSync bool
}

type LoansMandateInitiateScreenV2Params struct {
	DefaultMandateBankAccDetails    *palPb.MandateData_BankingDetails_AccountDetails
	AlternateAccountsScreenDeeplink *deeplinkPb.Deeplink
	AllowUserToChangeDefaultAccount bool
	NextDeeplink                    *deeplinkPb.Deeplink
}

type LoansAlternateAccountsScreenParams struct {
	AlternateMandateBankAccDetails []*palPb.MandateData_BankingDetails_AccountDetails
	BankingDetailsScreenDeeplink   *deeplinkPb.Deeplink
}

type WebViewParams struct {
	EntryUrl    string
	ExitUrl     string
	EncodedHtml string
}

type MandateOverlayDetailsParams struct {
	IsFiAccount bool
}

type LoansMandateSetupScreenParams struct {
	WebViewParams               *WebViewParams
	MandateOverlayDetailsParams *MandateOverlayDetailsParams
	SdkParams                   *SdkParams
}

type SdkParams struct {
	DigioSdkParams *DigioSdkParams
}

type DigioSdkParams struct {
	MandateId   string
	PhoneNumber *commontypes.PhoneNumber
}

type FetchCreditReportScreenParams struct {
	LrId               string
	LseId              string
	CreditReportVendor preapprovedloans.CreditReportVendor
}
