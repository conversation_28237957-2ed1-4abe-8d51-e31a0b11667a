//nolint:dupl,funlen
package processor

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sort"

	"go.uber.org/zap"

	"github.com/epifi/gamma/api/cx/developer/db_state"
	deepLinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	preApprovedLoanPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/preapprovedloan/developer"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/preapprovedloan/dao"

	"github.com/samber/lo"
)

var screensToHideOnSherlock = []deepLinkPb.Screen{
	deepLinkPb.Screen_PL_ELIGIBILITY_OFFER_AVAILABLE_SCREEN,
	deepLinkPb.Screen_PRE_APPROVED_LOAN_APPLICATION_STATUS_SCREEN,
	deepLinkPb.Screen_LOANS_APPLICATION_COMPLETION_SUCCESS_SCREEN,
}

type DevLoanRequestsEntity struct {
	loanReqDao dao.LoanRequestsDao
}

func NewDevLoanRequestsEntity(loanReqDao dao.LoanRequestsDao) *DevLoanRequestsEntity {
	return &DevLoanRequestsEntity{
		loanReqDao: loanReqDao,
	}
}

type byUpdatedAtloanRequests []*preApprovedLoanPb.LoanRequest

func (a byUpdatedAtloanRequests) Len() int {
	return len(a)
}
func (a byUpdatedAtloanRequests) Swap(i, j int) { a[i], a[j] = a[j], a[i] }
func (a byUpdatedAtloanRequests) Less(i, j int) bool {
	return a[i].GetUpdatedAt().AsTime().After(a[j].GetUpdatedAt().AsTime())
}
func (d *DevLoanRequestsEntity) FetchParamList(ctx context.Context, entity developer.PreApprovedLoanEntity) ([]*db_state.ParameterMeta, error) {
	paramList := []*db_state.ParameterMeta{
		{
			Name:            ActorId,
			Label:           ActorIdLabel,
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            LoanRequestId,
			Label:           LoanRequestId,
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            VendorRequestId,
			Label:           VendorRequestId,
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            Vendor,
			Label:           Vendor,
			Type:            db_state.ParameterDataType_DROPDOWN,
			Options:         vendors,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            LoanRequestType,
			Label:           LoanRequestTypeLabel,
			Type:            db_state.ParameterDataType_DROPDOWN,
			Options:         loanRequestTypes,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
	}
	return paramList, nil
}

func (d *DevLoanRequestsEntity) FetchData(ctx context.Context, entity developer.PreApprovedLoanEntity, filters []*db_state.Filter) (string, error) {
	if len(filters) == 0 {
		return "", errors.New("filter cannot be nil")
	}
	var actorId, lrId, vendorReqId string
	var vendor preApprovedLoanPb.Vendor
	var loanRequestType []preApprovedLoanPb.LoanRequestType
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case ActorId:
			actorId = filter.GetStringValue()
		case LoanRequestId:
			lrId = filter.GetStringValue()
		case VendorRequestId:
			vendorReqId = filter.GetStringValue()
		case Vendor:
			vendor = preApprovedLoanPb.Vendor(preApprovedLoanPb.Vendor_value[filter.GetDropdownValue()])
		case LoanRequestType:
			loanRequestType = append(loanRequestType, preApprovedLoanPb.LoanRequestType(preApprovedLoanPb.LoanRequestType_value[filter.GetDropdownValue()]))
		default:
			return "", fmt.Errorf("unknonw param type")
		}
	}
	if actorId != "" {
		var loanRequests []*preApprovedLoanPb.LoanRequest
		var resloanRequests []*preApprovedLoanPb.LoanRequest
		var err error
		loanRequests, err = d.loanReqDao.GetByActorIdAndVendorAndStatus(ctx, actorId, vendor, nil)
		if err != nil {
			logger.Error(ctx, "cannot fetch loan requests using actorID", zap.Error(err))
			return "cannot fetch loan requests using actorID", nil
		}
		for _, loanRequest := range loanRequests {
			loanRequest.Details = getMaskedLoanRequestDetails(loanRequest.GetDetails())
			if loanRequest.GetNextAction() != nil && lo.Contains(screensToHideOnSherlock, loanRequest.GetNextAction().GetScreen()) {
				loanRequest.GetNextAction().ScreenOptions = nil
				loanRequest.GetNextAction().ScreenOptionsV2 = nil
			}
		}
		if len(loanRequestType) > 0 {
			for _, loanRequest := range loanRequests {
				if loanRequest.GetType() == loanRequestType[0] {
					resloanRequests = append(resloanRequests, loanRequest)
				}
			}
		} else {
			resloanRequests = loanRequests
		}
		sort.Sort(byUpdatedAtloanRequests(resloanRequests))
		e, err := json.Marshal(resloanRequests)
		if err != nil {
			logger.Error(ctx, "cannot marshal struct to json", zap.Error(err))
			return marshalErr, nil
		}
		return string(e), nil
	}

	if lrId != "" {
		loanRequest, err := d.loanReqDao.GetById(ctx, lrId)
		if err != nil {
			logger.Error(ctx, "cannot fetch loan requests using ID", zap.Error(err))
			return "cannot fetch loan requests using Id", nil
		}
		loanRequest.Details = getMaskedLoanRequestDetails(loanRequest.GetDetails())
		e, err := json.Marshal(loanRequest)
		if err != nil {
			logger.Error(ctx, "cannot marshal struct to json", zap.Error(err))
			return marshalErr, nil
		}
		return string(e), nil
	}

	if vendorReqId != "" {
		loanRequest, err := d.loanReqDao.GetByVendorReqId(ctx, vendorReqId)
		if err != nil {
			logger.Error(ctx, "cannot fetch loan requests using vendor request id", zap.Error(err))
			return "cannot fetch loan requests using vendor request id", nil
		}
		loanRequest.Details = getMaskedLoanRequestDetails(loanRequest.GetDetails())
		e, err := json.Marshal(loanRequest)
		if err != nil {
			logger.Error(ctx, "cannot marshal struct to json", zap.Error(err))
			return marshalErr, nil
		}
		return string(e), nil
	}

	return "", nil
}

func getMaskedLoanRequestDetails(lrDetails *preApprovedLoanPb.LoanRequestDetails) *preApprovedLoanPb.LoanRequestDetails {
	return &preApprovedLoanPb.LoanRequestDetails{
		OtpInfo:             nil,
		MaskedAccountNumber: lrDetails.GetMaskedAccountNumber(),
		LoanInfo: &preApprovedLoanPb.LoanRequestDetails_LoanInfo{
			Amount:                  nil,
			TenureInMonths:          lrDetails.GetLoanInfo().GetTenureInMonths(),
			DisbursalAmount:         nil,
			InterestRate:            lrDetails.GetLoanInfo().GetInterestRate(),
			EmiAmount:               nil,
			Deductions:              nil,
			TotalPayable:            nil,
			AprRate:                 lrDetails.GetLoanInfo().GetAprRate(),
			PledgeDetails:           nil,
			ExpectedDisbursmentDate: lrDetails.GetLoanInfo().GetExpectedDisbursmentDate(),
			EmiStartDate:            lrDetails.GetLoanInfo().GetEmiStartDate(),
			DisbursementUtr:         lrDetails.GetLoanInfo().GetDisbursementUtr(),
			ActualDisbursalAmount:   nil,
			IsDiscountedOffer:       lrDetails.GetLoanInfo().GetIsDiscountedOffer(),
		},
		PhoneNumber:      nil,
		CustomerDeviceId: "",
		EmailId:          "",
		LoanOfferExpiry:  lrDetails.GetLoanOfferExpiry(),
		Details:          nil,
		ProgramVersion:   lrDetails.GetProgramVersion(),
		LocationToken:    "",
	}
}
