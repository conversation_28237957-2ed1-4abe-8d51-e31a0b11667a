package federal

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	commonTypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/api/recurringpayment"
	rpEnachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	"github.com/epifi/gamma/api/typesv2/ui"
	palActivity "github.com/epifi/gamma/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/baseprovider"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/helper"
)

func (p *Processor) FedSetupMandateIntroScreen(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep:      lse,
			LseFieldMasks: []palPb.LoanStepExecutionFieldMask{},
		}
		lg := activity.GetLogger(ctx)

		// fetch loan request
		lr, lrErr := p.loanRequestDao.GetById(ctx, lse.GetRefId())
		if lrErr != nil {
			lg.Error("error in lr get by id", zap.Error(lrErr))
			if errors.Is(lrErr, epifierrors.ErrRecordNotFound) {
				palActivity.MarkLoanStepFail(lse)
				return res, nil
			}
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in lr get by id, err: %v", lrErr))
		}

		pennyDropLse, pdLseErr := p.loanStepExecutionDao.GetByRefIdAndFlowAndName(ctx, lse.GetRefId(), lse.GetFlow(), palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PENNY_DROP)
		if pdLseErr != nil {
			lg.Error("error in pd lse get by ref id and flow and name", zap.Error(pdLseErr))
			if errors.Is(pdLseErr, epifierrors.ErrRecordNotFound) {
				palActivity.MarkLoanStepFail(lse)
				return res, nil
			}
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in pd lse get by ref id and flow and name, err: %v", pdLseErr))
		}
		bankingDetails := pennyDropLse.GetDetails().GetOnboardingData().GetBankingDetails()

		deeplinkProvider := p.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{Vendor: req.GetVendor(), LoanProgram: req.GetLoanProgram()})
		authBottomViewDl := baseprovider.GetMandateAuthBottomViewDeeplink(deeplinkProvider.GetLoanHeader(), []*ui.OptionSelectionItem{
			{
				Id:          1,
				Identifier:  rpEnachEnumsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_DEBIT_CARD.String(),
				OptionBadge: ui.NewITC().WithTexts(helper.GetText("Debit Card", colors.ColorLead, "", commonTypes.FontStyle_SUBTITLE_3)),
				// OptionValue: ui.NewITC(), // to show text stating higher/lower success rate
			},
			{
				Id:          2,
				Identifier:  rpEnachEnumsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_NET_BANKING.String(),
				OptionBadge: ui.NewITC().WithTexts(helper.GetText("Net Banking", colors.ColorLead, "", commonTypes.FontStyle_SUBTITLE_3)),
				// OptionValue: ui.NewITC(), // to show text stating higher/lower success rate
			},
		})
		// construct v2 dl from lse and default acc details
		mandateInitV2Dl, dlErr := deeplink.ConstructPlMandateInitScreenV2FromLse(ctx, deeplinkProvider, lse, &palPb.MandateData_BankingDetails_AccountDetails{
			AccountNumber:     bankingDetails.GetAccountNumber(),
			AccountHolderName: bankingDetails.GetAccountHolderName(),
			IfscCode:          bankingDetails.GetIfscCode(),
			BankName:          bankingDetails.GetBankName(),
		}, false, authBottomViewDl)
		if dlErr != nil {
			lg.Error("error while constructing pl mandate init v2 deeplink in fed mandate activity", zap.Error(dlErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error while constructing pl mandate init v2 deeplink in fed mandate activity, err: %v", dlErr))
		}

		lr.NextAction = mandateInitV2Dl
		if updateErr := p.loanRequestDao.Update(ctx, lr, []palPb.LoanRequestFieldMask{
			palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION,
		}); updateErr != nil {
			lg.Error("failed to update lr", zap.Error(updateErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update lr, err: %v", updateErr))
		}

		lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED
		if lseUpdateErr := p.loanStepExecutionDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
		}); lseUpdateErr != nil {
			lg.Error("error while updating lse", zap.Error(lseUpdateErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error while updating lse, err: %v", lseUpdateErr))
		}

		return res, nil
	})
	return actRes, actErr
}

func (p *Processor) FedGetMandateStatus(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep:      lse,
			LseFieldMasks: []palPb.LoanStepExecutionFieldMask{},
		}
		lg := activity.GetLogger(ctx)

		mandateAttemptOrchIdList := lse.GetDetails().GetMandateData().GetFedMandateData().GetMandateAttemptOrchIdList()
		if len(mandateAttemptOrchIdList) == 0 {
			lg.Error("mandate attempt orch id list is empty")
			return nil, errors.Wrap(epifierrors.ErrTransient, "mandate attempt orch id list is empty")
		}
		latestMandateAttemptOrchId := mandateAttemptOrchIdList[len(mandateAttemptOrchIdList)-1]

		mandateActionResp, mandateActionErr := p.recurringPaymentClient.GetActionStatusV1(ctx, &recurringpayment.GetActionStatusV1Request{
			ActionType:      recurringpayment.Action_CREATE,
			ClientRequestId: latestMandateAttemptOrchId,
		})
		if te := epifigrpc.RPCError(mandateActionResp, mandateActionErr); te != nil {
			lg.Error("failed to get action status from recurring payment client", zap.Error(te))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get action status from recurring payment client, err: %v", te))
		}

		switch mandateActionResp.GetActionStatus() {
		case recurringpayment.ActionState_ACTION_CREATED, recurringpayment.ActionState_ACTION_IN_PROGRESS:
			if mandateActionResp.GetActionSubStatus() != recurringpayment.ActionSubState_ACTION_SUB_STATE_AUTHORIZATION_COMPLETED {
				lg.Info("mandate is in progress right now", zap.String("mandate_action_status", mandateActionResp.GetActionStatus().String()), zap.String("mandate_action_sub_status", mandateActionResp.GetActionSubStatus().String()))
				return res, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("received transient failure via getActionStatus, status: %v", mandateActionResp.GetActionStatus().String()))
			}
			// Treat authorization completed substate as success since it takes a few days to complete the
			// mandate registration on bank's end which succeeds in 99% of the cases.
			// TODO: Need product solve to handle for cases where mandate registration fails.
			palActivity.MarkLoanStepSuccess(lse)
		case recurringpayment.ActionState_ACTION_FAILURE, recurringpayment.ActionState_ACTION_EXPIRED,
			recurringpayment.ActionState_ACTION_MANUAL_INTERVENTION, recurringpayment.ActionState_ACTION_REJECT:
			lg.Info("mandate action failed", zap.String("mandate_action_status", mandateActionResp.GetActionStatus().String()),
				zap.String("mandate_action_sub_status", mandateActionResp.GetActionSubStatus().String()), zap.String("detailed_status_fi_status_code", mandateActionResp.GetActionDetailedStatusInfo().GetFiStatusCode()),
				zap.String("detailed_status_error_description", mandateActionResp.GetActionDetailedStatusInfo().GetErrorDescription()))
			palActivity.MarkLoanStepFail(lse)
		case recurringpayment.ActionState_ACTION_SUCCESS:
			palActivity.MarkLoanStepSuccess(lse)
		default:
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("unrecognized mandate next action status: %s", mandateActionResp.GetActionStatus()))
		}

		return res, nil
	})
	return actRes, actErr
}
