package syncproxy

import (
	"context"
	"errors"
	"fmt"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	"github.com/epifi/gamma/preapprovedloan/activity"

	"github.com/epifi/gamma/preapprovedloan/activity/common"
	"github.com/epifi/gamma/preapprovedloan/activity/federal"
	palNamespace "github.com/epifi/gamma/preapprovedloan/workflow/namespace"

	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/activity/abfl"
	"github.com/epifi/gamma/preapprovedloan/activity/lenden"
	"github.com/epifi/gamma/preapprovedloan/activity/stock_guardian"
)

type PalActivityFunction func(context.Context, *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error)

type Processor struct {
	activityNameToFuncMap map[string]PalActivityFunction
}

func NewProcessor(
	sgProc *stock_guardian.Processor,
	lendenProc *lenden.Processor,
	abflProc *abfl.Processor,
	commonProc *common.Processor,
	fedProc *federal.Processor,
	activityProc *activity.Processor,
) *Processor {
	activityNameToFuncMap := map[string]PalActivityFunction{
		"SgGetMandateStatus":                         sgProc.SgGetMandateStatus,
		"SgInitiateLivenessAndStatus":                sgProc.SgInitiateLivenessAndStatus,
		"SgInitiateVkyc":                             sgProc.SgInitiateVkyc,
		"SgCheckVkycStatus":                          sgProc.SgCheckVkycStatus,
		"SgInitiateEmploymentCheck":                  sgProc.SgInitiateEmploymentCheck,
		"SgEmploymentCheckStatus":                    sgProc.SgEmploymentCheckStatus,
		"SgKycAmlStatus":                             sgProc.SgKycAmlStatus,
		"FetchAndSetAbflPwaJourneyLink":              abflProc.FetchAndSetAbflPwaJourneyLink,
		palNamespace.ExecuteOnboardingStage.String(): commonProc.ExecuteOnboardingStage,
		palNamespace.FedGetMandateStatus.String():    fedProc.FedGetMandateStatus,
		palNamespace.SgKycDocumentDownload.String():  sgProc.SgKycDocumentDownload,
		palNamespace.SgKycDataVerification.String():  sgProc.SgKycDataVerification,
		palNamespace.FedKfsInSync.String():           fedProc.FedKfsInSync,

		// LDC activities
		palNs.LdcCheckKycStatus.String():                 lendenProc.LdcCheckKycStatus,
		palNs.LdcCheckMandateStatus.String():             lendenProc.LdcCheckMandateStatus,
		palNamespace.WaitForDataCollection.String():      activityProc.WaitForDataCollection,
		palNamespace.UserOfferSelection.String():         commonProc.UserOfferSelection,
		palNamespace.CreditReportFetch.String():          commonProc.CreditReportFetch,
		palNamespace.SgGetOTPVerificationStatus.String(): sgProc.SgGetOTPVerificationStatus,
		palNamespace.EstimateSalary.String():             commonProc.EstimateSalary,
	}
	return &Processor{
		activityNameToFuncMap: activityNameToFuncMap,
	}
}

func (p *Processor) ExecuteInSync(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	fn, ok := p.activityNameToFuncMap[req.GetSyncActivityName()]
	if !ok {
		return nil, epifitemporal.NewPermanentError(fmt.Errorf("function not found: %s", req.GetSyncActivityName()))
	}
	res, err := fn(ctx, req)
	// in case of transient failures, fn should return the deeplink to be shown to the user while the activity is being retried
	// in case of success or permanent failure, marking the task as done and returning the response
	switch {
	case epifitemporal.IsRetryableError(err) || errors.Is(err, epifierrors.ErrTransient):
		return res, nil
	default:
		if res == nil {
			res = &palActivityPb.PalActivityResponse{}
		}
		res.IsSyncTaskDone = true
		return res, err
	}
}
