package providers

import (
	"context"
	"regexp"
	"strings"

	"github.com/pkg/errors"

	typesPb "github.com/epifi/be-common/api/typesv2/common"
	accountPb "github.com/epifi/be-common/api/typesv2/common/account"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	enumsPb "github.com/epifi/gamma/api/preapprovedloan/enums"
	"github.com/epifi/gamma/api/typesv2/account"
	ldcVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden"
	"github.com/epifi/gamma/preapprovedloan/dao"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider"
	"github.com/epifi/gamma/preapprovedloan/helper"
	lendenPkg "github.com/epifi/gamma/preapprovedloan/pkg/lenden"
	"github.com/epifi/gamma/preapprovedloan/utils"
)

type LDCAutoPayProvider struct {
	loanStepExecutionDao     dao.LoanStepExecutionsDao
	loanRequestDao           dao.LoanRequestsDao
	loanOfferDao             dao.LoanOffersDao
	deeplinkFactory          *deeplink.ProviderFactory
	ldcMandateSetupProcessor *lendenPkg.MandateSetupProcessor
	ldcVgClient              ldcVgPb.LendenClient
	loanApplicantDao         dao.LoanApplicantDao
	rpcHelper                *helper.RpcHelper
}

var _ AutoPayProvider = &LDCAutoPayProvider{}

func NewLDCAutoPayProvider(
	loanStepExecutionDao dao.LoanStepExecutionsDao,
	loanRequestDao dao.LoanRequestsDao,
	loanOfferDao dao.LoanOffersDao,
	deeplinkFactory *deeplink.ProviderFactory,
	ldcMandateSetupProcessor *lendenPkg.MandateSetupProcessor,
	ldcVgClient ldcVgPb.LendenClient,
	loanApplicantDao dao.LoanApplicantDao,
	rpcHelper *helper.RpcHelper,
) *LDCAutoPayProvider {
	return &LDCAutoPayProvider{
		loanStepExecutionDao:     loanStepExecutionDao,
		loanRequestDao:           loanRequestDao,
		loanOfferDao:             loanOfferDao,
		deeplinkFactory:          deeplinkFactory,
		ldcMandateSetupProcessor: ldcMandateSetupProcessor,
		ldcVgClient:              ldcVgClient,
		loanApplicantDao:         loanApplicantDao,
		rpcHelper:                rpcHelper,
	}
}

func (s *LDCAutoPayProvider) GetAccountVerificationStatus(ctx context.Context, lseId string) (*GetAccountVerificationStatusResponse, error) {
	lse, err := s.loanStepExecutionDao.GetById(ctx, lseId)
	if err != nil {
		return nil, errors.Wrap(err, "error getting loan step execution")
	}
	lr, err := s.loanRequestDao.GetById(ctx, lse.GetRefId())
	if err != nil {
		return nil, errors.Wrap(err, "error getting loan request")
	}
	offer, err := s.loanOfferDao.GetById(ctx, lr.GetOfferId())
	if err != nil {
		return nil, errors.Wrap(err, "error getting loan offer")
	}
	applicant, err := s.loanApplicantDao.GetByActorId(ctx, lse.GetActorId())
	if err != nil {
		return nil, errors.Wrap(err, "error getting applicant")
	}
	dlProvider := s.deeplinkFactory.GetDeeplinkGenerator(ctx,
		&deeplink.GetDeeplinkProviderRequest{Vendor: lr.GetVendor(), LoanProgram: lr.GetLoanProgram()})
	if lse.GetDetails().GetMandateData().GetBankingDetails().GetFinalAccDetailsUsed().GetAccountNumber() != "" {
		return &GetAccountVerificationStatusResponse{
			Status:     AccountVerificationStatusSuccess,
			NextAction: dlProvider.GetApplicationStatusPollScreenDeepLink(dlProvider.GetLoanHeader(), lr.GetId()),
		}, nil
	}
	savingsAccount, err := s.rpcHelper.GetSavingsAccountDetails(ctx, lse.GetActorId(),
		commonvgpb.Vendor_FEDERAL_BANK, account.AccountProductOffering_APO_REGULAR)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return nil, errors.Wrap(err, "error getting savings account")
	}
	user, err := s.rpcHelper.GetUserByActorId(ctx, lse.GetActorId())
	if err != nil {
		return nil, errors.Wrap(err, "error getting user")
	}
	var savingsAccountFound bool
	var savingsAccountDetails *palPb.MandateData_BankingDetails_AccountDetails
	if savingsAccount != nil {
		savingsAccountFound = true
		savingsAccountDetails = &palPb.MandateData_BankingDetails_AccountDetails{
			AccountNumber:     savingsAccount.GetAccountNo(),
			AccountHolderName: user.GetProfile().GetKycName().ToString(),
			IfscCode:          savingsAccount.GetIfscCode(),
			BankName:          utils.FiFederalBank,
		}
	}

	/*
		Types of users and corresponding flows for bank account collection and verification:
		1. No account constraints (Thick file user)
			A. No Fi-Federal savings account (NTB) - Ask user to add an account, any account
			B. Fi-Federal savings account present (ETB) - Use Fi-Federal savings account for verification
		2. Account constraints present (NTC / Thin file user, offer approved via account income analysis)
			A. No Fi-Federal savings account (NTB) - Ask user to add account adhering to constraints
			B. Fi-Federal savings account present (ETB)
				I. Match (Fi Salary Account Holder) - Use Fi-Federal salary(savings) account for verification
				II. No match (Non-Fi Salary Account Holder) - Ask user to add account adhering to constraints
	*/
	accountConstraints := offer.GetOfferConstraints().GetLendenConstraintInfo().GetBankDetails()
	if accountConstraints == nil {
		if !savingsAccountFound {
			var addAccountDl *deeplinkPb.Deeplink
			addAccountDl, err = s.getDeeplinkToAddAccount(ctx, lr, lse, "")
			if err != nil {
				return nil, errors.Wrap(err, "error getting screen for adding bank account details")
			}
			return &GetAccountVerificationStatusResponse{
				Status:     AccountVerificationStatusCollectionPending,
				NextAction: addAccountDl,
			}, nil
		}
		var verifyAndStoreAccountRes *validateAndStoreAccountResponse
		verifyAndStoreAccountRes, err = s.validateAndStoreAccount(ctx, lr, lse, applicant, savingsAccountDetails)
		if err != nil {
			return nil, errors.Wrap(err, "error validating and storing account")
		}
		if verifyAndStoreAccountRes.FailureReason != AccountVerificationFailureReasonUnspecified {
			return &GetAccountVerificationStatusResponse{
				Status:              AccountVerificationStatusFailed,
				FailureReason:       verifyAndStoreAccountRes.FailureReason,
				DisplayErrorMessage: verifyAndStoreAccountRes.DisplayErrorMessage,
			}, nil
		}
		return &GetAccountVerificationStatusResponse{
			Status:     AccountVerificationStatusSuccess,
			NextAction: dlProvider.GetApplicationStatusPollScreenDeepLink(dlProvider.GetLoanHeader(), lr.GetId()),
		}, nil
	}
	accountNumberSuffixConstraint := accountConstraints.GetAccountNumber()[len(accountConstraints.GetAccountNumber())-4:]
	if !savingsAccountFound {
		var addAccountDl *deeplinkPb.Deeplink
		addAccountDl, err = s.getDeeplinkToAddAccount(ctx, lr, lse, accountNumberSuffixConstraint)
		if err != nil {
			return nil, errors.Wrap(err, "error getting screen for adding bank account details")
		}
		return &GetAccountVerificationStatusResponse{
			Status:     AccountVerificationStatusCollectionPending,
			NextAction: addAccountDl,
		}, nil
	}
	ifscMatch := accountConstraints.GetIfsc() == savingsAccountDetails.GetIfscCode()
	savingsAccountNumberSuffix := savingsAccountDetails.GetAccountNumber()[len(savingsAccountDetails.GetAccountNumber())-4:]
	accountSuffixMatch := accountNumberSuffixConstraint == savingsAccountNumberSuffix
	if !ifscMatch || !accountSuffixMatch {
		var addAccountDl *deeplinkPb.Deeplink
		addAccountDl, err = s.getDeeplinkToAddAccount(ctx, lr, lse, accountNumberSuffixConstraint)
		if err != nil {
			return nil, errors.Wrap(err, "error getting screen for adding bank account details")
		}
		return &GetAccountVerificationStatusResponse{
			Status:     AccountVerificationStatusCollectionPending,
			NextAction: addAccountDl,
		}, nil
	}
	verifyAndStoreAccountRes, err := s.validateAndStoreAccount(ctx, lr, lse, applicant, savingsAccountDetails)
	if err != nil {
		return nil, errors.Wrap(err, "error validating and storing account")
	}
	if verifyAndStoreAccountRes.FailureReason != AccountVerificationFailureReasonUnspecified {
		return &GetAccountVerificationStatusResponse{
			Status:              AccountVerificationStatusFailed,
			FailureReason:       verifyAndStoreAccountRes.FailureReason,
			DisplayErrorMessage: verifyAndStoreAccountRes.DisplayErrorMessage,
		}, nil
	}
	return &GetAccountVerificationStatusResponse{
		Status:     AccountVerificationStatusSuccess,
		NextAction: dlProvider.GetApplicationStatusPollScreenDeepLink(dlProvider.GetLoanHeader(), lr.GetId()),
	}, nil
}

func (s *LDCAutoPayProvider) getDeeplinkToAddAccount(ctx context.Context,
	lr *palPb.LoanRequest, lse *palPb.LoanStepExecution, accountNumberSuffix string) (*deeplinkPb.Deeplink, error) {
	dlProvider := s.deeplinkFactory.GetDeeplinkGenerator(ctx,
		&deeplink.GetDeeplinkProviderRequest{Vendor: lr.GetVendor(), LoanProgram: lr.GetLoanProgram()})
	addBankDl, err := dlProvider.GetBankingDetailsScreenDeeplink(dlProvider.GetLoanHeader(), lse.GetRefId(),
		lse.GetId(), 0, 0, accountNumberSuffix)
	if err != nil {
		return nil, errors.Wrap(err, "error getting screen for adding savings bank account details")
	}
	dl, err := deeplink.ConstructPlMandateInitScreenV2FromLse(ctx, dlProvider, lse,
		nil, false, addBankDl)
	if err != nil {
		return nil, errors.Wrap(err, "error creating mandate init screen")
	}
	return dl, nil
}

func (s *LDCAutoPayProvider) CollectAndVerifyAccount(ctx context.Context, req *CollectAndVerifyAccountRequest) (*CollectAndVerifyAccountResponse, error) {
	if req.AccountDetails == nil {
		accVerificationStatusRes, err := s.GetAccountVerificationStatus(ctx, req.LoanStepExecutionId)
		if err != nil {
			return nil, errors.Wrap(err, "error getting account verification status")
		}
		return &CollectAndVerifyAccountResponse{
			Status:     accVerificationStatusRes.Status,
			NextAction: accVerificationStatusRes.NextAction,
		}, nil
	}
	lse, err := s.loanStepExecutionDao.GetById(ctx, req.LoanStepExecutionId)
	if err != nil {
		return nil, errors.Wrap(err, "error getting loan step execution")
	}
	lr, err := s.loanRequestDao.GetById(ctx, lse.GetRefId())
	if err != nil {
		return nil, errors.Wrap(err, "error getting loan request")
	}
	applicant, err := s.loanApplicantDao.GetByActorId(ctx, lse.GetActorId())
	if err != nil {
		return nil, errors.Wrap(err, "error getting applicant")
	}
	verifyAndStoreAccountRes, err := s.validateAndStoreAccount(ctx, lr, lse, applicant, req.AccountDetails)
	if err != nil {
		return nil, errors.Wrap(err, "error validating and storing account")
	}
	if verifyAndStoreAccountRes.FailureReason != AccountVerificationFailureReasonUnspecified {
		return &CollectAndVerifyAccountResponse{
			Status:              AccountVerificationStatusFailed,
			FailureReason:       verifyAndStoreAccountRes.FailureReason,
			DisplayErrorMessage: verifyAndStoreAccountRes.DisplayErrorMessage,
		}, nil
	}
	accVerificationStatusRes, err := s.GetAccountVerificationStatus(ctx, req.LoanStepExecutionId)
	if err != nil {
		return nil, errors.Wrap(err, "error getting account verification status")
	}
	return &CollectAndVerifyAccountResponse{
		Status:     accVerificationStatusRes.Status,
		NextAction: accVerificationStatusRes.NextAction,
	}, nil
}

type validateAndStoreAccountResponse struct {
	FailureReason       AccountVerificationFailureReason
	DisplayErrorMessage string
}

func (s *LDCAutoPayProvider) validateAndStoreAccount(ctx context.Context,
	lr *palPb.LoanRequest, lse *palPb.LoanStepExecution, applicant *palPb.LoanApplicant,
	inputAccount *palPb.MandateData_BankingDetails_AccountDetails) (*validateAndStoreAccountResponse, error) {
	offer, err := s.loanOfferDao.GetById(ctx, lr.GetOfferId())
	if err != nil {
		return nil, errors.Wrap(err, "error getting loan offer")
	}
	accountConstraints := offer.GetOfferConstraints().GetLendenConstraintInfo().GetBankDetails()
	validateAccountRes := s.validateAccount(inputAccount, accountConstraints)
	if validateAccountRes.FailureReason != AccountVerificationFailureReasonUnspecified {
		return &validateAndStoreAccountResponse{
			FailureReason:       validateAccountRes.FailureReason,
			DisplayErrorMessage: validateAccountRes.DisplayErrorMessage,
		}, nil
	}
	validatedAccount := validateAccountRes.Account
	registerAccountRes, err := s.registerAccountWithLDC(ctx, lr, applicant, validatedAccount)
	if err != nil {
		return nil, errors.Wrap(err, "error verifying account")
	}
	if registerAccountRes.FailureReason != AccountVerificationFailureReasonUnspecified {
		return &validateAndStoreAccountResponse{
			FailureReason:       registerAccountRes.FailureReason,
			DisplayErrorMessage: registerAccountRes.DisplayErrorMessage,
		}, nil
	}
	if lse.GetDetails().GetMandateData().GetBankingDetails() == nil {
		if lse.GetDetails().GetMandateData() == nil {
			lse.GetDetails().Details = &palPb.LoanStepExecutionDetails_MandateData{MandateData: &palPb.MandateData{}}
		}
		lse.GetDetails().GetMandateData().BankingDetails = &palPb.MandateData_BankingDetails{}
	}
	lse.GetDetails().GetMandateData().GetBankingDetails().FinalAccDetailsUsed = validatedAccount
	err = s.loanStepExecutionDao.Update(ctx, lse,
		[]palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS})
	if err != nil {
		return nil, errors.Wrap(err, "error updating lse with savings account details")
	}
	lr.GetDetails().VerifiedBankAccount = &palPb.BankAccount{
		AccountNumber:     validatedAccount.GetAccountNumber(),
		Ifsc:              validatedAccount.GetIfscCode(),
		BankName:          validatedAccount.GetBankName(),
		AccountHolderName: validatedAccount.GetAccountHolderName(),
	}
	err = s.loanRequestDao.Update(ctx, lr,
		[]palPb.LoanRequestFieldMask{palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_DETAILS})
	if err != nil {
		return nil, errors.Wrap(err, "error updating lr with savings account details")
	}
	return &validateAndStoreAccountResponse{}, nil
}

type validateAccountResponse struct {
	Account             *palPb.MandateData_BankingDetails_AccountDetails
	FailureReason       AccountVerificationFailureReason
	DisplayErrorMessage string
}

func (s *LDCAutoPayProvider) validateAccount(
	account *palPb.MandateData_BankingDetails_AccountDetails,
	accountConstraints *palPb.AaAnalysisBankDetails,
) *validateAccountResponse {
	if strings.TrimSpace(account.GetAccountNumber()) == "" {
		return &validateAccountResponse{
			FailureReason:       AccountVerificationFailureReasonInvalidAccountNumber,
			DisplayErrorMessage: "Account number is required",
		}
	}
	// Regex match account number and return error if it contains non-numeric characters.
	if !regexp.MustCompile(`^\d+$`).MatchString(account.GetAccountNumber()) {
		return &validateAccountResponse{
			FailureReason:       AccountVerificationFailureReasonInvalidAccountNumber,
			DisplayErrorMessage: "Account number should only contain numbers",
		}
	}
	if accountConstraints != nil {
		// No need to validate details already present in constraints
		return &validateAccountResponse{
			Account: &palPb.MandateData_BankingDetails_AccountDetails{
				AccountNumber:     account.GetAccountNumber(),
				AccountHolderName: accountConstraints.GetAccountHolderName(),
				IfscCode:          accountConstraints.GetIfsc(),
				BankName:          accountConstraints.GetBankName(),
			},
		}
	}
	if strings.TrimSpace(account.GetIfscCode()) == "" {
		return &validateAccountResponse{
			FailureReason:       AccountVerificationFailureReasonInvalidAccountDetails,
			DisplayErrorMessage: "IFSC is required",
		}
	}
	if strings.TrimSpace(account.GetAccountHolderName()) == "" {
		return &validateAccountResponse{
			FailureReason:       AccountVerificationFailureReasonInvalidAccountDetails,
			DisplayErrorMessage: "Account holder name is required",
		}
	}
	if !regexp.MustCompile(`^[a-zA-Z\s]+$`).MatchString(account.GetAccountHolderName()) {
		return &validateAccountResponse{
			FailureReason:       AccountVerificationFailureReasonInvalidAccountDetails,
			DisplayErrorMessage: "Account holder name should only contain alphabets and spaces",
		}
	}
	if strings.TrimSpace(account.GetBankName()) == "" {
		return &validateAccountResponse{
			FailureReason:       AccountVerificationFailureReasonInvalidAccountDetails,
			DisplayErrorMessage: "Bank name is required",
		}
	}
	return &validateAccountResponse{
		Account: &palPb.MandateData_BankingDetails_AccountDetails{
			AccountNumber:     account.GetAccountNumber(),
			AccountHolderName: account.GetAccountHolderName(),
			IfscCode:          account.GetIfscCode(),
			BankName:          account.GetBankName(),
		},
	}
}

type registerAccountResponse struct {
	FailureReason       AccountVerificationFailureReason
	DisplayErrorMessage string
}

func (s *LDCAutoPayProvider) registerAccountWithLDC(ctx context.Context,
	lr *palPb.LoanRequest, applicant *palPb.LoanApplicant,
	account *palPb.MandateData_BankingDetails_AccountDetails) (*registerAccountResponse, error) {
	res, err := s.ldcVgClient.AddBankDetails(ctx, &ldcVgPb.AddBankDetailsRequest{
		Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_LENDEN},
		UserId: applicant.GetVendorApplicantId(),
		LoanId: lr.GetVendorRequestId(),
		BankAccountDetails: &typesPb.BankAccountDetails{
			AccountName:   account.GetAccountHolderName(),
			AccountNumber: account.GetAccountNumber(),
			BankName:      account.GetBankName(),
			Ifsc:          account.GetIfscCode(),
			AccountType:   accountPb.AccountType_SAVINGS,
		},
	})
	if err = epifigrpc.RPCError(res, err); err != nil {
		if res.GetStatus().GetCode() == uint32(ldcVgPb.AddBankDetailsResponse_BANK_ACCOUNT_NOT_ACTIVE) {
			return &registerAccountResponse{
				FailureReason:       AccountVerificationFailureReasonInactiveAccount,
				DisplayErrorMessage: "Bank account not active",
			}, nil
		}
		if res.GetStatus().GetCode() == uint32(ldcVgPb.AddBankDetailsResponse_BANK_CONNECTION_ERROR) {
			return &registerAccountResponse{
				FailureReason:       AccountVerificationFailureReasonErrorConnectingToBank,
				DisplayErrorMessage: "Error connecting to bank for verification",
			}, nil
		}
		if res.GetStatus().GetCode() == uint32(ldcVgPb.AddBankDetailsResponse_INVALID_BANK_DETAILS) {
			return &registerAccountResponse{
				FailureReason:       AccountVerificationFailureReasonInvalidAccountDetails,
				DisplayErrorMessage: "Invalid bank details",
			}, nil
		}
		return nil, errors.Wrap(err, "error verifying bank account with LDC")
	}
	return &registerAccountResponse{}, nil
}

func (s *LDCAutoPayProvider) GetMandateSetupStatus(ctx context.Context, lseId string) (*GetMandateSetupStatusResponse, error) {
	lse, err := s.loanStepExecutionDao.GetById(ctx, lseId)
	if err != nil {
		return nil, errors.Wrap(err, "error getting loan step execution")
	}
	lr, err := s.loanRequestDao.GetById(ctx, lse.GetRefId())
	if err != nil {
		return nil, errors.Wrap(err, "error getting loan request")
	}
	applicant, err := s.loanApplicantDao.GetByActorId(ctx, lse.GetActorId())
	if err != nil {
		return nil, errors.Wrap(err, "error getting applicant")
	}
	deeplinkProvider := s.deeplinkFactory.GetDeeplinkGenerator(ctx,
		&deeplink.GetDeeplinkProviderRequest{Vendor: lr.GetVendor(), LoanProgram: lr.GetLoanProgram()})
	if lse.GetDetails().GetMandateData().GetMerchantTxnId() == "" {
		// TODO(Brijesh): Think on how to show e-NACH as disabled if user hasn't failed through UPI for at least 5 times
		mandateTypeBottomSheetDl := deeplinkProvider.GetMandateAuthBottomViewDeeplink(deeplinkProvider.GetLoanHeader())
		mandateInitDl, err := deeplink.ConstructPlMandateInitScreenV2FromLse(ctx, deeplinkProvider, lse,
			nil, false, mandateTypeBottomSheetDl)
		if err != nil {
			return nil, errors.Wrap(err, "error creating mandate init screen")
		}
		return &GetMandateSetupStatusResponse{
			Status:     MandateSetupStatusNotStarted,
			NextAction: mandateInitDl,
		}, nil
	}
	ldcMandateType, err := getMandateTypeForLDC(lse.GetDetails().GetMandateData().GetMandateType())
	if err != nil {
		return nil, errors.Wrap(err, "error getting mandate type for LDC")
	}
	res, err := s.ldcVgClient.CheckMandateStatus(ctx, &ldcVgPb.CheckMandateStatusRequest{
		UserId:      applicant.GetVendorApplicantId(),
		TrackingId:  lse.GetDetails().GetMandateData().GetMerchantTxnId(),
		MandateType: ldcMandateType,
	})
	if err = epifigrpc.RPCError(res, err); err != nil {
		return nil, errors.Wrap(err, "error checking mandate status")
	}
	switch res.GetMandateStatus() {
	case ldcVgPb.MandateStatus_MANDATE_STATUS_COMPLETED:
		lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
		lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_SETUP_SUCCESSFUL
		err := s.loanStepExecutionDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS,
		})
		if err != nil {
			return nil, errors.Wrap(err, "error updating LSE")
		}
		return &GetMandateSetupStatusResponse{
			Status:     MandateSetupStatusCompleted,
			NextAction: deeplinkProvider.GetApplicationStatusPollScreenDeepLink(deeplinkProvider.GetLoanHeader(), lr.GetId()),
			// TODO(Brijesh): Think on following improvements :
			//  1. how to show a success / failure screen before moving on to next step's screen
			//  2. how to show the next step's screen directly instead of an intermediate polling screen
		}, nil
	case ldcVgPb.MandateStatus_MANDATE_STATUS_FAILED,
		ldcVgPb.MandateStatus_MANDATE_STATUS_EXPIRED:
		lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED
		lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_FAILED_FROM_VENDOR
		err := s.loanStepExecutionDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS,
		})
		if err != nil {
			return nil, errors.Wrap(err, "error updating LSE")
		}
		return &GetMandateSetupStatusResponse{
			Status:     MandateSetupStatusFailed,
			NextAction: deeplinkProvider.GetApplicationStatusPollScreenDeepLink(deeplinkProvider.GetLoanHeader(), lr.GetId()),
		}, nil
	case ldcVgPb.MandateStatus_MANDATE_STATUS_IN_PROGRESS:
		// Note: The app calls this method periodically when a user is setting up their mandate to check for its status.
		// Hence, we want to send the same screen with the same URL back to the user.
		// Sending a new URL will lead to a reload of the webpage on the app
		// which will lead to users losing their mandate setup progress.
		mandateUrl := lse.GetDetails().GetMandateData().GetUrl()
		if mandateUrl == "" {
			return nil, errors.New("mandate URL is empty")
		}
		dl, err := deeplinkProvider.GetLoansMandateSetupScreen(ctx,
			deeplinkProvider.GetLoanHeader(), lse.GetActorId(), lse.GetRefId(), lse.GetId(),
			&provider.LoansMandateSetupScreenParams{WebViewParams: &provider.WebViewParams{EntryUrl: mandateUrl}},
		)
		if err != nil {
			return nil, errors.Wrap(err, "error getting mandate setup screen deeplink")
		}
		return &GetMandateSetupStatusResponse{
			Status:     MandateSetupStatusInProgress,
			NextAction: dl,
		}, nil
	default:
		return nil, errors.Errorf("unexpected mandate status: %s", res.GetMandateStatus())
	}
}

func (s *LDCAutoPayProvider) InitiateMandateSetup(
	ctx context.Context,
	req *InitiateMandateSetupRequest,
) (*InitiateMandateSetupResponse, error) {
	lse, err := s.loanStepExecutionDao.GetById(ctx, req.LoanStepExecutionId)
	if err != nil {
		return nil, errors.Wrap(err, "error getting loan step execution")
	}
	lr, err := s.loanRequestDao.GetById(ctx, lse.GetRefId())
	if err != nil {
		return nil, errors.Wrap(err, "error getting loan request")
	}
	if lse.GetDetails().GetMandateData() == nil {
		lse.GetDetails().Details = &palPb.LoanStepExecutionDetails_MandateData{MandateData: &palPb.MandateData{}}
	}
	lse.GetDetails().GetMandateData().MandateType = req.MandateType
	err = s.loanStepExecutionDao.Update(ctx, lse,
		[]palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS})
	if err != nil {
		return nil, errors.Wrap(err, "error updating LSE")
	}
	mandateSetupInfo, err := s.ldcMandateSetupProcessor.FetchOrRefreshMandateStatus(ctx, lse)
	if err != nil {
		return nil, errors.Wrap(err, "error getting mandate setup status")
	}
	deeplinkProvider := s.deeplinkFactory.GetDeeplinkGenerator(ctx,
		&deeplink.GetDeeplinkProviderRequest{Vendor: lr.GetVendor(), LoanProgram: lr.GetLoanProgram()})
	if mandateSetupInfo.IsCompleted {
		return &InitiateMandateSetupResponse{
			NextAction: deeplinkProvider.GetApplicationStatusPollScreenDeepLink(deeplinkProvider.GetLoanHeader(), lr.GetId()),
		}, nil
	}
	mandateUrl := lse.GetDetails().GetMandateData().GetUrl()
	if mandateUrl == "" {
		return nil, errors.New("mandate URL is empty")
	}
	dl, err := deeplinkProvider.GetLoansMandateSetupScreen(ctx,
		deeplinkProvider.GetLoanHeader(), lse.GetActorId(), lse.GetRefId(), lse.GetId(),
		&provider.LoansMandateSetupScreenParams{WebViewParams: &provider.WebViewParams{EntryUrl: mandateUrl}},
	)
	if err != nil {
		return nil, errors.Wrap(err, "error getting mandate setup screen deeplink")
	}
	return &InitiateMandateSetupResponse{
		NextAction: dl,
	}, nil
}

func getMandateTypeForLDC(mandateType enumsPb.MandateType) (ldcVgPb.MandateType, error) {
	switch mandateType {
	case enumsPb.MandateType_MANDATE_TYPE_E_NACH:
		return ldcVgPb.MandateType_MANDATE_TYPE_NACH_MANDATE, nil
	case enumsPb.MandateType_MANDATE_TYPE_UPI:
		return ldcVgPb.MandateType_MANDATE_TYPE_UPI_MANDATE, nil
	default:
		return 0, errors.Errorf("unknown mandate type: %s", mandateType)
	}
}
