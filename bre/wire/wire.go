//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.
package wire

import (
	"github.com/epifi/be-common/pkg/events"

	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/group"
	breVgPb "github.com/epifi/gamma/api/vendorgateway/lending/bre"
	brePb "github.com/epifi/gamma/bre"
	"github.com/epifi/gamma/bre/config"
	httpPkg "github.com/epifi/gamma/pkg/http"

	"github.com/google/wire"
)

func InitialiseBreService(conf *config.Config, breVgClient breVgPb.BusinessRuleEngineClient, inhouseBreS3Client brePb.InhouseBreS3Client,
	rawInhouseBreS3Client brePb.RawInhouseBreS3Client, userClient user.UsersClient, eventBroker events.Broker, actorClient actor.ActorClient, userGroupClient group.GroupClient) *brePb.Service {
	wire.Build(
		httpPkg.ClientWireSet,
		brePb.NewService,
	)
	return &brePb.Service{}
}
