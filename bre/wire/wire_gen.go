// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/api/vendorgateway/lending/bre"
	bre2 "github.com/epifi/gamma/bre"
	"github.com/epifi/gamma/bre/config"
	"github.com/epifi/gamma/pkg/http"
)

// Injectors from wire.go:

func InitialiseBreService(conf *config.Config, breVgClient bre.BusinessRuleEngineClient, inhouseBreS3Client bre2.InhouseBreS3Client, rawInhouseBreS3Client bre2.RawInhouseBreS3Client, userClient user.UsersClient, eventBroker events.Broker, actorClient actor.ActorClient, userGroupClient group.GroupClient) *bre2.Service {
	client := http.NewClient()
	service := bre2.NewService(client, conf, breVgClient, inhouseBreS3Client, rawInhouseBreS3Client, userClient, eventBroker, actorClient, userGroupClient)
	return service
}
