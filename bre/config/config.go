package config

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"sync"

	"github.com/epifi/be-common/pkg/cfg"
)

var (
	once   sync.Once
	config *Config
	err    error
)

// nolint: gosec
const (
	BreBearerTokenSecretKeyName = "BreCredentials"
)

var (
	_, b, _, _ = runtime.Caller(0)
)

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig()
	})
	if err != nil {
		return nil, err
	}
	return config, err
}

func loadConfig() (*Config, error) {
	// will be used only for test environment
	configDirPath := testEnvConfigDir()
	conf := &Config{}
	k, _, err := cfg.LoadConfigUsingKoanf(configDirPath, cfg.BRE_SERVICE)
	if err != nil {
		return nil, fmt.Errorf("error in loading config %w", err)
	}
	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, fmt.Errorf("failed to refresh dymanic config: %w", err)
	}
	keyToSecretMap, err := cfg.LoadSecrets(conf.Secrets.Ids, conf.Application.Environment, conf.AWS.Region)
	if err != nil {
		return nil, err
	}

	if val, ok := keyToSecretMap[BreBearerTokenSecretKeyName]; ok {
		if val != "" {
			err := json.Unmarshal([]byte(val), &conf.BreCredentials)
			if err != nil {
				return nil, fmt.Errorf("failed to unmarshal Bre secrets")
			}
		}
	}

	return conf, nil
}

// readAndSetEnv reads and sets env vars to config
func readAndSetEnv(c *Config) error {
	if val, ok := os.LookupEnv("APPLICATION_NAME"); ok {
		c.Application.Name = val
	}

	if val, ok := os.LookupEnv("SERVER_PORT"); ok {
		intVal, err := strconv.Atoi(val)
		if err != nil {
			return fmt.Errorf("failed to convert port to int: %w", err)
		}

		c.Server.Ports.GrpcPort = intVal
	}

	return nil
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..", "values")
	return configPath
}

type Config struct {
	Application             *application
	Logging                 *cfg.Logging
	SecureLogging           *cfg.SecureLogging
	Flags                   *Flags
	Server                  *server
	Tracing                 *cfg.Tracing
	AWS                     *Aws
	Apis                    *Apis
	InHouseBreConfig        *InHouseBreConfig
	RawInhouseBreBucketName string `iam:"s3-readwrite"`
	InhouseBreBucketName    string `iam:"s3-readwrite"`
	Secrets                 *cfg.Secrets
	BreCredentials          *BreCredentials
}

type InHouseBreConfig struct {
	InhouseBreRawBucketS3FilePath string
	InhouseBreS3FilePath          string
}

type application struct {
	Environment string
	Name        string
	ServerName  string
}

type server struct {
	Ports *cfg.ServerPorts
}

type BreCredentials struct {
	LoanPreScreeningBearerToken                  string `json:"loanPreScreeningBearerToken"`
	LoanDecisioningBearerToken                   string `json:"loanDecisioningBearerToken"`
	LoanDecisioningRealtimeSubventionBearerToken string `json:"loanDecisioningSubventionBearerToken"`
	LoanDecisioningNonFiCoreBearerToken          string `json:"loanDecisioningNonFiCoreBearerToken"`
	LoanPreScreeningNonFiCoreBearerToken         string `json:"loanPreScreeningNonFiCoreBearerToken"`
	LoanDecisioningPreBreBearerToken             string `json:"loanDecisioningPreBreBearerToken"`
	LoanDecisioningFinalBreBearerToken           string `json:"loanDecisioningFinalBreBearerToken"`
	LoanDecisioningPreBreOfferBearerToken        string `json:"loanDecisioningPreBreOfferBearerToken"`
	LoanDecisioningFinalBreV2BearerToken         string `json:"loanDecisioningFinalBreV2BearerToken"`
}

type Aws struct {
	Region string
}

type Flags struct {
	// A flag to determine if the debug message in Status is to be trimmed
	TrimDebugMessageFromStatus bool
}

type Apis struct {
	LoanDecisioning                   *Endpoint
	LoanPreScreening                  *Endpoint
	LoanDecisioningRealtimeSubvention *Endpoint
	LoanDecisioningNonFiCore          *Endpoint
	LoanPreScreeningNonFiCore         *Endpoint
	LoanPreBre                        *Endpoint
	LoanFinalBre                      *Endpoint
	LoanPreBreOffer                   *Endpoint
	LoanFinalBreV2                    *Endpoint
}

type Endpoint struct {
	Url       string
	AuthToken string
}
