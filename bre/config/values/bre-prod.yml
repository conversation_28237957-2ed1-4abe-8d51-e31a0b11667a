Application:
  Environment: "prod"
  Name: "bre"
  ServerName: "lending"

Server:
  Ports:
    GrpcPort: 8111
    GrpcSecurePort: 9528
    HttpPort: 9999

AWS:
  Region: "ap-south-1"

Tracing:
  Enable: true

# TODO: Add url once available
Apis:
  LoanDecisioning:
    Url: "https://iris.epifi.in/evaluator/flow/v1/flow-finalOfferCibilATL"
    AuthToken: "Bearer R2WZ8jdH1smT-a1zLhxeDXd6NLJJh6IKyLKdQRCANO9ACQA0W82d6uffKpkn0UFDmmiXAKuAkHucXEwuRpcaHA=="
  LoanPreScreening:
    Url: "https://iris.epifi.in/evaluator/flow/v1/flow-preScreenExperianATL"
    AuthToken: "Bearer nyJEG8CRLZo8zCmTHqC4q95XfG5dBOQ2MbbzvvYZjWlCWfkCtm83dp5CyanPdOqBbEnr7D0kubatekImiqAZdw=="
  LoanDecisioningRealtimeSubvention:
    Url: "https://iris.epifi.in/evaluator/flow/v1/flow-finalOfferCibilPA"
  LoanPreScreeningNonFiCore:
    Url: "https://iris.epifi.in/evaluator/flow/v1/flow-245882904"
  LoanDecisioningNonFiCore:
    Url: "https://iris.epifi.in/evaluator/flow/v1/flow-245866728"
  LoanPreBre:
    Url: "https://iris.epifi.in/evaluator/flow/v1/flow-123564240"
  LoanFinalBre:
    Url: "https://iris.epifi.in/evaluator/flow/v1/flow-82141344"
  LoanPreBreOffer:
    Url: "https://iris.epifi.in/evaluator/flow/v1/flow-67c17926f4e8f34e5478863b"
  LoanFinalBreV2:
    Url: "https://iris.epifi.in/evaluator/flow/v1/flow-67d03026f4e8f34e54788fc2"
  LoanFinalBreV2LDC:
    Url: "https://iris.epifi.in/evaluator/flow/v1/flow-689c7d01533c3e6c4e3cce38"

RawInhouseBreBucketName: "epifi-raw"
InhouseBreBucketName: "epifi-prod-cc-inhouse-bre-responses"

InHouseBreConfig:
  InhouseBreRawS3BucketName: "epifi-raw"
  InhouseBreS3BucketName: "epifi-prod-cc-inhouse-bre-responses"
  InhouseBreRawBucketS3FilePath: "vendor/bre_scienaptic/cc_bre_output/%s/%s-inhouseBreOutputs.csv"
  InhouseBreS3FilePath: "cc/inhouse/bre/%s-inhouseBreResponses.csv"

Secrets:
  Ids:
    BreCredentials: "prod/lending/bre-credentials"


FeatureReleaseConfig:
  FeatureConstraints:
    - FEATURE_LDC_FINAL_BRE_NEW_ENDPOINT:
        StickyPercentageConstraintConfig:
          RolloutPercentage: 30
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
