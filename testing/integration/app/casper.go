// nolint:depguard
package app

import (
	"context"
	"fmt"
	"testing"
	"time"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	cmsPb "github.com/epifi/gamma/api/cms"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	accrualPkg "github.com/epifi/gamma/pkg/accrual"

	"github.com/golang/protobuf/ptypes"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"

	"github.com/epifi/be-common/api/rpc"

	"github.com/epifi/gamma/api/accrual"
	authPb "github.com/epifi/gamma/api/auth"
	casperPb "github.com/epifi/gamma/api/casper"
	exchangerPb "github.com/epifi/gamma/api/casper/exchanger"
	"github.com/epifi/gamma/api/frontend/header"
	feRewardsPb "github.com/epifi/gamma/api/frontend/rewards"
	types "github.com/epifi/gamma/api/typesv2"
)

type CasperTestSuite struct {
	beOfferCatalogClient      casperPb.OfferCatalogServiceClient
	beOfferInventoryClient    casperPb.OfferInventoryServiceClient
	beExchangerOfferSvcClient exchangerPb.ExchangerOfferServiceClient
	feRewardsClient           feRewardsPb.RewardsClient
	beAccrualClient           accrual.AccrualClient
	beAuthClient              authPb.AuthClient
	cmsServiceClient          cmsPb.CmsServiceClient
}

func NewCasperTestSuite(beOfferCatalogClient casperPb.OfferCatalogServiceClient, beOfferInventoryClient casperPb.OfferInventoryServiceClient, beExchangerOfferSvcClient exchangerPb.ExchangerOfferServiceClient, feRewardsClient feRewardsPb.RewardsClient, beAccrualClient accrual.AccrualClient, beAuthClient authPb.AuthClient, cmsServiceClient cmsPb.CmsServiceClient) *CasperTestSuite {
	return &CasperTestSuite{beOfferCatalogClient: beOfferCatalogClient, beOfferInventoryClient: beOfferInventoryClient, beExchangerOfferSvcClient: beExchangerOfferSvcClient, feRewardsClient: feRewardsClient, beAccrualClient: beAccrualClient, beAuthClient: beAuthClient, cmsServiceClient: cmsServiceClient}
}

func (cts *CasperTestSuite) CreateOfferAndAddInToInventory(t *testing.T, createOfferReq *casperPb.CreateOfferRequest) string {
	// create offer
	createOfferRes, err := cts.beOfferCatalogClient.CreateOffer(context.Background(), createOfferReq)

	assert.Nil(t, err)
	assert.Equal(t, createOfferRes.GetStatus().Code, rpc.StatusOk().Code)
	assert.NotEmpty(t, createOfferRes.GetOfferId())

	// create inventory for just created offer
	createInventoryRes, err := cts.beOfferInventoryClient.CreateOfferInventory(context.Background(), &casperPb.CreateOfferInventoryRequest{
		OfferId:         createOfferRes.GetOfferId(),
		TotalCount:      100,
		MaxPerUserLimit: 10,
	})
	assert.Nil(t, err)
	assert.Equal(t, createInventoryRes.GetStatus().Code, rpc.StatusOk().Code)
	assert.NotEmpty(t, createInventoryRes.GetOfferInventoryId())

	return createOfferRes.GetOfferId()
}

func (cts *CasperTestSuite) confirmRedemption(t *testing.T, confirmRedemptionReq *feRewardsPb.ConfirmRedemptionRequest) *feRewardsPb.ConfirmRedemptionResponse {
	confirmRedemptionRes, err := cts.feRewardsClient.ConfirmRedemption(context.Background(), confirmRedemptionReq)
	assert.Nil(t, err)
	assert.Equal(t, confirmRedemptionRes.GetStatus().Code, rpc.StatusOk().Code)
	return confirmRedemptionRes
}

func (cts *CasperTestSuite) CreateExchangerOffer(t *testing.T, createOfferReq *exchangerPb.CreateExchangerOfferRequest) string {
	// create offer
	createOfferRes, err := cts.beExchangerOfferSvcClient.CreateExchangerOffer(context.Background(), createOfferReq)
	assert.Nil(t, err)
	assert.Equal(t, createOfferRes.GetStatus().Code, rpc.StatusOk().Code)

	offer := createOfferRes.GetExchangerOffer()
	assert.NotNil(t, offer)
	assert.NotEmpty(t, offer.GetId())

	return offer.GetId()
}

func (cts *CasperTestSuite) redeemExchangerOffer(t *testing.T, redemptionReq *feRewardsPb.RedeemExchangerOfferRequest) *feRewardsPb.RedeemExchangerOfferResponse {
	redemptionRes, err := cts.feRewardsClient.RedeemExchangerOffer(context.Background(), redemptionReq)
	assert.Nil(t, err)
	assert.Equal(t, redemptionRes.GetStatus().Code, rpc.StatusOk().Code)
	return redemptionRes
}

func (cts *CasperTestSuite) chooseExchangerOrderOption(t *testing.T, req *feRewardsPb.ChooseExchangerOrderOptionRequest) *feRewardsPb.ChooseExchangerOrderOptionResponse {
	chooseOptionRes, err := cts.feRewardsClient.ChooseExchangerOrderOption(context.Background(), req)
	assert.Nil(t, err)
	assert.Equal(t, rpc.StatusOk().Code, chooseOptionRes.GetStatus().Code)
	return chooseOptionRes
}

func (cts *CasperTestSuite) getBeExchangerOrdersForActor(t *testing.T, req *exchangerPb.GetExchangerOfferOrdersForActorRequest) []*exchangerPb.ExchangerOfferOrder {
	exchangerOrdersForActorRes, err := cts.beExchangerOfferSvcClient.GetExchangerOfferOrdersForActor(context.Background(), req)
	assert.Nil(t, err)
	assert.Equal(t, rpc.StatusOk().Code, exchangerOrdersForActorRes.GetStatus().Code)
	return exchangerOrdersForActorRes.GetExchangerOfferOrders()
}

func (cts *CasperTestSuite) createExchangerOfferInventory(t *testing.T, req *exchangerPb.CreateExchangerOfferInventoryRequest) *exchangerPb.CreateExchangerOfferInventoryResponse {
	createInventoryRes, err := cts.beExchangerOfferSvcClient.CreateExchangerOfferInventory(context.Background(), req)
	assert.Nil(t, err)
	assert.Equal(t, rpc.StatusOk().GetCode(), createInventoryRes.GetStatus().GetCode())
	return createInventoryRes
}

func (cts *CasperTestSuite) getRedeemedOffersV1(t *testing.T, req *feRewardsPb.GetRedeemedOffersV1Request) *feRewardsPb.GetRedeemedOffersV1Response {
	getRedeemedOffersRes, err := cts.feRewardsClient.GetRedeemedOffersV1(context.Background(), req)
	assert.Nil(t, err)
	assert.Equal(t, rpc.StatusOk().GetCode(), getRedeemedOffersRes.GetStatus().GetCode())
	return getRedeemedOffersRes
}

func (cts *CasperTestSuite) submitUserInputForExchangerOrder(t *testing.T, req *feRewardsPb.SubmitExchangerOrderUserInputRequest) *feRewardsPb.SubmitExchangerOrderUserInputResponse {
	submitUserInputRes, err := cts.feRewardsClient.SubmitExchangerOrderUserInput(context.Background(), req)
	assert.Nil(t, err)
	assert.Equal(t, rpc.StatusOk().GetCode(), submitUserInputRes.GetRespHeader().GetStatus().GetCode())
	return submitUserInputRes
}

func (cts *CasperTestSuite) updateExchangerOfferStatus(t *testing.T, req *exchangerPb.UpdateExchangerOfferStatusRequest) *exchangerPb.UpdateExchangerOfferStatusResponse {
	updateStatusRes, err := cts.beExchangerOfferSvcClient.UpdateExchangerOfferStatus(context.Background(), req)
	assert.Nil(t, err)
	assert.Equal(t, rpc.StatusOk().GetCode(), updateStatusRes.GetStatus().GetCode())
	return updateStatusRes
}

// nolint: dupl, funlen
func (cts *CasperTestSuite) TestLoyltyVendor_EGVRedemptionHappyFlow(t *testing.T, auth *header.RequestHeader) {
	t.Parallel()

	// create EGV offer backed by loylty vendor
	offerId := cts.CreateOfferAndAddInToInventory(t, &casperPb.CreateOfferRequest{
		Name:           "offer-1",
		Desc:           "dummy offer for acceptance test",
		Price:          10000,
		OfferType:      casperPb.OfferType_GIFT_CARD,
		RedemptionMode: casperPb.OfferRedemptionMode_FI_COINS,
		VendorName:     casperPb.OfferVendor_LOYLTY,
		VendorOfferMetadata: &casperPb.VendorOfferMetadata{
			VendorOfferMetadata: &casperPb.VendorOfferMetadata_LoyltyVendorOfferMetadata{
				LoyltyVendorOfferMetadata: &casperPb.LoyltyVendorOfferMetadata{
					SkuId:     "M11082265",
					ProductId: "M11082265",
				},
			},
		},
		OfferMetadata: &casperPb.OfferMetadata{
			OfferTypeSpecificMetadata: &casperPb.OfferMetadata_GiftCardMetadata{
				GiftCardMetadata: &casperPb.GiftCardOfferMetadata{GiftCardValue: 500},
			},
		},
		OfferAdditionalDetails: &casperPb.OfferAdditionalDetails{
			OfferDetails:             []string{"dummy offer details"},
			HowToRedeem:              []string{"dummy how to redeem details"},
			NextSteps:                []string{"dummy steps post redemption"},
			BgColor:                  "#00FF00",
			AfterRedemptionOfferName: "dummy after redemption offer name",
			OfferDisplayRank:         1,
			HomeTitle:                "dummy home title",
			BrandName:                "dummy brand",
			OfferTitle:               "dummy offer title",
		},
		Tnc: &casperPb.OfferTnc{TncList: []string{"dummy tnc 1, dummy tnc2"}},
		Images: []*casperPb.OfferImage{{
			ImageType: casperPb.ImageType_BRAND_IMAGE,
			Url:       "dummy url",
		}},
		CategoryTag: casperPb.CategoryTag_CATEGORY_TAG_VOUCHERS,
	})

	// init redemption request
	initRedemptionRes, err := cts.feRewardsClient.InitiateRedemption(context.Background(), &feRewardsPb.InitiateRedemptionRequest{
		Req:     auth,
		OfferId: offerId,
	})
	assert.Nil(t, err)
	assert.Equal(t, initRedemptionRes.GetStatus().Code, rpc.StatusOk().Code)
	assert.NotEmpty(t, initRedemptionRes.GetRedemptionRequestId())

	// confirm redemption is idempotent for a given redemption request id and
	// currently there is no status check rpc for redemption, so keep on trying until terminal state is reached.
	var confirmRedemptionRes *feRewardsPb.ConfirmRedemptionResponse
	for i := 0; i < 20; i++ {
		confirmRedemptionRes = cts.confirmRedemption(t, &feRewardsPb.ConfirmRedemptionRequest{
			Req:                 auth,
			RedemptionRequestId: initRedemptionRes.GetRedemptionRequestId(),
		})
		// if terminal state is reached, no need to retry further
		if confirmRedemptionRes.GetRedeemedOffer().GetProcessingStatus() == feRewardsPb.RedeemedOfferProcessingStatus_RedeemedOfferProcessingStatus_SUCCESS ||
			confirmRedemptionRes.GetRedeemedOffer().GetProcessingStatus() == feRewardsPb.RedeemedOfferProcessingStatus_RedeemedOfferProcessingStatus_FAILED {
			break
		}
		// wait for 2 seconds before next retry
		time.Sleep(2 * time.Second)
	}
	assert.Equal(t, feRewardsPb.RedeemedOfferProcessingStatus_RedeemedOfferProcessingStatus_SUCCESS, confirmRedemptionRes.GetRedeemedOffer().GetProcessingStatus())
	assert.NotNil(t, confirmRedemptionRes.GetRedeemedOffer().GetRedeemedOfferDetails().GetEgiftCardDetails())
}

// nolint: dupl, funlen
func (cts *CasperTestSuite) TestInHouseVendor_CmsCouponRedemptionHappyFlow(t *testing.T, auth *header.RequestHeader) {
	t.Parallel()

	// create record for cms
	productId := cts.CreateCmsProduct(t)
	skuId := cts.CreateCmsSku(t, productId)
	cts.CreateCmsCoupon(t, skuId)

	// create cms offer backed by in house vendor
	offerId := cts.CreateOfferAndAddInToInventory(t, &casperPb.CreateOfferRequest{
		Name:           "offer-1",
		Desc:           "dummy offer for acceptance test",
		Price:          100,
		OfferType:      casperPb.OfferType_CMS_COUPON,
		RedemptionMode: casperPb.OfferRedemptionMode_FI_COINS,
		VendorName:     casperPb.OfferVendor_IN_HOUSE,
		VendorOfferMetadata: &casperPb.VendorOfferMetadata{
			VendorOfferMetadata: &casperPb.VendorOfferMetadata_InHouseVendorOfferMetadata{
				InHouseVendorOfferMetadata: &casperPb.InHouseVendorOfferMetadata{
					SkuId: skuId,
				},
			},
		},
		OfferAdditionalDetails: &casperPb.OfferAdditionalDetails{
			OfferDetails:             []string{"dummy offer details"},
			HowToRedeem:              []string{"dummy how to redeem details"},
			NextSteps:                []string{"dummy steps post redemption"},
			BgColor:                  "#00FF00",
			AfterRedemptionOfferName: "dummy after redemption offer name",
		},
		Tnc: &casperPb.OfferTnc{TncList: []string{"dummy tnc 1, dummy tnc2"}},
		Images: []*casperPb.OfferImage{{
			ImageType: casperPb.ImageType_BRAND_IMAGE,
			Url:       "dummy url",
		}},
		CategoryTag: casperPb.CategoryTag_CATEGORY_TAG_VOUCHERS,
	})

	// init redemption request
	initRedemptionRes, err := cts.feRewardsClient.InitiateRedemption(context.Background(), &feRewardsPb.InitiateRedemptionRequest{
		Req:     auth,
		OfferId: offerId,
	})
	assert.Nil(t, err)
	assert.Equal(t, initRedemptionRes.GetStatus().GetCode(), rpc.StatusOk().GetCode())
	assert.NotEmpty(t, initRedemptionRes.GetRedemptionRequestId())

	// confirm redemption is idempotent for a given redemption request id and
	// currently there is no status check rpc for redemption, so keep on trying until terminal state is reached.
	var confirmRedemptionRes *feRewardsPb.ConfirmRedemptionResponse
	for i := 0; i < 20; i++ {
		confirmRedemptionRes = cts.confirmRedemption(t, &feRewardsPb.ConfirmRedemptionRequest{
			Req:                 auth,
			RedemptionRequestId: initRedemptionRes.GetRedemptionRequestId(),
		})
		// if terminal state is reached, no need to retry further
		if confirmRedemptionRes.GetRedeemedOffer().GetProcessingStatus() == feRewardsPb.RedeemedOfferProcessingStatus_RedeemedOfferProcessingStatus_SUCCESS ||
			confirmRedemptionRes.GetRedeemedOffer().GetProcessingStatus() == feRewardsPb.RedeemedOfferProcessingStatus_RedeemedOfferProcessingStatus_FAILED {
			break
		}
		// wait for 2 seconds before next retry
		time.Sleep(2 * time.Second)
	}
	assert.Equal(t, feRewardsPb.RedeemedOfferProcessingStatus_RedeemedOfferProcessingStatus_SUCCESS, confirmRedemptionRes.GetRedeemedOffer().GetProcessingStatus())
	assert.NotNil(t, confirmRedemptionRes.GetRedeemedOffer().GetRedeemedOfferDetails().GetDynamicFields())
}

// nolint: dupl, funlen
func (cts *CasperTestSuite) TestQwikcilverVendor_EGVRedemptionHappyFlow(t *testing.T, auth *header.RequestHeader) {
	t.Parallel()

	// create EGV offer backed by qwikcilver vendor
	offerId := cts.CreateOfferAndAddInToInventory(t, &casperPb.CreateOfferRequest{
		Name:           "offer-2",
		Desc:           "dummy offer for acceptance test",
		Price:          10000,
		OfferType:      casperPb.OfferType_GIFT_CARD,
		RedemptionMode: casperPb.OfferRedemptionMode_FI_COINS,
		VendorName:     casperPb.OfferVendor_QWIKCILVER,
		VendorOfferMetadata: &casperPb.VendorOfferMetadata{
			VendorOfferMetadata: &casperPb.VendorOfferMetadata_QwikcilverVendorOfferMetadata{
				QwikcilverVendorOfferMetadata: &casperPb.QwikcilverVendorOfferMetadata{
					SkuId: "CNPIN",
				},
			},
		},
		OfferMetadata: &casperPb.OfferMetadata{
			OfferTypeSpecificMetadata: &casperPb.OfferMetadata_GiftCardMetadata{
				GiftCardMetadata: &casperPb.GiftCardOfferMetadata{GiftCardValue: 1},
			},
		},
		OfferAdditionalDetails: &casperPb.OfferAdditionalDetails{
			OfferDetails:             []string{"dummy offer details"},
			HowToRedeem:              []string{"dummy how to redeem details"},
			NextSteps:                []string{"dummy steps post redemption"},
			BgColor:                  "#00FF00",
			AfterRedemptionOfferName: "dummy after redemption offer name",
			OfferDisplayRank:         1,
			HomeTitle:                "dummy home title",
			BrandName:                "dummy brand",
			OfferTitle:               "dummy offer title",
		},
		Tnc: &casperPb.OfferTnc{TncList: []string{"dummy tnc 1, dummy tnc2"}},
		Images: []*casperPb.OfferImage{{
			ImageType: casperPb.ImageType_BRAND_IMAGE,
			Url:       "dummy url",
		}},
		CategoryTag: casperPb.CategoryTag_CATEGORY_TAG_VOUCHERS,
	})

	// init redemption request
	initRedemptionRes, err := cts.feRewardsClient.InitiateRedemption(context.Background(), &feRewardsPb.InitiateRedemptionRequest{
		Req:     auth,
		OfferId: offerId,
	})
	assert.Nil(t, err)
	assert.Equal(t, initRedemptionRes.GetStatus().Code, rpc.StatusOk().Code)
	assert.NotEmpty(t, initRedemptionRes.GetRedemptionRequestId())

	// confirm redemption is idempotent for a given redemption request id and
	// currently there is no status check rpc for redemption, so keep on trying until terminal state is reached.
	var confirmRedemptionRes *feRewardsPb.ConfirmRedemptionResponse
	for i := 0; i < 20; i++ {
		confirmRedemptionRes = cts.confirmRedemption(t, &feRewardsPb.ConfirmRedemptionRequest{
			Req:                 auth,
			RedemptionRequestId: initRedemptionRes.GetRedemptionRequestId(),
		})
		// if terminal state is reached, no need to retry further
		if confirmRedemptionRes.GetRedeemedOffer().GetProcessingStatus() == feRewardsPb.RedeemedOfferProcessingStatus_RedeemedOfferProcessingStatus_SUCCESS ||
			confirmRedemptionRes.GetRedeemedOffer().GetProcessingStatus() == feRewardsPb.RedeemedOfferProcessingStatus_RedeemedOfferProcessingStatus_FAILED {
			break
		}
		// wait for 2 seconds before next retry
		time.Sleep(2 * time.Second)
	}
	assert.Equal(t, feRewardsPb.RedeemedOfferProcessingStatus_RedeemedOfferProcessingStatus_SUCCESS, confirmRedemptionRes.GetRedeemedOffer().GetProcessingStatus())
	assert.NotNil(t, confirmRedemptionRes.GetRedeemedOffer().GetRedeemedOfferDetails().GetEgiftCardDetails())
}

// nolint: dupl, funlen
func (cts *CasperTestSuite) TestLoyltyVendor_CharityRedemptionHappyFlow(t *testing.T, auth *header.RequestHeader) {
	t.Parallel()

	// create charity offer
	offerId := cts.CreateOfferAndAddInToInventory(t, &casperPb.CreateOfferRequest{
		Name:           "offer-1",
		Desc:           "dummy offer for acceptance test",
		Price:          10000,
		OfferType:      casperPb.OfferType_CHARITY,
		RedemptionMode: casperPb.OfferRedemptionMode_FI_COINS,
		VendorName:     casperPb.OfferVendor_LOYLTY,
		VendorOfferMetadata: &casperPb.VendorOfferMetadata{
			VendorOfferMetadata: &casperPb.VendorOfferMetadata_LoyltyVendorOfferMetadata{
				LoyltyVendorOfferMetadata: &casperPb.LoyltyVendorOfferMetadata{
					OfferTypeSpecificMetadata: &casperPb.LoyltyVendorOfferMetadata_CharityOfferMetadata_{
						CharityOfferMetadata: &casperPb.LoyltyVendorOfferMetadata_CharityOfferMetadata{
							ProductName: "Akshaya Patra-COVID-19 Relief Feeding Programme (10 Meals)",
							CharityCode: "TAPF",
							Category:    "Environment",
							CharityName: "Akshay Patra",
						},
					},
				},
			},
		},
		OfferAdditionalDetails: &casperPb.OfferAdditionalDetails{
			OfferDetails:             []string{"dummy offer details"},
			HowToRedeem:              []string{"dummy how to redeem details"},
			NextSteps:                []string{"dummy steps post redemption"},
			BgColor:                  "#00FF00",
			AfterRedemptionOfferName: "dummy after redemption offer name",
			OfferDisplayRank:         1,
			HomeTitle:                "dummy home title",
			BrandName:                "dummy brand",
			OfferTitle:               "dummy offer title",
		},
		Tnc: &casperPb.OfferTnc{TncList: []string{"dummy tnc 1, dummy tnc2"}},
		Images: []*casperPb.OfferImage{{
			ImageType: casperPb.ImageType_BRAND_IMAGE,
			Url:       "dummy url",
		}},
		CategoryTag: casperPb.CategoryTag_CATEGORY_TAG_LOYALTY_POINTS,
	})

	// init redemption request
	initRedemptionRes, err := cts.feRewardsClient.InitiateRedemption(context.Background(), &feRewardsPb.InitiateRedemptionRequest{
		Req:     auth,
		OfferId: offerId,
	})
	assert.Nil(t, err)
	assert.Equal(t, initRedemptionRes.GetStatus().Code, rpc.StatusOk().Code)
	assert.NotEmpty(t, initRedemptionRes.GetRedemptionRequestId())

	// confirm redemption is idempotent for a given redemption request id and
	// currently there is no status check rpc for redemption, so keep on trying until terminal state is reached.
	var confirmRedemptionRes *feRewardsPb.ConfirmRedemptionResponse
	for i := 0; i < 20; i++ {
		confirmRedemptionRes = cts.confirmRedemption(t, &feRewardsPb.ConfirmRedemptionRequest{
			Req:                 auth,
			RedemptionRequestId: initRedemptionRes.GetRedemptionRequestId(),
		})
		// if terminal state is reached, no need to retry further
		if confirmRedemptionRes.GetRedeemedOffer().GetProcessingStatus() == feRewardsPb.RedeemedOfferProcessingStatus_RedeemedOfferProcessingStatus_SUCCESS ||
			confirmRedemptionRes.GetRedeemedOffer().GetProcessingStatus() == feRewardsPb.RedeemedOfferProcessingStatus_RedeemedOfferProcessingStatus_FAILED {
			break
		}
		// wait for 2 seconds before next retry
		time.Sleep(2 * time.Second)
	}
	assert.Equal(t, feRewardsPb.RedeemedOfferProcessingStatus_RedeemedOfferProcessingStatus_SUCCESS, confirmRedemptionRes.GetRedeemedOffer().GetProcessingStatus())
	assert.NotNil(t, confirmRedemptionRes.GetRedeemedOffer().GetRedeemedOfferDetails().GetCharityDetails())
}

// nolint: dupl, funlen
func (cts *CasperTestSuite) TestOfflineVendor_PhysicalMerchandiseRedemptionHappyFlow(t *testing.T, auth *header.RequestHeader) {
	t.Parallel()

	// create physical merchandise offer
	offerId := cts.CreateOfferAndAddInToInventory(t, &casperPb.CreateOfferRequest{
		Name:           "offer-1",
		Desc:           "dummy offer for acceptance test",
		Price:          10000,
		OfferType:      casperPb.OfferType_PHYSICAL_MERCHANDISE,
		RedemptionMode: casperPb.OfferRedemptionMode_FI_COINS,
		VendorName:     casperPb.OfferVendor_BLUE_TOKAI_OFFLINE,
		VendorOfferMetadata: &casperPb.VendorOfferMetadata{
			VendorOfferMetadata: &casperPb.VendorOfferMetadata_OfflineVendorOfferMetadata{
				OfflineVendorOfferMetadata: &casperPb.OfflineVendorOfferMetadata{
					ProductId: "product-id-1",
				},
			},
		},
		OfferMetadata: &casperPb.OfferMetadata{
			OfferTypeSpecificMetadata: &casperPb.OfferMetadata_PhysicalMerchMetadata{
				PhysicalMerchMetadata: &casperPb.PhysicalMerchandiseOfferMetadata{MerchValue: 10},
			},
		},
		OfferAdditionalDetails: &casperPb.OfferAdditionalDetails{
			OfferDetails:             []string{"dummy offer details"},
			HowToRedeem:              []string{"dummy how to redeem details"},
			NextSteps:                []string{"dummy steps post redemption"},
			BgColor:                  "#00FF00",
			AfterRedemptionOfferName: "dummy after redemption offer name",
			OfferDisplayRank:         1,
			HomeTitle:                "dummy home title",
			BrandName:                "dummy brand",
			OfferTitle:               "dummy offer title",
		},
		Tnc: &casperPb.OfferTnc{TncList: []string{"dummy tnc 1, dummy tnc2"}},
		Images: []*casperPb.OfferImage{{
			ImageType: casperPb.ImageType_BRAND_IMAGE,
			Url:       "dummy url",
		}},
		CategoryTag: casperPb.CategoryTag_CATEGORY_TAG_SHOPPING,
	})

	// init redemption request
	initRedemptionRes, err := cts.feRewardsClient.InitiateRedemption(context.Background(), &feRewardsPb.InitiateRedemptionRequest{
		Req:     auth,
		OfferId: offerId,
		RequestMetadata: &feRewardsPb.InitiateRedemptionRequest_RedemptionRequestMetadata{
			ShippingAddress: &types.PostalAddress{
				AddressLines: []string{"dummy address line1"},
			},
		},
	})
	assert.Nil(t, err)
	assert.Equal(t, initRedemptionRes.GetStatus().Code, rpc.StatusOk().Code)
	assert.NotEmpty(t, initRedemptionRes.GetRedemptionRequestId())

	// confirm redemption request
	confirmRedemptionRes, err := cts.feRewardsClient.ConfirmRedemption(context.Background(), &feRewardsPb.ConfirmRedemptionRequest{
		Req:                 auth,
		RedemptionRequestId: initRedemptionRes.GetRedemptionRequestId(),
	})

	assert.Nil(t, err)
	assert.Equal(t, confirmRedemptionRes.GetStatus().Code, rpc.StatusOk().Code)
	assert.Equal(t, feRewardsPb.RedeemedOfferProcessingStatus_RedeemedOfferProcessingStatus_SUCCESS, confirmRedemptionRes.GetRedeemedOffer().GetProcessingStatus())
	assert.NotNil(t, confirmRedemptionRes.GetRedeemedOffer().GetRedeemedOfferDetails().GetPhysicalMerchandiseDetails())
}

// nolint: dupl, funlen
func (cts *CasperTestSuite) TestOfflineVendor_CharityRedemptionHappyFlow(t *testing.T, auth *header.RequestHeader) {
	t.Parallel()

	// create physical merchandise offer
	offerId := cts.CreateOfferAndAddInToInventory(t, &casperPb.CreateOfferRequest{
		Name:           "offer-1",
		Desc:           "dummy offer for acceptance test",
		Price:          10000,
		OfferType:      casperPb.OfferType_CHARITY,
		RedemptionMode: casperPb.OfferRedemptionMode_FI_COINS,
		VendorName:     casperPb.OfferVendor_GENERIC_OFFLINE_VENDOR,
		VendorOfferMetadata: &casperPb.VendorOfferMetadata{
			VendorOfferMetadata: &casperPb.VendorOfferMetadata_OfflineVendorOfferMetadata{
				OfflineVendorOfferMetadata: &casperPb.OfflineVendorOfferMetadata{
					ProductId: "product-id-1",
				},
			},
		},
		OfferAdditionalDetails: &casperPb.OfferAdditionalDetails{
			OfferDetails:             []string{"dummy offer details"},
			HowToRedeem:              []string{"dummy how to redeem details"},
			NextSteps:                []string{"dummy steps post redemption"},
			BgColor:                  "#00FF00",
			AfterRedemptionOfferName: "dummy after redemption offer name",
			OfferDisplayRank:         1,
			HomeTitle:                "dummy home title",
			BrandName:                "dummy brand",
			OfferTitle:               "dummy offer title",
		},
		Tnc: &casperPb.OfferTnc{TncList: []string{"dummy tnc 1, dummy tnc2"}},
		Images: []*casperPb.OfferImage{{
			ImageType: casperPb.ImageType_BRAND_IMAGE,
			Url:       "dummy url",
		}},
		CategoryTag: casperPb.CategoryTag_CATEGORY_TAG_LOYALTY_POINTS,
	})

	// init redemption request
	initRedemptionRes, err := cts.feRewardsClient.InitiateRedemption(context.Background(), &feRewardsPb.InitiateRedemptionRequest{
		Req:     auth,
		OfferId: offerId,
	})
	assert.Nil(t, err)
	assert.Equal(t, initRedemptionRes.GetStatus().Code, rpc.StatusOk().Code)
	assert.NotEmpty(t, initRedemptionRes.GetRedemptionRequestId())

	// confirm redemption request
	confirmRedemptionRes, err := cts.feRewardsClient.ConfirmRedemption(context.Background(), &feRewardsPb.ConfirmRedemptionRequest{
		Req:                 auth,
		RedemptionRequestId: initRedemptionRes.GetRedemptionRequestId(),
	})

	assert.Nil(t, err)
	assert.Equal(t, confirmRedemptionRes.GetStatus().Code, rpc.StatusOk().Code)
	assert.Equal(t, feRewardsPb.RedeemedOfferProcessingStatus_RedeemedOfferProcessingStatus_SUCCESS, confirmRedemptionRes.GetRedeemedOffer().GetProcessingStatus())
	assert.NotNil(t, confirmRedemptionRes.GetRedeemedOffer().GetRedeemedOfferDetails().GetCharityDetails())
}

// nolint: dupl, funlen
func (cts *CasperTestSuite) TestExchangerOffer_RedemptionHappyFlow(t *testing.T, auth *header.RequestHeader) {
	t.Parallel()

	// create exchanger offer
	const allowedAttempts = uint32(10)
	offerId := cts.CreateExchangerOffer(t, &exchangerPb.CreateExchangerOfferRequest{
		RedemptionCurrency: exchangerPb.ExchangerOfferRedemptionCurrency_EXCHANGER_OFFER_REDEMPTION_CURRENCY_FI_COINS,
		RedemptionPrice:    10000,
		OfferDisplayDetails: &exchangerPb.ExchangerOfferDisplayDetails{
			Title:       "Let your Fi-Coins pay for stuff",
			Subtitle:    "Trade your Fi-Coins for cash",
			Desc:        "Win exciting cashback rewards",
			ImageUrl:    "image-url-1",
			TileBgColor: "#111111",
		},
		OfferOptionsConfig: &exchangerPb.ExchangerOfferOptionsConfig{
			DefaultDecideTimeInSecs: 100,
			// configuring 2 options
			OptionsConfig: []*exchangerPb.ExchangerOfferOptionConfig{
				{
					RewardConfigUnits: []*exchangerPb.ExchangerOfferOptionConfig_RewardConfigUnit{
						{
							RewardType:    exchangerPb.RewardType_REWARD_TYPE_FI_COINS,
							DisplayConfig: &exchangerPb.ExchangerOfferOptionConfig_Display{},
							Percentage:    100,
							RewardUnitsConfig: &exchangerPb.ExchangerOfferOptionConfig_RewardConfigUnit_RangeProbabilityConfig{
								RangeProbabilityConfig: &exchangerPb.RangeProbabilityUnitsConfig{
									ConfigUnits: []*exchangerPb.RangeProbabilityUnitsConfig_ConfigUnit{
										{
											Start:      100,
											End:        200,
											Percentage: 100,
										}}},
							},
						},
					},
				},
				{
					RewardConfigUnits: []*exchangerPb.ExchangerOfferOptionConfig_RewardConfigUnit{
						{
							RewardType:    exchangerPb.RewardType_REWARD_TYPE_CASH,
							DisplayConfig: &exchangerPb.ExchangerOfferOptionConfig_Display{},
							Percentage:    100,
							RewardUnitsConfig: &exchangerPb.ExchangerOfferOptionConfig_RewardConfigUnit_RangeProbabilityConfig{
								RangeProbabilityConfig: &exchangerPb.RangeProbabilityUnitsConfig{
									ConfigUnits: []*exchangerPb.RangeProbabilityUnitsConfig_ConfigUnit{
										{
											Start:      10,
											End:        20,
											Percentage: 100,
										}}},
							},
						},
					},
				},
			},
		},
		OfferAggregatesConfig: &exchangerPb.ExchangerOfferAggregatesConfig{
			DailyAllowedAttemptsPerUser: allowedAttempts,
		},
		CategoryTag: casperPb.CategoryTag_CATEGORY_TAG_PLAY_AND_WIN,
	})

	// update exchanger offer status to APPROVED -> ACTIVE to be able to redeem
	updateStatusRes := cts.updateExchangerOfferStatus(t, &exchangerPb.UpdateExchangerOfferStatusRequest{
		OfferId:   offerId,
		NewStatus: exchangerPb.ExchangerOfferStatus_EXCHANGER_OFFER_STATUS_APPROVED,
	})
	assert.Equal(t, exchangerPb.ExchangerOfferStatus_EXCHANGER_OFFER_STATUS_APPROVED, updateStatusRes.GetExchangerOffer().GetStatus())

	updateStatusRes = cts.updateExchangerOfferStatus(t, &exchangerPb.UpdateExchangerOfferStatusRequest{
		OfferId:   offerId,
		NewStatus: exchangerPb.ExchangerOfferStatus_EXCHANGER_OFFER_STATUS_ACTIVE,
	})
	assert.Equal(t, exchangerPb.ExchangerOfferStatus_EXCHANGER_OFFER_STATUS_ACTIVE, updateStatusRes.GetExchangerOffer().GetStatus())

	// redeem exchanger offer
	redemptionRes := cts.redeemExchangerOffer(t, &feRewardsPb.RedeemExchangerOfferRequest{
		Req:              auth,
		ExchangerOfferId: offerId,
		RequestId:        uuid.New().String(),
	})
	assert.NotNil(t, redemptionRes.GetExchangerOfferOrder())
	assert.Equal(t, allowedAttempts-1, redemptionRes.GetAttemptsLeft())
	rewardOptions := redemptionRes.GetExchangerOfferOrder().GetOptions()
	assert.Equal(t, 2, len(rewardOptions))

	// choose the first option (fi-coins one)
	chooseOptionRes := cts.chooseExchangerOrderOption(t, &feRewardsPb.ChooseExchangerOrderOptionRequest{
		Req:              auth,
		ExchangerOrderId: redemptionRes.GetExchangerOfferOrder().GetId(),
		OptionId:         rewardOptions[0].GetId(),
	})
	assert.NotNil(t, chooseOptionRes.GetExchangerOrder().GetChosenOption())
	assert.Equal(t, feRewardsPb.ExchangerOrderStatus_FULFILLED, chooseOptionRes.GetExchangerOrder().GetStatus())
}

// nolint: dupl, funlen
func (cts *CasperTestSuite) TestExchangerOffer_OptionAutoClaimFlow(t *testing.T, auth *header.RequestHeader) {
	t.Parallel()

	// create exchanger offer
	const allowedAttempts = uint32(10)
	const optionAutoClaimTimeInSecs = 5
	offerId := cts.CreateExchangerOffer(t, &exchangerPb.CreateExchangerOfferRequest{
		RedemptionCurrency: exchangerPb.ExchangerOfferRedemptionCurrency_EXCHANGER_OFFER_REDEMPTION_CURRENCY_FI_COINS,
		RedemptionPrice:    10000,
		OfferDisplayDetails: &exchangerPb.ExchangerOfferDisplayDetails{
			Title:       "Let your Fi-Coins pay for stuff",
			Subtitle:    "Trade your Fi-Coins for cash",
			Desc:        "Win exciting cashback rewards",
			ImageUrl:    "image-url-1",
			TileBgColor: "#111111",
		},
		OfferOptionsConfig: &exchangerPb.ExchangerOfferOptionsConfig{
			DefaultDecideTimeInSecs: optionAutoClaimTimeInSecs,
			// configuring 2 options
			OptionsConfig: []*exchangerPb.ExchangerOfferOptionConfig{
				{
					RewardConfigUnits: []*exchangerPb.ExchangerOfferOptionConfig_RewardConfigUnit{
						{
							RewardType:    exchangerPb.RewardType_REWARD_TYPE_FI_COINS,
							DisplayConfig: &exchangerPb.ExchangerOfferOptionConfig_Display{},
							Percentage:    100,
							RewardUnitsConfig: &exchangerPb.ExchangerOfferOptionConfig_RewardConfigUnit_RangeProbabilityConfig{
								RangeProbabilityConfig: &exchangerPb.RangeProbabilityUnitsConfig{
									ConfigUnits: []*exchangerPb.RangeProbabilityUnitsConfig_ConfigUnit{
										{
											Start:      100,
											End:        200,
											Percentage: 100,
										}}},
							},
						},
					},
				},
			},
		},
		OfferAggregatesConfig: &exchangerPb.ExchangerOfferAggregatesConfig{
			DailyAllowedAttemptsPerUser: allowedAttempts,
		},
		CategoryTag: casperPb.CategoryTag_CATEGORY_TAG_PLAY_AND_WIN,
	})

	// update exchanger offer status to APPROVED -> ACTIVE to be able to redeem
	updateStatusRes := cts.updateExchangerOfferStatus(t, &exchangerPb.UpdateExchangerOfferStatusRequest{
		OfferId:   offerId,
		NewStatus: exchangerPb.ExchangerOfferStatus_EXCHANGER_OFFER_STATUS_APPROVED,
	})
	assert.Equal(t, exchangerPb.ExchangerOfferStatus_EXCHANGER_OFFER_STATUS_APPROVED, updateStatusRes.GetExchangerOffer().GetStatus())

	updateStatusRes = cts.updateExchangerOfferStatus(t, &exchangerPb.UpdateExchangerOfferStatusRequest{
		OfferId:   offerId,
		NewStatus: exchangerPb.ExchangerOfferStatus_EXCHANGER_OFFER_STATUS_ACTIVE,
	})
	assert.Equal(t, exchangerPb.ExchangerOfferStatus_EXCHANGER_OFFER_STATUS_ACTIVE, updateStatusRes.GetExchangerOffer().GetStatus())

	// redeem exchanger offer
	redemptionRes := cts.redeemExchangerOffer(t, &feRewardsPb.RedeemExchangerOfferRequest{
		Req:              auth,
		ExchangerOfferId: offerId,
		RequestId:        uuid.New().String(),
	})
	assert.NotNil(t, redemptionRes.GetExchangerOfferOrder())
	assert.Equal(t, allowedAttempts-1, redemptionRes.GetAttemptsLeft())
	assert.Equal(t, 1, len(redemptionRes.GetExchangerOfferOrder().GetOptions()))

	// one of the reward option should get auto claimed after autoClaimTimeWithBuffer duration.
	autoClaimTimeWithBuffer := time.Duration(optionAutoClaimTimeInSecs)*time.Second + 10*time.Second
	time.Sleep(autoClaimTimeWithBuffer)

	// validate if option was auto claimed.
	exchangerOrders := cts.getBeExchangerOrdersForActor(t, &exchangerPb.GetExchangerOfferOrdersForActorRequest{
		ActorId:     cts.getActorIdUsingAuthHeader(t, auth),
		Filters:     &exchangerPb.GetExchangerOfferOrdersForActorRequest_Filters{OfferId: offerId},
		PageContext: &rpc.PageContextRequest{PageSize: 1},
	})
	assert.Equal(t, 1, len(exchangerOrders))
	assert.NotNil(t, exchangerOrders[0].GetChosenOption())
	assert.Equal(t, exchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_FULFILLED, exchangerOrders[0].GetState())
}

// nolint: funlen, dupl
func (cts *CasperTestSuite) TestExchangerOffer_HappyFlowForSubmittingUserInput(t *testing.T, auth *header.RequestHeader) {
	createInventoryRes := cts.createExchangerOfferInventory(t, &exchangerPb.CreateExchangerOfferInventoryRequest{
		RewardType:  exchangerPb.RewardType_REWARD_TYPE_PHYSICAL_MERCHANDISE,
		Description: "Physical Merch - 1",
		TotalCount:  100,
	})
	assert.NotNil(t, createInventoryRes.GetExchangerOfferInventory())
	assert.Equal(t, int32(100), createInventoryRes.GetExchangerOfferInventory().GetAvailableCount())

	exchangerOfferId := cts.CreateExchangerOffer(t, &exchangerPb.CreateExchangerOfferRequest{
		RedemptionCurrency: exchangerPb.ExchangerOfferRedemptionCurrency_EXCHANGER_OFFER_REDEMPTION_CURRENCY_FI_COINS,
		RedemptionPrice:    10000,
		OfferDisplayDetails: &exchangerPb.ExchangerOfferDisplayDetails{
			Title:       "Win Physical Merchandise",
			Subtitle:    "Trade your Fi-Coins for PM",
			Desc:        "Win exciting PM rewards",
			ImageUrl:    "image-url-1",
			TileBgColor: "#111111",
		},
		OfferOptionsConfig: &exchangerPb.ExchangerOfferOptionsConfig{
			DefaultDecideTimeInSecs: 100,
			OptionsConfig: []*exchangerPb.ExchangerOfferOptionConfig{
				{
					RewardConfigUnits: []*exchangerPb.ExchangerOfferOptionConfig_RewardConfigUnit{
						{
							RewardType:    exchangerPb.RewardType_REWARD_TYPE_PHYSICAL_MERCHANDISE,
							DisplayConfig: &exchangerPb.ExchangerOfferOptionConfig_Display{},
							Percentage:    100,
							RewardUnitsConfig: &exchangerPb.ExchangerOfferOptionConfig_RewardConfigUnit_RangeProbabilityConfig{
								RangeProbabilityConfig: &exchangerPb.RangeProbabilityUnitsConfig{
									ConfigUnits: []*exchangerPb.RangeProbabilityUnitsConfig_ConfigUnit{
										{
											Start:      1,
											End:        1,
											Percentage: 100,
										}}},
							},
							ExchangerOfferInventoryId: createInventoryRes.GetExchangerOfferInventory().GetId(),
						},
					},
				},
			},
		},
		OfferAggregatesConfig: &exchangerPb.ExchangerOfferAggregatesConfig{
			DailyAllowedAttemptsPerUser: 5,
		},
		CategoryTag: casperPb.CategoryTag_CATEGORY_TAG_SHOPPING,
	})
	assert.NotEmpty(t, exchangerOfferId)

	// update exchanger offer status to APPROVED -> ACTIVE to be able to redeem
	updateStatusRes := cts.updateExchangerOfferStatus(t, &exchangerPb.UpdateExchangerOfferStatusRequest{
		OfferId:   exchangerOfferId,
		NewStatus: exchangerPb.ExchangerOfferStatus_EXCHANGER_OFFER_STATUS_APPROVED,
	})
	assert.Equal(t, exchangerPb.ExchangerOfferStatus_EXCHANGER_OFFER_STATUS_APPROVED, updateStatusRes.GetExchangerOffer().GetStatus())

	updateStatusRes = cts.updateExchangerOfferStatus(t, &exchangerPb.UpdateExchangerOfferStatusRequest{
		OfferId:   exchangerOfferId,
		NewStatus: exchangerPb.ExchangerOfferStatus_EXCHANGER_OFFER_STATUS_ACTIVE,
	})
	assert.Equal(t, exchangerPb.ExchangerOfferStatus_EXCHANGER_OFFER_STATUS_ACTIVE, updateStatusRes.GetExchangerOffer().GetStatus())

	redeemOfferRes := cts.redeemExchangerOffer(t, &feRewardsPb.RedeemExchangerOfferRequest{
		ExchangerOfferId: exchangerOfferId,
		Req:              auth,
		RequestId:        uuid.New().String(),
	})
	assert.NotNil(t, redeemOfferRes.GetExchangerOfferOrder())
	assert.NotEmpty(t, redeemOfferRes.GetExchangerOfferOrder().GetOptions())

	chooseOptionRes := cts.chooseExchangerOrderOption(t, &feRewardsPb.ChooseExchangerOrderOptionRequest{
		Req:              auth,
		ExchangerOrderId: redeemOfferRes.GetExchangerOfferOrder().GetId(),
		OptionId:         redeemOfferRes.GetExchangerOfferOrder().GetOptions()[0].GetId(),
	})
	assert.NotNil(t, chooseOptionRes.GetExchangerOrder())
	assert.Equal(t, deeplinkPb.Screen_REWARD_SHIPPING_ADDRESS_INPUT_SCREEN, chooseOptionRes.GetNextScreen().GetScreen())

	// don't initiate submit address flow yet. we first check the order's state
	beExchangerOrdersRes := cts.getBeExchangerOrdersForActor(t, &exchangerPb.GetExchangerOfferOrdersForActorRequest{
		ActorId: cts.getActorIdUsingAuthHeader(t, auth),
		Filters: &exchangerPb.GetExchangerOfferOrdersForActorRequest_Filters{
			OfferId: exchangerOfferId,
		},
		PageContext: &rpc.PageContextRequest{
			PageSize: 1,
		},
	})
	assert.NotEmpty(t, beExchangerOrdersRes, "exchanger-order empty from BE")
	beExchangerOrder := beExchangerOrdersRes[0]
	// check BE exchanger-order's state
	assert.Equal(t, exchangerPb.ExchangerOfferOrderState_ORDER_STATE_USER_INPUT_NEEDED_FOR_FULFILLMENT, beExchangerOrder.GetState())

	// fetch FE exchanger-order to verify it's state
	redeemedOfferRes := cts.getRedeemedOffersV1(t, &feRewardsPb.GetRedeemedOffersV1Request{Req: auth})
	var feExchangerOrder *feRewardsPb.ExchangerOrder
	for _, redeemedOffer := range redeemedOfferRes.GetOrders() {
		switch redeemedOffer.GetOrderData().(type) {
		case *feRewardsPb.OrderV1_ExchangerOrder:
			if redeemedOffer.GetExchangerOrder().GetId() == beExchangerOrder.GetId() {
				feExchangerOrder = redeemedOffer.GetExchangerOrder()
				break
			}
		case *feRewardsPb.OrderV1_RedeemedOffer:
			// do nothing
		default:
			// do nothing
		}
	}
	assert.NotNil(t, feExchangerOrder, "FE exchanger-order not found")
	// check FE exchanger-order's state
	assert.Equal(t, feRewardsPb.ExchangerOrderStatus_USER_INTERVENTION_REQUIRED, feExchangerOrder.GetStatus())

	// submit address for the chosen option
	submitUserInputRes := cts.submitUserInputForExchangerOrder(t, &feRewardsPb.SubmitExchangerOrderUserInputRequest{
		ExchangerOrderId: feExchangerOrder.GetId(),
		Req:              auth,
		UserInput: &feRewardsPb.SubmitExchangerOrderUserInputRequest_UserInput{
			ShippingAddress: &types.PostalAddress{PostalCode: "random-postal-code"},
		},
	})
	assert.NotNil(t, submitUserInputRes.GetExchangerOrder())
	assert.Equal(t, deeplinkPb.Screen_REDEEMED_OFFERS_SCREEN, submitUserInputRes.GetCta().GetNextScreen().GetScreen())
	// order will be in in-progress or fulfilled state after submitting address
	assert.Contains(t, []feRewardsPb.ExchangerOrderStatus{feRewardsPb.ExchangerOrderStatus_IN_PROGRESS, feRewardsPb.ExchangerOrderStatus_FULFILLED}, submitUserInputRes.GetExchangerOrder().GetStatus())
}

func (cts *CasperTestSuite) CreateCmsProduct(t *testing.T) string {
	product, createProductErr := cts.cmsServiceClient.CreateProduct(context.Background(), &cmsPb.CreateProductRequest{
		BrandName: "Brand-1",
		// appending uuid to create a unique name every time
		Name:        fmt.Sprintf("Name-%s", uuid.NewString()),
		Description: "Description-1",
		ProductType: cmsPb.ProductType_PRODUCT_TYPE_EGV,
		Value:       100,
	})

	assert.Nil(t, createProductErr)
	assert.Equal(t, product.GetStatus().Code, rpc.StatusOk().Code)
	assert.NotEmpty(t, product.GetProduct().GetId())

	return product.GetProduct().GetId()
}

// nolint:dupl
func (cts *CasperTestSuite) CreateCmsSku(t *testing.T, productId string) string {
	sku, createSkuErr := cts.cmsServiceClient.CreateSku(context.Background(), &cmsPb.CreateSkuRequest{
		ProductId: productId,
		ValidFrom: timestampPb.Now(),
		ValidTill: timestampPb.New(time.Now().Add(time.Hour)),
		Tncs: &cmsPb.Tncs{
			Points: []string{
				"Condition-1",
				"Condition-2",
				"Condition-3",
				"Condition-4",
			},
		},
	})

	assert.Nil(t, createSkuErr)
	assert.Equal(t, sku.GetStatus().GetCode(), rpc.StatusOk().GetCode())
	assert.NotEmpty(t, sku.GetSku().GetId())

	return sku.GetSku().GetId()
}

func (cts *CasperTestSuite) CreateCmsCoupon(t *testing.T, skuId string) {
	coupons, createCouponsInBulkErr := cts.cmsServiceClient.CreateCouponsInBulk(context.Background(), &cmsPb.CreateCouponsInBulkRequest{
		CouponPayloads: []*cmsPb.CreateCouponsInBulkRequest_CouponPayload{
			{
				SkuId:          skuId,
				InventoryTotal: 100,
				CouponDetails: &cmsPb.CouponDetails{
					KeyValuePairs: []*cmsPb.CouponDetails_KeyValuePair{
						{
							Key:   "key-1",
							Value: "value-1",
						},
						{
							Key:   "key-2",
							Value: "value-2",
						},
					},
				},
			},
		},
	})

	assert.Nil(t, createCouponsInBulkErr)
	assert.Equal(t, coupons.GetStatus().GetCode(), rpc.StatusOk().GetCode())
	assert.NotEmpty(t, coupons.GetCoupons()[0].GetId())
}

func (cts *CasperTestSuite) AddFiCoinsToAccount(t *testing.T, auth *header.RequestHeader, amount int32) {
	actorId := cts.getActorIdUsingAuthHeader(t, auth)

	timeAfter1Hour, _ = ptypes.TimestampProto(time.Now().Add(60 * time.Minute))
	creditTxnRes, err := cts.beAccrualClient.Transact(context.Background(), &accrual.TransactRequest{
		RequestRefId:     uuid.New().String(),
		ActorId:          actorId,
		Amount:           amount,
		AmountExpiryTime: timeAfter1Hour,
		AccountType:      accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestampPb.Now()),
		TransactionType:  accrual.TransactionType_TRANSACTION_TYPE_CREDIT,
	})
	assert.Nil(t, err)
	assert.Equal(t, creditTxnRes.GetStatus().Code, rpc.StatusOk().Code)
	assert.Equal(t, accrual.TransactionStatus_TRANSACTION_STATUS_COMPLETED, creditTxnRes.GetTransactionStatus())
}

// nolint:dupl
func (cts *CasperTestSuite) getActorIdUsingAuthHeader(t *testing.T, auth *header.RequestHeader) string {
	validateAuthRes, err := cts.beAuthClient.ValidateToken(context.Background(), &authPb.ValidateTokenRequest{
		Token:     auth.Auth.GetAccessToken(),
		TokenType: authPb.TokenType_ACCESS_TOKEN,
	})
	assert.Nil(t, err)
	assert.Equal(t, rpc.StatusOk().Code, validateAuthRes.GetStatus().Code)
	assert.NotEmpty(t, validateAuthRes.GetActorId())
	return validateAuthRes.GetActorId()
}
