// nolint:ineffassign,gosec
package app

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"sync/atomic"
	"time"

	"github.com/stretchr/testify/require"
	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/anypb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"

	authPb "github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/card"
	"github.com/epifi/gamma/api/card/control"
	"github.com/epifi/gamma/api/frontend/account/signup"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	types "github.com/epifi/gamma/api/typesv2"
	afuSO "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/afu"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
)

const (
	AFUStatusPollCount       = 80
	ExpectDeviceUnregistered = false
	// phForSMSError            = **********
	withATMPIN    = "AFU_with_atm_pin"
	withoutATMPIN = ""
)

var (
	AddOAuthConfirmAFU = &LoginOpts{
		ExpectedRPCStatusCode: rpc.NewStatusWithoutDebug(uint32(301), "Confirm Auth Factor Update"),
	}
	suspectStateReRegRequestId  = "suspectStateReRegRequestId"
	testDeviceIdForWrongAtmPin  = "testDeviceIdForWrongAtmPin"
	testDeviceIdForInactiveCard = "testDeviceIdForInactiveCard"
	testDeviceIdForAutoRetries  = "testDeviceIdForAutoRetries"
)

type AFU_OnlyDevice_withLivenessOptions struct {
	ExistingUser bool
}

func AFU_OnlyEmail(ctx context.Context, dep *OnbDep, user1 *UserData) {
	// actions from start till AddOAuthAccount screen
	reqH, verifyRes := UserWithRefreshToken(ctx, dep, user1)
	addOAuthRes := AddOAuthAccount(ctx, dep, reqH, verifyRes.GetRefreshToken(), user1, AddOAuthConfirmAFU)
	reqH.Auth.AuthToken = &header.AuthHeader_RefreshToken{
		RefreshToken: verifyRes.GetRefreshToken(),
	}

	// Next Step: Confirm phone update start
	ScreenEquals(dep.Assert, deeplink.Screen_AFU_CONFIRM_START_POPUP, addOAuthRes.GetNextAction().GetScreen())
	addOAuthRes = AddOAuthAccountForAFU(ctx, dep, reqH, user1, false, true, user1.SimSubIds)
	reqH.Auth.AuthToken = &header.AuthHeader_AccessToken{
		AccessToken: addOAuthRes.GetAccessToken(),
	}

	// Next Step: Liveness
	livenessDl := addOAuthRes.GetNextAction()
	dep.Assert.Equal(deeplink.Screen_CHECK_LIVENESS.String(), livenessDl.GetScreen().String())
	var livReqId string
	opts := WaitForLivenessNextAction(ctx, dep, reqH, livenessDl)
	livReqId = opts.GetAttemptId()
	CheckLivenessWithNextAction(
		ctx, dep.Assert, dep.SignupClient, reqH, livReqId, user1.Video,
		false, deeplink.Screen_AFU_FINAL_CONFIRMATION, true,
	)
	confirmAFURes := ConfirmAFU(ctx, dep.Assert, dep.SignupClient, reqH, idgen.RandAlphaNumericString(10), withoutATMPIN)

	dep.Assert.Equal(deeplink.Screen_AFU_CHECK_UPDATE_STATUS.String(), confirmAFURes.GetNextAction().GetScreen().String())
	afuId := GetAfuIdFromCheckAFUStatusDl(dep, confirmAFURes.GetNextAction())
	nextOnbAction, _, err := PollAFUStatus(ctx, dep.SignupClient, dep.Assert, reqH, afuId)
	if err != nil {
		nextOnbAction = GetTokenAndPollStatus(ctx, dep, user1, reqH, afuId)
	}

	// AFU Completed.
	// Next action is the pending onboarding next action.
	dep.Assert.Equal(ActionAfterCreateSavingsAccount.String(), nextOnbAction.GetScreen().String())
}

func AFU_OnlyDevice_withATMPIN(ctx context.Context, dep *OnbDep, aUser *UserData) *deeplink.Deeplink {
	assert := dep.Assert

	// initial onboarding
	reqH1 := CreateSavingsAccountForUser(ctx, dep, aUser)
	// user comes again on a new device id
	oldDeviceId := aUser.Device.DeviceId
	aUser.Device.DeviceId = idgen.RandAlphaNumericString(10)
	dep.Assert.NotEqual(oldDeviceId, aUser.Device.DeviceId)

	// actions from start till AddOAuthAccount screen
	reqH2, addOAuthRes := UserWithAccessToken(ctx, dep, aUser)

	// Next Step: choosing ATM PIN verification from options
	assert.Equal(deeplink.Screen_INFO_ACKNOWLEDGEMENT_SCREEN.String(), addOAuthRes.GetNextAction().GetScreen().String())
	// validating if two options are available to authenticate user
	if infoOpts, ok := addOAuthRes.GetNextAction().GetScreenOptions().(*deeplink.Deeplink_InfoAcknowledgementScreenOptions); ok {
		ctas := infoOpts.InfoAcknowledgementScreenOptions.GetCtas()
		assert.Equal(2, len(ctas))
	} else {
		dep.Assert.Failf(fmt.Sprintf("invalid next action in addoauth; expected:INFO_ACKNOWLEDGEMENT, got: %s", addOAuthRes.GetNextAction().GetScreen().String()), "error msg")
	}
	// Next Step: Confirm update with ATM PIN
	confirmAFURes := ConfirmAFU(ctx, assert, dep.SignupClient, reqH2, idgen.RandAlphaNumericString(10), withATMPIN)

	// Next Step: Register device
	assert.Equal(deeplink.Screen_REGISTER_DEVICE.String(), confirmAFURes.GetNextAction().GetScreen().String())
	regDeviceRes := RegisterDevice(ctx, assert, dep.SignupClient, reqH2, ExpectDeviceUnregistered, aUser.SimSubIds[0], dep, aUser)

	assert.Equal(deeplink.Screen_AFU_CHECK_UPDATE_STATUS.String(), regDeviceRes.GetNextAction().GetScreen().String())
	afuId := GetAfuIdFromCheckAFUStatusDl(dep, regDeviceRes.GetNextAction())
	nextOnbAction, _, err := PollAFUStatus(ctx, dep.SignupClient, assert, reqH2, afuId)
	if err != nil {
		nextOnbAction = GetTokenAndPollStatus(ctx, dep, aUser, reqH2, afuId)
	}

	// make sure old auth header is expired
	AssertAuthTokenExpired(ctx, assert, dep.SignupClient, reqH1)
	return nextOnbAction
}

func GetAfuIdFromCheckAFUStatusDl(deps *OnbDep, checkUpdateStatus *deeplink.Deeplink) string {
	deps.Assert.Equal(deeplink.Screen_AFU_CHECK_UPDATE_STATUS.String(), checkUpdateStatus.GetScreen().String())
	screenOptions := &afuSO.AFUCheckUpdateStatus{}
	err := anypb.UnmarshalTo(checkUpdateStatus.GetScreenOptionsV2(), screenOptions, proto.UnmarshalOptions{})
	deps.Assert.NoError(err)
	deps.Assert.NotEmpty(screenOptions.GetAfuId(), "empty afuId")
	return screenOptions.GetAfuId()
}

func AFU_OnlyDevice_withLiveness(ctx context.Context, dep *OnbDep, aUser *UserData, reqOpts ...*AFU_OnlyDevice_withLivenessOptions) *deeplink.Deeplink {
	assert := dep.Assert

	var reqH1 *header.RequestHeader
	if reqOpts != nil {
		if reqOpts[0].ExistingUser {
			reqH1, _ = UserWithAccessToken(ctx, dep, aUser)
		}
	} else {
		// initial onboarding
		reqH1 = CreateSavingsAccountForUser(ctx, dep, aUser)
	}

	// user comes again on a new device id
	oldDeviceId := aUser.Device.DeviceId
	aUser.Device.DeviceId = idgen.RandAlphaNumericString(10)
	dep.Assert.NotEqual(oldDeviceId, aUser.Device.DeviceId)

	// actions from start till AddOAuthAccount screen
	reqH2, addOAuthRes := UserWithAccessToken(ctx, dep, aUser)

	livenessDL := addOAuthRes.GetNextAction()
	assert.Equal(deeplink.Screen_CHECK_LIVENESS.String(), livenessDL.GetScreen().String())

	// Next Step: Doing liveness
	var livReqId string
	opts := WaitForLivenessNextAction(ctx, dep, reqH2, livenessDL)
	livReqId = opts.GetAttemptId()
	CheckLivenessWithNextAction(
		ctx, dep.Assert, dep.SignupClient, reqH2, livReqId, aUser.Video,
		false, deeplink.Screen_AFU_FINAL_CONFIRMATION, true,
	)

	// Next Step: Confirm update
	confirmAFURes := ConfirmAFU(ctx, assert, dep.SignupClient, reqH2, idgen.RandAlphaNumericString(10), withoutATMPIN)

	// Next Step: Register device
	assert.Equal(deeplink.Screen_REGISTER_DEVICE.String(), confirmAFURes.GetNextAction().GetScreen().String())
	regDeviceRes := RegisterDevice(ctx, assert, dep.SignupClient, reqH2, ExpectDeviceUnregistered, aUser.SimSubIds[0], dep, aUser)

	assert.Equal(deeplink.Screen_AFU_CHECK_UPDATE_STATUS.String(), regDeviceRes.GetNextAction().GetScreen().String())
	afuId := GetAfuIdFromCheckAFUStatusDl(dep, regDeviceRes.GetNextAction())
	nextOnbAction, _, err := PollAFUStatus(ctx, dep.SignupClient, assert, reqH2, afuId)
	if err != nil {
		nextOnbAction = GetTokenAndPollStatus(ctx, dep, aUser, reqH2, afuId)
	}

	// make sure old auth header is expired
	AssertAuthTokenExpired(ctx, assert, dep.SignupClient, reqH1)
	return nextOnbAction
}

func AFU_OnlyDevice_withLivenessAndAutoRetries(ctx context.Context, dep *OnbDep, aUser *UserData) *deeplink.Deeplink {
	assert := dep.Assert

	// initial onboarding
	reqH1 := CreateSavingsAccountForUser(ctx, dep, aUser)

	// user comes again on a new device id
	oldDeviceId := aUser.Device.DeviceId
	aUser.Device.DeviceId = testDeviceIdForAutoRetries
	dep.Assert.NotEqual(oldDeviceId, aUser.Device.DeviceId)

	// actions from start till AddOAuthAccount screen
	reqH2, addOAuthRes := UserWithAccessToken(ctx, dep, aUser)

	// Next Step: getting two verification options to authenticate actor
	assert.Equal(deeplink.Screen_CHECK_LIVENESS.String(), addOAuthRes.GetNextAction().GetScreen().String())
	// Next Step: Doing liveness
	var livReqId string
	livenessDL := addOAuthRes.GetNextAction()
	opts := WaitForLivenessNextAction(ctx, dep, reqH2, livenessDL)
	livReqId = opts.GetAttemptId()
	CheckLivenessWithNextAction(
		ctx, dep.Assert, dep.SignupClient, reqH2, livReqId, aUser.Video,
		false, deeplink.Screen_AFU_FINAL_CONFIRMATION, true,
	)

	// Next Step: Confirm update
	confirmAFURes := ConfirmAFU(ctx, assert, dep.SignupClient, reqH2, idgen.RandAlphaNumericString(10), withoutATMPIN)

	// Next Step: Register device
	assert.Equal(deeplink.Screen_REGISTER_DEVICE.String(), confirmAFURes.GetNextAction().GetScreen().String())
	regDeviceRes := RegisterDevice(ctx, assert, dep.SignupClient, reqH2, ExpectDeviceUnregistered, aUser.SimSubIds[0], dep, aUser)

	assert.Equal(deeplink.Screen_AFU_CHECK_UPDATE_STATUS.String(), regDeviceRes.GetNextAction().GetScreen().String())
	afuId := GetAfuIdFromCheckAFUStatusDl(dep, regDeviceRes.GetNextAction())
	nextOnbAction, _, err := PollAFUStatus(ctx, dep.SignupClient, assert, reqH2, afuId)
	if err != nil {
		nextOnbAction = GetTokenAndPollStatus(ctx, dep, aUser, reqH2, afuId)
	}

	// make sure old auth header is expired
	AssertAuthTokenExpired(ctx, assert, dep.SignupClient, reqH1)
	return nextOnbAction
}

func AFU_OnlyPhoneNumber(ctx context.Context, dep *OnbDep, aUser, anotherUser *UserData) *deeplink.Deeplink {
	CreateSavingsAccountForUser(ctx, dep, aUser)
	actor, _ := GetUserDetails(ctx, dep, aUser.Phone)

	// aUser wants to update the phone number on old device
	oldPhoneNumber := aUser.Phone
	aUser.Phone = anotherUser.Phone // new phone number
	dep.Assert.NotEqual(oldPhoneNumber.NationalNumber, aUser.Phone.NationalNumber)

	// user tries to update the phone number
	// actions from start till AddOAuthAccount screen
	reqH, verifyRes := UserWithRefreshToken(ctx, dep, aUser)
	addOAuthRes := AddOAuthAccount(ctx, dep, reqH, verifyRes.RefreshToken, aUser, AddOAuthConfirmAFU)
	reqH.Auth.AuthToken = &header.AuthHeader_RefreshToken{
		RefreshToken: verifyRes.RefreshToken,
	}

	// Next Step: Confirm phone update start
	ScreenEquals(dep.Assert, deeplink.Screen_AFU_CONFIRM_START_POPUP, addOAuthRes.GetNextAction().GetScreen())
	addOAuthRes = AddOAuthAccountForAFU(ctx, dep, reqH, aUser, true, false, aUser.SimSubIds)
	reqH.Auth.AuthToken = &header.AuthHeader_AccessToken{
		AccessToken: addOAuthRes.GetAccessToken(),
	}

	ScreenEquals(dep.Assert, deeplink.Screen_START_EKYC, addOAuthRes.GetNextAction().GetScreen())
	source := addOAuthRes.GetNextAction().GetStartEkycOptions().GetEkycSource()
	InitiateEKYC(ctx, dep, reqH, source)
	_ = UploadEKYCRecord(ctx, dep, reqH, []byte{1, 1, 1}, aUser)

	afuRes, err := dep.AuthClient.GetAuthFactorUpdatesForActor(ctx, &authPb.GetAuthFactorUpdatesForActorRequest{
		ActorId: actor.GetId(),
	})
	if rpcErr := epifigrpc.RPCError(afuRes, err); rpcErr != nil {
		logger.Error(ctx, "error in getting afu res", zap.Error(rpcErr))
		dep.Assert.Fail(fmt.Sprintf("error in getting afu res %v", rpcErr))
	}
	dep.Assert.NotEmpty(afuRes.GetAuthFactorUpdates())
	afuId := afuRes.GetAuthFactorUpdates()[0].GetId()
	nextAfuAction, _, err := PollAFUStatus(ctx, dep.SignupClient, dep.Assert, reqH, afuId)

	// Next step: Liveness/FM
	dep.Assert.Equal(deeplink.Screen_CHECK_LIVENESS.String(), nextAfuAction.GetScreen().String(), nextAfuAction)
	var livReqId string
	if opts, ok := nextAfuAction.GetScreenOptions().(*deeplink.Deeplink_CheckLivenessScreenOptions); ok {
		livReqId = opts.CheckLivenessScreenOptions.GetAttemptId()
	} else {
		dep.Assert.Failf("invalid next action in addoauth; expected:LIVENESS, got: %v",
			nextAfuAction.GetScreen().String(),
		)
	}
	CheckLivenessWithNextAction(
		ctx, dep.Assert, dep.SignupClient, reqH, livReqId, aUser.Video,
		false, deeplink.Screen_AFU_ATM_PIN_VALIDATION,
	)

	// Next Step: Confirm update
	confirmAFURes := ConfirmAFU(ctx, dep.Assert, dep.SignupClient, reqH, idgen.RandAlphaNumericString(10), withATMPIN)

	// Next Step: Register device
	dep.Assert.Equal(deeplink.Screen_REGISTER_DEVICE, confirmAFURes.GetNextAction().GetScreen())
	regDeviceRes := RegisterDevice(ctx, dep.Assert, dep.SignupClient, reqH, ExpectDeviceUnregistered, aUser.SimSubIds[0], dep, aUser)

	dep.Assert.Equal(deeplink.Screen_AFU_CHECK_UPDATE_STATUS.String(), regDeviceRes.GetNextAction().GetScreen().String())
	afuId = GetAfuIdFromCheckAFUStatusDl(dep, regDeviceRes.GetNextAction())
	nextAfuAction, _, err = PollAFUStatus(ctx, dep.SignupClient, dep.Assert, reqH, afuId)
	if err != nil {
		nextAfuAction = GetTokenAndPollStatus(ctx, dep, aUser, reqH, afuId)
	}

	return nextAfuAction
}

func AFU_OnlyPhoneNumber_NoAtmPinRequired(ctx context.Context, dep *OnbDep, aUser, anotherUser *UserData) *deeplink.Deeplink {
	CreateSavingsAccountForUser(ctx, dep, aUser)
	actor, _ := GetUserDetails(ctx, dep, aUser.Phone)

	logger.Info(ctx, "mark card status as blocked to skip ATM pin check in AFU")
	blockCard(ctx, dep, actor.GetId())
	// aUser wants to update the phone number on old device
	oldPhoneNumber := aUser.Phone
	aUser.Phone = anotherUser.Phone // new phone number
	dep.Assert.NotEqual(oldPhoneNumber.GetNationalNumber(), aUser.Phone.GetNationalNumber())

	// user tries to update the phone number
	// actions from start till AddOAuthAccount screen
	reqH, verifyRes := UserWithRefreshToken(ctx, dep, aUser)
	addOAuthRes := AddOAuthAccount(ctx, dep, reqH, verifyRes.GetRefreshToken(), aUser, AddOAuthConfirmAFU)
	reqH.Auth.AuthToken = &header.AuthHeader_RefreshToken{
		RefreshToken: verifyRes.GetRefreshToken(),
	}

	// Next Step: Confirm phone update start
	ScreenEquals(dep.Assert, deeplink.Screen_AFU_CONFIRM_START_POPUP, addOAuthRes.GetNextAction().GetScreen())
	addOAuthRes = AddOAuthAccountForAFU(ctx, dep, reqH, aUser, true, false, aUser.SimSubIds)
	reqH.Auth.AuthToken = &header.AuthHeader_AccessToken{
		AccessToken: addOAuthRes.GetAccessToken(),
	}

	ScreenEquals(dep.Assert, deeplink.Screen_START_EKYC, addOAuthRes.GetNextAction().GetScreen())
	source := addOAuthRes.GetNextAction().GetStartEkycOptions().GetEkycSource()
	InitiateEKYC(ctx, dep, reqH, source)
	uploadEKYCRecordRes := UploadEKYCRecord(ctx, dep, reqH, []byte{1, 1, 1}, aUser)
	nextAfuAction := uploadEKYCRecordRes.GetNextAction()
	// Next step: Liveness/FM
	dep.Assert.Equal(deeplink.Screen_CHECK_LIVENESS.String(), nextAfuAction.GetScreen().String(), nextAfuAction)
	var livReqId string
	if opts, ok := nextAfuAction.GetScreenOptions().(*deeplink.Deeplink_CheckLivenessScreenOptions); ok {
		livReqId = opts.CheckLivenessScreenOptions.GetAttemptId()
	} else {
		dep.Assert.Failf("invalid next action in addoauth; expected:LIVENESS, got: %v",
			nextAfuAction.GetScreen().String(),
		)
	}
	CheckLivenessWithNextAction(
		ctx, dep.Assert, dep.SignupClient, reqH, livReqId, aUser.Video,
		false, deeplink.Screen_AFU_FINAL_CONFIRMATION,
	)

	// Next Step: Confirm update
	confirmAFURes := ConfirmAFU(ctx, dep.Assert, dep.SignupClient, reqH, idgen.RandAlphaNumericString(10), withoutATMPIN)

	// Next Step: Register device
	dep.Assert.Equal(deeplink.Screen_REGISTER_DEVICE, confirmAFURes.GetNextAction().GetScreen())
	regDeviceRes := RegisterDevice(ctx, dep.Assert, dep.SignupClient, reqH, ExpectDeviceUnregistered, aUser.SimSubIds[0], dep, aUser)

	dep.Assert.Equal(deeplink.Screen_AFU_CHECK_UPDATE_STATUS.String(), regDeviceRes.GetNextAction().GetScreen().String())
	afuId := GetAfuIdFromCheckAFUStatusDl(dep, regDeviceRes.GetNextAction())
	nextAfuAction, _, err := PollAFUStatus(ctx, dep.SignupClient, dep.Assert, reqH, afuId)
	if err != nil {
		nextAfuAction = GetTokenAndPollStatus(ctx, dep, aUser, reqH, afuId)
	}

	return nextAfuAction
}

func blockCard(ctx context.Context, dep *OnbDep, actorId string) {
	onbDetailsRes, err := dep.OnbClient.GetDetails(ctx, &onbPb.GetDetailsRequest{
		ActorId: actorId,
	})
	dep.Assert.NoError(epifigrpc.RPCError(onbDetailsRes, err), "error in getting onb details")
	cardIds := make([]string, 0)
	for _, cardInfo := range onbDetailsRes.GetDetails().GetCardInfo().GetCardDetails() {
		cardIds = append(cardIds, cardInfo.GetCardId())
	}
	logger.Info(ctx, fmt.Sprintf("user cardIds %v", cardIds))
	blockCardRes, err := dep.CardControlClient.BlockCard(ctx, &control.BlockCardRequest{
		CardIds:             cardIds,
		Vendor:              commonvgpb.Vendor_FEDERAL_BANK,
		BlockCardProvenance: card.Provenance_BANK,
		BlockCardReason:     "AFU testing",
		SkipVendorCall:      true,
	})
	if rpcErr := epifigrpc.RPCError(blockCardRes, err); rpcErr != nil {
		logger.Error(ctx, "error in blocking cards ", zap.Error(rpcErr))
		dep.Assert.Fail(fmt.Sprintf("error in getting afu res %v", rpcErr))
	}
}

func AFU_OnlyPhone_AadhaarMobileMismatch(ctx context.Context, dep *OnbDep, aUser, anotherUser *UserData) {
	CreateSavingsAccountForUser(ctx, dep, aUser)

	// aUser wants to update the phone number on old device
	oldPhoneNumber := aUser.Phone
	aUser.Phone = anotherUser.Phone // new phone number
	dep.Assert.NotEqual(oldPhoneNumber.GetNationalNumber(), aUser.Phone.GetNationalNumber())

	// user tries to update the phone number
	// actions from start till AddOAuthAccount screen
	reqH, verifyRes := UserWithRefreshToken(ctx, dep, aUser)
	addOAuthRes := AddOAuthAccount(ctx, dep, reqH, verifyRes.GetRefreshToken(), aUser, AddOAuthConfirmAFU)
	reqH.Auth.AuthToken = &header.AuthHeader_RefreshToken{
		RefreshToken: verifyRes.GetRefreshToken(),
	}

	// Next Step: Confirm phone update start
	ScreenEquals(dep.Assert, deeplink.Screen_AFU_CONFIRM_START_POPUP, addOAuthRes.GetNextAction().GetScreen())
	addOAuthRes = AddOAuthAccountForAFU(ctx, dep, reqH, aUser, true, false, aUser.SimSubIds)
	reqH.Auth.AuthToken = &header.AuthHeader_AccessToken{
		AccessToken: addOAuthRes.GetAccessToken(),
	}

	ScreenEquals(dep.Assert, deeplink.Screen_START_EKYC, addOAuthRes.GetNextAction().GetScreen())
	source := addOAuthRes.GetNextAction().GetStartEkycOptions().GetEkycSource()
	InitiateEKYC(ctx, dep, reqH, source)
	uploadEkycRes := UploadEKYCRecord(ctx, dep, reqH, []byte{1, 1, 1}, aUser)
	dep.Assert.Equal(deeplink.Screen_DETAILED_ERROR_VIEW_SCREEN, uploadEkycRes.GetNextAction().GetScreen())
}

// AFU_OnlyDevice asserts steps as in prod config
func AFU_OnlyDevice(ctx context.Context, dep *OnbDep, aUser *UserData) *deeplink.Deeplink {
	assert := dep.Assert
	// initial onboarding
	reqH1 := CreateSavingsAccountForUser(ctx, dep, aUser)
	// user comes again on a new device id
	oldDeviceId := aUser.Device.DeviceId
	aUser.Device.DeviceId = idgen.RandAlphaNumericString(10)
	dep.Assert.NotEqual(oldDeviceId, aUser.Device.DeviceId)
	// actions from start till AddOAuthAccount screen
	reqH2, addOAuthRes := UserWithAccessToken(ctx, dep, aUser)
	// Next step: Liveness/FM
	dep.Assert.Equal(deeplink.Screen_CHECK_LIVENESS.String(), addOAuthRes.GetNextAction().GetScreen().String(), addOAuthRes.GetNextAction())
	var livReqId string
	if opts, ok := addOAuthRes.NextAction.ScreenOptions.(*deeplink.Deeplink_CheckLivenessScreenOptions); ok {
		livReqId = opts.CheckLivenessScreenOptions.AttemptId
	} else {
		dep.Assert.Failf("invalid next action in addoauth; expected:LIVENESS, got: %v",
			addOAuthRes.NextAction.GetScreen().String(),
		)
	}
	CheckLivenessWithNextAction(
		ctx, dep.Assert, dep.SignupClient, reqH2, livReqId, aUser.Video,
		false, deeplink.Screen_AFU_FINAL_CONFIRMATION,
	)
	// Next Step: Confirm update
	confirmAFURes := ConfirmAFU(ctx, assert, dep.SignupClient, reqH2, idgen.RandAlphaNumericString(10), withoutATMPIN)
	// Next Step: Register device
	assert.Equal(deeplink.Screen_REGISTER_DEVICE.String(), confirmAFURes.GetNextAction().GetScreen().String())
	regDeviceRes := RegisterDevice(ctx, assert, dep.SignupClient, reqH2, ExpectDeviceUnregistered, aUser.SimSubIds[0], dep, aUser)
	assert.Equal(deeplink.Screen_AFU_CHECK_UPDATE_STATUS.String(), regDeviceRes.GetNextAction().GetScreen().String())
	afuId := GetAfuIdFromCheckAFUStatusDl(dep, regDeviceRes.GetNextAction())
	nextOnbAction, _, err := PollAFUStatus(ctx, dep.SignupClient, assert, reqH2, afuId)
	if err != nil {
		nextOnbAction = GetTokenAndPollStatus(ctx, dep, aUser, reqH2, afuId)
	}
	// make sure old auth header is expired
	AssertAuthTokenExpired(ctx, assert, dep.SignupClient, reqH1)
	return nextOnbAction
}

func AFU_EmailDevice(ctx context.Context, dep *OnbDep, aUser *UserData) *deeplink.Deeplink {
	// initial onboarding
	reqH := CreateSavingsAccountForUser(ctx, dep, aUser)
	// user comes again on a new device id
	newUser := CreateUserParams()
	oldDeviceId := newUser.Device.DeviceId
	aUser.Device.DeviceId = idgen.RandAlphaNumericString(10)
	dep.Assert.NotEqual(oldDeviceId, aUser.Device.DeviceId)

	// use new email
	dep.Assert.NotEqual(aUser.OAuthIDToken, newUser.OAuthIDToken)
	aUser.OAuthIDToken = newUser.OAuthIDToken

	// user tries to update the email
	// actions from start till AddOAuthAccount screen
	reqH, verifyRes := UserWithRefreshToken(ctx, dep, aUser)
	addOAuthRes := AddOAuthAccount(ctx, dep, reqH, verifyRes.RefreshToken, aUser, AddOAuthConfirmAFU)
	reqH.Auth.AuthToken = &header.AuthHeader_RefreshToken{
		RefreshToken: verifyRes.RefreshToken,
	}

	// Next Step: Confirm email update start
	ScreenEquals(dep.Assert, deeplink.Screen_AFU_CONFIRM_START_POPUP, addOAuthRes.GetNextAction().GetScreen())
	addOAuthRes = AddOAuthAccountForAFU(ctx, dep, reqH, aUser, false, true, aUser.SimSubIds)
	reqH.Auth.AuthToken = &header.AuthHeader_AccessToken{
		AccessToken: addOAuthRes.GetAccessToken(),
	}

	// Next step: Liveness/FM
	dep.Assert.Equal(deeplink.Screen_CHECK_LIVENESS.String(), addOAuthRes.GetNextAction().GetScreen().String(), addOAuthRes.GetNextAction())
	var livReqId string
	if opts, ok := addOAuthRes.NextAction.ScreenOptions.(*deeplink.Deeplink_CheckLivenessScreenOptions); ok {
		livReqId = opts.CheckLivenessScreenOptions.AttemptId
	} else {
		dep.Assert.Failf("invalid next action in addoauth; expected:LIVENESS, got: %v",
			addOAuthRes.NextAction.GetScreen().String(),
		)
	}
	CheckLivenessWithNextAction(
		ctx, dep.Assert, dep.SignupClient, reqH, livReqId, aUser.Video,
		false, deeplink.Screen_AFU_ATM_PIN_VALIDATION,
	)

	// Next Step: Confirm update
	confirmAFURes := ConfirmAFU(ctx, dep.Assert, dep.SignupClient, reqH, idgen.RandAlphaNumericString(10), withATMPIN)

	// Next Step: Register device
	dep.Assert.Equal(deeplink.Screen_REGISTER_DEVICE, confirmAFURes.GetNextAction().GetScreen())
	regDeviceRes := RegisterDevice(ctx, dep.Assert, dep.SignupClient, reqH, ExpectDeviceUnregistered, aUser.SimSubIds[0], dep, aUser)

	dep.Assert.Equal(deeplink.Screen_AFU_CHECK_UPDATE_STATUS.String(), regDeviceRes.GetNextAction().GetScreen().String())
	afuId := GetAfuIdFromCheckAFUStatusDl(dep, regDeviceRes.GetNextAction())
	nextOnbAction, _, err := PollAFUStatus(ctx, dep.SignupClient, dep.Assert, reqH, afuId)
	if err != nil {
		nextOnbAction = GetTokenAndPollStatus(ctx, dep, aUser, reqH, afuId)
	}

	return nextOnbAction
}

// nolint: funlen
func AFU_PhoneDevice(ctx context.Context, dep *OnbDep, aUser *UserData) *deeplink.Deeplink {
	// user comes again on a new device id
	newUser := CreateUserParams()

	actor, _ := GetUserDetails(ctx, dep, aUser.Phone)
	aUser.Phone = newUser.Phone
	oldDeviceId := newUser.Device.GetDeviceId()
	aUser.Device.DeviceId = idgen.RandAlphaNumericString(10)
	dep.Assert.NotEqual(oldDeviceId, aUser.Device.GetDeviceId())

	// user tries to update phone
	// actions from start till AddOAuthAccount screen
	reqH, verifyRes := UserWithRefreshToken(ctx, dep, aUser)
	addOAuthRes := AddOAuthAccount(ctx, dep, reqH, verifyRes.GetRefreshToken(), aUser, AddOAuthConfirmAFU)
	reqH.Auth.AuthToken = &header.AuthHeader_RefreshToken{
		RefreshToken: verifyRes.GetRefreshToken(),
	}

	// Next Step: Confirm email update start
	ScreenEquals(dep.Assert, deeplink.Screen_AFU_CONFIRM_START_POPUP, addOAuthRes.GetNextAction().GetScreen())
	addOAuthRes = AddOAuthAccountForAFU(ctx, dep, reqH, aUser, true, false, aUser.SimSubIds)
	reqH.Auth.AuthToken = &header.AuthHeader_AccessToken{
		AccessToken: addOAuthRes.GetAccessToken(),
	}

	ScreenEquals(dep.Assert, deeplink.Screen_START_EKYC, addOAuthRes.GetNextAction().GetScreen())
	source := addOAuthRes.GetNextAction().GetStartEkycOptions().GetEkycSource()
	InitiateEKYC(ctx, dep, reqH, source)
	_ = UploadEKYCRecord(ctx, dep, reqH, []byte{1, 1, 1}, aUser)

	afuRes, err := dep.AuthClient.GetAuthFactorUpdatesForActor(ctx, &authPb.GetAuthFactorUpdatesForActorRequest{
		ActorId: actor.GetId(),
	})
	if rpcErr := epifigrpc.RPCError(afuRes, err); rpcErr != nil {
		logger.Error(ctx, "error in getting afu res", zap.Error(rpcErr))
		dep.Assert.Fail(fmt.Sprintf("error in getting afu res %v", rpcErr))
	}
	dep.Assert.NotEmpty(afuRes.GetAuthFactorUpdates())
	afuId := afuRes.GetAuthFactorUpdates()[0].GetId()
	nextAfuAction, _, err := PollAFUStatus(ctx, dep.SignupClient, dep.Assert, reqH, afuId)

	// Next step: Liveness/FM
	dep.Assert.Equal(deeplink.Screen_CHECK_LIVENESS.String(), nextAfuAction.GetScreen().String(), nextAfuAction)
	var livReqId string
	if opts, ok := nextAfuAction.GetScreenOptions().(*deeplink.Deeplink_CheckLivenessScreenOptions); ok {
		livReqId = opts.CheckLivenessScreenOptions.GetAttemptId()
	} else {
		dep.Assert.Failf("invalid next action in addoauth; expected:LIVENESS, got: %v",
			nextAfuAction.GetScreen().String(),
		)
	}
	CheckLivenessWithNextAction(
		ctx, dep.Assert, dep.SignupClient, reqH, livReqId, aUser.Video,
		false, deeplink.Screen_AFU_ATM_PIN_VALIDATION,
	)

	// Next Step: Confirm update
	confirmAFURes := ConfirmAFU(ctx, dep.Assert, dep.SignupClient, reqH, idgen.RandAlphaNumericString(10), withATMPIN)

	// Next Step: Register device
	dep.Assert.Equal(deeplink.Screen_REGISTER_DEVICE, confirmAFURes.GetNextAction().GetScreen())
	regDeviceRes := RegisterDevice(ctx, dep.Assert, dep.SignupClient, reqH, ExpectDeviceUnregistered, aUser.SimSubIds[0], dep, aUser)

	dep.Assert.Equal(deeplink.Screen_AFU_CHECK_UPDATE_STATUS.String(), regDeviceRes.GetNextAction().GetScreen().String())
	afuId = GetAfuIdFromCheckAFUStatusDl(dep, regDeviceRes.GetNextAction())
	nextAfuAction, _, err = PollAFUStatus(ctx, dep.SignupClient, dep.Assert, reqH, afuId)
	if err != nil {
		nextAfuAction = GetTokenAndPollStatus(ctx, dep, aUser, reqH, afuId)
	}

	return nextAfuAction
}

func AFU_ManualReview(ctx context.Context, dep *OnbDep, aUser *UserData) *deeplink.Deeplink {
	// initial onboarding
	_ = CreateSavingsAccountForUser(ctx, dep, aUser)
	// user comes again on a new device id
	newUser := CreateUserParams()
	oldDeviceId := newUser.Device.DeviceId
	aUser.Device.DeviceId = idgen.RandAlphaNumericString(10)
	dep.Assert.NotEqual(oldDeviceId, aUser.Device.DeviceId)

	// use new email
	dep.Assert.NotEqual(aUser.OAuthIDToken, newUser.OAuthIDToken)
	aUser.OAuthIDToken = newUser.OAuthIDToken

	// user tries to update the email
	// actions from start till AddOAuthAccount screen
	reqH, verifyRes := UserWithRefreshToken(ctx, dep, aUser)
	addOAuthRes := AddOAuthAccount(ctx, dep, reqH, verifyRes.RefreshToken, aUser, AddOAuthConfirmAFU)
	reqH.Auth.AuthToken = &header.AuthHeader_RefreshToken{
		RefreshToken: verifyRes.RefreshToken,
	}

	// Next Step: Confirm email update start
	ScreenEquals(dep.Assert, deeplink.Screen_AFU_CONFIRM_START_POPUP, addOAuthRes.GetNextAction().GetScreen())
	addOAuthRes = AddOAuthAccountForAFU(ctx, dep, reqH, aUser, false, true, aUser.SimSubIds)
	reqH.Auth.AuthToken = &header.AuthHeader_AccessToken{
		AccessToken: addOAuthRes.GetAccessToken(),
	}

	// Next step: Liveness/FM
	dep.Assert.Equal(deeplink.Screen_CHECK_LIVENESS.String(), addOAuthRes.GetNextAction().GetScreen().String(), addOAuthRes.GetNextAction())
	var livReqId string
	if opts, ok := addOAuthRes.NextAction.ScreenOptions.(*deeplink.Deeplink_CheckLivenessScreenOptions); ok {
		livReqId = opts.CheckLivenessScreenOptions.AttemptId
	} else {
		dep.Assert.Failf("invalid next action in addoauth; expected:LIVENESS, got: %v",
			addOAuthRes.NextAction.GetScreen().String(),
		)
	}
	CheckLivenessVideo(ctx, dep.Assert, dep.SignupClient, reqH, aUser.Video, livReqId)
	// Next step: Mark risk review as passed
	markAfuRiskVerdict(ctx, reqH, dep, aUser, authPb.ProcessAFURiskVerdictRequest_VERDICT_PASS)
	// todo: call addOAuth to get AFU next action
	nextAfuAction, _, err := PollAFUStatus(ctx, dep.SignupClient, dep.Assert, reqH, "")
	dep.Assert.Nil(err)
	dep.Assert.Equal(deeplink.Screen_AFU_ATM_PIN_VALIDATION.String(), nextAfuAction.GetScreen().String())

	// Next Step: Confirm update
	confirmAFURes := ConfirmAFU(ctx, dep.Assert, dep.SignupClient, reqH, idgen.RandAlphaNumericString(10), withATMPIN)

	// Next Step: Register device
	dep.Assert.Equal(deeplink.Screen_REGISTER_DEVICE, confirmAFURes.GetNextAction().GetScreen())
	regDeviceRes := RegisterDevice(ctx, dep.Assert, dep.SignupClient, reqH, ExpectDeviceUnregistered, aUser.SimSubIds[0], dep, aUser)

	dep.Assert.Equal(deeplink.Screen_AFU_CHECK_UPDATE_STATUS.String(), regDeviceRes.GetNextAction().GetScreen().String())
	afuId := GetAfuIdFromCheckAFUStatusDl(dep, regDeviceRes.GetNextAction())
	nextAfuAction, _, err = PollAFUStatus(ctx, dep.SignupClient, dep.Assert, reqH, afuId)
	if err != nil {
		nextAfuAction = GetTokenAndPollStatus(ctx, dep, aUser, reqH, afuId)
	}

	return nextAfuAction
}

func markAfuRiskVerdict(ctx context.Context, reqH *header.RequestHeader, dep *OnbDep, data *UserData, verdict authPb.ProcessAFURiskVerdictRequest_Verdict) {
	actor, _ := GetUserDetails(ctx, dep, data.Phone)
	validateRes, err := dep.AuthClient.ValidateToken(ctx, &authPb.ValidateTokenRequest{
		Token:     reqH.GetAuth().GetAccessToken(),
		TokenType: authPb.TokenType_ACCESS_TOKEN,
		Device:    reqH.GetAuth().GetDevice(),
	})
	dep.Assert.Nil(epifigrpc.RPCError(validateRes, err))

	verdictRes, err := dep.AuthClient.ProcessAFURiskVerdict(ctx, &authPb.ProcessAFURiskVerdictRequest{
		AfuId:   validateRes.GetAuthFactorUpdateId(),
		ActorId: actor.GetId(),
		CaseId:  "12345",
		Verdict: verdict,
	})
	dep.Assert.Nil(epifigrpc.RPCError(verdictRes, err))
}

func TestAFU_Sim(ctx context.Context, dep *OnbDep, aUser *UserData) {
	CreateSavingsAccountForUser(ctx, dep, aUser)
	SimUpdate(ctx, dep, aUser)
}

func SimUpdate(ctx context.Context, dep *OnbDep, aUser *UserData) {
	// client sends request with sim update flag
	// actions from start till AddOAuthAccount screen
	reqH, verifyRes := UserWithRefreshToken(ctx, dep, aUser)
	reqH.Auth.AuthToken = &header.AuthHeader_RefreshToken{
		RefreshToken: verifyRes.RefreshToken,
	}
	aUser.SimSubIds = []int32{
		atomic.AddInt32(&ckycUserCount, 1), atomic.AddInt32(&ckycUserCount, 1),
	}
	addOAuthRes := AddOAuthAccountForAFU(ctx, dep, reqH, aUser, true, false, aUser.SimSubIds)
	reqH.Auth.AuthToken = &header.AuthHeader_AccessToken{
		AccessToken: addOAuthRes.GetAccessToken(),
	}

	// Next Step: Confirm update
	dep.Assert.Equal(deeplink.Screen_AFU_FINAL_CONFIRMATION.String(), addOAuthRes.GetNextAction().GetScreen().String())
	confirmAFURes := ConfirmAFU(ctx, dep.Assert, dep.SignupClient, reqH, idgen.RandAlphaNumericString(10), withoutATMPIN)

	// Next Step: Register device
	dep.Assert.Equal(deeplink.Screen_REGISTER_DEVICE.String(), confirmAFURes.GetNextAction().GetScreen().String())
	regDeviceRes := RegisterDevice(ctx, dep.Assert, dep.SignupClient, reqH, ExpectDeviceUnregistered, aUser.SimSubIds[0], dep, aUser)

	dep.Assert.Equal(deeplink.Screen_AFU_CHECK_UPDATE_STATUS.String(), regDeviceRes.GetNextAction().GetScreen().String())
	afuId := GetAfuIdFromCheckAFUStatusDl(dep, regDeviceRes.GetNextAction())
	nextOnbAction, _, err := PollAFUStatus(ctx, dep.SignupClient, dep.Assert, reqH, afuId)
	if err != nil {
		nextOnbAction = GetTokenAndPollStatus(ctx, dep, aUser, reqH, afuId)
	}

	// AFU Completed. resume onboarding
	dep.Assert.Equal(ActionAfterCreateSavingsAccount.String(),
		nextOnbAction.GetScreen().String(),
	)
}

//nolint:funlen
func TestAFU_ReLoginWhileVendorUpdateInProgress(ctx context.Context, dep *OnbDep, aUser *UserData, randUser *UserData) {
	assert := dep.Assert

	// initial onboarding
	CreateSavingsAccountForUser(ctx, dep, aUser)

	// user comes again on a new device id and new phone number
	oldDeviceId := aUser.Device.DeviceId
	aUser.Device.DeviceId = idgen.RandAlphaNumericString(10)
	dep.Assert.NotEqual(oldDeviceId, aUser.Device.DeviceId)

	// use new email
	dep.Assert.NotEqual(aUser.OAuthIDToken, randUser.OAuthIDToken)
	aUser.OAuthIDToken = randUser.OAuthIDToken

	// user generates and verifies OTP
	reqH, respVerify := UserWithRefreshToken(ctx, dep, aUser)
	// makes an addOauth request
	addOAuthRes := AddOAuthAccount(ctx, dep, reqH, respVerify.GetRefreshToken(), aUser, AddOAuthConfirmAFU)
	accessToken := addOAuthRes.GetAccessToken()
	reqH.Auth.AuthToken = &header.AuthHeader_AccessToken{AccessToken: accessToken}

	// Next Step: Confirm phone update start
	ScreenEquals(dep.Assert, deeplink.Screen_AFU_CONFIRM_START_POPUP, addOAuthRes.GetNextAction().GetScreen())
	reqH.Auth.AuthToken = &header.AuthHeader_RefreshToken{
		RefreshToken: respVerify.GetRefreshToken(),
	}
	addOAuthRes = AddOAuthAccountForAFU(ctx, dep, reqH, aUser, false, true, aUser.SimSubIds)
	accessTokenNew := addOAuthRes.GetAccessToken()
	reqH.Auth.AuthToken = &header.AuthHeader_AccessToken{AccessToken: accessTokenNew}
	// Next Step: Liveness/FM
	assert.Equal(deeplink.Screen_CHECK_LIVENESS.String(), addOAuthRes.GetNextAction().GetScreen().String(), addOAuthRes.GetNextAction())
	var livReqId string
	if opts, ok := addOAuthRes.NextAction.ScreenOptions.(*deeplink.Deeplink_CheckLivenessScreenOptions); ok {
		livReqId = opts.CheckLivenessScreenOptions.AttemptId
	} else {
		dep.Assert.Failf("invalid next action in addoauth; expected:LIVENESS, got: %v",
			addOAuthRes.NextAction.GetScreen().String(),
		)
	}
	CheckLivenessWithNextAction(
		ctx, dep.Assert, dep.SignupClient, reqH, livReqId, aUser.Video,
		false, deeplink.Screen_AFU_ATM_PIN_VALIDATION,
	)

	// Next Step: Confirm update
	confirmAFURes := ConfirmAFU(ctx, assert, dep.SignupClient, reqH, suspectStateReRegRequestId, withATMPIN)

	// Next Step: Register device
	assert.Equal(deeplink.Screen_REGISTER_DEVICE.String(), confirmAFURes.GetNextAction().GetScreen().String())
	regDeviceRes := RegisterDevice(ctx, assert, dep.SignupClient, reqH, ExpectDeviceUnregistered, aUser.SimSubIds[0], dep, aUser)

	assert.Equal(deeplink.Screen_AFU_CHECK_UPDATE_STATUS.String(), regDeviceRes.GetNextAction().GetScreen().String())

	// user killed the app and restarted it (on restart we make an addoauth call)
	addOAuthRes = AddOAuthAccount(ctx, dep, reqH, respVerify.GetRefreshToken(), aUser)
	assert.Equal(deeplink.Screen_AFU_CHECK_UPDATE_STATUS.String(), addOAuthRes.GetNextAction().GetScreen().String())

	// user tries to login with old details
	aUser.Device.DeviceId = oldDeviceId
	reqH, respVerify = UserWithRefreshToken(ctx, dep, aUser)
	lwoaOptsWithErrView := &LoginOpts{
		ExpectedRPCStatusCode: rpc.NewStatusWithoutDebug(999, "show error view"),
	}
	addOAuthRes = AddOAuthAccount(ctx, dep, reqH, respVerify.GetRefreshToken(), aUser, lwoaOptsWithErrView)
	assert.Equal("312", addOAuthRes.GetRespHeader().GetErrorView().GetFullScreenErrorView().GetErrorCode())
	assert.Empty(addOAuthRes.GetAccessToken())

	// user tries logging in from a third device
	oldDeviceId = aUser.Device.DeviceId
	aUser.Device.DeviceId = idgen.RandAlphaNumericString(10)
	dep.Assert.NotEqual(oldDeviceId, aUser.Device.DeviceId)
	// user generates and verifies OTP
	reqH, respVerify = UserWithRefreshToken(ctx, dep, aUser)
	// makes an addOauth request
	addOAuthRes = AddOAuthAccount(ctx, dep, reqH, respVerify.GetRefreshToken(), aUser, lwoaOptsWithErrView)
	assert.Equal(deeplink.Screen_DETAILED_ERROR_VIEW_SCREEN.String(), addOAuthRes.GetNextAction().GetScreen().String(), addOAuthRes.GetNextAction())
	assert.Equal("312", addOAuthRes.GetRespHeader().GetErrorView().GetFullScreenErrorView().GetErrorCode())
	assert.Empty(addOAuthRes.GetAccessToken())
}

func TestAFU_LastAFUResume(ctx context.Context, dep *OnbDep, aUser *UserData) {
	assert := dep.Assert

	// initial onboarding
	CreateSavingsAccountForUser(ctx, dep, aUser)

	// user comes again on a new device id
	oldDeviceId := aUser.Device.DeviceId
	aUser.Device.DeviceId = idgen.RandAlphaNumericString(10)
	dep.Assert.NotEqual(oldDeviceId, aUser.Device.DeviceId)

	// user generates and verifies OTP
	reqH, respVerify := UserWithRefreshToken(ctx, dep, aUser)
	// makes an addOauth request
	addOAuthRes := AddOAuthAccount(ctx, dep, reqH, respVerify.GetRefreshToken(), aUser)
	accessToken := addOAuthRes.GetAccessToken()
	reqH.Auth.AuthToken = &header.AuthHeader_AccessToken{AccessToken: accessToken}

	dep.Assert.Equal(deeplink.Screen_CHECK_LIVENESS.String(), addOAuthRes.GetNextAction().GetScreen().String())

	// user killed the app and restarted it (on restart we make an addoauth call)
	addOAuthRes = AddOAuthAccount(ctx, dep, reqH, respVerify.GetRefreshToken(), aUser)
	reqH.Auth.AuthToken = &header.AuthHeader_AccessToken{AccessToken: addOAuthRes.AccessToken}
	livenessDl := addOAuthRes.GetNextAction()
	dep.Assert.Equal(deeplink.Screen_CHECK_LIVENESS.String(), livenessDl.GetScreen().String())
	// Next Step: Doing liveness
	var livReqId string
	opts := WaitForLivenessNextAction(ctx, dep, reqH, livenessDl)
	livReqId = opts.GetAttemptId()
	CheckLivenessWithNextAction(
		ctx, dep.Assert, dep.SignupClient, reqH, livReqId, aUser.Video,
		false, deeplink.Screen_AFU_FINAL_CONFIRMATION, true,
	)

	// Next Step: Confirm update
	confirmAFURes := ConfirmAFU(ctx, assert, dep.SignupClient, reqH, idgen.RandAlphaNumericString(10), withoutATMPIN)

	// Next Step: Register device
	assert.Equal(deeplink.Screen_REGISTER_DEVICE.String(), confirmAFURes.GetNextAction().GetScreen().String())
	regDeviceRes := RegisterDevice(ctx, assert, dep.SignupClient, reqH, ExpectDeviceUnregistered, aUser.SimSubIds[0], dep, aUser)

	assert.Equal(deeplink.Screen_AFU_CHECK_UPDATE_STATUS.String(), regDeviceRes.GetNextAction().GetScreen().String())
}

func TestAFU_FailedWithNonRetryableError(ctx context.Context, dep *OnbDep, aUser *UserData) {
	assert := dep.Assert

	// initial onboarding
	CreateSavingsAccountForUser(ctx, dep, aUser)

	// user comes again on a new device id
	oldDeviceId := aUser.Device.DeviceId
	aUser.Device.DeviceId = testDeviceIdForInactiveCard
	dep.Assert.NotEqual(oldDeviceId, aUser.Device.DeviceId)

	// user generates and verifies OTP
	reqH, respVerify := UserWithRefreshToken(ctx, dep, aUser)
	// makes an addOauth request
	addOAuthRes := AddOAuthAccount(ctx, dep, reqH, respVerify.GetRefreshToken(), aUser)
	accessToken := addOAuthRes.GetAccessToken()
	reqH.GetAuth().AuthToken = &header.AuthHeader_AccessToken{AccessToken: accessToken}

	// Next Step: getting verification options for actor authentication
	livenessDl := addOAuthRes.GetNextAction()
	assert.Equal(deeplink.Screen_CHECK_LIVENESS.String(), livenessDl.GetScreen().String())
	// Next Step: Doing liveness
	var livReqId string
	opts := WaitForLivenessNextAction(ctx, dep, reqH, livenessDl)
	livReqId = opts.GetAttemptId()
	CheckLivenessWithNextAction(
		ctx, dep.Assert, dep.SignupClient, reqH, livReqId, aUser.Video,
		false, deeplink.Screen_AFU_FINAL_CONFIRMATION, true,
	)

	// Next Step: Confirm update with ATM PIN
	confirmAFURes := ConfirmAFU(ctx, assert, dep.SignupClient, reqH, idgen.RandAlphaNumericString(10), withoutATMPIN)

	// Next Step: Register device
	assert.Equal(deeplink.Screen_REGISTER_DEVICE.String(), confirmAFURes.GetNextAction().GetScreen().String())
	regDeviceRes := RegisterDevice(ctx, assert, dep.SignupClient, reqH, ExpectDeviceUnregistered, aUser.SimSubIds[0], dep, aUser)

	assert.Equal(deeplink.Screen_AFU_CHECK_UPDATE_STATUS.String(), regDeviceRes.GetNextAction().GetScreen().String())
	afuId := GetAfuIdFromCheckAFUStatusDl(dep, regDeviceRes.GetNextAction())
	_, pollRes, _ := PollAFUStatus(ctx, dep.SignupClient, dep.Assert, reqH, afuId)
	assert.Equal(999,
		int(pollRes.GetRespHeader().GetStatus().GetCode()), pollRes.String())
	assert.Equal("329",
		pollRes.GetRespHeader().GetErrorView().GetFullScreenErrorView().GetErrorCode(), pollRes.String())
}

// nolint: funlen
func TestAFU_FailedWithRetryableError(ctx context.Context, dep *OnbDep, aUser *UserData) *deeplink.Deeplink {
	assert := dep.Assert

	// initial onboarding
	CreateSavingsAccountForUser(ctx, dep, aUser)

	// user comes again on a new device id
	oldDeviceId := aUser.Device.DeviceId
	aUser.Device.DeviceId = testDeviceIdForWrongAtmPin
	dep.Assert.NotEqual(oldDeviceId, aUser.Device.DeviceId)

	// user generates and verifies OTP
	reqH, respVerify := UserWithRefreshToken(ctx, dep, aUser)
	// makes an addOauth request
	addOAuthRes := AddOAuthAccount(ctx, dep, reqH, respVerify.GetRefreshToken(), aUser)
	accessToken := addOAuthRes.GetAccessToken()
	reqH.Auth.AuthToken = &header.AuthHeader_AccessToken{AccessToken: accessToken}

	// Next Step: getting verification options for actor authentication
	assert.Equal(deeplink.Screen_INFO_ACKNOWLEDGEMENT_SCREEN.String(), addOAuthRes.GetNextAction().GetScreen().String())

	// Next Step: Confirm update with ATM PIN
	confirmAFURes := ConfirmAFU(ctx, assert, dep.SignupClient, reqH, idgen.RandAlphaNumericString(10), withATMPIN)

	// Next Step: Register device
	assert.Equal(deeplink.Screen_REGISTER_DEVICE.String(), confirmAFURes.GetNextAction().GetScreen().String())
	regDeviceRes := RegisterDevice(ctx, assert, dep.SignupClient, reqH, ExpectDeviceUnregistered, aUser.SimSubIds[0], dep, aUser)

	assert.Equal(deeplink.Screen_AFU_CHECK_UPDATE_STATUS.String(), regDeviceRes.GetNextAction().GetScreen().String())
	afuId := GetAfuIdFromCheckAFUStatusDl(dep, regDeviceRes.GetNextAction())
	nextActionDL, pollRes, _ := PollAFUStatus(ctx, dep.SignupClient, dep.Assert, reqH, afuId)

	assert.Equal(0,
		int(pollRes.GetRespHeader().GetStatus().GetCode()), pollRes.String())
	assert.Equal(deeplink.Screen_INFO_ACKNOWLEDGEMENT_SCREEN.String(), nextActionDL.GetScreen().String())
	// validating if two options are available to authenticate user for retry
	var ctas []*deeplink.Cta
	if infoOpts, ok := nextActionDL.GetScreenOptions().(*deeplink.Deeplink_InfoAcknowledgementScreenOptions); ok {
		ctas = infoOpts.InfoAcknowledgementScreenOptions.GetCtas()
		// check first cta to be retry with ATM PIN
		assert.Equal(deeplink.Screen_AFU_ATM_PIN_VALIDATION.String(), ctas[0].GetDeeplink().GetScreen().String())
		// check second cta to be retry with video verification
		assert.Equal(deeplink.Screen_CHECK_LIVENESS.String(), ctas[1].GetDeeplink().GetScreen().String())
	} else {
		dep.Assert.Failf(fmt.Sprintf("invalid next action in addoauth; expected:INFO_ACKNOWLEDGEMENT, got: %s", nextActionDL.GetScreen().String()), "error msg")
	}

	// Next Step: Retrying with liveness
	var livReqId string
	livenessDL := ctas[1].GetDeeplink()
	opts := WaitForLivenessNextAction(ctx, dep, reqH, livenessDL)
	livReqId = opts.GetAttemptId()
	CheckLivenessWithNextAction(
		ctx, dep.Assert, dep.SignupClient, reqH, livReqId, aUser.Video,
		false, deeplink.Screen_AFU_FINAL_CONFIRMATION, true,
	)

	// Next Step: Confirm update
	confirmAFURes = ConfirmAFU(ctx, assert, dep.SignupClient, reqH, idgen.RandAlphaNumericString(10), withoutATMPIN)

	// Next Step: Register device
	assert.Equal(deeplink.Screen_REGISTER_DEVICE.String(), confirmAFURes.GetNextAction().GetScreen().String())
	regDeviceRes = RegisterDevice(ctx, assert, dep.SignupClient, reqH, ExpectDeviceUnregistered, aUser.SimSubIds[0], dep, aUser)

	assert.Equal(deeplink.Screen_AFU_CHECK_UPDATE_STATUS.String(), regDeviceRes.GetNextAction().GetScreen().String())
	afuId = GetAfuIdFromCheckAFUStatusDl(dep, regDeviceRes.GetNextAction())
	nextOnbAction, _, err := PollAFUStatus(ctx, dep.SignupClient, dep.Assert, reqH, afuId)
	if err != nil {
		nextOnbAction = GetTokenAndPollStatus(ctx, dep, aUser, reqH, afuId)
	}

	return nextOnbAction

}

func AddOAuthAccountForAFU(ctx context.Context, dep *OnbDep, reqH *header.RequestHeader,
	data *UserData, phoneUpdateSrc bool, emailChange bool, simSubIds []int32) *signup.LoginWithOAuthResponse {
	var (
		respOAuth = &signup.LoginWithOAuthResponse{}
		err       error
	)
	androidSimSubIds := simSubIds
	respOAuth, err = dep.SignupClient.LoginWithOAuth(ctx, &signup.LoginWithOAuthRequest{
		Req: reqH,
		LoginDeviceInfo: &signup.LoginDeviceInfo{
			AndroidSimSubIds: androidSimSubIds,
		},
		OauthInfo: &signup.OAuthInfo{
			OauthToken:    data.OAuthIDToken,
			OauthProvider: signup.OAuthProvider_GOOGLE,
		},
		UserInfo: &signup.LoginWithOAuthRequest_UserInfo{
			GmailName: data.Name,
		},
		ConsentInfo: &signup.LoginWithOAuthRequest_ConsentInfo{
			PhoneUpdateConsent: commontypes.BoolToBooleanEnum(phoneUpdateSrc),
			EmailUpdateConsent: commontypes.BoolToBooleanEnum(emailChange),
		},
	})
	dep.Assert.NoError(err, fmt.Sprintf("error in AddOAuth for AFU: %v", err))
	dep.Assert.Equal(rpc.StatusOk().Code, respOAuth.GetRespHeader().GetStatus().GetCode(),
		fmt.Sprintf("AddOAuth for AFU res: %v", respOAuth.String()),
	)
	if reqH.GetAuth().GetDeviceIntegrity().GetDeviceIntegrityToken() == "" {
		// Do Device integrity
		integrityToken := VerifyDeviceIntegrity(ctx, &VerifyDeviceIntegrityParams{
			Deps:                dep,
			UserData:            data,
			TokenType:           types.DeviceIntegrityTokenType_DEVICE_INTEGRITY_TOKEN_TYPE_PLAY_INTEGRITY,
			RequestHeader:       reqH,
			AllowHighRiskDevice: commontypes.BooleanEnum_FALSE,
			ExpectedResult:      authPb.Result_RESULT_PASSED,
		})
		reqH.GetAuth().DeviceIntegrity = &header.DeviceIntegrity{
			DeviceIntegrityToken: integrityToken,
		}
		reqH.GetAuth().SafetyNetToken = ""

	}
	return respOAuth
}

func PollAFUStatus(ctx context.Context, sc signup.SignupClient, a *require.Assertions, reqH *header.RequestHeader, afuId string) (*deeplink.Deeplink, *signup.CheckAuthFactorUpdateStatusResponse, error) {
	var pollRes *signup.CheckAuthFactorUpdateStatusResponse
	var err error
	for i := 0; i < AFUStatusPollCount; i++ {
		pollRes, err = GetAFUStatus(ctx, a, sc, reqH, afuId)
		if err != nil {
			return nil, nil, err
		}

		if pollRes.GetRespHeader().GetStatus().IsSuccess() {
			if pollRes.GetNextAction().GetScreen() == deeplink.Screen_AFU_CHECK_UPDATE_STATUS {
				time.Sleep(2 * time.Second)
				continue
			}
			return pollRes.GetNextAction(), pollRes, nil
		}
		if pollRes.GetPollStatus() == signup.CheckAuthFactorUpdateStatusResponse_SUCCESS {
			a.True(pollRes.GetRespHeader().GetStatus().IsSuccess(), fmt.Sprintf("afu poll res: %v", pollRes))
		}
		if pollRes.GetRespHeader().GetStatus().GetCode() == 999 {
			a.NotNil(pollRes.GetRespHeader().GetErrorView())
			return pollRes.GetNextAction(), pollRes, nil
		}
		a.Equal(uint32(signup.CheckAuthFactorUpdateStatusResponse_AFU_IN_PROGRESS),
			pollRes.GetRespHeader().GetStatus().GetCode(), fmt.Sprintf("afu poll res: %v", pollRes))
		time.Sleep(2 * time.Second)
	}
	return pollRes.GetNextAction(), pollRes, nil
}

func GetTokenAndPollStatus(ctx context.Context, dep *OnbDep, user *UserData, reqH *header.RequestHeader, afuId string) *deeplink.Deeplink {
	_ = NewAccessTokenForUser(ctx, dep, user, reqH)
	nextOnbAction, _, err := PollAFUStatus(ctx, dep.SignupClient, dep.Assert, reqH, afuId)
	dep.Assert.NoError(err)
	return nextOnbAction
}

// todo (Vineet) revert soft deletion when afu request id handling is done
// func TestAfu_RetryReRegistration(ctx context.Context, dep *OnbDep, aUser *UserData) {
//	// Phone number update in afu
//	// This test tries to mimic retries in AFU after errors like- No SMS, Payload mismatch
//	CreateSavingsAccountForUser(ctx, dep, aUser)
//
//	// aUser wants to update the phone number on old device
//	oldPhoneNumber := aUser.Phone
//	// new phone number
//	aUser.Phone = &commontypes.PhoneNumber{
//		CountryCode:    91,
//		NationalNumber: phForSMSError,
//	}
//	dep.Assert.NotEqual(oldPhoneNumber.NationalNumber, aUser.Phone.NationalNumber)
//
//	// user tries to update the phone number
//	// actions from start till AddOAuthAccount screen
//	reqH, verifyRes := UserWithRefreshToken(ctx, dep, aUser)
//	addOAuthRes := AddOAuthAccount(ctx, dep.Assert, dep.SignupClient, aUser.Device, verifyRes.RefreshToken, aUser,
//		AddOAuthConfirmAFU,
//	)
//
//	// Next Step: Confirm phone update start
//	ScreenEquals(dep.Assert, deeplink.Screen_AFU_CONFIRM_START_POPUP, addOAuthRes.GetNextAction().GetScreen())
//	phSrc := signup.PhoneNumberUpdateSource_LOGIN_UPDATE_CTA
//	addOAuthRes = AddOAuthAccountForAFU(ctx, dep, aUser.Device, verifyRes.RefreshToken, aUser, phSrc, false)
//
//	// Next Step: Confirm update
//	dep.Assert.Equal(deeplink.Screen_AFU_ATM_PIN_VALIDATION.String(), addOAuthRes.GetNextAction().GetScreen().String())
//	reqH.Auth.AuthToken = &header.AuthHeader_AccessToken{
//		AccessToken: addOAuthRes.GetAccessToken(),
//	}
//	confirmAFURes := ConfirmAFU(ctx, dep.Assert, dep.SignupClient, reqH, idgen.RandAlphaNumericString(10))
//
//	// Next Step: Register device
//	dep.Assert.Equal(deeplink.Screen_REGISTER_DEVICE, confirmAFURes.GetNextAction().GetScreen())
//	regDeviceRes := RegisterDevice(ctx, dep.Assert, dep.SignupClient, reqH, ExpectDeviceUnregistered)
//
//	dep.Assert.Equal(deeplink.Screen_AFU_CHECK_UPDATE_STATUS.String(), regDeviceRes.GetNextAction().GetScreen().String())
//	nextOnbAction, _ := PollAFUStatus(ctx, dep.SignupClient, dep.Assert, reqH)
//
//	// AFU Completed. resume onboarding
//	dep.Assert.Equal(ActionAfterCreateSavingsAccount.String(),
//		nextOnbAction.GetScreen().String(),
//	)
// }
