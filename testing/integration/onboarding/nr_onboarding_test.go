// nolint
package onboarding

import (
	"context"
	"encoding/base64"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/anypb"

	"github.com/epifi/be-common/api/rpc"

	commonTypesPb "github.com/epifi/be-common/api/typesv2/common"
	vendorgatewayPb "github.com/epifi/be-common/api/vendorgateway"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	bankCustPb "github.com/epifi/gamma/api/bankcust"
	consentPb "github.com/epifi/gamma/api/consent"
	employmentPb "github.com/epifi/gamma/api/employment"
	"github.com/epifi/gamma/api/frontend/account/screening"
	"github.com/epifi/gamma/api/frontend/account/screening/uistate"
	"github.com/epifi/gamma/api/frontend/account/signup"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	fevkycpb "github.com/epifi/gamma/api/frontend/kyc/vkyc"
	feMediaPb "github.com/epifi/gamma/api/frontend/media"
	feuserpb "github.com/epifi/gamma/api/frontend/user"
	kycdocspb "github.com/epifi/gamma/api/kyc/docs"
	bevkycpb "github.com/epifi/gamma/api/kyc/vkyc"
	"github.com/epifi/gamma/api/omegle"
	omegleEnumsPb "github.com/epifi/gamma/api/omegle/enums"
	savingsPb "github.com/epifi/gamma/api/savings"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/account"
	form2 "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/form"
	mediaTypes "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/media"
	vkyc2 "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/vkyc"
	"github.com/epifi/gamma/api/typesv2/form"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/pkg/countrystdinfo"
	fepkg "github.com/epifi/gamma/pkg/frontend/msg"
	"github.com/epifi/gamma/testing/integration/app"
)

const (
	NriUaeOnbPhNumNoISD1 = ********* // phone number token f416c0247a98cccd657c5be4464523ce1ded7ed5 with ISD 971
	NriUaeOnbPhNumNoISD2 = ********* // phone number token c825269536e7dc63c8fa81217aeaaa0b31ee227b with ISD 971
	NriUaeOnbPhNumNoISD3 = ********* // phone number token c825269536e7dc63c8fa81217aeaaa0b31ee227b with ISD 971
	NriUaeOnbPhNumNoISD4 = ********* // phone number token b3c6b4abe6abd5b49901260a2439e7f9bc9eb4d7 with ISD 971
	NriUaeOnbPhNumNoISD5 = ********* // phone number token cfea345531e95422a8cbf3f5c3a9e52ec81ddf51 with ISD 971
	NriUaeOnbPhNumNoISD6 = 509710006 // phone number token 1052e6913a7b1544c02b7c8c9416a911cb29b2a9 with ISD 971

	NriQatarOnbPhNumNoISD1 = 34561234
	NriQatarOnbPhNumNoISD2 = 34561235
)

func TestNonResidentOnboarding(t *testing.T) {
	type args struct {
		userData   *app.UserData
		testConfig *TestConfig
	}
	tests := map[string]struct {
		args     *args
		test     func(args *args, deps *app.OnbDep)
		dbAssert func(args *args, deps *app.OnbDep)
	}{
		"happy onboarding flow": {
			args: &args{
				userData: app.CreateUserParams(app.OverrideUserParams{
					CountryCodePref: app.UAECountryCode,
					PhoneNoPref:     NriUaeOnbPhNumNoISD1,
					// to pass KYC cross validation
					DOB: &date.Date{
						Day:   1,
						Month: 1,
						Year:  1994,
					},
					// todo (NRI) : use dubai error codes and new stage proc for location check
				}),
			},
			test: func(args *args, deps *app.OnbDep) {
				nrHappyOnboardingFlow(deps, args.userData)
			},
			dbAssert: func(args *args, deps *app.OnbDep) {

			},
		},
		"happy onboarding flow Qatar": {
			args: &args{
				userData: app.CreateUserParams(app.OverrideUserParams{
					CountryCodePref: int(countrystdinfo.GetCountryPhoneInfo(types.CountryCode_COUNTRY_CODE_QAT).GetISDCode()),
					PhoneNoPref:     NriQatarOnbPhNumNoISD1,
					// to pass KYC cross validation
					DOB: &date.Date{
						Day:   1,
						Month: 1,
						Year:  1994,
					},
					// todo (NRI) : use Qatar location assertion
				}),
			},
			test: func(args *args, deps *app.OnbDep) {
				nrHappyOnboardingFlow(deps, args.userData)
			},
			dbAssert: func(args *args, deps *app.OnbDep) {
				/*
					List of assertions:
					onboarding feature assertion: NR SA Qatar Feature
					employment currency code: Qatari riyal
					communication address country code assertion
					Qatar ID captured
					VKYC channel assertion
					Customer creation residency info
					NRE account is created
				*/
				ctx := context.Background()
				actor, user := app.GetUserDetails(ctx, deps, args.userData.Phone)
				onbDetailsRes, err := deps.OnbClient.GetDetails(ctx, &onbPb.GetDetailsRequest{
					ActorId: actor.GetId(),
				})
				deps.Assert.NoError(epifigrpc.RPCError(onbDetailsRes, err), "error in getting onboarding details")
				deps.Assert.Equal(onbPb.Feature_FEATURE_NON_RESIDENT_SA_QATAR, onbDetailsRes.GetDetails().GetFeature())

				emplomentInfoRes, err := deps.BeEmploymentClient.GetEmploymentInfo(ctx, &employmentPb.GetEmploymentInfoRequest{
					ActorId: actor.GetId(),
				})
				deps.Assert.NoError(epifigrpc.RPCError(emplomentInfoRes, err), "error in getting employment info")
				deps.Assert.Equal(types.CurrencyCode_QAR, emplomentInfoRes.GetEmploymentData().GetEmploymentInfo().GetAnnualSalary().GetRange().GetAlternateDisplayed().GetCurrencyCode())

				// Assert country code in mailing address
				deps.Assert.Equal(countrystdinfo.GetCLDRCountryName(types.CountryCode_COUNTRY_CODE_QAT), user.GetProfile().GetAddresses()[types.AddressType_MAILING.String()].GetRegionCode())

				// VKYC flow assertion
				omegleRes, err := deps.BeOmegleClient.GetApplicantDetails(ctx, &omegle.GetApplicantDetailsRequest{
					ApplicationId: onbDetailsRes.GetDetails().GetStageMetadata().GetVkycMetadata().GetInhouseVKYC().GetApplicationId(),
				})
				deps.Assert.NoError(epifigrpc.RPCError(omegleRes, err), "error in getting applicant details")
				deps.Assert.Equal(omegleRes.GetChannel(), omegleEnumsPb.Channel_CHANNEL_NRI_ONBOARDING_QATAR)

				// Bank customer data assertion
				bankCustRes, err := deps.BankcustClient.GetBankCustomer(ctx, &bankCustPb.GetBankCustomerRequest{
					Vendor: vendorgatewayPb.Vendor_FEDERAL_BANK,
					Identifier: &bankCustPb.GetBankCustomerRequest_ActorId{
						ActorId: actor.GetId(),
					},
				})
				deps.Assert.NoError(epifigrpc.RPCError(bankCustRes, err), "error in getting bank customer")
				deps.Assert.Equal(bankCustRes.GetBankCustomer().GetSource(), bankCustPb.Source_SOURCE_NON_RESIDENT_SAVINGS_ACCOUNT_ONBOARDING_QATAR)
				deps.Assert.Equal(bankCustRes.GetBankCustomer().GetResidencyInfo().GetIsNonResidentIndian(), commonTypesPb.BooleanEnum_TRUE)
				deps.Assert.Equal(bankCustRes.GetBankCustomer().GetResidencyInfo().GetResidentCountryCode(), types.CountryCode_COUNTRY_CODE_QAT)

				// Savings account types assertion
				nreAccountRes, err := deps.BeSavingsClient.GetAccount(ctx, &savingsPb.GetAccountRequest{
					Identifier: &savingsPb.GetAccountRequest_ActorUniqueAccountIdentifier{
						ActorUniqueAccountIdentifier: &savingsPb.ActorUniqueAccountIdentifier{
							ActorId:                actor.GetId(),
							AccountProductOffering: account.AccountProductOffering_APO_NRE,
							PartnerBank:            vendorgatewayPb.Vendor_FEDERAL_BANK,
						},
					},
				})
				deps.Assert.NoError(err, "error in getting NRE account")
				deps.Assert.Equal(nreAccountRes.GetAccount().GetSkuInfo().GetAccountProductOffering(), account.AccountProductOffering_APO_NRE)
			},
		},
		"KYC data expired": {
			args: &args{
				userData: app.CreateUserParams(app.OverrideUserParams{
					CountryCodePref: app.UAECountryCode,
					PhoneNoPref:     NriUaeOnbPhNumNoISD2,
					// to pass KYC cross validation
					DOB: &date.Date{
						Day:   1,
						Month: 1,
						Year:  1994,
					},
					// todo (NRI) : use dubai error codes and new stage proc for location check
				}),
			},
			test: func(args *args, deps *app.OnbDep) {
				nrKycExpired(deps, args.userData)
			},
			dbAssert: func(args *args, deps *app.OnbDep) {

			},
		},
	}
	for name, tt := range tests {
		tt := tt
		t.Run(name, func(t *testing.T) {
			ctx := context.Background()
			dep, closeConnFunc := app.NewOnbDeps(ctx, require.New(t), dbConn)
			defer closeConnFunc()
			tt.test(tt.args, dep)
			tt.dbAssert(tt.args, dep)
		})
	}

}

func TestNonResidentUser_DeviceUpdate(t *testing.T) {
	type args struct {
		userData   *app.UserData
		testConfig *TestConfig
	}
	tests := map[string]struct {
		args     *args
		test     func(args *args, deps *app.OnbDep)
		dbAssert func(args *args, deps *app.OnbDep)
	}{
		"user with device update": {
			args: &args{
				userData: app.CreateUserParams(app.OverrideUserParams{
					CountryCodePref: app.UAECountryCode,
					PhoneNoPref:     NriUaeOnbPhNumNoISD3,
					// to pass KYC cross validation
					DOB: &date.Date{
						Day:   1,
						Month: 1,
						Year:  1994,
					},
				}),
			},
			test: func(args *args, deps *app.OnbDep) {
				nrHappyOnboardingFlow(deps, args.userData)
				ctx := getAFUAcceptanceContext()
				nextAction := app.AFU_OnlyDevice(ctx, deps, args.userData)
				// AFU Completed. resume onboarding
				deps.Assert.Equal(app.ActionAfterCreateSavingsAccount.String(),
					nextAction.GetScreen().String(),
				)
			},
			dbAssert: func(args *args, deps *app.OnbDep) {

			},
		},
	}
	for name, tt := range tests {
		tt := tt
		t.Run(name, func(t *testing.T) {
			ctx := context.Background()
			dep, closeConnFunc := app.NewOnbDeps(ctx, require.New(t), dbConn)
			defer closeConnFunc()
			tt.test(tt.args, dep)
			tt.dbAssert(tt.args, dep)
		})
	}

}

func nrHappyOnboardingFlow(dep *app.OnbDep, userData *app.UserData) {
	ctx := getAFUAcceptanceContext()
	reqH := nrKyc(ctx, dep, userData)
	nextAction := app.PollNextActionWithoutDeps(ctx, dep.Assert, dep.SignupClient, reqH)

	// Liveness
	lo := app.WaitForLivenessNextAction(ctx, dep, reqH, nextAction)
	app.CheckLivenessVideo(ctx, dep.Assert, dep.SignupClient, reqH, userData.Video, lo.GetAttemptId())
	nextAction = app.PollNextAction(ctx, dep, reqH, []deeplink.Screen{deeplink.Screen_CHECK_LIVENESS, deeplink.Screen_GET_NEXT_ONBOARDING_ACTION_API})

	actor, _ := app.GetUserDetails(ctx, dep, userData.Phone)
	res, err := dep.OnbClient.UpdateStage(ctx, &onbPb.UpdateStageRequest{
		ActorId:  actor.GetId(),
		Stage:    onbPb.OnboardingStage_NON_RESIDENT_ONBOARDING_CROSS_DATA_VALIDATION,
		NewState: onbPb.OnboardingState_SUCCESS,
	})
	dep.Assert.NoError(epifigrpc.RPCError(res, err), "error in updating cross validation status")

	// Update communication address
	collectCommunicationAddress(ctx, dep, reqH, userData)

	nextAction = app.PollNextActionWithoutDeps(ctx, dep.Assert, dep.SignupClient, reqH)
	dep.Assert.Equal(deeplink.Screen_CREATE_ACCOUNT.String(), nextAction.GetScreen().String())
	app.RegisterDevice(ctx, dep.Assert, dep.SignupClient, reqH, false, userData.SimSubIds[0], dep, userData)
	// app.AddShippingAddress(ctx, dep.Assert, dep.UserClient, reqH)
	legalNameRes := app.GetLegalName(ctx, dep.Assert, dep.UserClient, reqH)
	app.ConfirmCardPreference(ctx, dep.Assert, dep.UserClient, reqH, types.AddressType_SHIPPING, legalNameRes.GetLegalName())

	// Skipping VKYC status as of now
	vkycNextActionResp := vkycTillVideoCallScreen(ctx, dep, reqH)

	screenOptions := &vkyc2.VideoKycCallScreenOptions{}
	err = anypb.UnmarshalTo(vkycNextActionResp.GetNextAction().GetScreenOptionsV2(), screenOptions, proto.UnmarshalOptions{})
	dep.Assert.NoError(err)

	storeCallResp, err := dep.BeOmegleClient.StoreCallAndAuditDetails(ctx, &omegle.StoreCallAndAuditDetailsRequest{
		CallId: screenOptions.GetCallId(),
		RequestDetails: &omegle.StoreCallAndAuditDetailsRequest_CallDataDump{
			CallDataDump: &omegle.CallDataDump{
				AgentVerdict: types.Verdict_VERDICT_PASS,
			},
		},
	})
	dep.Assert.NoError(epifigrpc.RPCError(storeCallResp, err), "error in store call details")

	storeCallResp, err = dep.BeOmegleClient.StoreCallAndAuditDetails(ctx, &omegle.StoreCallAndAuditDetailsRequest{
		CallId: screenOptions.GetCallId(),
		RequestDetails: &omegle.StoreCallAndAuditDetailsRequest_AuditorReview{
			AuditorReview: &omegle.AuditorReview{
				Verdict: types.Verdict_VERDICT_PASS,
			},
		},
	})
	dep.Assert.NoError(epifigrpc.RPCError(storeCallResp, err), "error in store call details")

	nextAction = app.PollNextActionWithoutDeps(ctx, dep.Assert, dep.SignupClient, reqH)
	logger.Info(ctx, fmt.Sprintf("current screen %v", nextAction.GetScreen().String()))
	// assert and resume onboarding from there
	app.DoOnboardingFromAction(ctx, dep, reqH, nextAction, userData)
	nextAction = app.PollNextActionWithoutDeps(ctx, dep.Assert, dep.SignupClient, reqH)
	dep.Assert.Equal(deeplink.Screen_HOME.String(), nextAction.GetScreen().String())
}

func vkycTillVideoCallScreen(ctx context.Context, dep *app.OnbDep, reqH *header.RequestHeader) *fevkycpb.GetVKYCNextActionResponse {
	nextAction := app.PollNextActionWithoutDeps(ctx, dep.Assert, dep.SignupClient, reqH)
	dep.Assert.Equal(deeplink.Screen_GET_VKYC_NEXT_ACTION_API.String(), nextAction.GetScreen().String())
	vkycNextAction := nextAction.GetGetVkycNextActionApiScreenOptions()
	vkycNextActionResp, err := dep.FeVKYCClient.GetVKYCNextAction(ctx, &fevkycpb.GetVKYCNextActionRequest{
		Req:             reqH,
		ClientLastState: bevkycpb.VKYCClientState_VKYC_CLIENT_STATE_INITIATED.String(),
		EntryPoint:      bevkycpb.EntryPoint_ENTRY_POINT_NON_RESIDENT_ONBOARDING.String(),
		Blob:            vkycNextAction.GetBlob(),
	})

	dep.Assert.NoError(fepkg.FeRPCError(vkycNextActionResp, err), "error in vkyc next action")
	dep.Assert.Equal(deeplink.Screen_VKYC_INSTRUCTIONS_V2.String(), vkycNextActionResp.GetNextAction().GetScreen().String())
	screenOptions := &vkyc2.VkycInstructionsV2ScreenOptions{}
	err = anypb.UnmarshalTo(vkycNextActionResp.GetNextAction().GetScreenOptionsV2(), screenOptions, proto.UnmarshalOptions{})
	dep.Assert.Equal(deeplink.Screen_VKYC_INSTRUCTIONS_OVERLAY.String(), screenOptions.GetCtas()[0].GetDeeplink().GetScreen().String())

	vkycNextAction = nextAction.GetGetVkycNextActionApiScreenOptions()
	vkycNextActionResp, err = dep.FeVKYCClient.GetVKYCNextAction(ctx, &fevkycpb.GetVKYCNextActionRequest{
		Req:             reqH,
		ClientLastState: bevkycpb.VKYCClientState_VKYC_CLIENT_STATE_INSTRUCTIONS.String(),
		EntryPoint:      bevkycpb.EntryPoint_ENTRY_POINT_NON_RESIDENT_ONBOARDING.String(),
		Blob:            vkycNextAction.GetBlob(),
	})

	dep.Assert.NoError(fepkg.FeRPCError(vkycNextActionResp, err), "error in vkyc next action")
	dep.Assert.Equal(deeplink.Screen_USER_DETAILS_FORM.String(), vkycNextActionResp.GetNextAction().GetScreen().String())

	vkycNextAction = nextAction.GetGetVkycNextActionApiScreenOptions()
	vkycNextActionResp, err = dep.FeVKYCClient.GetVKYCNextAction(ctx, &fevkycpb.GetVKYCNextActionRequest{
		Req:             reqH,
		ClientLastState: bevkycpb.VKYCClientState_VKYC_CLIENT_STATE_CONFIRM_DETAILS.String(),
		EntryPoint:      bevkycpb.EntryPoint_ENTRY_POINT_NON_RESIDENT_ONBOARDING.String(),
		Blob:            vkycNextAction.GetBlob(),
	})

	dep.Assert.NoError(fepkg.FeRPCError(vkycNextActionResp, err), "error in vkyc next action")
	dep.Assert.Equal(deeplink.Screen_VIDEO_KYC_CALL_SCREEN.String(), vkycNextActionResp.GetNextAction().GetScreen().String())
	return vkycNextActionResp
}

func nrKycExpired(dep *app.OnbDep, userData *app.UserData) {
	ctx := getAFUAcceptanceContext()
	reqH := nrKyc(ctx, dep, userData)
	purgeDataAndAssertExpiry(ctx, dep, userData, reqH)
	nextAction := app.NextAction(ctx, dep.Assert, dep.SignupClient, reqH)
	// Liveness
	lo := app.WaitForLivenessNextAction(ctx, dep, reqH, nextAction)
	app.CheckLivenessVideo(ctx, dep.Assert, dep.SignupClient, reqH, userData.Video, lo.GetAttemptId())
	nextAction = app.PollNextAction(ctx, dep, reqH, []deeplink.Screen{deeplink.Screen_CHECK_LIVENESS, deeplink.Screen_GET_NEXT_ONBOARDING_ACTION_API})

	actor, _ := app.GetUserDetails(ctx, dep, userData.Phone)
	res, err := dep.OnbClient.UpdateStage(ctx, &onbPb.UpdateStageRequest{
		ActorId:  actor.GetId(),
		Stage:    onbPb.OnboardingStage_NON_RESIDENT_ONBOARDING_CROSS_DATA_VALIDATION,
		NewState: onbPb.OnboardingState_SUCCESS,
	})
	dep.Assert.NoError(epifigrpc.RPCError(res, err), "error in updating cross validation status")

	// Update communication address
	collectCommunicationAddress(ctx, dep, reqH, userData)

	nextAction = app.PollNextActionWithoutDeps(ctx, dep.Assert, dep.SignupClient, reqH)
	dep.Assert.Equal(deeplink.Screen_CREATE_ACCOUNT.String(), nextAction.GetScreen().String())
	app.RegisterDevice(ctx, dep.Assert, dep.SignupClient, reqH, false, userData.SimSubIds[0], dep, userData)
	// app.AddShippingAddress(ctx, dep.Assert, dep.UserClient, reqH)
	legalNameRes := app.GetLegalName(ctx, dep.Assert, dep.UserClient, reqH)
	app.ConfirmCardPreference(ctx, dep.Assert, dep.UserClient, reqH, types.AddressType_SHIPPING, legalNameRes.GetLegalName())

	vkycNextActionResp := vkycTillVideoCallScreen(ctx, dep, reqH)

	screenOptions := &vkyc2.VideoKycCallScreenOptions{}
	err = anypb.UnmarshalTo(vkycNextActionResp.GetNextAction().GetScreenOptionsV2(), screenOptions, proto.UnmarshalOptions{})
	dep.Assert.NoError(err)

	storeCallResp, err := dep.BeOmegleClient.StoreCallAndAuditDetails(ctx, &omegle.StoreCallAndAuditDetailsRequest{
		CallId: screenOptions.GetCallId(),
		RequestDetails: &omegle.StoreCallAndAuditDetailsRequest_CallDataDump{
			CallDataDump: &omegle.CallDataDump{
				AgentVerdict: types.Verdict_VERDICT_PASS,
			},
		},
	})
	dep.Assert.NoError(epifigrpc.RPCError(storeCallResp, err), "error in store call details")

	storeCallResp, err = dep.BeOmegleClient.StoreCallAndAuditDetails(ctx, &omegle.StoreCallAndAuditDetailsRequest{
		CallId: screenOptions.GetCallId(),
		RequestDetails: &omegle.StoreCallAndAuditDetailsRequest_AuditorReview{
			AuditorReview: &omegle.AuditorReview{
				Verdict: types.Verdict_VERDICT_PASS,
			},
		},
	})
	dep.Assert.NoError(epifigrpc.RPCError(storeCallResp, err), "error in store call details")

	nextAction = app.PollNextActionWithoutDeps(ctx, dep.Assert, dep.SignupClient, reqH)
	logger.Info(ctx, fmt.Sprintf("current screen %v", nextAction.GetScreen().String()))
	// assert and resume onboarding from there
	app.DoOnboardingFromAction(ctx, dep, reqH, nextAction, userData)
	nextAction = app.PollNextActionWithoutDeps(ctx, dep.Assert, dep.SignupClient, reqH)
	dep.Assert.Equal(deeplink.Screen_HOME.String(), nextAction.GetScreen().String())
}

func purgeDataAndAssertExpiry(ctx context.Context, dep *app.OnbDep, userData *app.UserData, reqH *header.RequestHeader) {
	actor, _ := app.GetUserDetails(ctx, dep, userData.Phone)
	getDetailsRes, getDetailsErr := dep.OnbClient.GetDetails(ctx, &onbPb.GetDetailsRequest{
		ActorId: actor.GetId(),
	})
	dep.Assert.NoError(epifigrpc.RPCError(getDetailsRes, getDetailsErr), "error in get details")
	// Wait for data expiry
	logger.Info(ctx, "waiting for KYC expiry")
	time.Sleep(150 * time.Second)
	// Purge extracted documents
	purgeRes, purgeErr := dep.DocExtractionClient.PurgeExtractedDocuments(ctx, &kycdocspb.PurgeExtractedDocumentsRequest{})
	dep.Assert.NoError(epifigrpc.RPCError(purgeRes, purgeErr), "error in purge response")
	// Polling does KYC expiry, resets corresponding stages
	app.NextAction(ctx, dep.Assert, dep.SignupClient, reqH)
	// Assert KYC records expiry
	passportFrontRes, passportFrontErr := dep.DocExtractionClient.GetExtractedData(ctx, &kycdocspb.GetExtractedDataRequest{
		ActorId:         actor.GetId(),
		ClientRequestId: getDetailsRes.GetDetails().GetStageMetadata().GetPassportVerificationMetadata().GetPassportFrontDocExtractClientReqId(),
	})
	dep.Assert.NoError(passportFrontErr)
	dep.Assert.Equal(passportFrontRes.GetStatus().GetCode(), rpc.StatusRecordNotFound().GetCode(), "unexpected response in passport front GetExtractedData")
	passportBackRes, passportBackErr := dep.DocExtractionClient.GetExtractedData(ctx, &kycdocspb.GetExtractedDataRequest{
		ActorId:         actor.GetId(),
		ClientRequestId: getDetailsRes.GetDetails().GetStageMetadata().GetPassportVerificationMetadata().GetPassportFrontDocExtractClientReqId(),
	})
	dep.Assert.NoError(passportBackErr)
	dep.Assert.Equal(passportBackRes.GetStatus().GetCode(), rpc.StatusRecordNotFound().GetCode(), "unexpected response in passport back GetExtractedData")
	countryIdRes, countryIdErr := dep.DocExtractionClient.GetExtractedData(ctx, &kycdocspb.GetExtractedDataRequest{
		ActorId:         actor.GetId(),
		ClientRequestId: getDetailsRes.GetDetails().GetStageMetadata().GetPassportVerificationMetadata().GetPassportFrontDocExtractClientReqId(),
	})
	dep.Assert.NoError(countryIdErr)
	dep.Assert.Equal(countryIdRes.GetStatus().GetCode(), rpc.StatusRecordNotFound().GetCode(), "unexpected response in countryID GetExtractedData")
}

func nrTillVkyc(ctx context.Context, dep *app.OnbDep, userData *app.UserData) *header.RequestHeader {
	reqH := nrKyc(ctx, dep, userData)
	nextAction := app.PollNextActionWithoutDeps(ctx, dep.Assert, dep.SignupClient, reqH)

	// Liveness
	lo := app.WaitForLivenessNextAction(ctx, dep, reqH, nextAction)
	app.CheckLivenessVideo(ctx, dep.Assert, dep.SignupClient, reqH, userData.Video, lo.GetAttemptId())
	nextAction = app.PollNextAction(ctx, dep, reqH, []deeplink.Screen{deeplink.Screen_CHECK_LIVENESS, deeplink.Screen_GET_NEXT_ONBOARDING_ACTION_API})

	actor, _ := app.GetUserDetails(ctx, dep, userData.Phone)
	res, err := dep.OnbClient.UpdateStage(ctx, &onbPb.UpdateStageRequest{
		ActorId:  actor.GetId(),
		Stage:    onbPb.OnboardingStage_NON_RESIDENT_ONBOARDING_CROSS_DATA_VALIDATION,
		NewState: onbPb.OnboardingState_SUCCESS,
	})
	dep.Assert.NoError(epifigrpc.RPCError(res, err), "error in updating cross validation status")

	// Update communication address
	collectCommunicationAddress(ctx, dep, reqH, userData)

	nextAction = app.PollNextActionWithoutDeps(ctx, dep.Assert, dep.SignupClient, reqH)
	dep.Assert.Equal(deeplink.Screen_CREATE_ACCOUNT.String(), nextAction.GetScreen().String())
	app.RegisterDevice(ctx, dep.Assert, dep.SignupClient, reqH, false, userData.SimSubIds[0], dep, userData)
	// app.AddShippingAddress(ctx, dep.Assert, dep.UserClient, reqH)
	legalNameRes := app.GetLegalName(ctx, dep.Assert, dep.UserClient, reqH)
	app.ConfirmCardPreference(ctx, dep.Assert, dep.UserClient, reqH, types.AddressType_SHIPPING, legalNameRes.GetLegalName())

	_ = vkycTillVideoCallScreen(ctx, dep, reqH)

	return reqH
}

// Complete onboarding till stage emirates ID verification
func nrKyc(ctx context.Context, dep *app.OnbDep, userData *app.UserData) *header.RequestHeader {
	reqH, _ := app.UserWithAccessToken(ctx, dep, userData)

	// Check that next action is TnC
	nextAction := app.PollNextAction(ctx, dep, reqH)
	if nextAction.GetScreen().String() != deeplink.Screen_CONSENT.String() {
		app.SkipOnboardingStage(ctx, reqH, dep.SignupClient, dep.Assert, signup.SkipOnboardingStageRequest_REFERRAL_FINITE_CODE)
	}
	nextAction = app.PollNextAction(ctx, dep, reqH)
	// Record user consent
	dep.Assert.Equal(deeplink.Screen_CONSENT.String(), nextAction.GetScreen().String())
	consents := []string{
		consentPb.ConsentType_FI_TNC.String(),
		consentPb.ConsentType_FED_TNC.String(),
		consentPb.ConsentType_CONSENT_FI_NRE_NRO_ACCOUNTS.String(),
		consentPb.ConsentType_FI_PRIVACY_POLICY.String(),
		consentPb.ConsentType_CONSENT_NOT_POLITICALLY_EXPOSED.String(),
		consentPb.ConsentType_CONSENT_NON_INDIAN_RESIDENCY.String(),
		consentPb.ConsentType_CONSENT_NON_RESIDENT_ACCOUNT_TESTING.String(),
		consentPb.ConsentType_CONSENT_FATCA_CRS.String(),
	}
	app.RecordFiConsent(ctx, dep.Assert, dep.ConsentClient, reqH, nil, consents...)

	// todo: assert PAN screen
	app.CallAndAssertSavePanDob(ctx, dep.Assert, dep, reqH, userData.Pan, userData.PanName, userData.DOB)

	nextAction = app.PollNextActionWithoutDeps(ctx, dep.Assert, dep.SignupClient, reqH)
	app.ScreenEquals(dep.Assert, deeplink.Screen_EMPLOYMENT_DECLARATION, nextAction.GetScreen())
	employmentType := uistate.EmploymentType_SALARIED
	if userData != nil && userData.EmploymentType != uistate.EmploymentType_EMPLOYMENT_TYPE_UNSPECIFIED {
		employmentType = userData.EmploymentType
	}

	countryCode := getCurrencyCode(ctx, userData.Phone.CountryCode)
	// Employment declaration
	_, err := dep.ScreeningClient.ProcessEmploymentData(ctx, &screening.ProcessEmploymentDataRequest{
		Req:            reqH,
		EmploymentType: employmentType,
		AnnualSalary: &screening.AnnualSalary{
			Range: &screening.AnnualSalaryRange{
				MinValue:     10000,
				MaxValue:     50000,
				CurrencyCode: types.CurrencyCode_INR,
				AlternateDisplayed: &screening.AnnualSalaryRange_AlternateDisplayed{
					MinVal:       1000,
					MaxVal:       5000,
					CurrencyCode: countryCode,
				},
			},
		},
		OccupationType: employmentPb.OccupationType_OCCUPATION_TYPE_SOFTWARE_AND_IT.String(),
	})

	dep.Assert.NoError(err)

	// save parents names and nominee details
	_ = app.CollectParentsName(ctx, dep, userData, reqH)
	return reqH
}

func getCurrencyCode(ctx context.Context, isdCode uint32) types.CurrencyCode {
	countryCode := countrystdinfo.GetCountryCodeByISDCode(isdCode)
	currencyCode := countrystdinfo.GetCurrencyCode(countryCode)
	logger.Debug(ctx, fmt.Sprintf("CountryCode: %v ,Currency code: %v", countryCode.String(), currencyCode.String()))
	return currencyCode
}

func collectCommunicationAddress(ctx context.Context, dep *app.OnbDep, reqH *header.RequestHeader, userData *app.UserData) {
	countryCodeToUpdateAddressReq := map[types.CountryCode]*feuserpb.UpdateFormDetailsRequest{
		types.CountryCode_COUNTRY_CODE_ARE: updateUaeCommunicationAddressReq(reqH),
		types.CountryCode_COUNTRY_CODE_QAT: updateQatarCommunicationAddressReq(reqH),
	}
	nextAction := app.PollNextActionWithoutDeps(ctx, dep.Assert, dep.SignupClient, reqH)
	dep.Assert.Equal(deeplink.Screen_USER_DETAILS_FORM.String(), nextAction.GetScreen().String())
	countryCode := countrystdinfo.GetCountryCodeByISDCode(userData.Phone.GetCountryCode())
	updateReq, ok := countryCodeToUpdateAddressReq[countryCode]
	if !ok {
		dep.Assert.Failf("address request for country code %v not handled", countryCode.String())
	}
	res, err := dep.UserClient.UpdateFormDetails(ctx, updateReq)
	dep.Assert.NoError(fepkg.FeRPCError(res, err))
}

func updateUaeCommunicationAddressReq(reqH *header.RequestHeader) *feuserpb.UpdateFormDetailsRequest {
	return &feuserpb.UpdateFormDetailsRequest{
		Req: reqH,
		Values: map[string]*form.FieldValue{
			form.FieldIdentifier_FIELD_IDENTIFIER_COUNTRY.String(): {
				Value: &form.FieldValue_StringValue{
					StringValue: "United Arab Emirates",
				},
			},
			form.FieldIdentifier_FIELD_IDENTIFIER_ADDRESS_LINE_1.String(): {
				Value: &form.FieldValue_StringValue{
					StringValue: "Address line 1",
				},
			},
			form.FieldIdentifier_FIELD_IDENTIFIER_ADDRESS_LINE_2.String(): {
				Value: &form.FieldValue_StringValue{
					StringValue: "Address line 2",
				},
			},
			form.FieldIdentifier_FIELD_IDENTIFIER_CITY.String(): {
				Value: &form.FieldValue_StringValue{
					StringValue: "Dubai",
				},
			},
		},
		Source: form2.UpdateUserDetailsFlow_UPDATE_USER_DETAILS_FLOW_NON_RESIDENT_ONBOARDING_COMMUNICATION_ADDRESS.String(),
	}
}

func updateQatarCommunicationAddressReq(reqH *header.RequestHeader) *feuserpb.UpdateFormDetailsRequest {
	return &feuserpb.UpdateFormDetailsRequest{
		Req: reqH,
		Values: map[string]*form.FieldValue{
			form.FieldIdentifier_FIELD_IDENTIFIER_COUNTRY.String(): {
				Value: &form.FieldValue_StringValue{
					StringValue: "Qatar",
				},
			},
			form.FieldIdentifier_FIELD_IDENTIFIER_STATE.String(): {
				Value: &form.FieldValue_StringValue{
					StringValue: "Doha",
				},
			},
			form.FieldIdentifier_FIELD_IDENTIFIER_ADDRESS_LINE_1.String(): {
				Value: &form.FieldValue_StringValue{
					StringValue: "Address line 1",
				},
			},
			form.FieldIdentifier_FIELD_IDENTIFIER_ADDRESS_LINE_2.String(): {
				Value: &form.FieldValue_StringValue{
					StringValue: "Address line 2",
				},
			},
			form.FieldIdentifier_FIELD_IDENTIFIER_CITY.String(): {
				Value: &form.FieldValue_StringValue{
					StringValue: "Random city",
				},
			},
		},
		Source: form2.UpdateUserDetailsFlow_UPDATE_USER_DETAILS_FLOW_NON_RESIDENT_ONBOARDING_QATAR_COMMUNICATION_ADDRESS.String(),
	}
}

func capturePassportImage(ctx context.Context, dep *app.OnbDep, reqH *header.RequestHeader) {
	nextAction := app.PollNextActionWithoutDeps(ctx, dep.Assert, dep.SignupClient, reqH)
	dep.Assert.Equal(deeplink.Screen_INFO_ACKNOWLEDGEMENT_SCREEN.String(), nextAction.GetScreen().String())
	captureImageDeeplink := nextAction.GetInfoAcknowledgementScreenOptions().GetCtas()[0].GetDeeplink() // Assuming existing CTA is to capture image
	dep.Assert.Equal(deeplink.Screen_CAPTURE_IMAGE.String(), captureImageDeeplink.GetScreen().String())
	captureImageScreenOpts := &mediaTypes.CaptureImageScreenOptions{}
	dep.Assert.NoError(anypb.UnmarshalTo(captureImageDeeplink.GetScreenOptionsV2(), captureImageScreenOpts, proto.UnmarshalOptions{}))

	// Passport front page
	res, err := dep.FeMediaClient.CaptureImage(ctx, &feMediaPb.CaptureImageRequest{
		Req:         reqH,
		Data:        passportImage(),
		ClientReqId: captureImageScreenOpts.GetClientRequestId(),
		Flow:        captureImageScreenOpts.GetFlow(),
	})
	dep.Assert.NoError(fepkg.FeRPCError(res, err))

	// Passport back page
	nextAction = app.PollNextActionWithoutDeps(ctx, dep.Assert, dep.SignupClient, reqH)
	dep.Assert.Equal(deeplink.Screen_CAPTURE_IMAGE.String(), nextAction.GetScreen().String())
	captureImageScreenOpts = &mediaTypes.CaptureImageScreenOptions{}
	dep.Assert.NoError(anypb.UnmarshalTo(nextAction.GetScreenOptionsV2(), captureImageScreenOpts, proto.UnmarshalOptions{}))
	res, err = dep.FeMediaClient.CaptureImage(ctx, &feMediaPb.CaptureImageRequest{
		Req:         reqH,
		Data:        passportImage(),
		ClientReqId: captureImageScreenOpts.GetClientRequestId(),
		Flow:        captureImageScreenOpts.GetFlow(),
	})
	dep.Assert.NoError(fepkg.FeRPCError(res, err))

	// Sync onboarding for Passport verification
	nextAction = app.PollNextActionWithoutDeps(ctx, dep.Assert, dep.SignupClient, reqH)

}

func passportImage() []byte {
	// feel free to change passport
	base64Image := "iVBORw0KGgoAAAANSUhEUgAAAgAAAAIACAMAAADDpiTIAAAAYFBMVEW+Si/XdkPq1KrkpnK4b1BzPjk+JzGiJjPkO0T3diL+rjT+52Fjx00+iUgmXEIZPD4STokAmdss6PX////Ay9yLm7RaaYg6RGYmK0QYFCX/AERoOGy1UIj2dXrot5bChWkyuxH2AAAAIHRSTlMA/////////////////////////////////////////5KarXYAAA61SURBVHic7d3Lrly3EUBRIYAecwWJNMj//2aaiIjwUnwW68HTd++JbKu7D1m1Bg1Jtr/8fvP+fVj0+a37En0A6wAwDgAAeO8AMO5tAJwu+rPCAAAA3iMAyAIAAJ6Z98JnRc9DGgCUip6HNAAoFT0PaQBQKnoe0h4FQGtRGp8ze8ZTAoBB0XPaCQAGRc9pJwAYFD2nna4HYLkUrc/efe5NAcA473ntBgDjvOe1GwCM857XbtcC8FyC5rOkZ4gKAMrPkp4hKgAoP0t6hqgAoPws6Rmiug5A5NA1n611JusAYPRsrTNZBwCjZ2udyToAGD1b60zWXQPgpiFrnsXqjFoBwPgsVmfUCgDGZ7E6o1YAMD6L1Rm1CgdgOdhfVTecb3TeiADgfL7ReSMCgPP5RueNCADO5xudN6IwAJaDrBffKuqcK+f3DADO51w5v2cAcD7nyvk9A4DzOVfO71kIAMsBrix/B4HFWWd38AwAAWed3cEzAAScdXYHzwAQcNbZHTxzBWA5vJ3F7yCwPHMvj13kABB45l4eu8gBIPDMvTx2kQNA4Jl7eewi5wbAemhSADME1ufu5bUXAADAJ+uBAUAWAABgn8fArAB4nb+Vx24AAAD7PIYFAFkAAIBdnsM6BfDt1Q33aGW5IwAAwC7PIQFAFgAAYJP3kE4A/Hj1vSj6Lq2s9gQAANjkPSAAyAIAAPTzHk5aYPoSt7v49J4ff/rnnxKAr68i79PLYlcAAIB+********************************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"
	bytesImage, _ := base64.StdEncoding.DecodeString(base64Image)
	return bytesImage
}
