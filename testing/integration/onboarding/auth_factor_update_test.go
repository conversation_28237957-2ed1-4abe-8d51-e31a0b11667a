package onboarding

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/mohae/deepcopy"
	require2 "github.com/stretchr/testify/require"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	authPb "github.com/epifi/gamma/api/auth"
	afuPb "github.com/epifi/gamma/api/auth/afu"
	"github.com/epifi/gamma/api/user"
	acceptanceTypes "github.com/epifi/gamma/pkg/acceptance/types"
	userType "github.com/epifi/gamma/testing/integration/pkg/types"
	"github.com/epifi/gamma/user/onboarding/dao/model"

	"github.com/epifi/gamma/testing/integration/app"

	"github.com/epifi/be-common/pkg/idgen"

	"github.com/jinzhu/copier"

	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
)

const (
	withATMPIN              = "withATMPIN"
	withoutATMPIN           = ""
	phoneNumberToSkipAtmPin = **********
)

func TestAuthFactorUpdate_SingleAFUAutoRetry(t *testing.T) {
	t.Parallel()

	ctx := getAFUAcceptanceContext()
	dep, closeConnFunc := app.NewOnbDeps(ctx, require2.New(t), dbConn)
	defer closeConnFunc()
	user1 := app.CreateUserParams()
	nextAction := app.AFU_OnlyDevice_withLivenessAndAutoRetries(ctx, dep, user1)
	// AFU Completed. resume onboarding
	dep.Assert.Equal(app.ActionAfterCreateSavingsAccount.String(),
		nextAction.GetScreen().String(),
	)
}

func TestAuthFactorUpdate_Sim(t *testing.T) {
	t.Parallel()

	// onboard a new user
	ctx := getAFUAcceptanceContext()
	dep, closeConnFunc := app.NewOnbDeps(ctx, require2.New(t), dbConn)
	defer closeConnFunc()
	aUser := app.CreateUserParams()
	app.TestAFU_Sim(ctx, dep, aUser)
}

func TestAuthFactorUpdate_OnlyPhoneNumber(t *testing.T) {
	t.Parallel()

	// onboard a new user
	ctx := getAFUAcceptanceContext()
	type args struct {
		oldUserData *app.UserData
		newUserData *app.UserData
		testConfig  *TestConfig
	}
	tests := map[string]struct {
		args     *args
		test     func(args *args, deps *app.OnbDep)
		dbAssert func(args *args, deps *app.OnbDep)
	}{
		"happy flow": {
			args: &args{
				oldUserData: app.CreateUserParams(),
				newUserData: app.CreateUserParams(),
			},
			test: func(args *args, deps *app.OnbDep) {
				nextAction := app.AFU_OnlyPhoneNumber(ctx, deps, deepcopy.Copy(args.oldUserData).(*app.UserData), deepcopy.Copy(args.newUserData).(*app.UserData))
				// AFU Completed. resume onboarding
				deps.Assert.Equal(app.ActionAfterCreateSavingsAccount.String(),
					nextAction.GetScreen().String(),
				)
			},
			dbAssert: func(args *args, deps *app.OnbDep) {

			},
		},
		"happy flow with no ATM PIN required": {
			args: &args{
				oldUserData: app.CreateUserParams(),
				newUserData: app.CreateUserParams(app.OverrideUserParams{
					PhoneNoPref: phoneNumberToSkipAtmPin,
				}),
			},
			test: func(args *args, deps *app.OnbDep) {
				nextAction := app.AFU_OnlyPhoneNumber_NoAtmPinRequired(ctx, deps, deepcopy.Copy(args.oldUserData).(*app.UserData), deepcopy.Copy(args.newUserData).(*app.UserData))
				// AFU Completed. resume onboarding
				deps.Assert.Equal(app.ActionAfterCreateSavingsAccount.String(),
					nextAction.GetScreen().String(),
				)
			},
			dbAssert: func(args *args, deps *app.OnbDep) {

			},
		},
		"aadhaar mobile mismatch": {
			args: &args{
				oldUserData: app.CreateUserParams(),
				newUserData: app.CreateUserParams(app.OverrideUserParams{
					PhoneNoPref: **********, // Aadhaar Mobile number validation fails with this number in simulator
				}),
			},
			test: func(args *args, deps *app.OnbDep) {
				app.AFU_OnlyPhone_AadhaarMobileMismatch(ctx, deps, deepcopy.Copy(args.oldUserData).(*app.UserData), deepcopy.Copy(args.newUserData).(*app.UserData))
			},
			dbAssert: func(args *args, deps *app.OnbDep) {
				logger.Info(ctx, "getting full user details")
				actor, _ := app.GetUserDetails(ctx, deps, args.oldUserData.Phone)
				res, _ := deps.AuthClient.GetAuthFactorUpdatesForActor(ctx, &authPb.GetAuthFactorUpdatesForActorRequest{
					ActorId: actor.GetId(),
				})
				afuRecord := res.GetAuthFactorUpdates()[0]
				deps.Assert.Equal(afuPb.OverallStatus_OVERALL_STATUS_FAILED, afuRecord.GetOverallStatus())
				deps.Assert.Equal(afuPb.FailureReason_AADHAAR_MOBILE_MISMATCH, afuRecord.GetFailureReason())
			},
		},
	}
	for name, tt := range tests {
		tt := tt
		testName := name
		t.Run(testName, func(t *testing.T) {
			testConfig := tt.args.testConfig
			if testConfig.IsTestSkipped() {
				t.Skip(testConfig.DetailsForTestSkip())
			}
			dep, closeConnFunc := app.NewOnbDeps(ctx, require2.New(t), dbConn)
			defer closeConnFunc()
			tt.test(tt.args, dep)
			if t.Failed() {
				t.Errorf("Test %s failed", testName)
			}
			tt.dbAssert(tt.args, dep)
		})
	}
}

func TestAuthFactorUpdate_ReLoginWhileVendorUpdateInProgress(t *testing.T) {
	t.Parallel()

	// onboard a new user
	ctx := getAFUAcceptanceContext()
	dep, closeConnFunc := app.NewOnbDeps(ctx, require2.New(t), dbConn)
	defer closeConnFunc()
	user, randUser := app.CreateUserParams(), app.CreateUserParams()
	app.TestAFU_ReLoginWhileVendorUpdateInProgress(ctx, dep, user, randUser)
}

func TestAuthFactorUpdate_LastAFUResume(t *testing.T) {
	t.Parallel()

	// onboard a new user
	ctx := getAFUAcceptanceContext()
	dep, closeConnFunc := app.NewOnbDeps(ctx, require2.New(t), dbConn)
	defer closeConnFunc()
	user := app.CreateUserParams()
	app.TestAFU_LastAFUResume(ctx, dep, user)
}

func TestAuthFactorUpdate_FailedWithNonRetryableError(t *testing.T) {
	t.Parallel()

	ctx := getAFUAcceptanceContext()
	dep, closeConnFunc := app.NewOnbDeps(ctx, require2.New(t), dbConn)
	defer closeConnFunc()
	user := app.CreateUserParams()
	app.TestAFU_FailedWithNonRetryableError(ctx, dep, user)
}

func TestAuthFactorUpdate_FailedWithRetryableError(t *testing.T) {
	t.Skip("TODO(Sai Teja): https://monorail.pointz.in/p/fi-app/issues/detail?id=78399")
	t.Parallel()

	ctx := getAFUAcceptanceContext()
	dep, closeConnFunc := app.NewOnbDeps(ctx, require2.New(t), dbConn)
	defer closeConnFunc()
	user := app.CreateUserParams()
	nextAction := app.TestAFU_FailedWithRetryableError(ctx, dep, user)
	dep.Assert.Equal(app.ActionAfterCreateSavingsAccount.String(), nextAction.GetScreen().String())
}

func TestAuthFactorUpdate_BypassAFU(t *testing.T) {
	t.Parallel()

	// onboard a new user
	ctx := getAFUAcceptanceContext()
	dep, closeConnFunc := app.NewOnbDeps(ctx, require2.New(t), dbConn)
	defer closeConnFunc()
	aUser := app.CreateUserParams()
	aUser.Pan = "**********"
	aUser.Phone = &commontypes.PhoneNumber{
		CountryCode:    91,
		NationalNumber: **********,
	}

	// initial onboarding
	app.CreateSavingsAccountForUser(ctx, dep, aUser)

	// user comes again on a new device id
	oldDeviceId := aUser.Device.DeviceId
	aUser.Device.DeviceId = uuid.New().String()
	dep.Assert.NotEqual(oldDeviceId, aUser.Device.DeviceId)

	// user generates and verifies OTP
	reqH, respVerify := app.UserWithRefreshToken(ctx, dep, aUser)
	// makes an addOauth request
	addOAuthRes := app.AddOAuthAccount(ctx, dep, reqH, respVerify.RefreshToken, aUser)
	accessToken := addOAuthRes.GetAccessToken()
	reqH.Auth.AuthToken = &header.AuthHeader_AccessToken{AccessToken: accessToken}

	nextOnbAction, _, err := app.PollAFUStatus(ctx, dep.SignupClient, dep.Assert, reqH, "")
	if err != nil {
		nextOnbAction = app.GetTokenAndPollStatus(ctx, dep, aUser, reqH, "")
	}
	// AFU Completed. // Next action is the pending onboarding next action.
	dep.Assert.Equal(app.ActionAfterCreateSavingsAccount.String(), nextOnbAction.GetScreen().String())
}

func TestAuthFactorUpdate_OnlyEmail(t *testing.T) {
	t.Parallel()

	// onboard a new user
	ctx := getAFUAcceptanceContext()
	dep, closeConnFunc := app.NewOnbDeps(ctx, require2.New(t), dbConn)
	defer closeConnFunc()
	user1 := app.CreateUserParams()
	if conf.UsePreloadedData.IsEnabled {
		pooledUser := app.GetPreloadedUser(ctx, t, userType.SAOnboardedMinKyc)
		user1 = pooledUser.User.Data
	} else {
		app.CreateSavingsAccountForUser(ctx, dep, user1)
	}

	// user updates the email
	userForEmail := app.CreateUserParams()
	user2 := app.UserData{}
	_ = copier.Copy(&user2, &user1)
	user2.OAuthIDToken = userForEmail.OAuthIDToken
	dep.Assert.NotEqual(user1.OAuthIDToken, user2.OAuthIDToken)

	// user tries to update the email
	// actions from start till AddOAuthAccount screen
	reqH, verifyRes := app.UserWithRefreshToken(ctx, dep, &user2)
	addOAuthRes := app.AddOAuthAccount(ctx, dep, reqH, verifyRes.RefreshToken, &user2, app.AddOAuthConfirmAFU)
	reqH.Auth.AuthToken = &header.AuthHeader_RefreshToken{
		RefreshToken: verifyRes.RefreshToken,
	}

	// Next Step: Confirm phone update start
	app.ScreenEquals(dep.Assert, deeplink.Screen_AFU_CONFIRM_START_POPUP, addOAuthRes.GetNextAction().GetScreen())
	addOAuthRes = app.AddOAuthAccountForAFU(ctx, dep, reqH, &user2, false, true, user2.SimSubIds)
	reqH.Auth.AuthToken = &header.AuthHeader_AccessToken{
		AccessToken: addOAuthRes.GetAccessToken(),
	}
	livenessDL := addOAuthRes.GetNextAction()
	// Next Step: Liveness screen
	dep.Assert.Equal(deeplink.Screen_CHECK_LIVENESS.String(), livenessDL.GetScreen().String())
	// Next Step: Doing liveness
	var livReqId string
	opts := app.WaitForLivenessNextAction(ctx, dep, reqH, livenessDL)
	livReqId = opts.GetAttemptId()
	app.CheckLivenessWithNextAction(
		ctx, dep.Assert, dep.SignupClient, reqH, livReqId, user1.Video,
		false, deeplink.Screen_AFU_FINAL_CONFIRMATION, true,
	)

	// Next Step: Confirm update
	confirmAFURes := app.ConfirmAFU(ctx, dep.Assert, dep.SignupClient, reqH, idgen.RandAlphaNumericString(10), withoutATMPIN)

	dep.Assert.Equal(deeplink.Screen_AFU_CHECK_UPDATE_STATUS.String(), confirmAFURes.GetNextAction().GetScreen().String())
	afuId := app.GetAfuIdFromCheckAFUStatusDl(dep, confirmAFURes.GetNextAction())
	nextOnbAction, _, err := app.PollAFUStatus(ctx, dep.SignupClient, dep.Assert, reqH, afuId)
	if err != nil {
		nextOnbAction = app.GetTokenAndPollStatus(ctx, dep, &user2, reqH, afuId)
	}

	// AFU Completed.
	// Next action is the pending onboarding next action.
	dep.Assert.Equal(app.ActionAfterCreateSavingsAccount.String(), nextOnbAction.GetScreen().String())
}

func TestAuthFactorUpdate_ConflictingPhoneNumber(t *testing.T) {
	t.Parallel()

	// onboard a new user
	ctx := getAFUAcceptanceContext()
	dep, closeConnFunc := app.NewOnbDeps(ctx, require2.New(t), dbConn)
	defer closeConnFunc()
	user1, user2 := app.CreateUserParams(), app.CreateUserParams()
	_, _ = app.DoEKYC(ctx, dep, user2)

	// get user and update onboarding details created at (more than 30 days) for user2
	userRes, err := dep.UserBEClient.GetUser(ctx, &user.GetUserRequest{
		Identifier: &user.GetUserRequest_PhoneNumber{
			PhoneNumber: user2.Phone,
		},
	})
	logger.Info(ctx, fmt.Sprintf("print userRes %v", userRes))
	if rpcErr := epifigrpc.RPCError(userRes, err); rpcErr != nil {
		dep.Assert.NoError(rpcErr)
	}

	err = dep.DB.Model(&model.OnboardingDetails{}).Where("user_id = ?", userRes.GetUser().GetId()).Updates(&model.OnboardingDetails{
		CreatedAt: time.Now().Add(-24 * 60 * time.Hour),
	}).Error
	dep.Assert.NoError(err)

	logger.Info(ctx, fmt.Sprintf("print num1 %v num2 %v", user1.Phone.String(), user2.Phone.String()))
	// user1 wants to update the phone number with a conflicting account
	nextAction := app.AFU_OnlyPhoneNumber(ctx, dep, user1, user2)
	// AFU Completed. resume onboarding
	app.ScreenEquals(dep.Assert, app.ActionAfterCreateSavingsAccount,
		nextAction.GetScreen(),
	)
}

func TestAuthFactorUpdate_ConflictingEmail(t *testing.T) {
	t.Parallel()

	// onboard a new user
	ctx := getAFUAcceptanceContext()
	dep, closeConnFunc := app.NewOnbDeps(ctx, require2.New(t), dbConn)
	defer closeConnFunc()
	user1, user2 := app.CreateUserParams(), app.CreateUserParams()
	app.CreateSavingsAccountForUser(ctx, dep, user1)
	_, _ = app.DoEKYC(ctx, dep, user2)

	// get user and update onboarding details created at (more than 30 days) for user2
	userRes, err := dep.UserBEClient.GetUser(ctx, &user.GetUserRequest{
		Identifier: &user.GetUserRequest_PhoneNumber{
			PhoneNumber: user2.Phone,
		},
	})
	logger.Info(ctx, fmt.Sprintf("print userRes %v", userRes))
	if rpcErr := epifigrpc.RPCError(userRes, err); rpcErr != nil {
		dep.Assert.NoError(rpcErr)
	}

	err = dep.DB.Model(&model.OnboardingDetails{}).Where("user_id = ?", userRes.GetUser().GetId()).Updates(&model.OnboardingDetails{
		CreatedAt: time.Now().Add(-24 * 60 * time.Hour),
	}).Error
	dep.Assert.NoError(err)

	logger.Info(ctx, fmt.Sprintf("print num1 %v num2 %v", user1.Phone.String(), user2.Phone.String()))
	// user1 wants to update the email with a conflicting account
	user1.OAuthIDToken = user2.OAuthIDToken

	app.AFU_OnlyEmail(ctx, dep, user1)
}

func TestAuthFactorUpdate_EmailDevice(t *testing.T) {
	t.Parallel()

	ctx := getAFUAcceptanceContext()
	dep, closeConnFunc := app.NewOnbDeps(ctx, require2.New(t), dbConn)
	defer closeConnFunc()
	user1 := app.CreateUserParams()
	nextAction := app.AFU_EmailDevice(ctx, dep, user1)
	// AFU Completed. resume onboarding
	dep.Assert.Equal(app.ActionAfterCreateSavingsAccount.String(),
		nextAction.GetScreen().String(),
	)
}

func TestAuthFactorUpdate_PhoneDevice(t *testing.T) {
	t.Parallel()

	ctx := getAFUAcceptanceContext()
	dep, closeConnFunc := app.NewOnbDeps(ctx, require2.New(t), dbConn)
	defer closeConnFunc()
	user1 := app.CreateUserParams()
	// initial onboarding
	_ = app.CreateSavingsAccountForUser(ctx, dep, user1)
	nextAction := app.AFU_PhoneDevice(ctx, dep, user1)
	// AFU Completed. resume onboarding
	dep.Assert.Equal(app.ActionAfterCreateSavingsAccount.String(),
		nextAction.GetScreen().String(),
	)
}

func TestAuthFactorUpdate_RiskManualReview(t *testing.T) {
	t.Parallel()

	ctx := getAFUAcceptanceContext()
	dep, closeConnFunc := app.NewOnbDeps(ctx, require2.New(t), dbConn)
	defer closeConnFunc()
	mailId := "<EMAIL>"
	user1 := app.CreateUserParams([]app.OverrideUserParams{
		{
			MailIdPref: mailId,
		},
	}...)
	nextAction := app.AFU_ManualReview(ctx, dep, user1)
	// AFU Completed. resume onboarding
	dep.Assert.Equal(app.ActionAfterCreateSavingsAccount.String(),
		nextAction.GetScreen().String(),
	)
}

func getAFUAcceptanceContext() context.Context {
	ctx := context.Background()
	ctx = epificontext.CtxWithAcceptanceFlow(ctx, acceptanceTypes.AuthFactorUpdate.String())
	return ctx
}

// todo (Vineet) revert soft deletion when afu request id handling is done
// func TestAuthFactorUpdate_RetryReRegistration(t *testing.T) {
//	t.Parallel()
//
//	// onboard a new user
//	ctx := getAFUAcceptanceContext()
//	dep, closeConnFunc := app.NewOnbDeps(ctx, t, dbConn)
//	defer closeConnFunc()
//	user := app.CreateUserParams()
//	app.TestAfu_RetryReRegistration(ctx, dep, user)
// }
