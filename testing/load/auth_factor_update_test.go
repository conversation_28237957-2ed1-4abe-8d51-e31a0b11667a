package load

import (
	"context"
	"io/ioutil"
	"path/filepath"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/testing/integration/app"

	"go.uber.org/zap"
)

func BenchmarkAuthFactorUpdate_OnlyDevice(b *testing.B) {
	b.Run<PERSON>ara<PERSON>l(func(pb *testing.PB) {
		for pb.Next() {
			a := require.New(b)
			ctx := context.Background()
			dep := CreateOnbDep(a)

			userData := CreateNewUser(a)
			livenessVideoFilePath := filepath.Join(currentDirPath, conf.Application.RelativeLivenessVideoFilePath)
			v, err := ioutil.ReadFile(livenessVideoFilePath)
			assert.NoError(b, err, "Failed to read video file")
			userData.Video = v
			logger.Info(ctx, "Going to perform auth factor update only device", zap.Uint64("phone", userData.Phone.NationalNumber), zap.String("PAN", userData.Pan))
			nextAction := app.AFU_OnlyDevice_withLiveness(ctx, dep, userData)
			// AFU Completed. resume onboarding
			dep.Assert.Equal(deeplink.Screen_ONBOARDING_ADD_MONEY,
				nextAction.GetScreen(),
			)
			logger.Info(ctx, "Successfully completed auth factor update only device", zap.Uint64("phone", userData.Phone.NationalNumber), zap.String("PAN", userData.Pan))
		}
	})
}

func BenchmarkAuthFactorUpdate_OnlyPhoneNumber(b *testing.B) {
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			a := require.New(b)
			ctx := context.Background()

			dep := CreateOnbDep(a)

			userData1 := CreateNewUser(a)
			livenessVideoFilePath := filepath.Join(currentDirPath, conf.Application.RelativeLivenessVideoFilePath)
			v, err := ioutil.ReadFile(livenessVideoFilePath)
			assert.NoError(b, err, "Failed to read video file")
			userData1.Video = v
			userData2 := CreateNewUser(a)

			logger.Info(ctx, "Going to perform auth factor update only phone number", zap.Uint64("phone1", userData1.Phone.NationalNumber),
				zap.String("PAN1", userData1.Pan), zap.Uint64("phone2", userData2.Phone.NationalNumber), zap.String("PAN2", userData2.Pan))
			nextAction := app.AFU_OnlyPhoneNumber(ctx, dep, userData1, userData2)
			// AFU Completed. resume onboarding
			dep.Assert.Equal(deeplink.Screen_ONBOARDING_ADD_MONEY,
				nextAction.GetScreen(),
			)
			logger.Info(ctx, "Successfully completed auth factor update only phone number", zap.Uint64("phone1", userData1.Phone.NationalNumber),
				zap.String("PAN1", userData1.Pan), zap.Uint64("phone2", userData2.Phone.NationalNumber), zap.String("PAN2", userData2.Pan))
		}
	})
}
