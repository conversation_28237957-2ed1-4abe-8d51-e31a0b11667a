# Usage:
# make build target=frontend  # Builds frontend service
# make run target=simulator	  # Runs simulator

# Src: https://stackoverflow.com/a/10858332
# Check that given variables are set and all have non-empty values,
# die with an error otherwise.
#
# Params:
#   1. Variable name(s) to test.
#   2. (optional) Error message to print.
check_defined = \
    $(strip $(foreach 1,$1, \
        $(call __check_defined,$1,$(strip $(value 2)))))
__check_defined = \
    $(if $(value $1),, \
      $(error Undefined $1$(if $2, ($2))))

# great stuff here for `make help` @ https://gist.github.com/prwhite/8168133
# COLORS for help sections
GREEN  := $(shell tput -Txterm setaf 2)
YELLOW := $(shell tput -Txterm setaf 3)
WHITE  := $(shell tput -Txterm setaf 7)
RESET  := $(shell tput -Txterm sgr0)

TARGET_MAX_CHAR_NUM=20
GOPATH ?= $(shell go env GOPATH)
GOMODCACHE ?= $(shell go env GOMODCACHE)
CURDIR ?= $(shell pwd)
timeout ?= 15m
export PATH := $(GOPATH)/bin:$(PATH)
MIGRATE_COCKROACH_URL = "cockroachdb://root:@localhost:26257/"
COCKROACH_URL = "postgresql://root:@localhost:26257/"
POSTGRES_URL = "postgres://root@localhost:5432/"
COVERFILE = code-coverage.out
COVERFILE_HTML = code-coverage.html
RUN_TEST = .
COMMA:= ,
DIRECTORY_DELIMITER:= /
EMPTY:=
SPACE:= $(EMPTY) $(EMPTY)
GOMAXPROCS = $(shell gomaxprocs)
GOLAND_CONFIG_DIR = "$(HOME)/Library/Application Support/JetBrains/$(shell ls ~/Library/Application\ Support/JetBrains/ | grep "GoLand" | sort -rn | head -n 1)"
BE_COMMON_VERSION := $(shell go list -m -f '{{.Version}}' github.com/epifi/be-common)
BE_COMMON_PATH := $(GOMODCACHE)/github.com/epifi/be-common@$(BE_COMMON_VERSION)

ifeq ($(RUN_TEST_PARALLEL),)
RUN_TEST_PARALLEL = false
endif

ifeq ($(SEND_SLACK_ALERT),)
SEND_SLACK_ALERT = false
endif

ifeq ($(BASE_BRANCH),)
# if empty, set master as base branch
BASE_BRANCH = master
endif

ifeq ($(LINT_PATHS),)
	ifdef PR_NUMBER
		# set lint check path to include all go packages which include modified files in the current branch
		LINT_PATHS = $(shell gh pr diff $(PR_NUMBER) --name-only | xargs -n 1 dirname | uniq | xargs -n 1 -I % find % -maxdepth 1 -name '*.go' | xargs -n 1 dirname | uniq | xargs -n 1 -I % echo ./%/ | tr '\n' ' ')
	else
		# set lint check path to include all go packages which include modified files in the current branch
        LINT_PATHS = $(shell git diff --name-only origin/$(BASE_BRANCH)... | xargs -n 1 dirname | uniq | xargs -n 1 -I % find % -maxdepth 1 -name '*.go' | xargs -n 1 dirname | uniq | xargs -n 1 -I % echo ./%/ | tr '\n' ' ')
	endif
endif

# install path for M1 and intel macs
# Check if the architecture is ARM (Apple Silicon)
UNAME_P := $(shell uname -p)
ifeq ($(UNAME_P),arm)
	INSTALL_PATH = "/opt/homebrew"
else
	INSTALL_PATH = "/usr/local"
endif

CRDB_DATA_DIR = $(INSTALL_PATH)/var/cockroach

## Show help
help:
	@echo ''
	@echo 'Usage:'
	@echo '  ${YELLOW}make${RESET} ${GREEN}<target>${RESET}'
	@echo ''
	@echo 'Targets:'
	@awk '/^[a-zA-Z\-\_0-9]+:/ { \
		helpMessage = match(lastLine, /^## (.*)/); \
		if (helpMessage) { \
			helpCommand = substr($$1, 0, index($$1, ":")-1); \
			helpMessage = substr(lastLine, RSTART + 3, RLENGTH); \
			printf "  ${YELLOW}%-$(TARGET_MAX_CHAR_NUM)s${RESET} ${GREEN}%s${RESET}\n", helpCommand, helpMessage; \
		} \
	} \
	{ lastLine = $$0 }' $(MAKEFILE_LIST)

# Checks if target is set
target:
	$(call check_defined, target, Build target)

targets:
	$(call check_defined, targets, Space separated list of target services)

## For a group DB, we need to choose any one DB from its subDBs to populate latest.sql
## if a new group DB is added, new alias for target needs to be made to generate latest.sql
## In case of loans, we use loans_federal DB as target for snapshot
pgdb_target_group_loan := loans_federal
pgdb_target_group_tsp_users := stock_guardian_tsp
## If loans is present in target, use pgdb_target_group_loan,
## i.e. target=loans will act as targets = loans_federal
ifeq ($(filter loans,$(target)),loans)
    updated_target := $(pgdb_target_group_loan)
else ifeq ($(filter tsp_users,$(target)),tsp_users)
    updated_target := $(pgdb_target_group_tsp_users)
else
    updated_target := $(target)
endif

## download the version of 'github.com/epifi/be-common' specified in go.mod if not already downloaded
fetch-deps:
	# Check if the same version of be-common is already present
	@if [ ! -d "$(BE_COMMON_PATH)" ]; then \
		echo "Fetching github.com/epifi/be-common@$(BE_COMMON_VERSION)..."; \
		go get github.com/epifi/be-common@$(BE_COMMON_VERSION); \
	fi

pr:
	$(call check_defined, pr, Specify PR number ex: make <rule> pr=1234)

image:
	$(call check_defined, image)

crdb_image:
	$(call check_defined, crdb_image)

pgdb_image:
	$(call check_defined, pgdb_image)

# Checks if target is set
testcase:
	$(call check_defined, testcase, Testcase pattern)

test_pattern:
	$(call check_defined, test_pattern)

# Checks if test tag is defined
tag:
	$(call check_defined, tag, Test tag)

# Checks if name is set
name:
	$(call check_defined, name, Revision name)

# Checks if proxy is set
proxy:
	$(call check_defined, proxy, GOPROXY)

# Checks if gocache is set
gocache:
	$(call check_defined, gocache, GOCACHE)

# Checks if gocachepath is set
gocachepath:
	$(call check_defined, gocachepath, go cache path)

# Checks if gomodcache is set
gomodcache:
	$(call check_defined, gomodcache, GOMODCACHE)

# Checks if port is set
port:
	$(call check_defined, port, Port to be exposed)

dkronJobName:
	$(call check_defined, dkronJobName, build dkronJobName)

# Checks if release branch tag is defined
releasebranch:
	$(call check_defined, releasebranch, base branch for building)

# Checks if workflow non-determinism release branch tag is defined
workflow-nd-release-branch:
	$(call check_defined, workflow-nd-release-branch, release branch for testing workflow non-determinism)

env:
	$(call check_defined, env, environment to run the script for)

imagetype:
	$(call check_defined, imagetype, Specify imagetype for building image either 'worker' or 'server')

## Builds the target Ex: `make build target=frontend,auth,user env=qa profilePath=profilePath`
## env flag is a required parameter because based on the env the server may contain a different combination of service groups.
## profilePath flag is an optional parameter to use given profile for enabling profile-guided optimization (PGO).
build: target fetch-deps
	@echo "Building the target.."
	bash ./scripts/makefile/go_build_helper.sh build_servers $(target) "$(env)" "$(profilePath)"

## Builds servers and workers to prefill go build cache to efficiently run subsequent build commands on the repository.
## Ex: `make build-go-cache`
build-go-cache:
	@echo "Prefilling go build cache..."
	bash ./scripts/makefile/go_build_helper.sh build_go_cache

build-server-health-check: pr
	@PR_NUMBER=$(pr) bash ./scripts/makefile/go_build_helper.sh build_server_health_check
## builds and validates the changes in the given pr Ex: `make build-validate pr=<PRNUMBER>`
build-validate: pr
	@PR_NUMBER=$(pr) bash ./scripts/makefile/go_build_helper.sh validate_go_changes
## builds and validates the changes in your current branch Ex: `make build-validate modified_files='actor/dao/actor.go actor/developer/factory.go'`
build-validate-lci:
	@bash ./scripts/makefile/go_build_helper.sh validate_go_changes $(modified_files)

get-be-common-path:
	@echo $(BE_COMMON_PATH)

build-proxy: target proxy _setGoProxy build _unsetGoProxy

_setGoProxy: proxy
	export GOPROXY=${proxy}

_unsetGoProxy:
	export GOPROXY=https://proxy.golang.org,direct

install-gofork:
	sudo rm -rf $(shell go env GOPATH)/src/github.com/epiFi/go 2> /dev/null && \
	mkdir -p $(shell go env GOPATH)/src/github.com/epiFi
	cd $(shell go env GOPATH)/src/github.com/epiFi && \
	git clone --depth 1 -b go1.24.2-checkerr https://github.com/epiFi/go.git && \
	cd go/src && \
	./make.bash && \
	sudo cp ../bin/go /usr/local/bin/gofork

build-cron-script: target dkronJobName fetch-deps
	@echo "Building the target.."
	mkdir -p ./output/$(dkronJobName)/config
	cp -r ./cmd/$(target)/config/*.yml ./output/$(dkronJobName)/config/ 2>/dev/null || :
	cp -r ./cmd/$(target)/config/*crt ./output/$(dkronJobName)/config/ 2>/dev/null || :
	cp -r ./cmd/$(target)/config/*key ./output/$(dkronJobName)/config/ 2>/dev/null || :
	cp -r ./cmd/$(target)/config/policy_model.conf ./output/$(dkronJobName)/config/ 2>/dev/null || :
	# TODO(pruthvi): This is a temporary change and remove once we start using secret manager
	#  to fetch crdb keys and fix the file permissions
	cp -r ./cmd/$(target)/config/crdb-demo ./output/$(dkronJobName)/config/crdb 2>/dev/null || :
	chmod 600 ./output/$(target)/config/crdb/* 2>/dev/null || :

	cp -r ./cmd/$(target)/config/rules ./output/$(dkronJobName)/config/ 2>/dev/null || :
	cp -r ./cmd/$(target)/config/stubs ./output/$(dkronJobName)/config/ 2>/dev/null || :
	cp -r ./cmd/$(target)/config/mappingCsv ./output/$(dkronJobName)/config/ 2>/dev/null || :
	cp -r ./cmd/$(target)/config/mappingJson ./output/$(dkronJobName)/config/ 2>/dev/null || :
	cp -r ./cmd/$(target)/config/pdf-templates ./output/$(target)/config/ 2>/dev/null || :
	cp -r "${BE_COMMON_PATH}/pkg/cfg/config/"* ./output/$(dkronJobName)/config/
	CGO_ENABLED=0 go build -mod=readonly -ldflags '-s' -o ./output/$(dkronJobName)/$(dkronJobName)_bin ./jobs/$(target)/$(dkronJobName)/cmd/;

build-script: target fetch-deps
	@echo "Building the target for LINUX.."
	mkdir -p ./output/$(target)/config ./output/$(target)/config_archive ./output/$(target)/testdata ./output/$(target)/init
#todo(saiteja): standardize if required
ifeq ($(target),wealth_dragon)
	@echo "Loading service configs required for wealth_dragon..."
	cp -r ./insights/config/values/* ./output/$(target)/config/ 2>/dev/null || :
	cp -r ./frontend/config/values/* ./output/$(target)/config/ 2>/dev/null || :
endif
	cp -r ./scripts/$(target)/config/* ./output/$(target)/config/ 2>/dev/null || :
	cp -r ./scripts/$(target)/testdata/* ./output/$(target)/testdata/ 2>/dev/null || :
	cp -r ./scripts/$(target)/config_archive/* ./output/$(target)/config_archive/ 2>/dev/null || :
	cp -r ./scripts/$(target)/init/* ./output/$(target)/init/ 2>/dev/null || :
	cp -r "${BE_COMMON_PATH}/pkg/cfg/config/"* ./output/$(target)/config/ 2>/dev/null || :

ifndef bin
	$(eval bin_name=$(target)_bin)
else
	$(eval bin_name=$(bin))
endif
ifeq ($(target), dev_actions)
	GOOS=darwin GOARCH=amd64 CGO_ENABLED=0 go build -mod=readonly -ldflags '-s' -o ./output/$(target)/$(bin_name) ./scripts/$(target);
else
	GOOS=linux GOARCH=amd64 CGO_ENABLED=0 go build -mod=readonly -ldflags '-s' -o ./output/$(target)/$(bin_name) ./scripts/$(target);
endif

build-tool: target fetch-deps
	@echo "Building the target for LINUX.."
	cp -r ./tools/$(target)/config/* ./output/$(target)/config/ 2>/dev/null || :
	cp -r "${BE_COMMON_PATH}/pkg/cfg/config/"* ./output/$(target)/config/ 2>/dev/null || :
ifndef bin
	$(eval bin_name=$(target)_bin)
else
	$(eval bin_name=$(bin))
endif
	GOOS=linux GOARCH=amd64 CGO_ENABLED=0 go build -mod=readonly -ldflags '-s' -o ./output/$(target)/$(bin_name) ./tools/$(target);


build-scheduled-script: target fetch-deps
	@echo "Building the target for LINUX.."
	mkdir -p ./output/$(target)/config
	cp -r ./scripts/scheduled/$(target)/config/* ./output/$(target)/config/ 2>/dev/null || :
	cp -r "${BE_COMMON_PATH}/pkg/cfg/config/"* ./output/$(target)/config/
	GOOS=linux GOARCH=amd64 CGO_ENABLED=0 go build -mod=readonly -ldflags '-s' -o ./output/$(target)/$(target)_bin ./scripts/scheduled/$(target);

build-worker: target fetch-deps
	@echo "Building the target.."
	bash ./scripts/makefile/go_build_helper.sh build_workers $(target)

## Runs the target Ex: `make run target=simulator`
run: target
# Services that are not dependent on a database don't need to start database
ifeq ($(target),$(filter $(target),frontend vendorgateway))
run: _gorun
else
run: startDB _gorun
endif

# Runs the target
_gorun: fetch-deps
	@echo "Running the target..."
	bash ./scripts/makefile/go_build_helper.sh setup_server_output_dir $(target) development
	ENVIRONMENT=development CONFIG_DIR=./output/$(target)/config go run -mod=readonly ./cmd/servers/development/$(target)

## Runs the comma seperated targets in Remote Debug mode. Ex: `ENVIRONMENT=staging make remoteDebug target=frontend,fittt`
remoteDebug: target
	LOCAL_SERVERS=$(target) AWS_PROFILE=epifi-$(ENVIRONMENT)  sh ${BE_COMMON_PATH}/scripts/remote_debug/init.sh run_gen_apps

remoteDebugConfigure:
	AWS_PROFILE=epifi-$(ENVIRONMENT) ENV=${ENVIRONMENT}  sh ${BE_COMMON_PATH}/scripts/remote_debug/configure.sh

## Deletes
clean:
	go clean -i -r -x ./...

## Run go clean and purge all caches
goclean:
	go clean -i -r -cache -testcache -modcache -x

## Use GOGC if this fails in CI https://github.com/golangci/golangci-lint#memory-usage-of-golangci-lint
## Use LINT_PATHS to restrict go packages. Default is "./...". ex: LINT_PATHS="./fitt/... ./rms/... ./cmd/fittt/..."
## Runs linter analysis for the all the git diff from master in the provided target branch
lint: target
ifneq ($(LINT_PATHS),)
	@$(eval REV = $(shell git merge-base origin/$(BASE_BRANCH) ${target}))
	golangci-lint -v run --new-from-rev=$(REV) $(LINT_PATHS)
else
	echo $(BASE_BRANCH)
	@echo "Skipping lint check since LINT_PATHS is empty"
endif

## Runs pre-commit hooks
pre-commit-hooks:
	pre-commit run -v --all-files

## Starts Cockroach database server, if installed
startDB:
	@echo "Starting a local single node-cluster"
	brew services start cockroach
	@echo "Started local cluster and created schemas"
	@echo "${GREEN}You can browse admin panel at http://localhost:26256/#/overview/list${RESET}"

## Stops Cockroach database server, if running
stopDB:
	@echo "Stopping local single-node cockroach DB"
	brew services stop cockroach

## Starts local postgres sql cluster Ex: 'make startPGDB'
startPGDB:
	brew services start postgres || brew services start postgresql@14

## Stops local postgres sql cluster Ex: 'make stopPGDB'
stopPGDB:
	brew services stop postgres || brew services stop postgresql@14

## Creates database Ex: `make createDB target=epifi`
createDB: target
	cockroach sql --insecure --url ${COCKROACH_URL} --echo-sql --execute "CREATE DATABASE IF NOT EXISTS ${target};"

## Drop the database Ex: `make dropDB target=epifi`
dropDB: target startDB
	cockroach sql --insecure --url ${COCKROACH_URL} --echo-sql --execute "DROP DATABASE IF EXISTS ${target};"

## Record the latest schema of the database Ex: `make snapshotDB target=epifi`
snapshotDB: target
	@echo '> Setting up latest.sql'
	@$(eval VERSION = $(shell migrate -database ${MIGRATE_COCKROACH_URL}${target}?sslmode=disable -path db/${target}/migrations version 2>&1 | cat))
	@{ echo '--> Migration Version: $(VERSION) \n' & \
	cockroach sql --insecure --url ${COCKROACH_URL} --database ${target} --format raw --execute "SHOW CREATE ALL TABLES;" | sed '/^#/d';} > db/${target}/latest.sql

_upgradeDB: target
	migrate -database ${MIGRATE_COCKROACH_URL}${target}?sslmode=disable -path db/${target}/migrations up ${N}

_downgradeDB: target
	migrate -database ${MIGRATE_COCKROACH_URL}${target}?sslmode=disable -path db/${target}/migrations down ${N}

migration-version: target
	migrate -database ${MIGRATE_COCKROACH_URL}${target}?sslmode=disable -path db/${target}/migrations version

setupDbACLs: target
	@echo '> Setting up ACLs'
	@cockroach sql --insecure --url ${COCKROACH_URL}${target}?sslmode=disable --echo-sql < db/${target}/users.sql

## Apply all or [N] up migrations Ex: `make upgradeDB target=epifi [N=1]`
upgradeDB: target createDB _upgradeDB snapshotDB setupDbACLs

## Applies latest.sql and sets migration version to latest Ex: `make restoreDbLatest target=epifi`
restoreDbLatest: target createDB
	@cockroach sql --insecure --url ${COCKROACH_URL}${target}?sslmode=disable -f db/${target}/latest.sql
	@migrate -database ${MIGRATE_COCKROACH_URL}${target}?sslmode=disable -path db/${target}/migrations force $(shell ls db/${target}/migrations/*.up.sql | tail -n1 | sed 's/[^1-9]*\([0-9]*\).*/\1/')

## Apply all or [N] down migrations Ex: `make downgradeDB target=epifi [N=1]`
downgradeDB: target _downgradeDB snapshotDB

## Create a revision for target database Ex: `make create-migration target=epifi`
create-migration: target
    # Explicitly setting title of the migration file as static.
    # It helps avoid conflicting migration versions from getting merged.
	migrate create -ext sql -dir db/${target}/migrations -seq ''

## Explicitly force set a specific migration version. Set version N but don't run migration (ignores dirty state).
## It helps fix the dirty DB version on migration failure.
## Ex: `make force-migration target=epifi N=100`
force-migration: target
	migrate -database ${MIGRATE_COCKROACH_URL}${target}?sslmode=disable -path db/${target}/migrations force ${N}

deletePGUser:
	psql postgres -c "DROP USER IF EXISTS ROOT"

## create user one time in the local machine using make createPGUser
createPGUser:  deletePGUser
	psql postgres -c "CREATE USER root"
	psql postgres -c "ALTER USER root WITH SUPERUSER"

_createPGDB: target
	psql postgres -tc "SELECT 1 FROM pg_database WHERE datname = '${target}'" | grep -q 1 || psql postgres -c "CREATE DATABASE ${target}"

_upgradePGDB: target
	migrate -database ${POSTGRES_URL}${target}?sslmode=disable -path db/${target}/migrations up ${N}

# runs up migration for any given target using run_migration_v2 script
_upgradeDbScript: target
	cd $(BE_COMMON_PATH)/scripts/dbtools/run_migration_v2/ && \
	ENVIRONMENT=development go run $$(find . -name "*.go" -and -not -name "*_test.go" -maxdepth 1) \
	--dbs=${target} --operation=migrate-up --n=${N} --override-working-dir=$(CURDIR)

# runs down migration for any given target using run_migration_v2 script
_downgradeDbScript: target
	cd $(BE_COMMON_PATH)/scripts/dbtools/run_migration_v2/ && \
	ENVIRONMENT=development go run $$(find . -name "*.go" -and -not -name "*_test.go" -maxdepth 1) \
	--dbs=${target} --operation=migrate-down --n=${N} --override-working-dir=$(CURDIR)

_downgradePGDB: target
	migrate -database ${POSTGRES_URL}${target}?sslmode=disable -path db/${target}/migrations down ${N}

## create database Ex: 'make createPGDB target=sherlock' or 'make createPGDB target=rewards'
createPGDB: target _createPGDB

## Drop the database Ex: `make dropPGDB target=sherlock` or `make dropPGDB target=rewards`
dropPGDB: target
	psql postgres -c "DROP DATABASE IF EXISTS ${target}"

## Take latest db snapshot in latest.sql file Ex: 'make snapshotPGDB target=sherlock' or 'make snapshotPGDB target='rewards'
snapshotPGDB: target
	@echo '> Setting up latest.sql'
	@$(eval VERSION = $(shell migrate -database ${POSTGRES_URL}${target}?sslmode=disable -path db/${target}/migrations version 2>&1 | cat))
	@{ echo '--> Migration Version: $(VERSION) \n' & \
	pg_dump --schema-only --no-owner --no-privileges --no-security-labels --no-tablespaces ${target};} | sed '/^SET/d' | sed '/^SELECT/d' | grep -v "^--" | grep "\S"> db/${target}/latest.sql

## Take db snapshot in latest.sql file. Can be used for both grouped DB(using alias) and individual DB. Ex: 'make snapshotDbScript target=sherlock
snapshotDbScript: target
	@for targeti in $(updated_target); do \
		echo '> Setting up latest.sql for ${target}'; \
		VERSION=$$(migrate -database ${POSTGRES_URL}$$targeti?sslmode=disable -path db/${target}/migrations version 2>&1 | cat); \
		{ echo '--> Migration Version: $${VERSION} \n' & \
		pg_dump --schema-only --no-owner --no-privileges --no-security-labels --no-tablespaces $$targeti; } | sed '/^SET/d' | sed '/^SELECT/d' | grep -v "^--" | grep "\S" > db/${target}/latest.sql; \
		exit 0; \
	done

## Load latest schema latest.sql into a test cockroach database and create a backup in the db/test_schema_backup directory. Ex: 'make schemaBackup target=epifi'
schemaBackup: target
	cockroach sql --insecure --execute "DROP DATABASE IF EXISTS ${target}_test"; \
	echo '> Create new test database'; \
	cockroach sql --insecure --execute "CREATE DATABASE ${target}_test"; \
	echo '> Load latest schema into the new test database'; \
	cockroach sql --insecure --database ${target}_test --file db/${target}/latest.sql; \
	VERSION=`migrate -database ${MIGRATE_COCKROACH_URL}${target}?sslmode=disable -path db/${target}/migrations version 2>&1 | cat`; \
	cockroach sql --insecure --execute "insert into ${target}_test.schema_migrations values($$VERSION, false)"; \
	echo '> Clean existing backups'; \
	rm -rf ${CRDB_DATA_DIR}/extern/${target}_test; \
	rm -rf db/test_schema_backup/${target}_test/; \
	echo '> Backup the test database'; \
	cockroach sql --insecure --execute "backup database ${target}_test into 'nodelocal://self/${target}_test?AUTH=implicit'"; \
	echo "> Copying backup from the default location ${CRDB_DATA_DIR}/extern"; \
	mkdir -p db/test_schema_backup/${target}_test/; \
	cp -r ${CRDB_DATA_DIR}/extern/${target}_test/ db/test_schema_backup/${target}_test/; \
	echo '> Copied the backup to db/test_schema_backup';

## Create a revision for target database Ex: `make create-pg-migration target=epifi`
create-pg-migration: target
    # Explicitly setting title of the migration file as static.
    # It helps avoid conflicting migration versions from getting merged.
	migrate create -ext sql -dir db/${target}/migrations -seq ''

## Apply all or [N] up migrations Ex: `make upgradePGDB target=epifi [N=1]`
## Deprecated, use upgradeDbScript
upgradePGDB: target createPGDB _upgradePGDB snapshotPGDB

## Apply all or [N] up migrations Ex: `make downgradePGDB target=epifi [N=1]`
## Deprecated, use downgradeDbScript
downgradePGDB: target _downgradePGDB snapshotPGDB

## Apply all or [N] up migrations Ex: `make upgradeDbScript target=epifi [N=1]` using script run_migration_v2 . To run upgradeDbScript for a new DB, add the respective DB params in scripts/dbtools/run_migration_v2/config/run-migration-development.yml
upgradeDbScript: target _upgradeDbScript snapshotDbScript

## Apply all or [N] down migrations Ex: `make downgradeDbScript target=epifi [N=1]` using script run_migration_v2
downgradeDbScript: target _downgradeDbScript snapshotDbScript

## Explicitly force set a specific migration version. Set version N but don't run migration (ignores dirty state).
## It helps fix the dirty DB version on migration failure.
## Ex: `make force-pg-migration target=epifi N=100`
force-pg-migration: target
	migrate -database ${POSTGRES_URL}${target}?sslmode=disable -path db/${target}/migrations force ${N}

## Starts es cluster with node=1, if installed
startES :
	brew services start elasticsearch

stopES:
	brew services stop elasticsearch

## Starts redis server locally
start-redis:
	brew services start redis

## Stops redis server locally
stop-redis:
	brew services stop redis

install-pinot:
	brew install ./scripts/platform/homebrew/formulas/fipinot/fipinot.rb

start-pinot:
	brew services start fipinot

stop-pinot:
	brew services stop fipinot

## Starts localstack in staging environment
start-localstack-remote-staging:
	ENVIRONMENT=staging podman-compose up localstack

## Starts all local databases and services (CRDB, Postgres, Pinot, Redis)
start-all-local: startDB startPGDB start-pinot start-redis
	@echo "All local databases and services started."

## Stops all local databases and services (CRDB, Postgres, Pinot, Redis)
stop-all-local: stopDB stopPGDB stop-pinot stop-redis
	@echo "All local databases and services stopped."

# Build docker image
docker-build: target
	docker build -f Dockerfile . -t ${target}:latest --build-arg service_name=${target} --build-arg env="$(env)"

docker-build-proxy: target proxy
	docker build -f Dockerfile . -t ${target}:${ENV}-${BUILD_NUMBER} --build-arg service_name=${target} --build-arg goproxy=${proxy} --build-arg env=${ENV}

docker-build-dkron-proxy: target dkronJobName proxy
	docker build -f Dockerfile.dkron . -t $(dkronJobName):latest --build-arg target=$(target) --build-arg job_name=$(dkronJobName) --build-arg goproxy=$(proxy)

docker-build-worker-proxy: target proxy tag releasebranch
	docker build -f ./build/worker/Dockerfile ./output -t ${tag} --build-arg target=${target} --build-arg goproxy=${proxy} --build-arg release_branch=${releasebranch}

docker-build-teamspace-image: target imagetype proxy tag releasebranch
	@echo "Building image for teamspace.."
ifeq ($(imagetype), worker)
	docker build -f ./build/teamspace/worker.Dockerfile ./output -t ${tag} --build-arg target=${target} --build-arg goproxy=${proxy} --build-arg release_branch=${releasebranch}
else
	docker build -f ./build/teamspace/server.Dockerfile ./output -t ${tag} --build-arg target=${target} --build-arg goproxy=${proxy} --build-arg release_branch=${releasebranch}
endif

docker-build-jarvis-proxy: target tag
	docker build -f ./build/jarvis/Dockerfile . -t ${tag} --build-arg target=${target} --build-arg env=${env}

# Run docker image after building
docker-run: target port
	docker run -p ${port}:${port} -e ENVIRONMENT=development \
		-e CONFIG_DIR=/go/src/github.com/epifi/gamma/config \
		-e APPLICATION_NAME=${target} \
		-e EPIFI_DB_HOST=host.docker.internal \
		-d ${target}:latest

# Start environment (multiple services) via docker-compose
docker-dev:
	docker-compose -f docker-compose.yml up --build --abort-on-container-exit

install-gotestsum:
	if command -v gotestsum > /dev/null; then \
		echo "gotestsum command already exists. Not installing it again" ; \
	else \
		echo "gotestsum command does not exist. Installing it..." ; \
		go install gotest.tools/gotestsum@latest ; \
	fi

# Run tests for a specific target service.
# To run tests for specific packages use `includepkgs` with package name passed with `,` separated delimiter.
# To exclude tests for specific packages use `excludepkgs` with package name passed with `,` separated delimiter.
# To specify custom timeout while running tests use `timeout`
# Setting `count` to 1 ensures no tests are cached
# `p` flag makes sure no tests run in parallel
# `coverpkg` applies coverage analysis in each test to packages matching the patterns.
#   The default is for each test to analyze only the package being tested.
_test: target
# Set base packages based on the target
ifneq (${target}, pkg)
	$(eval basepkgs=./${target}% ./api/${target}%)
else
	$(eval basepkgs=./${target}%)
endif
	@echo "base package ${basepkgs}"

# generate a list of packages to be considered for running unit tests
# after applying `includepkgs` filter (if any)
ifdef includepkgs
	# parse pkgs passed as command separated list
	$(eval sanitizedpkgs=$(subst $(COMMA),$(SPACE),$(includepkgs)))
	@echo "including packages..."
	# append sub-pkgs to be included at the end of base pkgs
	# this filters out other sub-packages inside target
	$(eval testpkgs=$(foreach pkg,$(sanitizedpkgs),$(subst %,/$(pkg)/...,$(basepkgs))))
	@echo "${testpkgs}"
else
	# append /... at the end of base pkgs to include all the files
	$(eval testpkgs = $(subst %,/...,$(basepkgs)))
	@echo "${testpkgs}"
endif

# generate `testfiles` and `coverfiles` after removing files mentioned in `excludepkgs` (if any)
ifdef excludepkgs
	# parse pkgs passed as command separated list
	$(eval sanitizedpkgs=$(subst $(COMMA),$(SPACE), $(excludepkgs)))
	@echo "excluding packages..."
	# prepare and sanitize pkgs list to be excluded after appending base pkgs
	$(eval exludetestpkgs=$(foreach pkg, $(sanitizedpkgs), $(subst %,/$(pkg), $(basepkgs))))
	$(eval exludetestpkgs=$(subst ./,/, $(exludetestpkgs)))
	$(eval exludetestpkgs=$(strip $(exludetestpkgs)))
	@echo "${exludetestpkgs}"
	# form grep regex to be used to exlcude matching files
	$(eval exludetestpkgs=$(subst $(SPACE),|,$(exludetestpkgs)))
	# -e flag helps in suppressing file not found error
	# ref- https://pkg.go.dev/cmd/go#hdr-List_packages_or_modules
	$(eval testfiles=$(shell go list -e ${testpkgs} | grep -E -v '${exludetestpkgs}|/\...'))
	$(eval coverfiles=$(shell go list -e ${testpkgs} | grep -E -v '/api/${target}|/developer|/mocks|/wire|/config|/metrics|/test|/\...|${exludetestpkgs}' | paste -s -d, -))
else
	$(eval testfiles=$(shell go list -e ${testpkgs} | grep -v '/\...'))
	$(eval coverfiles=$(shell go list -e ${testpkgs} | grep -E -v '/api/${target}|/developer|/mocks|/wire|/config|/metrics|/test|/\...' | paste -s -d, -))
endif

ifneq (${RUN_TEST_PARALLEL}, true)
	GOMAXPROCS=$(GOMAXPROCS) ENVIRONMENT=test gotestsum --format=standard-verbose -- -count 1 -timeout ${timeout} -p 1 -covermode count -coverpkg $(coverfiles) -coverprofile $(COVERFILE) -v ${testfiles}
else
	@echo "Running test in parallel"
	GOMAXPROCS=$(GOMAXPROCS) ENVIRONMENT=test gotestsum --format=standard-verbose -- -count 1 -timeout ${timeout} -covermode count -coverpkg $(coverfiles) -coverprofile $(COVERFILE) -v ${testfiles}
endif


# todo(saiteja): figure out a way to generate test coverage from test binaries
test_acceptance: _test_acceptance

_test_acceptance: target testcase
	ENVIRONMENT=test gotestsum --jsonfile-timing-events="./testing/integration/output/test-report.json" --format=standard-verbose --raw-command -- go tool test2json -t  ./${target}/output.test -test.run "${testcase}" -test.v

## Run smoke tests for a given pattern of testcases Ex: make test-smoke testcase=".*".
test-smoke: testcase install-gotestsum workflow-nd-release-branch fetch-deps
	mkdir -p ./testing/smoke/config/worker/
	cp "${BE_COMMON_PATH}/pkg/cfg/config/"* ./testing/smoke/config/
	find ./cmd/worker -type f | grep '.*/config/.*' | xargs -n 1 -t -I % cp % ./testing/smoke/config/worker/
	time go test -c -o smoke.test -v ./testing/smoke
	SMOKE_TEST_RUN=true DISABLE_REMOTE_CFG_SERVER_ENDPOINTS_LOOKUP=true CONFIG_DIR=$(CURDIR)/testing/smoke/config gotestsum --raw-command --format=standard-verbose -- go tool test2json -p 1 ./smoke.test -test.timeout 25m -test.run="${testcase}" -test.v -workflow-nd-release-branch=${workflow-nd-release-branch}

test-integration: test_pattern install-gotestsum workflow-nd-release-branch fetch-deps
	cp "${BE_COMMON_PATH}/pkg/cfg/config/"* ./testing/integration/config/
	cp ./owners.yml ./testing/integration/config/
	mkdir -p ./testing/integration/config/worker/
	find ./cmd/worker -type f | grep '.*/config/.*' | xargs -n 1 -t -I % cp % ./testing/integration/config/worker/
	time go test -c -o integration.test -v ./testing/integration
	SMOKE_TEST_RUN=true DISABLE_REMOTE_CFG_SERVER_ENDPOINTS_LOOKUP=true CONFIG_DIR=$(CURDIR)/testing/integration/config gotestsum --jsonfile ./testing/integration/output/test_output.log --raw-command --format=standard-verbose -- go tool test2json -p 1 ./integration.test -test.timeout 120m -test.run="^TestExecutor$$" -test.v -workflow-nd-release-branch=${workflow-nd-release-branch} --test-pattern="${test_pattern}" --subtest-pattern="${subtest_pattern}" --bu="${bu_args}" --scope="${scope_args}" --build-no="${build_number}" --slack-alert=${slack_alert} --phone-number=${phone_number} --target-checkpoint=${target_checkpoint}

test-local: test_pattern install-gotestsum
	time go test -c -o integration.test -v ./testing/integration
	ENVIRONMENT=test DISABLE_REMOTE_CFG_SERVER_ENDPOINTS_LOOKUP=true gotestsum --raw-command --format=standard-verbose -- go tool test2json -p 1 ./integration.test -test.timeout 25m -test.run="^TestExecutor$$" -test.v --test-pattern="${test_pattern}" --subtest-pattern="${subtest_pattern}" --bu="${bu_args}" --scope="${scope_args}"

## Run tests for a specific target service with code coverage Ex: `make test target=upi includepkgs=mandate,consumer excludepkgs=dao,simulation timeout=20`
test: _test test-generate-coverage

## Run tests for a given space-separated list of targets. Ex: `make test target="upi auth preapprovedloan" includepkgs="upi/dao" excludepkgs="auth/dao auth/orchestrator" timeout=20m`
test-services: _test-services test-generate-coverage

_test-services: targets
	$(eval testfiles=$(shell INCLUDE_PKGS="$(includepkgs)" EXCLUDE_PKGS="$(excludepkgs)" bash ./scripts/makefile/get_pkg_names_for_gotestsum.sh "${targets}"))
ifneq (${RUN_TEST_PARALLEL}, true)
	GOMAXPROCS=$(GOMAXPROCS) ENVIRONMENT=test gotestsum --format=standard-verbose -- -count 1 -timeout ${timeout} -p 1 -covermode count -coverprofile $(COVERFILE) -v ${testfiles}
else
	@echo "Running test in parallel"
	GOMAXPROCS=$(GOMAXPROCS) ENVIRONMENT=test gotestsum --format=standard-verbose -- -count 1 -timeout ${timeout} -covermode count -coverprofile $(COVERFILE) -v ${testfiles}
endif

## Used by lci tool to run tests for a given space-separated list of targets.
test-services-lci: targets
	$(eval testDirs=$(shell INCLUDE_PKGS="$(includepkgs)" EXCLUDE_PKGS="$(excludepkgs)" bash ./scripts/makefile/get_pkg_names_for_gotestsum.sh "${targets}"))
ifneq (${RUN_TEST_PARALLEL}, true)
	GOMAXPROCS=$(GOMAXPROCS) ENVIRONMENT=test gotestsum --format=standard-verbose ${GOTESTSUM_FLAGS} -- -timeout ${timeout} -p 1 -v ${testDirs}
else
	@echo "Running tests with ${GOTESTSUM_FLAGS}"
	@echo "Running test in parallel"
	GOMAXPROCS=$(GOMAXPROCS) ENVIRONMENT=test gotestsum --format=standard-verbose ${GOTESTSUM_FLAGS} -- -timeout ${timeout} -v ${testDirs}
endif


# Run tests for all services
_test-all:
	ENVIRONMENT=test gotestsum --format=standard-verbose -- -count 1 -timeout 5m -p 1 -covermode count -coverprofile $(COVERFILE) \
 		-v ./accrual/... ./actor/... ./auth/... ./casbin/... ./casper/... ./comms/... ./consent/... ./cx/... ./frontend/... \
 		./inapphelp/... ./insights/... ./kyc/... ./order/... ./paymentinstrument/... \
 		./savings/...  ./user/... ./vendorgateway/... ./vendormapping/... ./vendornotification/... ./rewards/... ./acceptance/... \
 		 ./timeline/... ./tokenizer/... ./merchant/... ./connectedaccount/... ./wealthonboarding/... ./p2pinvestment/... ./pkg/... \
 		 ./dynamic_elements/... ./salaryprogram/...

# Run tests for all services along with generating the code coverage as HTML
test-all: _test-all test-generate-coverage

# View test coverage in browser
test-view-coverage:
	go tool cover -html=$(COVERFILE)

# Generate test coverage as HTML, to be used in CI
test-generate-coverage:
	go tool cover -html=$(COVERFILE) -o ./code-coverage.html
	@echo "Use 'open code-coverage.html' to view test coverage"

test-search:
	ENVIRONMENT=test go run ./search/testing/golden_query_set/cmd/main.go

# Build test env using docker-compose for a specific service
docker-test-up: target
	docker-compose -f docker-compose.test.yml build ${target}_test

# Run tests via docker-compose for a specific service
docker-test-run: target
	docker-compose -f docker-compose.test.yml run --name ${target}_container -e testcase=$(RUN_TEST) -e RUN_TEST_PARALLEL=$(RUN_TEST_PARALLEL) -e alert=$(SEND_SLACK_ALERT)  ${target}_test
	# todo(saiteja): revert this once code coverage issue is fixed
	# docker cp ${target}_container:/go/src/github.com/epifi/gamma/$(COVERFILE_HTML) ./$(COVERFILE_HTML)
	# docker cp ${target}_container:/go/src/github.com/epifi/gamma/$(COVERFILE) ./$(COVERFILE)

# Run tests via docker-compose for a specific service
# It also creates images for crdb, pgdb and uploads it to ecr
docker-test-run-and-upload-data: target crdb_image pgdb_image
	docker-compose -f docker-compose.test.yml run --name ${target}_container -e testcase=$(RUN_TEST) -e PINOT_CONTROLLER_URL=$(PINOT_CONTROLLER_URL) ${target}_test
	docker commit gamma_cockroachdb_1 632884248997.dkr.ecr.ap-south-1.amazonaws.com/${crdb_image}:latest
	docker image push 632884248997.dkr.ecr.ap-south-1.amazonaws.com/${crdb_image}:latest
	docker commit gamma_postgresdb_1 632884248997.dkr.ecr.ap-south-1.amazonaws.com/${pgdb_image}:latest
	docker image push 632884248997.dkr.ecr.ap-south-1.amazonaws.com/${pgdb_image}:latest

# Teardown docker-compose
docker-test-down:
	docker-compose -f docker-compose.test.yml down --rmi local --volumes

# Build test env for a acceptance directory such as "testing/integration/onboarding".
docker-acceptance-test-up: target
	docker-compose -f docker-compose.test.yml build --build-arg dir=${target} --build-arg goproxy=https://goproxy.pointz.in,direct acceptance_test

# Build test env for a acceptance directory such as "acceptance/pay" with pre uploaded data.
# Brings up crdb from the image name provided as input
docker-acceptance-with-fixtures-test-up: target crdb_image pgdb_image
	docker pull 632884248997.dkr.ecr.ap-south-1.amazonaws.com/${crdb_image}:latest
	docker pull 632884248997.dkr.ecr.ap-south-1.amazonaws.com/${pgdb_image}:latest
	docker-compose -f docker-compose.test.yml build --build-arg dir=${target} acceptance_with_fixtures_test
	docker tag gamma_acceptance_with_fixtures_test:latest gamma_acceptance_test:latest
# Run tests via docker-compose for a specific service
docker-test: docker-test-up docker-test-run docker-test-down

# Run tests via docker-compose for all services
docker-test-all:
	docker-compose -f docker-compose.test.yml build all_test
	docker-compose -f docker-compose.test.yml run --name all_container all_test
	docker cp all_container:/go/src/github.com/epifi/gamma/$(COVERFILE_HTML) ./$(COVERFILE_HTML)
	docker cp all_container:/go/src/github.com/epifi/gamma/$(COVERFILE) ./$(COVERFILE)
	docker-compose -f docker-compose.test.yml down --rmi local --volumes

# Run load tests,  GOMAXPROCS -> max concurrent tests that can be run at a time, benchtime -> number of times to execute a requests,
test-load:
	GOMAXPROCS=2 go test -bench=./testing/load/... -benchtime=4x -timeout=1h -v ./testing/load/...

# Build env using docker-compose to run multiple steps of a github action for a specific service
docker-ghaction-up: target
	docker-compose -f docker-compose.ghaction.yml build ${target}

# Run multiple steps of a github action via docker-compose for a specific service
docker-ghaction-run: target
	docker-compose -f docker-compose.ghaction.yml run --name ${target}_container ${target}
	docker cp ${target}_container:/go/src/github.com/epifi/gamma/$(COVERFILE) ./$(COVERFILE)
	docker cp ${target}_container:/go/src/github.com/epifi/gamma/$(COVERFILE_HTML) ./$(COVERFILE_HTML)

# Teardown docker-compose
docker-ghaction-down:
	docker-compose -f docker-compose.ghaction.yml down --rmi local --volumes

docker-ghaction: docker-ghaction-up docker-ghaction-run docker-ghaction-down

_test_tag: tag target
	ENVIRONMENT=test gotestsum --format=standard-verbose -- -count 1 -timeout 7m -p 1 -covermode count -coverprofile $(COVERFILE) -tags=${tag} -v ./${target}/... ./api/${target}/...

test_tag: _test_tag test-generate-coverage

fetch-crdb-secrets: env target
	ENV=${env} ./scripts/remote_debug/fetch_crdb_secret.sh ${target}

## Installs shared intellij goland live templates for code completion. Refer scripts/live-template/README.md for more details
install-livetemplates:
	bash ./scripts/live-template/install.sh ${GOLAND_CONFIG_DIR}
