package analytics

import (
	"context"
	"fmt"
	"time"

	s3types "github.com/aws/aws-sdk-go-v2/service/s3/types"
	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/grpc"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	rpcPb "github.com/epifi/be-common/api/rpc"
	commonTypesPb "github.com/epifi/be-common/api/typesv2/common"
	vgPb "github.com/epifi/be-common/api/vendorgateway"
	datetimePkg "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/epifitemporal"
	"github.com/epifi/be-common/pkg/logger"

	caPb "github.com/epifi/gamma/api/connected_account"
	aaAnalyticsPb "github.com/epifi/gamma/api/connected_account/analytics"
	"github.com/epifi/gamma/api/connected_account/enums"
	beCaExtPb "github.com/epifi/gamma/api/connected_account/external"
	caWorkflowPb "github.com/epifi/gamma/api/connected_account/workflow"
	typesV2 "github.com/epifi/gamma/api/typesv2"
	ignosisVgPb "github.com/epifi/gamma/api/vendorgateway/aa/analytics/ignosis"
	vmPb "github.com/epifi/gamma/api/vendormapping"
)

var (
	maxAllowedVendorResponseSize = grpc.MaxCallRecvMsgSize(128 * 1024 * 1024)
)

func (p *Processor) GetDataAndInitiateAnalysis(ctx context.Context, req *caWorkflowPb.WealthAnalysisActivityRequest) (*caWorkflowPb.WealthAnalysisActivityResponse, error) {
	lg := activity.GetLogger(ctx)
	ctx = epificontext.CtxWithActorId(ctx, req.GetActorId())

	ar, err := p.arDao.GetByOrchId(ctx, req.GetAnalysisRequest().GetOrchId())
	if err != nil {
		lg.Error("[GetDataAndInitiateAnalysis] failed to get latest analysis request", zap.Error(err), zap.String(logger.ACTOR_ID_V2, ar.GetActorId()))
		return nil, epifitemporal.NewTransientError(fmt.Errorf("failed to get latest analysis request, err: %w", err))
	}

	if ar.GetStatus() != aaAnalyticsPb.AttemptStatus_ATTEMPT_REQUEST_STATUS_PROCESSING {
		ar.Status = aaAnalyticsPb.AttemptStatus_ATTEMPT_REQUEST_STATUS_PROCESSING
		err = p.arDao.Update(ctx, ar, []aaAnalyticsPb.AnalysisRequestFieldMask{aaAnalyticsPb.AnalysisRequestFieldMask_ANALYSIS_REQUEST_FIELD_MASK_STATUS})
		if err != nil {
			lg.Error("[GetDataAndInitiateAnalysis] failed to update analysis request status", zap.Error(err), zap.String(logger.ACTOR_ID_V2, ar.GetActorId()))
			return nil, epifitemporal.NewTransientError(fmt.Errorf("failed to update analysis request status, err: %w", err))
		}
	}

	au, err := p.auDao.GetByActorId(ctx, ar.GetActorId(), commonTypesPb.Owner_OWNER_CONNECTED_ACCOUNT_WEALTH)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		lg.Error("[GetDataAndInitiateAnalysis] failed to get analysed user", zap.Error(err), zap.String(logger.ACTOR_ID_V2, ar.GetActorId()))
		return nil, epifitemporal.NewTransientError(fmt.Errorf("failed to get analysed user, err: %w", err))
	}

	var reqData []*ignosisVgPb.GetFastAnalysisRequest_Data
	awa := map[string]*aaAnalyticsPb.AccountAnalysis{}
	// if data present in request, use that, otherwise fetch data from AA for analysis
	if req.GetDataExchangeRecord() != nil {
		reqData, err = p.fillDataFromRequest(req, awa)
		if err != nil {
			lg.Error("[GetDataAndInitiateAnalysis] failed to fill data from request for analysis", zap.Error(err),
				zap.String(logger.ACTOR_ID_V2, ar.GetActorId()))
			ar.Status = aaAnalyticsPb.AttemptStatus_ATTEMPT_REQUEST_STATUS_FAILED
			ar.CompletedAt = timestampPb.Now()
			updErr := p.arDao.Update(ctx, ar, []aaAnalyticsPb.AnalysisRequestFieldMask{
				aaAnalyticsPb.AnalysisRequestFieldMask_ANALYSIS_REQUEST_FIELD_MASK_STATUS,
				aaAnalyticsPb.AnalysisRequestFieldMask_ANALYSIS_REQUEST_FIELD_MASK_COMPLETED_AT,
			})
			if updErr != nil {
				lg.Error("[GetDataAndInitiateAnalysis] failed to update analysis request status when failed", zap.Error(updErr), zap.String(logger.ACTOR_ID_V2, ar.GetActorId()))
				return nil, epifitemporal.NewTransientError(fmt.Errorf("failed to update analysis request status when failed, err: %w", updErr))
			}
			return nil, epifitemporal.NewPermanentError(errors.Wrapf(err, "\"failed to fill data from request for analysis"))
		}
	} else {
		reqData, err = p.fillRequestDataFromAaData(ctx, req, au, awa)
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				lg.Error("no AA data found", zap.Error(err))
				ar.Status = aaAnalyticsPb.AttemptStatus_ATTEMPT_REQUEST_STATUS_FAILED
				ar.CompletedAt = timestampPb.Now()
				updErr := p.arDao.Update(ctx, ar, []aaAnalyticsPb.AnalysisRequestFieldMask{
					aaAnalyticsPb.AnalysisRequestFieldMask_ANALYSIS_REQUEST_FIELD_MASK_STATUS,
					aaAnalyticsPb.AnalysisRequestFieldMask_ANALYSIS_REQUEST_FIELD_MASK_COMPLETED_AT,
				})
				if updErr != nil {
					lg.Error("[GetDataAndInitiateAnalysis] failed to update analysis request status when failed", zap.Error(updErr), zap.String(logger.ACTOR_ID_V2, ar.GetActorId()))
					return nil, epifitemporal.NewTransientError(fmt.Errorf("failed to update analysis request status when failed, err: %w", updErr))
				}
				return nil, epifitemporal.NewPermanentError(errors.Wrapf(err, "no AA data found"))
			}
			lg.Error("[GetDataAndInitiateAnalysis] failed to fill data from AA using actor for analysis", zap.Error(err),
				zap.String(logger.ACTOR_ID_V2, ar.GetActorId()))
			return nil, epifitemporal.NewTransientError(fmt.Errorf("failed to fill data from AA using actor for analysis, err: %w", err))
		}
	}

	rawVendorResp, valErr := p.s3Client.ReadObject(ctx, getTempFastAnalysisStoragePath(ar.GetActorId()))
	if valErr != nil && !errors.Is(valErr, epifierrors.ErrRecordNotFound) {
		lg.Error("[GetDataAndInitiateAnalysis] error in getting old vendor response from s3", zap.Error(valErr), zap.String(logger.ACTOR_ID_V2, ar.GetActorId()))
		return nil, epifitemporal.NewTransientError(fmt.Errorf("error in getting old vendor response from s3, err: %v", valErr))
	}

	if valErr != nil && errors.Is(valErr, epifierrors.ErrRecordNotFound) {
		// Get RecordIdentifier from vendormapping service
		vmRes, vmErr := p.vmClient.GetBEMappingById(ctx, &vmPb.GetBEMappingByIdRequest{Id: ar.GetActorId()})
		if vmErr != nil {
			lg.Error("[GetDataAndInitiateAnalysis] error in getting vendor record identifier", zap.Error(vmErr), zap.String(logger.ACTOR_ID_V2, ar.GetActorId()))
			return nil, epifitemporal.NewTransientError(fmt.Errorf("error in getting vendor record identifier, err: %v", vmErr))
		}

		// initiate analysis with vendor
		vgResp, vgErr := p.ignosisVgClient.GetFastAnalysis(ctx, &ignosisVgPb.GetFastAnalysisRequest{
			Header:         &vgPb.RequestHeader{Vendor: vgPb.Vendor_IGNOSIS},
			TrackingId:     vmRes.GetIgnosisId(),
			Data:           reqData,
			EmploymentType: ar.GetDetails().GetEmploymentType(),
			Employer:       ar.GetDetails().GetOrganisationName(),
		}, maxAllowedVendorResponseSize)
		if err = epifigrpc.RPCError(vgResp, vgErr); err != nil {
			lg.Error("[GetDataAndInitiateAnalysis] error in getting fast analysis from vg", zap.Error(err), zap.String(logger.ACTOR_ID_V2, ar.GetActorId()))
			return nil, epifitemporal.NewTransientError(fmt.Errorf("error in getting fast analysis from vg, err: %v", err))
		}
		rawVendorResp = vgResp.GetRawResponse()

		err = p.s3Client.Write(ctx, getTempFastAnalysisStoragePath(ar.GetActorId()), rawVendorResp, string(s3types.ObjectCannedACLBucketOwnerFullControl))
		if err != nil {
			lg.Error("[GetDataAndInitiateAnalysis] error in writing raw resp to s3", zap.Error(err), zap.String(logger.ACTOR_ID_V2, ar.GetActorId()))
			return nil, epifitemporal.NewTransientError(fmt.Errorf("error in writing raw resp to s3, err: %v", err))
		}
	}

	unmarshalledVendorResp, uvrErr := unmarshalVendorAnalysisResponse(rawVendorResp)
	if uvrErr != nil {
		lg.Error("[GetDataAndInitiateAnalysis] failed to unmarshal vendor response", zap.Error(uvrErr), zap.String(logger.ACTOR_ID_V2, ar.GetActorId()))
		return nil, epifitemporal.NewTransientError(fmt.Errorf("failed to unmarshal vendor response, err: %v", uvrErr))
	}

	// store response in s3
	storageErr := p.getAndStoreModifiedRawInS3(ctx, getFastAnalysisStoragePath(ar.GetActorId()), unmarshalledVendorResp.ConvertToModifiedResponse())
	if storageErr != nil {
		lg.Error("[GetDataAndInitiateAnalysis] failed to store fast analysis response in s3", zap.Error(storageErr), zap.String(logger.ACTOR_ID_V2, ar.GetActorId()))
		return nil, epifitemporal.NewTransientError(fmt.Errorf("failed to store fast analysis response in s3, err: %v", storageErr))
	}

	// create/update analysis request and analysed user
	if au == nil {
		_, createErr := p.auDao.Create(ctx, &aaAnalyticsPb.AnalysedUser{
			ActorId: ar.GetActorId(),
			Analysis: &aaAnalyticsPb.Analysis{
				L1AnalysisFilePath:    getFastAnalysisStoragePath(ar.GetActorId()),
				L1AnalysisCompletedAt: timestampPb.Now(),
				AccountAnalyses:       awa,
			},
		}, commonTypesPb.Owner_OWNER_CONNECTED_ACCOUNT_WEALTH)
		if createErr != nil {
			lg.Error("[GetDataAndInitiateAnalysis] failed to create analysed user", zap.Error(createErr), zap.String(logger.ACTOR_ID_V2, ar.GetActorId()))
			return nil, epifitemporal.NewTransientError(fmt.Errorf("failed to create analysed user, err: %v", createErr))
		}
	} else {
		au.GetAnalysis().L1AnalysisFilePath = getFastAnalysisStoragePath(ar.GetActorId())
		au.GetAnalysis().L1AnalysisCompletedAt = timestampPb.Now()
		for k, v := range awa {
			m, ok := au.GetAnalysis().GetAccountAnalyses()[k]
			if !ok {
				au.GetAnalysis().GetAccountAnalyses()[k] = v
				continue
			}
			if v.GetLastAnalysedTransactionTs().AsTime().After(m.GetLastAnalysedTransactionTs().AsTime()) {
				m.LastAnalysedTransactionTs = v.GetLastAnalysedTransactionTs()
			}
		}
		if err = p.auDao.Update(ctx, au, []aaAnalyticsPb.AnalysedUserFieldMask{aaAnalyticsPb.AnalysedUserFieldMask_ANALYSED_USER_FIELD_MASK_ANALYSIS}, commonTypesPb.Owner_OWNER_CONNECTED_ACCOUNT_WEALTH); err != nil {
			lg.Error("[GetDataAndInitiateAnalysis] failed to update analysed user", zap.Error(err), zap.String(logger.ACTOR_ID_V2, ar.GetActorId()))
			return nil, epifitemporal.NewTransientError(fmt.Errorf("failed to update analysed user, err: %v", err))
		}
	}

	ar.GetDetails().L1CompletedAt = timestampPb.Now()
	ar.GetDetails().ReferenceId = unmarshalledVendorResp.GetReferenceId()
	if err = p.arDao.Update(ctx, ar, []aaAnalyticsPb.AnalysisRequestFieldMask{aaAnalyticsPb.AnalysisRequestFieldMask_ANALYSIS_REQUEST_FIELD_MASK_DETAILS}); err != nil {
		lg.Error("[GetDataAndInitiateAnalysis] failed to update analysis request details", zap.Error(err), zap.String(logger.ACTOR_ID_V2, ar.GetActorId()))
		return nil, epifitemporal.NewTransientError(fmt.Errorf("failed to update analysis request details, err: %v", err))
	}

	return &caWorkflowPb.WealthAnalysisActivityResponse{}, nil
}

// nolint:unparam
func (p *Processor) fillDataFromRequest(req *caWorkflowPb.WealthAnalysisActivityRequest, awa map[string]*aaAnalyticsPb.AccountAnalysis) ([]*ignosisVgPb.GetFastAnalysisRequest_Data, error) {
	// TODO: Implement below method
	return nil, errors.New("not implemented")
}

// TODO: move this out to RPC for improving testability and and re-usability
func (p *Processor) fillRequestDataFromAaData(ctx context.Context, req *caWorkflowPb.WealthAnalysisActivityRequest, au *aaAnalyticsPb.AnalysedUser,
	awa map[string]*aaAnalyticsPb.AccountAnalysis) ([]*ignosisVgPb.GetFastAnalysisRequest_Data, error) {
	var reqData []*ignosisVgPb.GetFastAnalysisRequest_Data

	allAccountResp, err := p.caClient.GetAllAccounts(ctx, &caPb.GetAllAccountsRequest{
		PageContext: &rpcPb.PageContextRequest{
			PageSize: 100,
		},
		ActorId: req.GetActorId(),
		AccountFilterList: []beCaExtPb.AccountFilter{beCaExtPb.AccountFilter_ACCOUNT_FILTER_ACTIVE,
			beCaExtPb.AccountFilter_ACCOUNT_FILTER_INCL_FI_FEDERAL},
		AccInstrumentTypeList: []enums.AccInstrumentType{enums.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT},
	})
	if err = epifigrpc.RPCError(allAccountResp, err); err != nil {
		if allAccountResp.GetStatus().IsRecordNotFound() {
			return nil, errors.Wrapf(epifierrors.ErrRecordNotFound, "no acccounts found")
		}
		return nil, fmt.Errorf("failed to get all aa accounts, err: %v", err)
	}

	var accountIDs []string
	for _, account := range allAccountResp.GetAccountDetailsList() {
		accountIDs = append(accountIDs, account.GetAccountId())
	}

	accountDetailsResp, err := p.caClient.GetAccountDetailsBulk(ctx, &caPb.GetAccountDetailsBulkRequest{
		AccountIdList: accountIDs,
		AccountDetailsMaskList: []beCaExtPb.AccountDetailsMask{
			beCaExtPb.AccountDetailsMask_ACCOUNT_DETAILS_MASK_PROFILE,
			beCaExtPb.AccountDetailsMask_ACCOUNT_DETAILS_MASK_SUMMARY,
		},
	})
	if te := epifigrpc.RPCError(accountDetailsResp, err); te != nil {
		return nil, fmt.Errorf("failed to get account details in bulk, err: %v", err)
	}

	for _, account := range allAccountResp.GetAccountDetailsList() {
		accountDetails := accountDetailsResp.GetAccountDetailsMap()[account.GetAccountId()]
		data, dataErr := p.fillEachAccountRequestData(ctx, au, account, accountDetails, awa)
		if dataErr != nil {
			if errors.Is(dataErr, epifierrors.ErrRecordNotFound) {
				continue
			}
			return nil, fmt.Errorf("failed to fill data for account: %s, err: %v", account.GetAccountId(), dataErr)
		}
		reqData = append(reqData, data)
	}
	if len(reqData) == 0 {
		return nil, errors.Wrapf(epifierrors.ErrRecordNotFound, "no data found for any account for actor id: %s", req.GetActorId())
	}
	return reqData, nil
}

func (p *Processor) fillEachAccountRequestData(ctx context.Context, au *aaAnalyticsPb.AnalysedUser, account *beCaExtPb.AccountDetails,
	accountDetails *caPb.AccountProfileSummaryDetails, awa map[string]*aaAnalyticsPb.AccountAnalysis) (*ignosisVgPb.GetFastAnalysisRequest_Data, error) {
	txnsStartTs := timestampPb.New(time.Now().Add(time.Duration(p.conf.AaAnalytics.OldestTsFromTodayInMonths) * -1 * (30 * 24 * time.Hour)))
	if au != nil {
		// if analysis already done till t1, only fetch txns with ts after t1
		if m, ok := au.GetAnalysis().GetAccountAnalyses()[account.GetAccountId()]; ok {
			if m.GetLastAnalysedTransactionTs() != nil && m.GetLastAnalysedTransactionTs().AsTime().After(txnsStartTs.AsTime()) && au.GetAnalysis().GetL2AnalysisCompletedAt() != nil {
				txnsStartTs = m.GetLastAnalysedTransactionTs()
			}
		}
		awa = au.GetAnalysis().GetAccountAnalyses()
	}

	accountSummary := accountDetails.GetDepositSummary()
	accountProfile := accountDetails.GetDepositProfile()

	fipMetaResp, fipMetaErr := p.caClient.GetFipMeta(ctx, &caPb.GetFipMetaRequest{
		Identifiers: []*caPb.FipMetaIdentifier{{Identifier: &caPb.FipMetaIdentifier_FipId{FipId: account.GetFipId()}}},
	})
	if te := epifigrpc.RPCError(fipMetaResp, fipMetaErr); te != nil {
		return nil, fmt.Errorf("failed to get fip meta for account, err: %v", fipMetaErr)
	}
	var bankName typesV2.Bank
	for _, fipData := range fipMetaResp.GetFipMetaList() {
		if fipData.GetFipId() == account.GetFipId() {
			bankName = fipData.GetBank()
		}
	}

	var txnList []*caPb.RawAaTransaction
	txnFetchEndTs := timestampPb.Now()
	pageContext := &rpcPb.PageContextRequest{PageSize: 100}
	hasAfter := true
	for hasAfter {
		txnsResp, txnsErr := p.caClient.GetRawTxnsForAccountV2(ctx, &caPb.GetRawTxnsForAccountV2Request{
			PageContext: pageContext,
			FiType:      account.GetAccInstrumentType(),
			AccountId:   account.GetAccountId(),
			Filters: &caPb.RawTransactionFilters{
				TransactionDateAfter:  txnsStartTs,
				TransactionDateBefore: txnFetchEndTs,
			},
		})
		if te := epifigrpc.RPCError(txnsResp, txnsErr); te != nil {
			if txnsResp.GetStatus().GetCode() == rpc.StatusRecordNotFound().GetCode() {
				logger.Info(ctx, fmt.Sprintf("no transactions found for time range %v - %v", txnsStartTs, txnFetchEndTs),
					zap.Any(logger.ACCOUNT_ID, account.GetAccountId()))
				break
			}
			return nil, fmt.Errorf("failed to get raw txns for account, err: %v", txnsErr)
		}
		hasAfter = txnsResp.GetPageContext().GetHasAfter()
		pageContext = &rpcPb.PageContextRequest{
			PageSize: 100,
			Token: &rpcPb.PageContextRequest_AfterToken{
				AfterToken: txnsResp.GetPageContext().GetAfterToken(),
			},
		}
		txnList = append(txnList, txnsResp.GetRawTxnList()...)
	}
	if len(txnList) == 0 {
		return nil, errors.Wrap(epifierrors.ErrRecordNotFound, "no txns found")
	}

	if _, ok := awa[account.GetAccountId()]; !ok {
		awa[account.GetAccountId()] = &aaAnalyticsPb.AccountAnalysis{}
	}
	txns, txnsEndDate := fillRequestTxnsData(txnList, awa[account.GetAccountId()])
	if txnsEndDate == nil {
		return nil, fmt.Errorf("failed to get txns end date from all txns")
	}
	for _, txn := range txns {
		if txn.GetCurrentBalance() == "" {
			logger.Info(ctx, "empty current balance found after filling txns", zap.String(logger.TXN_ID, txn.GetTxnRefId()))
		}
	}
	newTempStartTs := timestampPb.New(time.Now().Add(time.Duration(p.conf.AaAnalytics.OldestTsFromTodayInMonths) * -1 * (30 * 24 * time.Hour)))
	return &ignosisVgPb.GetFastAnalysisRequest_Data{
		Profile: &ignosisVgPb.GetFastAnalysisRequest_Data_Profile{
			Account: &ignosisVgPb.GetFastAnalysisRequest_Data_Profile_Account{
				Number:       accountDetails.GetAccountDetails().GetMaskedAccountNumber(),
				AccType:      accountSummary.GetDepositAccountType(),
				FiType:       account.GetAccInstrumentType(),
				Bank:         bankName,
				LinkedAccRef: account.GetLinkedAccountRef(),
				FipId:        account.GetFipId(),
			},
			Holders: &ignosisVgPb.GetFastAnalysisRequest_Data_Profile_Holders{
				Type:   accountProfile.GetHolderType(),
				Holder: fillHoldersData(accountProfile.GetHolderDetailsList()),
			},
		},
		Summary: &ignosisVgPb.GetFastAnalysisRequest_Data_Summary{
			TransactionStartDate: datetimePkg.TimestampToDateInLoc(newTempStartTs, datetimePkg.IST),
			TransactionEndDate:   txnsEndDate,
			OpeningDate:          accountSummary.GetOpeningDate(),
			BalanceDateTime:      accountSummary.GetBalanceDate(),
			Branch:               accountSummary.GetBranch(),
			Ifsc:                 accountSummary.GetIfscCode(),
			MicrCode:             accountSummary.GetMicrCode(),
			CurrentBalance:       accountSummary.GetCurrentBalance(),
		},
		Transactions: txns,
	}, nil
}

func fillHoldersData(holders []*beCaExtPb.HolderDetails) []*ignosisVgPb.GetFastAnalysisRequest_Data_Profile_Holders_Holder {
	var respHolders []*ignosisVgPb.GetFastAnalysisRequest_Data_Profile_Holders_Holder
	for _, holder := range holders {
		respHolders = append(respHolders, &ignosisVgPb.GetFastAnalysisRequest_Data_Profile_Holders_Holder{
			Name: holder.GetName(),
			// TODO(Brijesh): Populate mobile number after addressing redundant prefix/suffix issues in raw mobile numbers in AA data
			Mobile:  nil,
			Address: holder.GetAddress(),
			Email:   holder.GetEmail(),
			Pan:     holder.GetPan(),
		})
	}
	return respHolders
}

func fillRequestTxnsData(txns []*caPb.RawAaTransaction, awa *aaAnalyticsPb.AccountAnalysis) ([]*ignosisVgPb.GetFastAnalysisRequest_Data_Transaction, *date.Date) {
	var reqTxns []*ignosisVgPb.GetFastAnalysisRequest_Data_Transaction
	var txnsEndTs *timestampPb.Timestamp
	var txnsStartTs *timestampPb.Timestamp
	for _, rawTxn := range txns {
		txn := rawTxn.GetAaTransaction()
		if txn.GetUpdatedAt() != nil && (txnsEndTs == nil || txn.GetUpdatedAt().AsTime().After(txnsEndTs.AsTime())) {
			txnsEndTs = txn.GetUpdatedAt() // since the txn attributes can be updated after the txn date
		}
		if txn.GetUpdatedAt() != nil && (txnsStartTs == nil || txn.GetUpdatedAt().AsTime().Before(txnsStartTs.AsTime())) {
			txnsStartTs = txn.GetUpdatedAt() // since the txn attributes can be updated after the txn date
		}
		reqTxns = append(reqTxns, &ignosisVgPb.GetFastAnalysisRequest_Data_Transaction{
			Type:                 txn.GetType(),
			Amount:               txn.GetAmount(),
			Narration:            txn.GetNarration(),
			CurrentBalance:       rawTxn.GetAaDepositTransaction().GetDepositTxnMeta().GetCurrentBalance(),
			TransactionTimestamp: txn.GetTransactionDate(),
			TxnRefId:             txn.GetId(),
		})
	}
	awa.LastAnalysedTransactionTs = txnsEndTs
	if awa.GetFirstAnalysedTransactionTs() == nil || txnsStartTs.AsTime().Before(awa.GetFirstAnalysedTransactionTs().AsTime()) {
		awa.FirstAnalysedTransactionTs = txnsStartTs
	}
	return reqTxns, datetimePkg.TimestampToDateInLoc(txnsEndTs, datetimePkg.IST)
}
