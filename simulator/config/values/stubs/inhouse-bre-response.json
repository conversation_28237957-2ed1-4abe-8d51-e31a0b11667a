{"GetFeaturesData": {"DEFAULT": {"TestCaseDescription": "default_case:comprehensive_credit_data", "FeaturesResponseDataList": [{"Identifiers": {"ActorId": "AC3gjp6DWQVr250718"}, "FeatureValueMap": {"CreditReportsLendingRawV3.al_max_amt_ever": 0, "CreditReportsLendingRawV3.bl_flag_ever": 0, "CreditReportsLendingRawV3.bureau_score": 860, "CreditReportsLendingRawV3.bureau_vintage": 188, "CreditReportsLendingRawV3.cc_balance": 11000, "CreditReportsLendingRawV3.cc_current_utilisation_perc": 2.1999999999999997, "CreditReportsLendingRawV3.cc_flag_ever": 1, "CreditReportsLendingRawV3.cc_max_amt_ever": 500000, "CreditReportsLendingRawV3.current_foir": 4.***************, "CreditReportsLendingRawV3.date_of_birth": "1991-02-02T00:00:00+00:00", "CreditReportsLendingRawV3.final_income": 125000, "CreditReportsLendingRawV3.hl_max_amt_ever": null, "CreditReportsLendingRawV3.lap_flag_ever": 1, "CreditReportsLendingRawV3.overall_count_inquiries_l1m": 4, "CreditReportsLendingRawV3.overall_count_inquiries_l3m": 4, "CreditReportsLendingRawV3.overall_current_overdue_amt": 47000, "CreditReportsLendingRawV3.overall_max_dpd_ever": 165, "CreditReportsLendingRawV3.overall_max_dpd_l12m": 165, "CreditReportsLendingRawV3.overall_max_dpd_l1m": 160, "CreditReportsLendingRawV3.overall_max_dpd_l24m": 165, "CreditReportsLendingRawV3.overall_max_dpd_l36m": 165, "CreditReportsLendingRawV3.overall_max_dpd_l3m": 165, "CreditReportsLendingRawV3.overall_rs_flag_ever": 0, "CreditReportsLendingRawV3.overall_sf_wd_wo_flag_ever": 0, "CreditReportsLendingRawV3.overall_sub_dbt_lss_flag_ever": 0, "CreditReportsLendingRawV3.phone_number_match": 0, "CreditReportsLendingRawV3.pincodes": ["412207"], "CreditReportsLendingRawV3.pl_gte_10k_count_active_trades": 0, "CreditReportsLendingRawV3.pl_gte_10k_count_trades_opened_l12m": 0, "CreditReportsLendingRawV3.pl_max_amt_ever": null, "CreditReportsLendingRawV3.report_date": "2021-06-20T00:00:00+00:00", "CreditReportsLendingRawV3.report_id": "1624136584406", "CreditReportsLendingRawV3.unsecured_count_inquiries_l3m": 3, "CreditReportsLendingRawV3.unsecured_wo_cc_count_trades_opened_l3m": 0}}]}, "AC3gjp6DWQVr250718": {"TestCaseDescription": "comprehensive_credit_data:al_max_amt_ever:50000", "FeaturesResponseDataList": [{"Identifiers": {"ActorId": "AC3gjp6DWQVr250718"}, "FeatureValueMap": {"CreditReportsLendingRawV3.al_max_amt_ever": 50000, "CreditReportsLendingRawV3.bl_flag_ever": 0, "CreditReportsLendingRawV3.bureau_score": 746, "CreditReportsLendingRawV3.bureau_vintage": 188, "CreditReportsLendingRawV3.cc_balance": 11000, "CreditReportsLendingRawV3.cc_current_utilisation_perc": 2.1999999999999997, "CreditReportsLendingRawV3.cc_flag_ever": 1, "CreditReportsLendingRawV3.cc_max_amt_ever": 500000, "CreditReportsLendingRawV3.current_foir": 4.***************, "CreditReportsLendingRawV3.date_of_birth": "1991-02-02T00:00:00+00:00", "CreditReportsLendingRawV3.final_income": 125000, "CreditReportsLendingRawV3.hl_max_amt_ever": null, "CreditReportsLendingRawV3.lap_flag_ever": 1, "CreditReportsLendingRawV3.overall_count_inquiries_l1m": 4, "CreditReportsLendingRawV3.overall_count_inquiries_l3m": 4, "CreditReportsLendingRawV3.overall_current_overdue_amt": 47000, "CreditReportsLendingRawV3.overall_max_dpd_ever": 165, "CreditReportsLendingRawV3.overall_max_dpd_l12m": 165, "CreditReportsLendingRawV3.overall_max_dpd_l1m": 160, "CreditReportsLendingRawV3.overall_max_dpd_l24m": 165, "CreditReportsLendingRawV3.overall_max_dpd_l36m": 165, "CreditReportsLendingRawV3.overall_max_dpd_l3m": 165, "CreditReportsLendingRawV3.overall_rs_flag_ever": 0, "CreditReportsLendingRawV3.overall_sf_wd_wo_flag_ever": 0, "CreditReportsLendingRawV3.overall_sub_dbt_lss_flag_ever": 0, "CreditReportsLendingRawV3.phone_number_match": 0, "CreditReportsLendingRawV3.pincodes": ["412207"], "CreditReportsLendingRawV3.pl_gte_10k_count_active_trades": 0, "CreditReportsLendingRawV3.pl_gte_10k_count_trades_opened_l12m": 0, "CreditReportsLendingRawV3.pl_max_amt_ever": null, "CreditReportsLendingRawV3.report_date": "2021-06-20T00:00:00+00:00", "CreditReportsLendingRawV3.report_id": "1624136584406", "CreditReportsLendingRawV3.unsecured_count_inquiries_l3m": 3, "CreditReportsLendingRawV3.unsecured_wo_cc_count_trades_opened_l3m": 0}}]}, "AC3gjp6DWQVr250719": {"TestCaseDescription": "post_2021_flags:all_zero", "FeaturesResponseDataList": [{"Identifiers": {"ActorId": "AC7Yzf5KFSPs250725"}, "FeatureValueMap": {"CreditReportsLendingRawV3.dbt_post_2021_flag": 0, "CreditReportsLendingRawV3.los_post_2021_flag": 0, "CreditReportsLendingRawV3.overall_current_overdue_amt_post_2021": null, "CreditReportsLendingRawV3.report_date": "2021-06-20T00:00:00+00:00", "CreditReportsLendingRawV3.report_id": "1624136584406", "CreditReportsLendingRawV3.sf_post_2021_flag": 0, "CreditReportsLendingRawV3.wo_amt_post_2021": null}}], "experianReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND"}, "AC3gjp6DWQVr250720": {"TestCaseDescription": "overdue_amt_post_2021:500", "FeaturesResponseDataList": [{"Identifiers": {"ActorId": "AC3gjp6DWQVr250720"}, "FeatureValueMap": {"CreditReportsLendingRawV3.overall_current_overdue_amt_post_2021": 500, "CreditReportsLendingRawV3.dbt_post_2021_flag": 0, "CreditReportsLendingRawV3.los_post_2021_flag": 0, "CreditReportsLendingRawV3.report_date": "2021-06-20T00:00:00+00:00", "CreditReportsLendingRawV3.report_id": "1624136584406", "CreditReportsLendingRawV3.sf_post_2021_flag": 0, "CreditReportsLendingRawV3.wo_amt_post_2021": null}}], "experianReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND"}, "AC3gjp6DWQVr250721": {"TestCaseDescription": "overdue_amt_post_2021:1000", "FeaturesResponseDataList": [{"Identifiers": {"ActorId": "AC3gjp6DWQVr250721"}, "FeatureValueMap": {"CreditReportsLendingRawV3.overall_current_overdue_amt_post_2021": 1000, "CreditReportsLendingRawV3.dbt_post_2021_flag": 0, "CreditReportsLendingRawV3.los_post_2021_flag": 0, "CreditReportsLendingRawV3.report_date": "2021-06-20T00:00:00+00:00", "CreditReportsLendingRawV3.report_id": "1624136584406", "CreditReportsLendingRawV3.sf_post_2021_flag": 0, "CreditReportsLendingRawV3.wo_amt_post_2021": null}}], "experianReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND"}, "AC3gjp6DWQVr250722": {"TestCaseDescription": "overdue_amt_post_2021:1500", "FeaturesResponseDataList": [{"Identifiers": {"ActorId": "AC3gjp6DWQVr250722"}, "FeatureValueMap": {"CreditReportsLendingRawV3.overall_current_overdue_amt_post_2021": 1500, "CreditReportsLendingRawV3.dbt_post_2021_flag": 0, "CreditReportsLendingRawV3.los_post_2021_flag": 0, "CreditReportsLendingRawV3.report_date": "2021-06-20T00:00:00+00:00", "CreditReportsLendingRawV3.report_id": "1624136584406", "CreditReportsLendingRawV3.sf_post_2021_flag": 0, "CreditReportsLendingRawV3.wo_amt_post_2021": null}}], "experianReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND"}, "AC3gjp6DWQVr250723": {"TestCaseDescription": "overdue_amt_post_2021:0", "FeaturesResponseDataList": [{"Identifiers": {"ActorId": "AC3gjp6DWQVr250723"}, "FeatureValueMap": {"CreditReportsLendingRawV3.overall_current_overdue_amt_post_2021": 0, "CreditReportsLendingRawV3.dbt_post_2021_flag": 0, "CreditReportsLendingRawV3.los_post_2021_flag": 0, "CreditReportsLendingRawV3.report_date": "2021-06-20T00:00:00+00:00", "CreditReportsLendingRawV3.report_id": "1624136584406", "CreditReportsLendingRawV3.sf_post_2021_flag": 0, "CreditReportsLendingRawV3.wo_amt_post_2021": null}}], "experianReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND"}, "AC3gjp6DWQVr250724": {"TestCaseDescription": "wo_amt_post_2021:500", "FeaturesResponseDataList": [{"Identifiers": {"ActorId": "AC3gjp6DWQVr250724"}, "FeatureValueMap": {"CreditReportsLendingRawV3.overall_current_overdue_amt_post_2021": null, "CreditReportsLendingRawV3.dbt_post_2021_flag": 0, "CreditReportsLendingRawV3.los_post_2021_flag": 0, "CreditReportsLendingRawV3.report_date": "2021-06-20T00:00:00+00:00", "CreditReportsLendingRawV3.report_id": "1624136584406", "CreditReportsLendingRawV3.sf_post_2021_flag": 0, "CreditReportsLendingRawV3.wo_amt_post_2021": null}}], "experianReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND"}, "AC3gjp6DWQVr250725": {"TestCaseDescription": "wo_amt_post_2021:1000", "FeaturesResponseDataList": [{"Identifiers": {"ActorId": "AC3gjp6DWQVr250725"}, "FeatureValueMap": {"CreditReportsLendingRawV3.overall_current_overdue_amt_post_2021": null, "CreditReportsLendingRawV3.dbt_post_2021_flag": 0, "CreditReportsLendingRawV3.los_post_2021_flag": 0, "CreditReportsLendingRawV3.report_date": "2021-06-20T00:00:00+00:00", "CreditReportsLendingRawV3.report_id": "1624136584406", "CreditReportsLendingRawV3.sf_post_2021_flag": 0, "CreditReportsLendingRawV3.wo_amt_post_2021": 1000}}], "experianReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND"}, "AC3gjp6DWQVr250726": {"TestCaseDescription": "wo_amt_post_2021:1500", "FeaturesResponseDataList": [{"Identifiers": {"ActorId": "AC3gjp6DWQVr250726"}, "FeatureValueMap": {"CreditReportsLendingRawV3.overall_current_overdue_amt_post_2021": null, "CreditReportsLendingRawV3.dbt_post_2021_flag": 0, "CreditReportsLendingRawV3.los_post_2021_flag": 0, "CreditReportsLendingRawV3.report_date": "2021-06-20T00:00:00+00:00", "CreditReportsLendingRawV3.report_id": "1624136584406", "CreditReportsLendingRawV3.sf_post_2021_flag": 0, "CreditReportsLendingRawV3.wo_amt_post_2021": 1500}}], "experianReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND"}, "AC3gjp6DWQVr250727": {"TestCaseDescription": "wo_amt_post_2021:0", "FeaturesResponseDataList": [{"Identifiers": {"ActorId": "AC3gjp6DWQVr250727"}, "FeatureValueMap": {"CreditReportsLendingRawV3.overall_current_overdue_amt_post_2021": null, "CreditReportsLendingRawV3.dbt_post_2021_flag": 0, "CreditReportsLendingRawV3.los_post_2021_flag": 0, "CreditReportsLendingRawV3.report_date": "2021-06-20T00:00:00+00:00", "CreditReportsLendingRawV3.report_id": "1624136584406", "CreditReportsLendingRawV3.sf_post_2021_flag": 0, "CreditReportsLendingRawV3.wo_amt_post_2021": 0}}], "experianReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND"}, "AC3gjp6DWQVr250728": {"TestCaseDescription": "wo_amt_post_2021:null", "FeaturesResponseDataList": [{"Identifiers": {"ActorId": "AC3gjp6DWQVr250728"}, "FeatureValueMap": {"CreditReportsLendingRawV3.overall_current_overdue_amt_post_2021": null, "CreditReportsLendingRawV3.dbt_post_2021_flag": 0, "CreditReportsLendingRawV3.los_post_2021_flag": 0, "CreditReportsLendingRawV3.report_date": "2021-06-20T00:00:00+00:00", "CreditReportsLendingRawV3.report_id": "1624136584406", "CreditReportsLendingRawV3.sf_post_2021_flag": 0, "CreditReportsLendingRawV3.wo_amt_post_2021": null}}], "experianReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND"}, "AC3gjp6DWQVr250729": {"TestCaseDescription": "all_post_2021_flags_null", "FeaturesResponseDataList": [{"Identifiers": {"ActorId": "AC3gjp6DWQVr250729"}, "FeatureValueMap": {"CreditReportsLendingRawV3.overall_current_overdue_amt_post_2021": null, "CreditReportsLendingRawV3.dbt_post_2021_flag": 0, "CreditReportsLendingRawV3.los_post_2021_flag": 0, "CreditReportsLendingRawV3.report_date": "2021-06-20T00:00:00+00:00", "CreditReportsLendingRawV3.report_id": "1624136584406", "CreditReportsLendingRawV3.sf_post_2021_flag": 0, "CreditReportsLendingRawV3.wo_amt_post_2021": null}}], "experianReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND"}, "AC3gjp6DWQVr250730": {"TestCaseDescription": "sf_post_2021_flag:1", "FeaturesResponseDataList": [{"Identifiers": {"ActorId": "AC3gjp6DWQVr250730"}, "FeatureValueMap": {"CreditReportsLendingRawV3.overall_current_overdue_amt_post_2021": null, "CreditReportsLendingRawV3.dbt_post_2021_flag": 0, "CreditReportsLendingRawV3.los_post_2021_flag": 0, "CreditReportsLendingRawV3.report_date": "2021-06-20T00:00:00+00:00", "CreditReportsLendingRawV3.report_id": "1624136584406", "CreditReportsLendingRawV3.sf_post_2021_flag": 1, "CreditReportsLendingRawV3.wo_amt_post_2021": null}}], "experianReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND"}, "AC3gjp6DWQVr250731": {"TestCaseDescription": "sf_post_2021_flag:null", "FeaturesResponseDataList": [{"Identifiers": {"ActorId": "AC3gjp6DWQVr250731"}, "FeatureValueMap": {"CreditReportsLendingRawV3.overall_current_overdue_amt_post_2021": null, "CreditReportsLendingRawV3.dbt_post_2021_flag": 0, "CreditReportsLendingRawV3.los_post_2021_flag": 0, "CreditReportsLendingRawV3.report_date": "2021-06-20T00:00:00+00:00", "CreditReportsLendingRawV3.report_id": "1624136584406", "CreditReportsLendingRawV3.sf_post_2021_flag": null, "CreditReportsLendingRawV3.wo_amt_post_2021": null}}], "experianReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND"}, "AC3gjp6DWQVr250732": {"TestCaseDescription": "sf_post_2021_flag:0", "FeaturesResponseDataList": [{"Identifiers": {"ActorId": "AC3gjp6DWQVr250732"}, "FeatureValueMap": {"CreditReportsLendingRawV3.overall_current_overdue_amt_post_2021": null, "CreditReportsLendingRawV3.dbt_post_2021_flag": 0, "CreditReportsLendingRawV3.los_post_2021_flag": 0, "CreditReportsLendingRawV3.report_date": "2021-06-20T00:00:00+00:00", "CreditReportsLendingRawV3.report_id": "1624136584406", "CreditReportsLendingRawV3.sf_post_2021_flag": 0, "CreditReportsLendingRawV3.wo_amt_post_2021": null}}], "experianReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND"}, "AC3gjp6DWQVr250733": {"TestCaseDescription": "dbt_post_2021_flag:1", "FeaturesResponseDataList": [{"Identifiers": {"ActorId": "AC3gjp6DWQVr250733"}, "FeatureValueMap": {"CreditReportsLendingRawV3.overall_current_overdue_amt_post_2021": null, "CreditReportsLendingRawV3.dbt_post_2021_flag": 1, "CreditReportsLendingRawV3.los_post_2021_flag": 0, "CreditReportsLendingRawV3.report_date": "2021-06-20T00:00:00+00:00", "CreditReportsLendingRawV3.report_id": "1624136584406", "CreditReportsLendingRawV3.sf_post_2021_flag": 0, "CreditReportsLendingRawV3.wo_amt_post_2021": null}}], "experianReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND"}, "AC3gjp6DWQVr250734": {"TestCaseDescription": "dbt_post_2021_flag:null", "FeaturesResponseDataList": [{"Identifiers": {"ActorId": "AC3gjp6DWQVr250734"}, "FeatureValueMap": {"CreditReportsLendingRawV3.overall_current_overdue_amt_post_2021": null, "CreditReportsLendingRawV3.dbt_post_2021_flag": null, "CreditReportsLendingRawV3.los_post_2021_flag": 0, "CreditReportsLendingRawV3.report_date": "2021-06-20T00:00:00+00:00", "CreditReportsLendingRawV3.report_id": "1624136584406", "CreditReportsLendingRawV3.sf_post_2021_flag": 0, "CreditReportsLendingRawV3.wo_amt_post_2021": null}}], "experianReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND"}, "AC3gjp6DWQVr250735": {"TestCaseDescription": "dbt_post_2021_flag:0", "FeaturesResponseDataList": [{"Identifiers": {"ActorId": "AC3gjp6DWQVr250735"}, "FeatureValueMap": {"CreditReportsLendingRawV3.overall_current_overdue_amt_post_2021": null, "CreditReportsLendingRawV3.dbt_post_2021_flag": 0, "CreditReportsLendingRawV3.los_post_2021_flag": 0, "CreditReportsLendingRawV3.report_date": "2021-06-20T00:00:00+00:00", "CreditReportsLendingRawV3.report_id": "1624136584406", "CreditReportsLendingRawV3.sf_post_2021_flag": 0, "CreditReportsLendingRawV3.wo_amt_post_2021": null}}], "experianReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND"}, "AC3gjp6DWQVr250736": {"TestCaseDescription": "los_post_2021_flag:1", "FeaturesResponseDataList": [{"Identifiers": {"ActorId": "AC3gjp6DWQVr250736"}, "FeatureValueMap": {"CreditReportsLendingRawV3.overall_current_overdue_amt_post_2021": null, "CreditReportsLendingRawV3.dbt_post_2021_flag": 0, "CreditReportsLendingRawV3.los_post_2021_flag": 1, "CreditReportsLendingRawV3.report_date": "2021-06-20T00:00:00+00:00", "CreditReportsLendingRawV3.report_id": "1624136584406", "CreditReportsLendingRawV3.sf_post_2021_flag": 0, "CreditReportsLendingRawV3.wo_amt_post_2021": null}}], "experianReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND"}, "AC3gjp6DWQVr250737": {"TestCaseDescription": "los_post_2021_flag:null", "FeaturesResponseDataList": [{"Identifiers": {"ActorId": "AC3gjp6DWQVr250737"}, "FeatureValueMap": {"CreditReportsLendingRawV3.overall_current_overdue_amt_post_2021": null, "CreditReportsLendingRawV3.dbt_post_2021_flag": 0, "CreditReportsLendingRawV3.los_post_2021_flag": null, "CreditReportsLendingRawV3.report_date": "2021-06-20T00:00:00+00:00", "CreditReportsLendingRawV3.report_id": "1624136584406", "CreditReportsLendingRawV3.sf_post_2021_flag": 0, "CreditReportsLendingRawV3.wo_amt_post_2021": null}}], "experianReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND"}, "AC3gjp6DWQVr250738": {"TestCaseDescription": "report_date:2024-01-01", "FeaturesResponseDataList": [{"Identifiers": {"ActorId": "AC3gjp6DWQVr250738"}, "FeatureValueMap": {"CreditReportsLendingRawV3.overall_current_overdue_amt_post_2021": null, "CreditReportsLendingRawV3.dbt_post_2021_flag": 0, "CreditReportsLendingRawV3.los_post_2021_flag": 0, "CreditReportsLendingRawV3.report_date": "2024-01-01T00:00:00+00:00", "CreditReportsLendingRawV3.report_id": "1624136584406", "CreditReportsLendingRawV3.sf_post_2021_flag": 0, "CreditReportsLendingRawV3.wo_amt_post_2021": null}}], "experianReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND"}, "AC3gjp6DWQVr250739": {"TestCaseDescription": "report_date:2024-06-15", "FeaturesResponseDataList": [{"Identifiers": {"ActorId": "AC3gjp6DWQVr250739"}, "FeatureValueMap": {"CreditReportsLendingRawV3.overall_current_overdue_amt_post_2021": null, "CreditReportsLendingRawV3.dbt_post_2021_flag": 0, "CreditReportsLendingRawV3.los_post_2021_flag": 0, "CreditReportsLendingRawV3.report_date": "2024-06-15T00:00:00+00:00", "CreditReportsLendingRawV3.report_id": "1624136584406", "CreditReportsLendingRawV3.sf_post_2021_flag": 0, "CreditReportsLendingRawV3.wo_amt_post_2021": null}}], "experianReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND"}, "AC3gjp6DWQVr250740": {"TestCaseDescription": "report_date:null", "FeaturesResponseDataList": [{"Identifiers": {"ActorId": "AC3gjp6DWQVr250740"}, "FeatureValueMap": {"CreditReportsLendingRawV3.overall_current_overdue_amt_post_2021": null, "CreditReportsLendingRawV3.dbt_post_2021_flag": 0, "CreditReportsLendingRawV3.los_post_2021_flag": 0, "CreditReportsLendingRawV3.report_date": null, "CreditReportsLendingRawV3.report_id": "1624136584406", "CreditReportsLendingRawV3.sf_post_2021_flag": 0, "CreditReportsLendingRawV3.wo_amt_post_2021": null}}], "experianReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND"}, "AC3gjp6DWQVr250756": {"TestCaseDescription": "experian_bureau_score:550", "FeaturesResponseDataList": [{"Identifiers": {"ActorId": "AC3gjp6DWQVr250756"}, "FeatureValueMap": {"CreditReportsLendingRawV3.bureau_score": 550, "CreditReportsLendingRawV3.overall_wo_l24m_flag": 0, "CreditReportsLendingRawV3.active_max_dpd_l3m": 10, "CreditReportsLendingRawV3.active_max_dpd_l12m": 80, "CreditReportsLendingRawV3.pl_count_inquiries_l3m": 8, "CreditReportsLendingRawV3.report_date": "2021-06-20T00:00:00+00:00", "CreditReportsLendingRawV3.report_id": "1624136584406"}}]}, "AC3gjp6DWQVr250757": {"TestCaseDescription": "experian_bureau_score:600", "FeaturesResponseDataList": [{"Identifiers": {"ActorId": "AC3gjp6DWQVr250757"}, "FeatureValueMap": {"CreditReportsLendingRawV3.bureau_score": 600, "CreditReportsLendingRawV3.overall_wo_l24m_flag": 0, "CreditReportsLendingRawV3.active_max_dpd_l3m": 10, "CreditReportsLendingRawV3.active_max_dpd_l12m": 80, "CreditReportsLendingRawV3.pl_count_inquiries_l3m": 8, "CreditReportsLendingRawV3.report_date": "2021-06-20T00:00:00+00:00", "CreditReportsLendingRawV3.report_id": "1624136584406"}}]}, "AC3gjp6DWQVr250758": {"TestCaseDescription": "experian_bureau_score:700", "FeaturesResponseDataList": [{"Identifiers": {"ActorId": "AC3gjp6DWQVr250758"}, "FeatureValueMap": {"CreditReportsLendingRawV3.bureau_score": 700, "CreditReportsLendingRawV3.overall_wo_l24m_flag": 0, "CreditReportsLendingRawV3.active_max_dpd_l3m": 10, "CreditReportsLendingRawV3.active_max_dpd_l12m": 80, "CreditReportsLendingRawV3.pl_count_inquiries_l3m": 8, "CreditReportsLendingRawV3.report_date": "2021-06-20T00:00:00+00:00", "CreditReportsLendingRawV3.report_id": "1624136584406"}}]}, "AC3gjp6DWQVr250759": {"TestCaseDescription": "experian_overall_wo_l24m_flag:0", "FeaturesResponseDataList": [{"Identifiers": {"ActorId": "AC3gjp6DWQVr250759"}, "FeatureValueMap": {"CreditReportsLendingRawV3.bureau_score": 700, "CreditReportsLendingRawV3.overall_wo_l24m_flag": 0, "CreditReportsLendingRawV3.active_max_dpd_l3m": 10, "CreditReportsLendingRawV3.active_max_dpd_l12m": 80, "CreditReportsLendingRawV3.pl_count_inquiries_l3m": 8, "CreditReportsLendingRawV3.report_date": "2021-06-20T00:00:00+00:00", "CreditReportsLendingRawV3.report_id": "1624136584406"}}]}, "AC3gjp6DWQVr250760": {"TestCaseDescription": "experian_overall_wo_l24m_flag:1", "FeaturesResponseDataList": [{"Identifiers": {"ActorId": "AC3gjp6DWQVr250760"}, "FeatureValueMap": {"CreditReportsLendingRawV3.bureau_score": 700, "CreditReportsLendingRawV3.overall_wo_l24m_flag": 1, "CreditReportsLendingRawV3.active_max_dpd_l3m": 10, "CreditReportsLendingRawV3.active_max_dpd_l12m": 80, "CreditReportsLendingRawV3.pl_count_inquiries_l3m": 8, "CreditReportsLendingRawV3.report_date": "2021-06-20T00:00:00+00:00", "CreditReportsLendingRawV3.report_id": "1624136584406"}}]}, "AC3gjp6DWQVr250761": {"TestCaseDescription": "experian_active_max_dpd_l3m:10", "FeaturesResponseDataList": [{"Identifiers": {"ActorId": "AC3gjp6DWQVr250761"}, "FeatureValueMap": {"CreditReportsLendingRawV3.bureau_score": 700, "CreditReportsLendingRawV3.overall_wo_l24m_flag": 0, "CreditReportsLendingRawV3.active_max_dpd_l3m": 10, "CreditReportsLendingRawV3.active_max_dpd_l12m": 80, "CreditReportsLendingRawV3.pl_count_inquiries_l3m": 8, "CreditReportsLendingRawV3.report_date": "2021-06-20T00:00:00+00:00", "CreditReportsLendingRawV3.report_id": "1624136584406"}}]}, "AC3gjp6DWQVr250762": {"TestCaseDescription": "experian_active_max_dpd_l3m:15", "FeaturesResponseDataList": [{"Identifiers": {"ActorId": "AC3gjp6DWQVr250762"}, "FeatureValueMap": {"CreditReportsLendingRawV3.bureau_score": 700, "CreditReportsLendingRawV3.overall_wo_l24m_flag": 0, "CreditReportsLendingRawV3.active_max_dpd_l3m": 15, "CreditReportsLendingRawV3.active_max_dpd_l12m": 80, "CreditReportsLendingRawV3.pl_count_inquiries_l3m": 8, "CreditReportsLendingRawV3.report_date": "2021-06-20T00:00:00+00:00", "CreditReportsLendingRawV3.report_id": "1624136584406"}}]}, "AC3gjp6DWQVr250763": {"TestCaseDescription": "experian_active_max_dpd_l3m:20", "FeaturesResponseDataList": [{"Identifiers": {"ActorId": "AC3gjp6DWQVr250763"}, "FeatureValueMap": {"CreditReportsLendingRawV3.bureau_score": 700, "CreditReportsLendingRawV3.overall_wo_l24m_flag": 0, "CreditReportsLendingRawV3.active_max_dpd_l3m": 20, "CreditReportsLendingRawV3.active_max_dpd_l12m": 80, "CreditReportsLendingRawV3.pl_count_inquiries_l3m": 8, "CreditReportsLendingRawV3.report_date": "2021-06-20T00:00:00+00:00", "CreditReportsLendingRawV3.report_id": "1624136584406"}}]}, "AC3gjp6DWQVr250764": {"TestCaseDescription": "experian_active_max_dpd_l12m:80", "FeaturesResponseDataList": [{"Identifiers": {"ActorId": "AC3gjp6DWQVr250764"}, "FeatureValueMap": {"CreditReportsLendingRawV3.bureau_score": 700, "CreditReportsLendingRawV3.overall_wo_l24m_flag": 0, "CreditReportsLendingRawV3.active_max_dpd_l3m": 10, "CreditReportsLendingRawV3.active_max_dpd_l12m": 80, "CreditReportsLendingRawV3.pl_count_inquiries_l3m": 8, "CreditReportsLendingRawV3.report_date": "2021-06-20T00:00:00+00:00", "CreditReportsLendingRawV3.report_id": "1624136584406"}}]}, "AC3gjp6DWQVr250765": {"TestCaseDescription": "experian_active_max_dpd_l12m:90", "FeaturesResponseDataList": [{"Identifiers": {"ActorId": "AC3gjp6DWQVr250765"}, "FeatureValueMap": {"CreditReportsLendingRawV3.bureau_score": 700, "CreditReportsLendingRawV3.overall_wo_l24m_flag": 0, "CreditReportsLendingRawV3.active_max_dpd_l3m": 10, "CreditReportsLendingRawV3.active_max_dpd_l12m": 90, "CreditReportsLendingRawV3.pl_count_inquiries_l3m": 8, "CreditReportsLendingRawV3.report_date": "2021-06-20T00:00:00+00:00", "CreditReportsLendingRawV3.report_id": "1624136584406"}}]}, "AC3gjp6DWQVr250766": {"TestCaseDescription": "experian_active_max_dpd_l12m:100", "FeaturesResponseDataList": [{"Identifiers": {"ActorId": "AC3gjp6DWQVr250766"}, "FeatureValueMap": {"CreditReportsLendingRawV3.bureau_score": 700, "CreditReportsLendingRawV3.overall_wo_l24m_flag": 0, "CreditReportsLendingRawV3.active_max_dpd_l3m": 10, "CreditReportsLendingRawV3.active_max_dpd_l12m": 100, "CreditReportsLendingRawV3.pl_count_inquiries_l3m": 8, "CreditReportsLendingRawV3.report_date": "2021-06-20T00:00:00+00:00", "CreditReportsLendingRawV3.report_id": "1624136584406"}}]}, "AC3gjp6DWQVr250767": {"TestCaseDescription": "experian_pl_count_inquiries_l3m:8", "FeaturesResponseDataList": [{"Identifiers": {"ActorId": "AC3gjp6DWQVr250767"}, "FeatureValueMap": {"CreditReportsLendingRawV3.bureau_score": 700, "CreditReportsLendingRawV3.overall_wo_l24m_flag": 0, "CreditReportsLendingRawV3.active_max_dpd_l3m": 10, "CreditReportsLendingRawV3.active_max_dpd_l12m": 80, "CreditReportsLendingRawV3.pl_count_inquiries_l3m": 8, "CreditReportsLendingRawV3.report_date": "2021-06-20T00:00:00+00:00", "CreditReportsLendingRawV3.report_id": "1624136584406"}}]}, "AC3gjp6DWQVr250768": {"TestCaseDescription": "experian_pl_count_inquiries_l3m:10", "FeaturesResponseDataList": [{"Identifiers": {"ActorId": "AC3gjp6DWQVr250768"}, "FeatureValueMap": {"CreditReportsLendingRawV3.bureau_score": 700, "CreditReportsLendingRawV3.overall_wo_l24m_flag": 0, "CreditReportsLendingRawV3.active_max_dpd_l3m": 10, "CreditReportsLendingRawV3.active_max_dpd_l12m": 80, "CreditReportsLendingRawV3.pl_count_inquiries_l3m": 10, "CreditReportsLendingRawV3.report_date": "2021-06-20T00:00:00+00:00", "CreditReportsLendingRawV3.report_id": "1624136584406"}}]}, "AC3gjp6DWQVr250769": {"TestCaseDescription": "experian_pl_count_inquiries_l3m:15", "FeaturesResponseDataList": [{"Identifiers": {"ActorId": "AC3gjp6DWQVr250769"}, "FeatureValueMap": {"CreditReportsLendingRawV3.bureau_score": 700, "CreditReportsLendingRawV3.overall_wo_l24m_flag": 0, "CreditReportsLendingRawV3.active_max_dpd_l3m": 10, "CreditReportsLendingRawV3.active_max_dpd_l12m": 80, "CreditReportsLendingRawV3.pl_count_inquiries_l3m": 15, "CreditReportsLendingRawV3.report_date": "2021-06-20T00:00:00+00:00", "CreditReportsLendingRawV3.report_id": "1624136584406"}}]}, "AC3gjp6DWQVr250741": {"TestCaseDescription": "data_not_found", "FeaturesResponseDataList": [{"Identifiers": {"ActorId": "AC3gjp6DWQVr250741"}, "FeatureValueMap": {}}], "experianReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_NOT_FOUND"}}, "GetPdScore": {"DEFAULT": {"TestCaseDescription": "default_pd_score", "pdScore": 0.03813092282806667, "pdScoreVersion": "v1"}, "AC3gjp6DWQVr250718": {"TestCaseDescription": "pd_score:0.03813092282806667", "pdScore": 0.03813092282806667, "pdScoreVersion": "v1"}}, "GetCibilReportFeatures": {"DEFAULT": {"TestCaseDescription": "default_cibil_comprehensive_data", "cibilReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND", "featureValueMap": {"CibilReportAmts.cc_max_credit_limit_active": null, "CibilReportAmts.overall_current_overdue_amt": -80, "CibilReportCategoriesDerived.overall_thickness_category": 5, "CibilReportCounts.overall_max_dpd_l12m_ref_max_payment_start_date": 111, "CibilReportCounts.overall_max_dpd_l6m_ref_max_payment_start_date": 111, "CibilReportCounts.pl_count_trades_opened_l1m": 0, "CibilReportCounts.pl_gte_10k_count_trades_opened_l12m": 0, "CibilReportCounts.unsecured_count_inquiries_l1m_excl_report_dt": 1, "CibilReportCounts.unsecured_count_inquiries_l3m_excl_report_dt": 1, "CibilReportCounts.unsecured_count_inquiries_l7d_excl_report_dt": null, "CibilReportCounts.unsecured_wo_cc_gte10k_count_trades_opened_l6m_active_with_current_balance_gte5k": 0, "CibilReportFlags.bl_active_flag": 0, "CibilReportFlags.cc_active_flag": 0, "CibilReportFlags.joint_liability_group_active_flag": 0, "CibilReportFlags.overall_kcc_active_flag": 0, "CibilReportFlags.overall_rs_flag_l36m": 0, "CibilReportFlags.overall_sf_wd_wo_flag_ever": 1, "CibilReportFlags.overall_sub_dbt_lss_flag_ever": 0, "CibilReportIncome.cc_util_max_limit_active_card": null, "CibilReportIncome.emi_m1": null, "CibilReportIncome.final_income": null, "CibilReportIncome.max_cc_util_active_card": null, "CibilReportMetadata.bureau_score": 676, "CibilReportMetadata.date_of_birth": "1990-01-01T00:00:00+00:00", "CibilReportMetadata.phone_number_match": 0, "CibilReportMetadata.pincodes": [], "CibilReportMetadata.report_date": "2022-08-02T10:58:19+00:00", "CibilReportMetadata.report_id": "2157168019", "CibilReportVintage.overall_months_since_latest_90p_dpd_ref_max_payment_start_dt": 1, "CibilReportVintage.overall_months_since_latest_90p_dpd_ref_report_dt": 67, "CibilReportVintage.overall_months_since_latest_xp_dpd": 1, "CibilReportVintage.overall_months_since_latest_xp_dpd_ref_report_dt": 67}}, "AC3gjp6DWQVr250718": {"TestCaseDescription": "cibil_comprehensive_data:al_max_amt_ever:50000", "cibilReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND", "featureValueMap": {"CibilReportAmts.cc_max_credit_limit_active": null, "CibilReportAmts.overall_current_overdue_amt": -80, "CibilReportCategoriesDerived.overall_thickness_category": 5, "CibilReportCounts.overall_max_dpd_l12m_ref_max_payment_start_date": 111, "CibilReportCounts.overall_max_dpd_l6m_ref_max_payment_start_date": 111, "CibilReportCounts.pl_count_trades_opened_l1m": 0, "CibilReportCounts.pl_gte_10k_count_trades_opened_l12m": 0, "CibilReportCounts.unsecured_count_inquiries_l1m_excl_report_dt": 1, "CibilReportCounts.unsecured_count_inquiries_l3m_excl_report_dt": 1, "CibilReportCounts.unsecured_count_inquiries_l7d_excl_report_dt": null, "CibilReportCounts.unsecured_wo_cc_gte10k_count_trades_opened_l6m_active_with_current_balance_gte5k": 0, "CibilReportFlags.bl_active_flag": 0, "CibilReportFlags.cc_active_flag": 0, "CibilReportFlags.joint_liability_group_active_flag": 0, "CibilReportFlags.overall_kcc_active_flag": 0, "CibilReportFlags.overall_rs_flag_l36m": 0, "CibilReportFlags.overall_sf_wd_wo_flag_ever": 1, "CibilReportFlags.overall_sub_dbt_lss_flag_ever": 0, "CibilReportIncome.cc_util_max_limit_active_card": null, "CibilReportIncome.emi_m1": null, "CibilReportIncome.final_income": null, "CibilReportIncome.max_cc_util_active_card": null, "CibilReportMetadata.bureau_score": 676, "CibilReportMetadata.date_of_birth": "1990-01-01T00:00:00+00:00", "CibilReportMetadata.phone_number_match": 0, "CibilReportMetadata.pincodes": [], "CibilReportMetadata.report_date": "2022-08-02T10:58:19+00:00", "CibilReportMetadata.report_id": "2157168019", "CibilReportVintage.overall_months_since_latest_90p_dpd_ref_max_payment_start_dt": 1, "CibilReportVintage.overall_months_since_latest_90p_dpd_ref_report_dt": 67, "CibilReportVintage.overall_months_since_latest_xp_dpd": 1, "CibilReportVintage.overall_months_since_latest_xp_dpd_ref_report_dt": 67}}, "AC3gjp6DWQVr250719": {"TestCaseDescription": "cibil_post_2021_flags:all_zero", "featureValueMap": {"CibilReportAmts.overall_current_overdue_amt_post_2021": null, "CibilReportAmts.wo_amt_post_2021": null, "CibilReportFlags.dbt_post_2021_flag": 0, "CibilReportFlags.los_post_2021_flag": 0, "CibilReportFlags.sf_post_2021_flag": 0, "CibilReportMetadata.report_date": "2022-08-02T10:58:19+00:00", "CibilReportMetadata.report_id": "2157168019"}, "cibilReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND"}, "AC3gjp6DWQVr250720": {"TestCaseDescription": "cibil_overdue_amt_post_2021:500", "cibilReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND", "featureValueMap": {"CibilReportAmts.overall_current_overdue_amt_post_2021": 500, "CibilReportAmts.wo_amt_post_2021": null, "CibilReportFlags.dbt_post_2021_flag": 0, "CibilReportFlags.los_post_2021_flag": 0, "CibilReportFlags.sf_post_2021_flag": 0, "CibilReportMetadata.report_date": "2022-08-02T10:58:19+00:00", "CibilReportMetadata.report_id": "2157168019"}}, "AC3gjp6DWQVr250721": {"TestCaseDescription": "cibil_overdue_amt_post_2021:1000", "cibilReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND", "featureValueMap": {"CibilReportAmts.overall_current_overdue_amt_post_2021": 1000, "CibilReportAmts.wo_amt_post_2021": null, "CibilReportFlags.dbt_post_2021_flag": 0, "CibilReportFlags.los_post_2021_flag": 0, "CibilReportFlags.sf_post_2021_flag": 0, "CibilReportMetadata.report_date": "2022-08-02T10:58:19+00:00", "CibilReportMetadata.report_id": "2157168019"}}, "AC3gjp6DWQVr250722": {"TestCaseDescription": "cibil_overdue_amt_post_2021:1500", "cibilReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND", "featureValueMap": {"CibilReportAmts.overall_current_overdue_amt_post_2021": 1500, "CibilReportAmts.wo_amt_post_2021": null, "CibilReportFlags.dbt_post_2021_flag": 0, "CibilReportFlags.los_post_2021_flag": 0, "CibilReportFlags.sf_post_2021_flag": 0, "CibilReportMetadata.report_date": "2022-08-02T10:58:19+00:00", "CibilReportMetadata.report_id": "2157168019"}}, "AC3gjp6DWQVr250723": {"TestCaseDescription": "cibil_overdue_amt_post_2021:0", "cibilReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND", "featureValueMap": {"CibilReportAmts.overall_current_overdue_amt_post_2021": 0, "CibilReportAmts.wo_amt_post_2021": null, "CibilReportFlags.dbt_post_2021_flag": 0, "CibilReportFlags.los_post_2021_flag": 0, "CibilReportFlags.sf_post_2021_flag": 0, "CibilReportMetadata.report_date": "2022-08-02T10:58:19+00:00", "CibilReportMetadata.report_id": "2157168019"}}, "AC3gjp6DWQVr250724": {"TestCaseDescription": "cibil_wo_amt_post_2021:null", "cibilReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND", "featureValueMap": {"CibilReportAmts.overall_current_overdue_amt_post_2021": null, "CibilReportAmts.wo_amt_post_2021": null, "CibilReportFlags.dbt_post_2021_flag": 0, "CibilReportFlags.los_post_2021_flag": 0, "CibilReportFlags.sf_post_2021_flag": 0, "CibilReportMetadata.report_date": "2022-08-02T10:58:19+00:00", "CibilReportMetadata.report_id": "2157168019"}}, "AC3gjp6DWQVr250725": {"TestCaseDescription": "cibil_wo_amt_post_2021:1000", "cibilReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND", "featureValueMap": {"CibilReportAmts.overall_current_overdue_amt_post_2021": null, "CibilReportAmts.wo_amt_post_2021": 1000, "CibilReportFlags.dbt_post_2021_flag": 0, "CibilReportFlags.los_post_2021_flag": 0, "CibilReportFlags.sf_post_2021_flag": 0, "CibilReportMetadata.report_date": "2022-08-02T10:58:19+00:00", "CibilReportMetadata.report_id": "2157168019"}}, "AC3gjp6DWQVr250726": {"TestCaseDescription": "cibil_wo_amt_post_2021:1500", "cibilReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND", "featureValueMap": {"CibilReportAmts.overall_current_overdue_amt_post_2021": null, "CibilReportAmts.wo_amt_post_2021": 1500, "CibilReportFlags.dbt_post_2021_flag": 0, "CibilReportFlags.los_post_2021_flag": 0, "CibilReportFlags.sf_post_2021_flag": 0, "CibilReportMetadata.report_date": "2022-08-02T10:58:19+00:00", "CibilReportMetadata.report_id": "2157168019"}}, "AC3gjp6DWQVr250727": {"TestCaseDescription": "cibil_wo_amt_post_2021:0", "cibilReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND", "featureValueMap": {"CibilReportAmts.overall_current_overdue_amt_post_2021": null, "CibilReportAmts.wo_amt_post_2021": 0, "CibilReportFlags.dbt_post_2021_flag": 0, "CibilReportFlags.los_post_2021_flag": 0, "CibilReportFlags.sf_post_2021_flag": 0, "CibilReportMetadata.report_date": "2022-08-02T10:58:19+00:00", "CibilReportMetadata.report_id": "2157168019"}}, "AC3gjp6DWQVr250728": {"TestCaseDescription": "cibil_wo_amt_post_2021:null", "cibilReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND", "featureValueMap": {"CibilReportAmts.overall_current_overdue_amt_post_2021": null, "CibilReportAmts.wo_amt_post_2021": null, "CibilReportFlags.dbt_post_2021_flag": 0, "CibilReportFlags.los_post_2021_flag": 0, "CibilReportFlags.sf_post_2021_flag": 0, "CibilReportMetadata.report_date": "2022-08-02T10:58:19+00:00", "CibilReportMetadata.report_id": "2157168019"}}, "AC3gjp6DWQVr250729": {"TestCaseDescription": "cibil_all_post_2021_flags_null", "cibilReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND", "featureValueMap": {"CibilReportAmts.overall_current_overdue_amt_post_2021": null, "CibilReportAmts.wo_amt_post_2021": null, "CibilReportFlags.dbt_post_2021_flag": 0, "CibilReportFlags.los_post_2021_flag": 0, "CibilReportFlags.sf_post_2021_flag": 0, "CibilReportMetadata.report_date": "2022-08-02T10:58:19+00:00", "CibilReportMetadata.report_id": "2157168019"}}, "AC3gjp6DWQVr250730": {"TestCaseDescription": "cibil_sf_post_2021_flag:1", "cibilReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND", "featureValueMap": {"CibilReportAmts.overall_current_overdue_amt_post_2021": null, "CibilReportAmts.wo_amt_post_2021": null, "CibilReportFlags.dbt_post_2021_flag": 0, "CibilReportFlags.los_post_2021_flag": 0, "CibilReportFlags.sf_post_2021_flag": 1, "CibilReportMetadata.report_date": "2022-08-02T10:58:19+00:00", "CibilReportMetadata.report_id": "2157168019"}}, "AC3gjp6DWQVr250731": {"TestCaseDescription": "cibil_sf_post_2021_flag:null", "cibilReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND", "featureValueMap": {"CibilReportAmts.overall_current_overdue_amt_post_2021": null, "CibilReportAmts.wo_amt_post_2021": null, "CibilReportFlags.dbt_post_2021_flag": 0, "CibilReportFlags.los_post_2021_flag": 0, "CibilReportFlags.sf_post_2021_flag": null, "CibilReportMetadata.report_date": "2022-08-02T10:58:19+00:00", "CibilReportMetadata.report_id": "2157168019"}}, "AC3gjp6DWQVr250732": {"TestCaseDescription": "cibil_sf_post_2021_flag:0", "cibilReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND", "featureValueMap": {"CibilReportAmts.overall_current_overdue_amt_post_2021": null, "CibilReportAmts.wo_amt_post_2021": null, "CibilReportFlags.dbt_post_2021_flag": 0, "CibilReportFlags.los_post_2021_flag": 0, "CibilReportFlags.sf_post_2021_flag": 0, "CibilReportMetadata.report_date": "2022-08-02T10:58:19+00:00", "CibilReportMetadata.report_id": "2157168019"}}, "AC3gjp6DWQVr250733": {"TestCaseDescription": "cibil_dbt_post_2021_flag:1", "cibilReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND", "featureValueMap": {"CibilReportAmts.overall_current_overdue_amt_post_2021": null, "CibilReportAmts.wo_amt_post_2021": null, "CibilReportFlags.dbt_post_2021_flag": 1, "CibilReportFlags.los_post_2021_flag": 0, "CibilReportFlags.sf_post_2021_flag": 0, "CibilReportMetadata.report_date": "2022-08-02T10:58:19+00:00", "CibilReportMetadata.report_id": "2157168019"}}, "AC3gjp6DWQVr250734": {"TestCaseDescription": "cibil_dbt_post_2021_flag:null", "cibilReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND", "featureValueMap": {"CibilReportAmts.overall_current_overdue_amt_post_2021": null, "CibilReportAmts.wo_amt_post_2021": null, "CibilReportFlags.dbt_post_2021_flag": null, "CibilReportFlags.los_post_2021_flag": 0, "CibilReportFlags.sf_post_2021_flag": 0, "CibilReportMetadata.report_date": "2022-08-02T10:58:19+00:00", "CibilReportMetadata.report_id": "2157168019"}}, "AC3gjp6DWQVr250735": {"TestCaseDescription": "cibil_dbt_post_2021_flag:0", "cibilReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND", "featureValueMap": {"CibilReportAmts.overall_current_overdue_amt_post_2021": null, "CibilReportAmts.wo_amt_post_2021": null, "CibilReportFlags.dbt_post_2021_flag": 0, "CibilReportFlags.los_post_2021_flag": 0, "CibilReportFlags.sf_post_2021_flag": 0, "CibilReportMetadata.report_date": "2022-08-02T10:58:19+00:00", "CibilReportMetadata.report_id": "2157168019"}}, "AC3gjp6DWQVr250736": {"TestCaseDescription": "cibil_los_post_2021_flag:1", "cibilReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND", "featureValueMap": {"CibilReportAmts.overall_current_overdue_amt_post_2021": null, "CibilReportAmts.wo_amt_post_2021": null, "CibilReportFlags.dbt_post_2021_flag": 0, "CibilReportFlags.los_post_2021_flag": 1, "CibilReportFlags.sf_post_2021_flag": 0, "CibilReportMetadata.report_date": "2022-08-02T10:58:19+00:00", "CibilReportMetadata.report_id": "2157168019"}}, "AC3gjp6DWQVr250737": {"TestCaseDescription": "cibil_los_post_2021_flag:null", "cibilReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND", "featureValueMap": {"CibilReportAmts.overall_current_overdue_amt_post_2021": null, "CibilReportAmts.wo_amt_post_2021": null, "CibilReportFlags.dbt_post_2021_flag": 0, "CibilReportFlags.los_post_2021_flag": null, "CibilReportFlags.sf_post_2021_flag": 0, "CibilReportMetadata.report_date": "2022-08-02T10:58:19+00:00", "CibilReportMetadata.report_id": "2157168019"}}, "AC3gjp6DWQVr250738": {"TestCaseDescription": "cibil_report_date:2024-01-01", "cibilReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND", "featureValueMap": {"CibilReportAmts.overall_current_overdue_amt_post_2021": null, "CibilReportAmts.wo_amt_post_2021": null, "CibilReportFlags.dbt_post_2021_flag": 0, "CibilReportFlags.los_post_2021_flag": 0, "CibilReportFlags.sf_post_2021_flag": 0, "CibilReportMetadata.report_date": "2024-01-01T00:00:00+00:00", "CibilReportMetadata.report_id": "2157168019"}}, "AC3gjp6DWQVr250739": {"TestCaseDescription": "cibil_report_date:2024-06-15", "cibilReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND", "featureValueMap": {"CibilReportAmts.overall_current_overdue_amt_post_2021": null, "CibilReportAmts.wo_amt_post_2021": null, "CibilReportFlags.dbt_post_2021_flag": 0, "CibilReportFlags.los_post_2021_flag": 0, "CibilReportFlags.sf_post_2021_flag": 0, "CibilReportMetadata.report_date": "2024-06-15T00:00:00+00:00", "CibilReportMetadata.report_id": "2157168019"}}, "AC3gjp6DWQVr250740": {"TestCaseDescription": "cibil_report_date:null", "cibilReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND", "featureValueMap": {"CibilReportAmts.overall_current_overdue_amt_post_2021": null, "CibilReportAmts.wo_amt_post_2021": null, "CibilReportFlags.dbt_post_2021_flag": 0, "CibilReportFlags.los_post_2021_flag": 0, "CibilReportFlags.sf_post_2021_flag": 0, "CibilReportMetadata.report_date": null, "CibilReportMetadata.report_id": "2157168019"}}, "AC3gjp6DWQVr250742": {"TestCaseDescription": "cibil_bureau_score:550", "cibilReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND", "featureValueMap": {"CibilReportMetadata.bureau_score": 550, "CibilReportFlags.overall_wo_l24m_flag": 0, "CibilReportCounts.active_max_dpd_l3m": 10, "CibilReportCounts.active_max_dpd_l12m": 80, "CibilReportCounts.pl_count_inquiries_l3m": 8, "CibilReportMetadata.report_date": "2022-08-02T10:58:19+00:00", "CibilReportMetadata.report_id": "2157168019"}}, "AC3gjp6DWQVr250743": {"TestCaseDescription": "cibil_bureau_score:600", "cibilReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND", "featureValueMap": {"CibilReportMetadata.bureau_score": 600, "CibilReportFlags.overall_wo_l24m_flag": 0, "CibilReportCounts.active_max_dpd_l3m": 10, "CibilReportCounts.active_max_dpd_l12m": 80, "CibilReportCounts.pl_count_inquiries_l3m": 8, "CibilReportMetadata.report_date": "2022-08-02T10:58:19+00:00", "CibilReportMetadata.report_id": "2157168019"}}, "AC3gjp6DWQVr250744": {"TestCaseDescription": "cibil_bureau_score:700", "cibilReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND", "featureValueMap": {"CibilReportMetadata.bureau_score": 700, "CibilReportFlags.overall_wo_l24m_flag": 0, "CibilReportCounts.active_max_dpd_l3m": 10, "CibilReportCounts.active_max_dpd_l12m": 80, "CibilReportCounts.pl_count_inquiries_l3m": 8, "CibilReportMetadata.report_date": "2022-08-02T10:58:19+00:00", "CibilReportMetadata.report_id": "2157168019"}}, "AC3gjp6DWQVr250745": {"TestCaseDescription": "cibil_overall_wo_l24m_flag:0", "cibilReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND", "featureValueMap": {"CibilReportMetadata.bureau_score": 700, "CibilReportFlags.overall_wo_l24m_flag": 0, "CibilReportCounts.active_max_dpd_l3m": 10, "CibilReportCounts.active_max_dpd_l12m": 80, "CibilReportCounts.pl_count_inquiries_l3m": 8, "CibilReportMetadata.report_date": "2022-08-02T10:58:19+00:00", "CibilReportMetadata.report_id": "2157168019"}}, "AC3gjp6DWQVr250746": {"TestCaseDescription": "cibil_overall_wo_l24m_flag:1", "cibilReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND", "featureValueMap": {"CibilReportMetadata.bureau_score": 700, "CibilReportFlags.overall_wo_l24m_flag": 1, "CibilReportCounts.active_max_dpd_l3m": 10, "CibilReportCounts.active_max_dpd_l12m": 80, "CibilReportCounts.pl_count_inquiries_l3m": 8, "CibilReportMetadata.report_date": "2022-08-02T10:58:19+00:00", "CibilReportMetadata.report_id": "2157168019"}}, "AC3gjp6DWQVr250747": {"TestCaseDescription": "cibil_active_max_dpd_l3m:10", "cibilReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND", "featureValueMap": {"CibilReportMetadata.bureau_score": 700, "CibilReportFlags.overall_wo_l24m_flag": 0, "CibilReportCounts.active_max_dpd_l3m": 10, "CibilReportCounts.active_max_dpd_l12m": 80, "CibilReportCounts.pl_count_inquiries_l3m": 8, "CibilReportMetadata.report_date": "2022-08-02T10:58:19+00:00", "CibilReportMetadata.report_id": "2157168019"}}, "AC3gjp6DWQVr250748": {"TestCaseDescription": "cibil_active_max_dpd_l3m:15", "cibilReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND", "featureValueMap": {"CibilReportMetadata.bureau_score": 700, "CibilReportFlags.overall_wo_l24m_flag": 0, "CibilReportCounts.active_max_dpd_l3m": 15, "CibilReportCounts.active_max_dpd_l12m": 80, "CibilReportCounts.pl_count_inquiries_l3m": 8, "CibilReportMetadata.report_date": "2022-08-02T10:58:19+00:00", "CibilReportMetadata.report_id": "2157168019"}}, "AC3gjp6DWQVr250749": {"TestCaseDescription": "cibil_active_max_dpd_l3m:20", "cibilReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND", "featureValueMap": {"CibilReportMetadata.bureau_score": 700, "CibilReportFlags.overall_wo_l24m_flag": 0, "CibilReportCounts.active_max_dpd_l3m": 20, "CibilReportCounts.active_max_dpd_l12m": 80, "CibilReportCounts.pl_count_inquiries_l3m": 8, "CibilReportMetadata.report_date": "2022-08-02T10:58:19+00:00", "CibilReportMetadata.report_id": "2157168019"}}, "AC3gjp6DWQVr250750": {"TestCaseDescription": "cibil_active_max_dpd_l12m:80", "cibilReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND", "featureValueMap": {"CibilReportMetadata.bureau_score": 700, "CibilReportFlags.overall_wo_l24m_flag": 0, "CibilReportCounts.active_max_dpd_l3m": 10, "CibilReportCounts.active_max_dpd_l12m": 80, "CibilReportCounts.pl_count_inquiries_l3m": 8, "CibilReportMetadata.report_date": "2022-08-02T10:58:19+00:00", "CibilReportMetadata.report_id": "2157168019"}}, "AC3gjp6DWQVr250751": {"TestCaseDescription": "cibil_active_max_dpd_l12m:90", "cibilReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND", "featureValueMap": {"CibilReportMetadata.bureau_score": 700, "CibilReportFlags.overall_wo_l24m_flag": 0, "CibilReportCounts.active_max_dpd_l3m": 10, "CibilReportCounts.active_max_dpd_l12m": 90, "CibilReportCounts.pl_count_inquiries_l3m": 8, "CibilReportMetadata.report_date": "2022-08-02T10:58:19+00:00", "CibilReportMetadata.report_id": "2157168019"}}, "AC3gjp6DWQVr250752": {"TestCaseDescription": "cibil_active_max_dpd_l12m:100", "cibilReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND", "featureValueMap": {"CibilReportMetadata.bureau_score": 700, "CibilReportFlags.overall_wo_l24m_flag": 0, "CibilReportCounts.active_max_dpd_l3m": 10, "CibilReportCounts.active_max_dpd_l12m": 100, "CibilReportCounts.pl_count_inquiries_l3m": 8, "CibilReportMetadata.report_date": "2022-08-02T10:58:19+00:00", "CibilReportMetadata.report_id": "2157168019"}}, "AC3gjp6DWQVr250753": {"TestCaseDescription": "cibil_pl_count_inquiries_l3m:8", "cibilReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND", "featureValueMap": {"CibilReportMetadata.bureau_score": 700, "CibilReportFlags.overall_wo_l24m_flag": 0, "CibilReportCounts.active_max_dpd_l3m": 10, "CibilReportCounts.active_max_dpd_l12m": 80, "CibilReportCounts.pl_count_inquiries_l3m": 8, "CibilReportMetadata.report_date": "2022-08-02T10:58:19+00:00", "CibilReportMetadata.report_id": "2157168019"}}, "AC3gjp6DWQVr250754": {"TestCaseDescription": "cibil_pl_count_inquiries_l3m:10", "cibilReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND", "featureValueMap": {"CibilReportMetadata.bureau_score": 700, "CibilReportFlags.overall_wo_l24m_flag": 0, "CibilReportCounts.active_max_dpd_l3m": 10, "CibilReportCounts.active_max_dpd_l12m": 80, "CibilReportCounts.pl_count_inquiries_l3m": 10, "CibilReportMetadata.report_date": "2022-08-02T10:58:19+00:00", "CibilReportMetadata.report_id": "2157168019"}}, "AC3gjp6DWQVr250755": {"TestCaseDescription": "cibil_pl_count_inquiries_l3m:15", "cibilReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_FOUND", "featureValueMap": {"CibilReportMetadata.bureau_score": 700, "CibilReportFlags.overall_wo_l24m_flag": 0, "CibilReportCounts.active_max_dpd_l3m": 10, "CibilReportCounts.active_max_dpd_l12m": 80, "CibilReportCounts.pl_count_inquiries_l3m": 15, "CibilReportMetadata.report_date": "2022-08-02T10:58:19+00:00", "CibilReportMetadata.report_id": "2157168019"}}, "AC3gjp6DWQVr250741": {"TestCaseDescription": "cibil_data_not_found", "cibilReportDataAvailabilityStatus": "DATA_AVAILABILITY_STATUS_NOT_FOUND", "featureValueMap": {}}}, "GetEpfoData": {"DEFAULT": {"TestCaseDescription": "default_epfo_data", "vendor": "digitap", "rawEpfoData": {"http_response_code": 200, "client_ref_num": "cf956e4a-d63b-4370-94fd-3f5ca0c75c16", "request_id": "dummy_id", "result_code": 101, "mobile": "**********", "result": {"uan": ["241421"], "summary": {"recent_employer_data": {"member_id": "547645", "establishment_id": "76986", "date_of_joining": "2020-07-01", "establishment_name": "EPIFI", "employer_confidence_score": 1, "matching_uan": "54364", "epfo": {"is_recent": true, "is_name_unique": true, "has_pf_filings_details": true}}, "matching_uan": "41321", "is_employed": true, "uan_count": 1}, "uan_details": {"********": {"basic_details": {"gender": "Male", "date_of_birth": "1989-07-18", "employee_confidence_score": 2, "name": "RAHUL", "aadhaar_verification_status": 1}, "employment_details": {"member_id": "4361113", "establishment_id": "8456853", "date_of_joining": "2020-07-01", "establishment_name": "EPIFI", "employer_confidence_score": 1}, "additional_details": {"aadhaar": "14352", "member_id": "8764", "email": "<EMAIL>", "bank_ifsc": "978890", "bank_acc_no": "393180", "bank_address": "BANGALORE", "relation": "sakjc", "relative_name": "RAJIV"}}}, "uan_source": [{"uan": "5325345", "source": "mobile"}], "name_dob_filtering_score": 2, "epfo_details": {"matches": [{"name": "RAHUL", "confidence": 1.1, "epf_history": {}}], "pf_filing_details": [{"total_amount": 100, "employees_count": 2, "wage_month": "JUL-24"}], "establishment_info": {"establishment_id": "********", "establishment_name": "EPIFI", "date_of_setup": "2019-05-29", "ownership_type": "Private Limited Company"}}}, "input_data": {"mobile": "**********"}}}, "AC3gjp6DWQVr250718": {"TestCaseDescription": "epfo_data:al_max_amt_ever:50000", "vendor": "digitap", "rawEpfoData": {"http_response_code": 200, "client_ref_num": "cf956e4a-d63b-4370-94fd-3f5ca0c75c16", "request_id": "dummy_id", "result_code": 101, "mobile": "**********", "result": {"uan": ["241421"], "summary": {"recent_employer_data": {"member_id": "547645", "establishment_id": "76986", "date_of_joining": "2020-07-01", "establishment_name": "EPIFI", "employer_confidence_score": 1, "matching_uan": "54364", "epfo": {"is_recent": true, "is_name_unique": true, "has_pf_filings_details": true}}, "matching_uan": "41321", "is_employed": true, "uan_count": 1}, "uan_details": {"********": {"basic_details": {"gender": "Male", "date_of_birth": "1989-07-18", "employee_confidence_score": 2, "name": "RAHUL", "aadhaar_verification_status": 1}, "employment_details": {"member_id": "4361113", "establishment_id": "8456853", "date_of_joining": "2020-07-01", "establishment_name": "EPIFI", "employer_confidence_score": 1}, "additional_details": {"aadhaar": "14352", "member_id": "8764", "email": "<EMAIL>", "bank_ifsc": "978890", "bank_acc_no": "393180", "bank_address": "BANGALORE", "relation": "sakjc", "relative_name": "RAJIV"}}}, "uan_source": [{"uan": "5325345", "source": "mobile"}], "name_dob_filtering_score": 2, "epfo_details": {"matches": [{"name": "RAHUL", "confidence": 1.1, "epf_history": {}}], "pf_filing_details": [{"total_amount": 100, "employees_count": 2, "wage_month": "JUL-24"}], "establishment_info": {"establishment_id": "********", "establishment_name": "EPIFI", "date_of_setup": "2019-05-29", "ownership_type": "Private Limited Company"}}}, "input_data": {"mobile": "**********"}}}}}