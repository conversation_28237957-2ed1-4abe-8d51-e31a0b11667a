// Code generated by tools/conf_gen/dynamic_conf_gen.go
package config

import (
	"fmt"
	"strings"
)

func (obj *Config) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "maxpagesize":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.<PERSON><PERSON><PERSON>("invalid path %q for primitive field \"MaxPageSize\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MaxPageSize, nil
	case "emailbatchsize":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.<PERSON><PERSON><PERSON>("invalid path %q for primitive field \"EmailBatchSize\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EmailBatchSize, nil
	case "instantpurchaseamount":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.<PERSON><PERSON><PERSON>("invalid path %q for primitive field \"InstantPurchaseAmount\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.InstantPurchaseAmount, nil
	case "maxbatchsizetocreateorupdateaccountactivities":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MaxBatchSizeToCreateOrUpdateAccountActivities\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MaxBatchSizeToCreateOrUpdateAccountActivities, nil
	case "daytradeslimit":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DayTradesLimit\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DayTradesLimit, nil
	case "minandroidversiontosupporttabbedcard":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinAndroidVersionToSupportTabbedCard\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinAndroidVersionToSupportTabbedCard, nil
	case "miniosversiontosupporttabbedcard":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinIOSVersionToSupportTabbedCard\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinIOSVersionToSupportTabbedCard, nil
	case "minandroidversionwithsupportforemptytabbedsection":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinAndroidVersionWithSupportForEmptyTabbedSection\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinAndroidVersionWithSupportForEmptyTabbedSection, nil
	case "miniosversionwithsupportforemptytabbedsection":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinIOSVersionWithSupportForEmptyTabbedSection\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinIOSVersionWithSupportForEmptyTabbedSection, nil
	case "minandroidappversiontodirecttoaddfundretrybugfix":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinAndroidAppVersionToDirectToAddFundRetryBugFix\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinAndroidAppVersionToDirectToAddFundRetryBugFix, nil
	case "shoulduseselllock":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ShouldUseSellLock\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ShouldUseSellLock, nil
	case "isvalidconnectedaccount":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsValidConnectedAccount\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsValidConnectedAccount, nil
	case "skippanvalidation":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SkipPanValidation\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SkipPanValidation, nil
	case "usevendorapiforexchangestatus":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"UseVendorAPIForExchangeStatus\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.UseVendorAPIForExchangeStatus, nil
	case "isetfenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsETFEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsETFEnabled, nil
	case "ignorecanceledtxnsforinwardremittance":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IgnoreCanceledTxnsForInwardRemittance\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IgnoreCanceledTxnsForInwardRemittance, nil
	case "enablezincingestioninsync":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableZincIngestionInSync\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableZincIngestionInSync, nil
	case "isnewstockuniverseenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsNewStockUniverseEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsNewStockUniverseEnabled, nil
	case "authfactorupdatecooloffperiod":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AuthFactorUpdateCoolOffPeriod\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AuthFactorUpdateCoolOffPeriod, nil
	case "marketindexconfig":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.MarketIndexConfig, nil
		case len(dynamicFieldPath) > 1:

			return obj.MarketIndexConfig[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.MarketIndexConfig, nil
	case "sbimonthlybuyexchangerate":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.SBIMonthlyBuyExchangeRate, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"SBIMonthlyBuyExchangeRate\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.SBIMonthlyBuyExchangeRate[dynamicFieldPath[1]], nil

		}
		return obj.SBIMonthlyBuyExchangeRate, nil
	case "costinflationindexmap":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.CostInflationIndexMap, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"CostInflationIndexMap\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.CostInflationIndexMap[dynamicFieldPath[1]], nil

		}
		return obj.CostInflationIndexMap, nil
	case "creditfrozenactorids":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CreditFrozenActorIds\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CreditFrozenActorIds, nil
	case "stockuniversefilepath":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"StockUniverseFilePath\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.StockUniverseFilePath, nil
	case "etfuniversefilepath":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ETFUniverseFilePath\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ETFUniverseFilePath, nil
	case "suitabilityquestionconfigfilepath":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SuitabilityQuestionConfigFilePath\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SuitabilityQuestionConfigFilePath, nil
	case "usstocksopsalertslackchannelid":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"UsStocksOpsAlertSlackChannelId\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.UsStocksOpsAlertSlackChannelId, nil
	case "indexationbenefitnotapplicablefrom":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IndexationBenefitNotApplicableFrom\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IndexationBenefitNotApplicableFrom, nil
	case "flags":
		return obj.Flags.Get(dynamicFieldPath[1:])
	case "processusstockcatalogupdatesubscriber":
		return obj.ProcessUSStockCatalogUpdateSubscriber.Get(dynamicFieldPath[1:])
	case "processusetfcatalogupdatesubscriber":
		return obj.ProcessUSEtfCatalogUpdateSubscriber.Get(dynamicFieldPath[1:])
	case "usstockcatalogrefreshsubscriber":
		return obj.USStockCatalogRefreshSubscriber.Get(dynamicFieldPath[1:])
	case "processorderupdateeventsubscriber":
		return obj.ProcessOrderUpdateEventSubscriber.Get(dynamicFieldPath[1:])
	case "fetchreversefeedfilesubscriber":
		return obj.FetchReverseFeedFileSubscriber.Get(dynamicFieldPath[1:])
	case "amlactioneventsubscriber":
		return obj.AmlActionEventSubscriber.Get(dynamicFieldPath[1:])
	case "celestialworkflowupdatesubscriber":
		return obj.CelestialWorkflowUpdateSubscriber.Get(dynamicFieldPath[1:])
	case "processaccountactivitysyncsubscriber":
		return obj.ProcessAccountActivitySyncSubscriber.Get(dynamicFieldPath[1:])
	case "usstockstaxdocumentgenerationrequestsubscriber":
		return obj.USStocksTaxDocumentGenerationRequestSubscriber.Get(dynamicFieldPath[1:])
	case "accountmanagerconfig":
		return obj.AccountManagerConfig.Get(dynamicFieldPath[1:])
	case "priceupdatesconnectioninfo":
		return obj.PriceUpdatesConnectionInfo.Get(dynamicFieldPath[1:])
	case "orderupdateeventsconnectioninfo":
		return obj.OrderUpdateEventsConnectionInfo.Get(dynamicFieldPath[1:])
	case "accountupdateeventsconnectioninfo":
		return obj.AccountUpdateEventsConnectionInfo.Get(dynamicFieldPath[1:])
	case "catalogsearchconfig":
		return obj.CatalogSearchConfig.Get(dynamicFieldPath[1:])
	case "accountactivitysyncconfig":
		return obj.AccountActivitySyncConfig.Get(dynamicFieldPath[1:])
	case "priceupdateratelimiterconfig":
		return obj.PriceUpdateRateLimiterConfig.Get(dynamicFieldPath[1:])
	case "commsnotification":
		return obj.CommsNotification.Get(dynamicFieldPath[1:])
	case "commsemail":
		return obj.CommsEmail.Get(dynamicFieldPath[1:])
	case "usstockssendmailtouserssubscriber":
		return obj.UsStocksSendMailToUsersSubscriber.Get(dynamicFieldPath[1:])
	case "morningstars3bucket":
		return obj.MorningStarS3Bucket.Get(dynamicFieldPath[1:])
	case "brokerfirmaccountdetailsforforeignremittance":
		return obj.BrokerFirmAccountDetailsForForeignRemittance.Get(dynamicFieldPath[1:])
	case "usstocksiftremittancefileprocessingeventssubscriber":
		return obj.USStocksIFTRemittanceFileProcessingEventsSubscriber.Get(dynamicFieldPath[1:])
	case "usstocksincomeupdateeventssubscriber":
		return obj.USStocksIncomeUpdateEventsSubscriber.Get(dynamicFieldPath[1:])
	case "addfundsprereqchecksdata":
		return obj.AddFundsPreReqChecksData.Get(dynamicFieldPath[1:])
	case "walletordereta":
		return obj.WalletOrderEta.Get(dynamicFieldPath[1:])
	case "journalupdateeventsconnectioninfo":
		return obj.JournalUpdateEventsConnectionInfo.Get(dynamicFieldPath[1:])
	case "fundtransferupdateeventsconnectioninfo":
		return obj.FundTransferUpdateEventsConnectionInfo.Get(dynamicFieldPath[1:])
	case "pgdbmigrationconf":
		return obj.PgdbMigrationConf.Get(dynamicFieldPath[1:])
	case "getstockspricesnapshotsbatchconfig":
		return obj.GetStocksPriceSnapshotsBatchConfig.Get(dynamicFieldPath[1:])
	case "usstocksrewarddetails":
		return obj.USStocksRewardDetails.Get(dynamicFieldPath[1:])
	case "homeusstockscollection":
		return obj.HomeUsStocksCollection.Get(dynamicFieldPath[1:])
	case "brokerage":
		return obj.Brokerage.Get(dynamicFieldPath[1:])
	case "featurereleaseconfig":
		return obj.FeatureReleaseConfig.Get(dynamicFieldPath[1:])
	case "portfolioconfig":
		return obj.PortfolioConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Config", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Flags) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "disablemorningstardatausages":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DisableMorningStarDataUsages\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DisableMorningStarDataUsages, nil
	case "enableaggregatedinwardremittance":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableAggregatedInwardRemittance\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableAggregatedInwardRemittance, nil
	case "createcustomdividendactivities":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CreateCustomDividendActivities\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CreateCustomDividendActivities, nil
	case "usebankapifortcscalculation":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"UseBankApiForTcsCalculation\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.UseBankApiForTcsCalculation, nil
	case "enablecatalogsearchsync":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableCatalogSearchSync\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableCatalogSearchSync, nil
	case "enableimpendingsavingsaccountfreezevalidation":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableImpendingSavingsAccountFreezeValidation\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableImpendingSavingsAccountFreezeValidation, nil
	case "enablemodifiedtransactionremarkforinwardremittance":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableModifiedTransactionRemarkForInwardRemittance\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableModifiedTransactionRemarkForInwardRemittance, nil
	case "enableinwardremittancegstreportingflowusingapi":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableInwardRemittanceGstReportingFlowUsingApi\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableInwardRemittanceGstReportingFlowUsingApi, nil
	case "enableseparatettumfilechanges":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableSeparateTTUMFileChanges\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableSeparateTTUMFileChanges, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Flags", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *AccountManagerConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "w8benformversion":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"W8BenFormVersion\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.W8BenFormVersion, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for AccountManagerConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *PriceUpdatesConnectionInfo) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "vgconnectionmaxretries":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"VGConnectionMaxRetries\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.VGConnectionMaxRetries, nil
	case "enable":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Enable\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Enable, nil
	case "vgconnectionretryinterval":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"VGConnectionRetryInterval\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.VGConnectionRetryInterval, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for PriceUpdatesConnectionInfo", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *OrderUpdateEventsConnectionInfo) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "vgconnectionmaxretries":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"VGConnectionMaxRetries\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.VGConnectionMaxRetries, nil
	case "enable":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Enable\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Enable, nil
	case "vgconnectionretryinterval":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"VGConnectionRetryInterval\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.VGConnectionRetryInterval, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for OrderUpdateEventsConnectionInfo", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *AccountUpdateEventsConnectionInfo) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "vgconnectionmaxretries":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"VGConnectionMaxRetries\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.VGConnectionMaxRetries, nil
	case "enable":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Enable\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Enable, nil
	case "vgconnectionretryinterval":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"VGConnectionRetryInterval\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.VGConnectionRetryInterval, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for AccountUpdateEventsConnectionInfo", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *CatalogSearchConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "catalogfetchdefaultpagesize":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CatalogFetchDefaultPageSize\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CatalogFetchDefaultPageSize, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for CatalogSearchConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *AccountActivitySyncConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "pagesize":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"PageSize\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.PageSize, nil
	case "firstactivityts":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FirstActivityTs\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.FirstActivityTs, nil
	case "isenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnabled, nil
	case "duration":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Duration\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Duration, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for AccountActivitySyncConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Notifications) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "notificationiconurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"NotificationIconURL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.NotificationIconURL, nil
	case "buyordercancelled":
		return obj.BuyOrderCancelled.Get(dynamicFieldPath[1:])
	case "sellordercancelled":
		return obj.SellOrderCancelled.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Notifications", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *NotificationParams) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "title":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Title\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Title, nil
	case "body":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Body\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Body, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for NotificationParams", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Email) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "fromemailid":
		return obj.FromEmailId.Get(dynamicFieldPath[1:])
	case "alpacaemail":
		return obj.AlpacaEmail.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Email", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *EmailDetails) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "emailid":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EmailId\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EmailId, nil
	case "emailname":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EmailName\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EmailName, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for EmailDetails", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *MorningStarS3Bucket) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "bucketname":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BucketName\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BucketName, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for MorningStarS3Bucket", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *AddFundsPreReqChecksData) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "enabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Enabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Enabled, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for AddFundsPreReqChecksData", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *WalletOrderEta) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "withdrawfundspaymentsettlement":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"WithdrawFundsPaymentSettlement\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.WithdrawFundsPaymentSettlement, nil
	case "addfundspaymentsettlement":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AddFundsPaymentSettlement\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AddFundsPaymentSettlement, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for WalletOrderEta", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *SSEUpdateEventsConnectionInfo) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "vgconnectionmaxretries":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"VGConnectionMaxRetries\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.VGConnectionMaxRetries, nil
	case "enable":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Enable\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Enable, nil
	case "vgconnectionretryinterval":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"VGConnectionRetryInterval\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.VGConnectionRetryInterval, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for SSEUpdateEventsConnectionInfo", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *GetStocksPriceSnapshotsBatchConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "batchsize":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BatchSize\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BatchSize, nil
	case "jitterseconds":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"JitterSeconds\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.JitterSeconds, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for GetStocksPriceSnapshotsBatchConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *USStocksRewardDetails) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "option1stockname":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Option1StockName\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Option1StockName, nil
	case "option2stockname":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Option2StockName\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Option2StockName, nil
	case "bglottieurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BgLottieUrl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BgLottieUrl, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for USStocksRewardDetails", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *MarketIndexConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "constituentsymbols":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ConstituentSymbols\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ConstituentSymbols, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for MarketIndexConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *HomeUsStocksCollection) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "ishomeusstockscollectionreleased":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsHomeUsStocksCollectionReleased\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsHomeUsStocksCollectionReleased, nil
	case "collectionid":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CollectionId\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CollectionId, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for HomeUsStocksCollection", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *PortfolioConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "enablesymbolfiltering":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableSymbolFiltering\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableSymbolFiltering, nil
	case "filteredsymbols":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FilteredSymbols\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.FilteredSymbols, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for PortfolioConfig", strings.Join(dynamicFieldPath, "."))
	}
}
