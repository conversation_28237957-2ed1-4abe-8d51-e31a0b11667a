Application:
  Environment: "prod"
  Name: "usstocks"

Server:
  Ports:
    GrpcPort: 8110
    GrpcSecurePort: 9526
    HttpPort: 9999



VendorActorId:
  1: "actor-federal-pool-outward-account" # Vendor 1 is ALPACA

USStocksAlpacaDb:
  DbType: "CRDB"
  AppName: "usstocks"
  StatementTimeout: 1s
  Username: "usstocks_alpaca_dev_user"
  Password: ""
  Name: "usstocks_alpaca"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/cockroach/ca.crt"
  SSLClientCert: "prod/cockroach/client.usstocks_alpaca_dev_user.crt"
  SSLClientKey: "prod/cockroach/client.usstocks_alpaca_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false
    EnableMultiDBSupport: true
    DBResolverList:
      - Alias: "usstocks_alpaca_pgdb"
        DbDsn:
          DbType: "PGDB"
          StatementTimeout: 10s
          Name: "usstocks_alpaca"
          EnableDebug: false
          SSLMode: "verify-full"
          SSLRootCert: "prod/rds/rds-ca-root-2061"
          MaxOpenConn: 10
          MaxIdleConn: 10
          MaxConnTtl: "30m"
          SecretName: "prod/rds/epifimetis/usstocks_alpaca_dev_user"

Flags:
  DisableMorningStarDataUsages: true
  # Note: This flag should be changed only when all pending inward remittance batch files are processed completely
  EnableAggregatedInwardRemittance: true
  CreateCustomDividendActivities: false
  EnableImpendingSavingsAccountFreezeValidation: false
  # Note: This flag should be changed only when all pending inward remittance batch files are processed completely
  EnableSeparateTTUMFileChanges: true

AWS:
  Region: "ap-south-1"

RudderStack:
  IntervalInSec: 10
  BatchSize: 20
  Verbose: false

Secrets:
  Ids:
    RudderWriteKey: "prod/rudder/internal-writekey"
    ZincCredentials: "prod/investment/zinc"
    UsStocksSlackBotOauthToken: "prod/ift/slack-bot-oauth-token"


Tracing:
  Enable: true

Profiling:
  StackDriverProfiling:
    ProjectId: "epifi-prod"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-prod-automatic-profiling"
    CPUPercentageLimit: 80
    MemoryPercentageLimit: 80
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

CatalogS3Conf:
  BucketName: "epifi-prod-usstocks-alpaca"

ProcessUSStockCatalogUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-usstock-process-catalog-update-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 25
      MaxAttempts: 3
      TimeUnit: "Minute"

ProcessUSEtfCatalogUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-usetf-process-catalog-update-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 25
      MaxAttempts: 3
      TimeUnit: "Minute"

ProcessOrderUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-usstocks-oms-order-update-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Minute"

AmlActionEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-usstocks-aml-action-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 5
      TimeUnit: "Minute"

CelestialWorkflowUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-usstocks-celestial-wf-update-consumer-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~68 min post that regular interval is followed for next 7 days with interval of 2 hours.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 1
          MaxAttempts: 12
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 2
          MaxAttempts: 84
          TimeUnit: "Hour"
      MaxAttempts: 96
      CutOff: 12

AccountManagerConfig:
  KycDocumentsBucketName: "epifi-prod-usstocks-alpaca"

ProcessAccountActivitySyncSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-usstocks-account-activity-sync-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 5
      TimeUnit: "Minute"

ProcessAccountActivitySyncPublisher:
  QueueName: "prod-usstocks-account-activity-sync-queue"

PriceUpdatesConnectionInfo:
  Enable: true
  VGConnectionRetryInterval: "30s" # retry after 30 seconds of VG connection failure
  # no of retries are intentionally kept higher for handling first prod push where gap between vg and investment server deployment can be more than 30 mins.
  # this should be revisited and changed to some smaller number eg: 20
  VGConnectionMaxRetries: 100  # retry for max 100 times after every 30 seconds. connection retry happens for ~ 50 mins

OrderUpdateEventsConnectionInfo:
  Enable: true
  VGConnectionRetryInterval: "30s" # retry after 30 seconds of VG connection failure
  # no of retries are intentionally kept higher for handling first prod push where gap between vg and investment server deployment can be more than 30 mins.
  # this should be revisited and changed to some smaller number eg: 20
  VGConnectionMaxRetries: 100  # retry for max 100 times after every 30 seconds. connection retry happens for ~ 50 mins

AccountUpdateEventsConnectionInfo:
  Enable: true
  VGConnectionRetryInterval: "30s" # retry after 30 seconds of VG connection failure
  # no of retries are intentionally kept higher for handling first prod push where gap between vg and investment server deployment can be more than 30 mins.
  # this should be revisited and changed to some smaller number eg: 20
  VGConnectionMaxRetries: 100  # retry for max 100 times after every 30 seconds. connection retry happens for ~ 50 mins

JournalUpdateEventsConnectionInfo:
  Enable: true
  VGConnectionRetryInterval: "30s" # retry after 30 seconds of VG connection failure
  VGConnectionMaxRetries: 100

FundTransferUpdateEventsConnectionInfo:
  Enable: false
  VGConnectionRetryInterval: "30s" # retry after 30 seconds of VG connection failure
  VGConnectionMaxRetries: 100

LivenessS3BucketName: "epifi-prod-liveness"

FaceMatchThreshold: 80

USStockCatalogRefreshPublisher:
  QueueName: "prod-usstocks-catalog-refresh-queue"

USStockCatalogRefreshSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "prod-usstocks-catalog-refresh-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 30
      MaxAttempts: 10
      TimeUnit: "Second"

UsStocksSendMailToUsersPublisher:
  QueueName: "prod-usstocks-send-mail-to-users-queue"

UsStocksSendMailToUsersSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-usstocks-send-mail-to-users-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 5
      TimeUnit: "Minute"

CommsEmail:
  FromEmailId:
    EmailId: "<EMAIL>"
    EmailName: "Fi Money"
  AlpacaEmail:
    EmailId: "<EMAIL>"
    EmailName: "Fi Money"

USStocksRedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "redis-11355.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:11355"
    Password: ""
  AuthDetails:
    SecretPath: "prod/redis/wealth/prefixaccess"
    Environment: "prod"
    Region: "ap-south-1"

SkipPanValidation: false

NonFinancialEventSqsPublisher:
  QueueName: "prod-non-financial-investment-event-queue"

MorningStarS3Bucket:
  BucketName: "epifi-prod-usstocks-morningstar-u"

BrokerFirmAccountDetailsForForeignRemittance:
  OutwardRemittanceAccount:
    AccountId: "4f53eb9f-b13d-3748-912b-a14b6fa4ed9e"
  InwardRemittanceAccount:
    AccountId: "36e23240-3cd3-3f53-a1ba-e4d06eab9866"
    BankRelationshipId: "f52e5684-486f-46ff-9d8a-a06630b9fa1c"
  RewardsFirmAccount:
    AccountId: "a5759c5a-8147-3d74-9d0c-94f78bf4bbd4"


USStocksIFTRemittanceFileProcessingEventsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-usstocks-remittance-file-processing-events-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 5
      MaxAttempts: 20
      TimeUnit: "Minute"

IsETFEnabled: true

AccountActivitySyncConfig:
  IsEnabled: true
  PageSize: 100
  Duration: 1080h # 45 days
  FirstActivityTs: ********** # Sunday, 25 December 2022 0:00:00 GMT, First activity is on 28th Dec 2022
  FirmAccountIDs:
    - "9c1a7ef3-f598-32f6-9dae-6fcc858fc06f"
    - "8f25405f-66b8-3c0a-adf1-b5dd8afee259"
    - "ba8ff5ec-1dc4-3a20-9ec8-38558716c0b6"
    - "a5759c5a-8147-3d74-9d0c-94f78bf4bbd4"
    - "4f53eb9f-b13d-3748-912b-a14b6fa4ed9e"
    - "014a5d86-0cc0-32d0-ad80-1aab0187370f"
    - "36e23240-3cd3-3f53-a1ba-e4d06eab9866"

PgdbMigrationConf:
  UsePgdb: true
  PgdbConnAlias: "usstocks_alpaca_pgdb"

HomeUsStocksCollection:
  IsHomeUsStocksCollectionReleased: true
  CollectionId: "COLLECTION_NAME_USER_PREFERRED_STOCKS"

VendorAccountActivitiesBucketName: "epifi-prod-usstocks-alpaca"

# Note: A duplicate config for Brokerage is defined at frontend and usstocks service
# If below Brokerage config is changed, respective value changes should be done at usstocks config as well and vice versa
Brokerage:
  Enabled: false
  BrokerageInPercentage: 0

USStocksIncomeUpdateEventsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-usstocks-income-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Minute"

UsStocksOpsAlertSlackChannelId: "C04RTFQCBPZ" # us-stocks-ops-alerts

USStocksTaxDocumentGenerationRequestSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-usstocks-tax-document-generation-request-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1m
    Namespace: "usstocks"

TaxDocumentsBucketName: "epifi-prod-usstocks-alpaca"

MinAndroidVersionToSupportTabbedCard: 346
MinIOSVersionToSupportTabbedCard: 495
MinAndroidVersionWithSupportForEmptyTabbedSection: 351
MinIOSVersionWithSupportForEmptyTabbedSection: 505

CreditFrozenActorIds:
  - "ACLJ7pHaeMQH+ucKurLWxD1Q240503=="
CreditFrozenActorIdsForTTUMGeneration:
  - "ACLJ7pHaeMQH+ucKurLWxD1Q240503=="

IsNewStockUniverseEnabled: true
