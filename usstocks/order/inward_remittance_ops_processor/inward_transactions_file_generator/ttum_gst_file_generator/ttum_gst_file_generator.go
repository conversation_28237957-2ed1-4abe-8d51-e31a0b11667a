package ttum_gst_file_generator

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"strings"
	"sync"

	"github.com/google/wire"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/slack-go/slack"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"

	bankCustomerPb "github.com/epifi/gamma/api/bankcust"
	iftPb "github.com/epifi/gamma/api/pay/internationalfundtransfer"
	iftFileGenPb "github.com/epifi/gamma/api/pay/internationalfundtransfer/file_generator"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/usstocks/config/genconf"
	ussErrors "github.com/epifi/gamma/usstocks/internal/errors"
	"github.com/epifi/gamma/usstocks/internal/savings"
	"github.com/epifi/gamma/usstocks/order/inward_remittance_ops_processor/inward_transaction_utils"
	"github.com/epifi/gamma/usstocks/order/inward_remittance_ops_processor/inward_transactions_file_generator"
	"github.com/epifi/gamma/usstocks/order/inward_remittance_ops_processor/inward_transactions_file_generator/ttum_gst_file_generator/recon_processor"
	"github.com/epifi/gamma/usstocks/order/inward_remittance_ops_processor/inward_transactions_file_generator/ttum_gst_file_generator/recon_processor/rounding_recon_policy"
	"github.com/epifi/gamma/usstocks/order/uservalidator"
)

type GenerateFileForTransaction struct {
	iftClient                 iftPb.InternationalFundTransferClient
	reconProcessor            recon_processor.ReconProcess
	iftFileGenClient          iftFileGenPb.FileGeneratorClient
	genConf                   *genconf.Config
	bankCustomerClient        bankCustomerPb.BankCustomerServiceClient
	userCreditFreezeValidator uservalidator.IValidator
	savingsAccessor           savings.ISavingsAccessor
	slackClient               *slack.Client
}

var GenerateFileForTransactionWireSet = wire.NewSet(NewGenerateFileForTransaction, wire.Bind(new(inward_transactions_file_generator.InwardTransactionsProcessor), new(*GenerateFileForTransaction)))

func NewGenerateFileForTransaction(
	iftClient iftPb.InternationalFundTransferClient,
	reconProcessor recon_processor.ReconProcess,
	iftFileGenClient iftFileGenPb.FileGeneratorClient,
	genConf *genconf.Config,
	bankCustomerClient bankCustomerPb.BankCustomerServiceClient,
	userCreditFreezeValidator *uservalidator.CreditFreezeValidator,
	savingsAccessor savings.ISavingsAccessor,
	slackClient *slack.Client,
) *GenerateFileForTransaction {
	return &GenerateFileForTransaction{
		iftClient:                 iftClient,
		reconProcessor:            reconProcessor,
		iftFileGenClient:          iftFileGenClient,
		genConf:                   genConf,
		bankCustomerClient:        bankCustomerClient,
		userCreditFreezeValidator: userCreditFreezeValidator,
		savingsAccessor:           savingsAccessor,
		slackClient:               slackClient,
	}
}

// nolint:funlen
func (o *GenerateFileForTransaction) GenerateAndUploadFile(ctx context.Context, request *inward_transactions_file_generator.GenerateAndUploadFileRequest) (*inward_transactions_file_generator.GenerateAndUploadFileResponse, error) {
	var (
		creditFreezeTransactions []*inward_transaction_utils.InwardTransaction
		eligibleTransactions     []*inward_transaction_utils.InwardTransaction
		userReqDataForFileGenMap = make(map[string]*UserDataForInwardRemittanceFile, 0)
	)
	grp, gCtx := errgroup.WithContext(ctx)
	m := sync.Mutex{}
	for _, _transaction := range request.Transactions {
		transaction := _transaction
		grp.Go(func() error {
			userReqDataForFileGen, err := o.getUserRequiredDataForFileGen(gCtx, transaction)
			if err != nil {
				return errors.Wrapf(err, "error getting file generation data for transaction: %s", transaction.Id)
			}
			// to avoid race condition taking lock before modify of common variable
			m.Lock()
			defer m.Unlock()

			// Populating GST for updating aggregated txn later (avoiding the need for fetching GST details again)
			transaction.Gst = userReqDataForFileGen.gstReportingDataObj.GetGst()

			// check if credit freeze or not
			if userReqDataForFileGen.isCreditFrozen {
				creditFreezeTransactions = append(creditFreezeTransactions, transaction)
			} else {
				eligibleTransactions = append(eligibleTransactions, transaction)
			}
			userReqDataForFileGenMap[transaction.ExternalId] = userReqDataForFileGen
			return nil
		})
	}
	err := grp.Wait()
	if err != nil {
		return nil, errors.Wrap(err, "error while calling go routine")
	}

	totalDebitFromPoolAccount, err := rounding_recon_policy.SumInwardTransactions(eligibleTransactions)
	if err != nil {
		return nil, errors.Wrap(err, "error getting sum of all inward transactions")
	}
	if request.RoundTransactions {
		// Note: Only non-credit frozen accounts are taken into consideration here.
		// For credit-frozen accounts, once the credit-freeze is lifted, no rounding-off is necessary.
		resp, roundingErr := o.reconProcessor.ReconTransactions(eligibleTransactions)
		if roundingErr != nil {
			return nil, errors.Wrap(roundingErr, "error rounding eligible transactions")
		}
		eligibleTransactions = resp.Transactions
		totalDebitFromPoolAccount = resp.TotalAmountDebitPoolAccount
	}

	ttumFileGenerationRequest := &TTUMFileGenerationRequest{
		eligibleTransaction:      eligibleTransactions,
		userReqDataForFileGenMap: userReqDataForFileGenMap,
		totalPoolAccountDebit:    totalDebitFromPoolAccount,
		fileGenAttempt:           request.FileGenAttempt,
		exchangeRate:             request.ExchangeRate,
	}

	if o.genConf.Flags().EnableSeparateTTUMFileChanges() {
		// create separate ttum file for user transaction and gst transaction and upload to bucket
		logger.Debug(ctx, "generating separate ttum files for user txn and gst txn")
		err = o.generateTTUMAndUploadV2(ctx, ttumFileGenerationRequest)
		if err != nil {
			return nil, errors.Wrap(err, "error while creating and uploading separate TTUM file")
		}
	} else {
		// create ttum file and upload at bucket
		logger.Debug(ctx, "generating single ttum file for inward fund transfer")
		err = o.generateTTUMAndUpload(ctx, ttumFileGenerationRequest)
		if err != nil {
			return nil, errors.Wrap(err, "error while create and upload of ttum")
		}
	}

	// create gst reporting file and upload at bucket
	err = o.generateAndUploadGstReportingFile(ctx, &GstReportingGenerationRequest{
		eligibleTransactions:     eligibleTransactions,
		userReqDataForFileGenMap: userReqDataForFileGenMap,
		fgaClientReqId:           fmt.Sprintf("%s_GST", request.FileGenAttempt.GetClientRequestId()),
	})
	if err != nil {
		return nil, errors.Wrap(err, "error while create and upload of gst reporting")
	}

	if len(creditFreezeTransactions) > 0 {
		goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
			_, _, err = o.slackClient.PostMessage(o.genConf.UsStocksOpsAlertSlackChannelId(),
				slack.MsgOptionAttachments(slack.Attachment{
					Pretext: "Transactions excluded during bulk inward remittance",
					Fields: []slack.AttachmentField{
						{
							Title: "File Generation Attempt Client Request ID",
							Value: request.FileGenAttempt.GetClientRequestId(),
						},
						{
							Title: "Transactions",
							Value: strings.Join(lo.Map(creditFreezeTransactions,
								func(t *inward_transaction_utils.InwardTransaction, _ int) string { return t.Id }), ","),
						},
					}},
				),
			)
			if err != nil {
				// not propagating error as transactions would eventually be picked up during recon.
				logger.Error(ctx, "error sending slack message for transactions excluded during bulk inward remittance", zap.Error(err))
			}
		})
	}
	// return all inward_transaction_processor type
	return &inward_transactions_file_generator.GenerateAndUploadFileResponse{
		EligibleTransactions:     eligibleTransactions,
		CreditFreezeTransactions: creditFreezeTransactions,
	}, nil
}

func (o *GenerateFileForTransaction) getUserRequiredDataForFileGen(_ctx context.Context, transaction *inward_transaction_utils.InwardTransaction) (*UserDataForInwardRemittanceFile, error) {
	var (
		savingsAccount *savingsPb.Account
		gstResp        *iftPb.GetGstReportingDataForInwardTxnResponse
		solId          string
		isCreditFrozen bool
	)
	grp, ctx := errgroup.WithContext(_ctx)
	grp.Go(func() error {
		var err error
		savingsAccount, err = o.savingsAccessor.GetSavingsAccount(ctx, transaction.ActorId)
		if err != nil {
			return errors.Wrap(err, "error getting savings account")
		}
		return nil
	})

	grp.Go(func() error {
		var err error
		gstResp, err = o.iftClient.GetGstReportingDataForInwardTxn(ctx, &iftPb.GetGstReportingDataForInwardTxnRequest{
			Identifier: &iftPb.GetGstReportingDataForInwardTxnRequest_ActorId{ActorId: transaction.ActorId},
			GrossValue: transaction.AmountInINR,
			TxnTime:    timestampPb.Now(),
		})
		if err = epifigrpc.RPCError(gstResp, err); err != nil {
			return errors.Wrap(err, "error while calling GetGstReportingDataForInwardTxn")
		}
		return nil
	})

	grp.Go(func() error {
		bankCustomer, err := o.getBankCustomerForActor(ctx, transaction.ActorId)
		if err != nil {
			return errors.Wrap(err, "unable to fetch bank customer")
		}
		solId = bankCustomer.GetVendorMetadata().GetFederalMetadata().GetSolId()
		return nil
	})

	grp.Go(func() error {
		err := o.userCreditFreezeValidator.Validate(ctx, transaction.ActorId)
		if err != nil {
			if errors.Is(err, ussErrors.ErrCreditFrozenForUser) {
				logger.Info(ctx, "credit is frozen to account", zap.Error(err), zap.String(logger.ACTOR_ID_V2, transaction.ActorId))
				isCreditFrozen = true
				return nil
			}
			return errors.Wrap(err, "error validating if credit is frozen to account")
		}
		// TODO (Aniket): remove this after, wire transfer fee charges issue is resolved
		if lo.Contains(o.genConf.CreditFrozenActorIdsForTTUMGeneration().ToStringArray(), transaction.ActorId) {
			logger.Info(ctx, "ignoring actor id for ttum file generation and marking credit freeze", zap.Error(err), zap.String(logger.ACTOR_ID_V2, transaction.ActorId))
			isCreditFrozen = true
			return nil
		}
		return nil
	})

	err := grp.Wait()
	if err != nil {
		return nil, errors.Wrap(err, "error in go routine")
	}
	return &UserDataForInwardRemittanceFile{
		gstReportingDataObj: gstResp,
		accountNumber:       savingsAccount.GetAccountNo(),
		solId:               solId,
		isCreditFrozen:      isCreditFrozen,
	}, nil
}

func (o *GenerateFileForTransaction) getBankCustomerForActor(ctx context.Context, actorId string) (*bankCustomerPb.BankCustomer, error) {
	resp, err := o.bankCustomerClient.GetBankCustomer(ctx, &bankCustomerPb.GetBankCustomerRequest{
		Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{ActorId: actorId},
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		return nil, te
	}
	return resp.GetBankCustomer(), nil
}
