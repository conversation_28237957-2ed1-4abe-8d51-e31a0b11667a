package ttum_gst_file_generator

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"os"
	"sort"
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/testing/protocmp"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	pkgMocks "github.com/epifi/be-common/pkg/mock"
	"github.com/epifi/be-common/pkg/money"

	bankCustomerPb "github.com/epifi/gamma/api/bankcust"
	bankCustMocks "github.com/epifi/gamma/api/bankcust/mocks"
	iftPb "github.com/epifi/gamma/api/pay/internationalfundtransfer"
	iftFileGenPb "github.com/epifi/gamma/api/pay/internationalfundtransfer/file_generator"
	iftFgMocks "github.com/epifi/gamma/api/pay/internationalfundtransfer/file_generator/mocks"
	iftMocks "github.com/epifi/gamma/api/pay/internationalfundtransfer/mocks"
	savingsClientPb "github.com/epifi/gamma/api/savings"
	ussErrors "github.com/epifi/gamma/usstocks/internal/errors"
	internalSavingsMocks "github.com/epifi/gamma/usstocks/internal/savings/mocks"
	"github.com/epifi/gamma/usstocks/order/inward_remittance_ops_processor/inward_transaction_utils"
	"github.com/epifi/gamma/usstocks/order/inward_remittance_ops_processor/inward_transactions_file_generator"
	"github.com/epifi/gamma/usstocks/order/inward_remittance_ops_processor/inward_transactions_file_generator/ttum_gst_file_generator/recon_processor"
	reconProcessorMocks "github.com/epifi/gamma/usstocks/order/inward_remittance_ops_processor/inward_transactions_file_generator/ttum_gst_file_generator/recon_processor/mocks"
	userValidatorMocks "github.com/epifi/gamma/usstocks/order/uservalidator/mocks"
)

func TestGenerateFileForTransaction_ProcessTransaction(t *testing.T) {
	ctr := gomock.NewController(t)
	mockIftClient := iftMocks.NewMockInternationalFundTransferClient(ctr)
	mockIftFgClient := iftFgMocks.NewMockFileGeneratorClient(ctr)
	mockReconProcess := reconProcessorMocks.NewMockReconProcess(ctr)
	mockBankCustomerServiceClient := bankCustMocks.NewMockBankCustomerServiceClient(ctr)
	mockCreditFreezeValidator := userValidatorMocks.NewMockIValidator(ctr)
	mockSavingsAccessor := internalSavingsMocks.NewMockISavingsAccessor(ctr)

	actorId1 := "actor-id-1"
	actorId2 := "actor-id-2"
	actorId3 := "actor-id-3"
	actorId4 := "actor-id-credit-freeze"

	aggTxn1 := "agg-txn-id-1"
	aggTxn2 := "agg-txn-id-2"
	aggTxn3 := "agg-txn-id-3"
	aggTxn4 := "agg-txn-id-4"

	externalId1 := "external-id-1"
	externalId2 := "external-id-2"
	externalId3 := "external-id-3"
	externalId4 := "external-id-4"

	sampleFileGenAttempt := &iftFileGenPb.FileGenerationAttempt{
		Id:              "fg-id-1",
		ClientRequestId: "client-req-id",
		FileName:        "fileName.txt",
		FileType:        iftFileGenPb.FileType_FILE_TYPE_INWARD_FUND_TRANSFER,
		FileStatus:      iftFileGenPb.FileStatus_FILE_STATUS_VENDOR_PROCESSED,
		Vendor:          commonvgpb.Vendor_FEDERAL_BANK,
	}

	sampleFileGenAttempt2 := &iftFileGenPb.FileGenerationAttempt{
		Id:              "fg-id-2",
		ClientRequestId: "client-req-id_GST",
		FileName:        "fileName2.txt",
		FileType:        iftFileGenPb.FileType_FILE_TYPE_INWARD_FUND_TRANSFER,
		FileStatus:      iftFileGenPb.FileStatus_FILE_STATUS_VENDOR_PROCESSED,
		Vendor:          commonvgpb.Vendor_FEDERAL_BANK,
	}

	reconSample1 := []*inward_transaction_utils.InwardTransaction{
		{
			ActorId: actorId1,
			AmountInUSD: &moneyPb.Money{
				CurrencyCode: money.USDCurrencyCode,
				Units:        10,
				Nanos:        350_000_000,
			},
			AmountInINR: &moneyPb.Money{
				CurrencyCode: money.RupeeCurrencyCode,
				Units:        10499,
				Nanos:        *********,
			},
			Gst: &moneyPb.Money{
				CurrencyCode: money.RupeeCurrencyCode,
				Units:        45,
			},
			Id:         aggTxn1,
			ExternalId: externalId1,
		},
		{
			ActorId: actorId2,
			AmountInUSD: &moneyPb.Money{
				CurrencyCode: money.USDCurrencyCode,
				Units:        10,
				Nanos:        350_000_000,
			},
			AmountInINR: &moneyPb.Money{
				CurrencyCode: money.RupeeCurrencyCode,
				Units:        2300,
				Nanos:        *********,
			},
			Gst: &moneyPb.Money{
				CurrencyCode: money.RupeeCurrencyCode,
				Units:        45,
			},
			Id:         aggTxn2,
			ExternalId: externalId2,
		},
		{
			ActorId: actorId3,
			AmountInUSD: &moneyPb.Money{
				CurrencyCode: money.USDCurrencyCode,
				Units:        10,
				Nanos:        350_000_000,
			},
			AmountInINR: &moneyPb.Money{
				CurrencyCode: money.RupeeCurrencyCode,
				Units:        2900,
			},
			Gst: &moneyPb.Money{
				CurrencyCode: money.RupeeCurrencyCode,
				Units:        45,
			},
			Id:         aggTxn3,
			ExternalId: externalId3,
		},
	}

	reconSample2 := []*inward_transaction_utils.InwardTransaction{
		{
			ActorId: actorId1,
			AmountInUSD: &moneyPb.Money{
				CurrencyCode: money.USDCurrencyCode,
				Units:        10,
				Nanos:        350_000_000,
			},
			AmountInINR: &moneyPb.Money{
				CurrencyCode: money.RupeeCurrencyCode,
				Units:        10499,
				Nanos:        *********,
			},
			Gst: &moneyPb.Money{
				CurrencyCode: money.RupeeCurrencyCode,
				Units:        45,
			},
			Id:         aggTxn1,
			ExternalId: externalId1,
		},
		{
			ActorId: actorId2,
			AmountInUSD: &moneyPb.Money{
				CurrencyCode: money.USDCurrencyCode,
				Units:        10,
				Nanos:        350_000_000,
			},
			AmountInINR: &moneyPb.Money{
				CurrencyCode: money.RupeeCurrencyCode,
				Units:        2300,
				Nanos:        *********,
			},
			Gst: &moneyPb.Money{
				CurrencyCode: money.RupeeCurrencyCode,
				Units:        45,
			},
			Id:         aggTxn2,
			ExternalId: externalId2,
		},
	}

	sampleTransactionsCreditFreeze := []*inward_transaction_utils.InwardTransaction{
		{
			ActorId: actorId1,
			AmountInUSD: &moneyPb.Money{
				CurrencyCode: money.USDCurrencyCode,
				Units:        10,
				Nanos:        350_000_000,
			},
			AmountInINR: &moneyPb.Money{
				CurrencyCode: money.RupeeCurrencyCode,
				Units:        10500,
			},
			Gst: &moneyPb.Money{
				CurrencyCode: money.RupeeCurrencyCode,
				Units:        45,
			},
			Id:         aggTxn1,
			ExternalId: externalId1,
		},
		{
			ActorId: actorId2,
			AmountInUSD: &moneyPb.Money{
				CurrencyCode: money.USDCurrencyCode,
				Units:        10,
				Nanos:        350_000_000,
			},
			AmountInINR: &moneyPb.Money{
				CurrencyCode: money.RupeeCurrencyCode,
				Units:        2300,
				Nanos:        *********,
			},
			Gst: &moneyPb.Money{
				CurrencyCode: money.RupeeCurrencyCode,
				Units:        45,
			},
			Id:         aggTxn2,
			ExternalId: externalId2,
		},
	}

	sampleTransactions := []*inward_transaction_utils.InwardTransaction{
		{
			ActorId: actorId1,
			AmountInUSD: &moneyPb.Money{
				CurrencyCode: money.USDCurrencyCode,
				Units:        10,
				Nanos:        350_000_000,
			},
			AmountInINR: &moneyPb.Money{
				CurrencyCode: money.RupeeCurrencyCode,
				Units:        10500,
			},
			Gst: &moneyPb.Money{
				CurrencyCode: money.RupeeCurrencyCode,
				Units:        45,
			},
			Id:         aggTxn1,
			ExternalId: externalId1,
		},
		{
			ActorId: actorId2,
			AmountInUSD: &moneyPb.Money{
				CurrencyCode: money.USDCurrencyCode,
				Units:        10,
				Nanos:        350_000_000,
			},
			AmountInINR: &moneyPb.Money{
				CurrencyCode: money.RupeeCurrencyCode,
				Units:        2300,
				Nanos:        *********,
			},
			Gst: &moneyPb.Money{
				CurrencyCode: money.RupeeCurrencyCode,
				Units:        45,
			},
			Id:         aggTxn2,
			ExternalId: externalId2,
		},
		{
			ActorId: actorId3,
			AmountInUSD: &moneyPb.Money{
				CurrencyCode: money.USDCurrencyCode,
				Units:        10,
				Nanos:        350_000_000,
			},
			AmountInINR: &moneyPb.Money{
				CurrencyCode: money.RupeeCurrencyCode,
				Units:        2900,
			},
			Gst: &moneyPb.Money{
				CurrencyCode: money.RupeeCurrencyCode,
				Units:        45,
			},
			Id:         aggTxn3,
			ExternalId: externalId3,
		},
	}
	sampleTransactions2 := []*inward_transaction_utils.InwardTransaction{
		{
			ActorId: actorId1,
			AmountInUSD: &moneyPb.Money{
				CurrencyCode: money.USDCurrencyCode,
				Units:        10,
				Nanos:        350_000_000,
			},
			AmountInINR: &moneyPb.Money{
				CurrencyCode: money.RupeeCurrencyCode,
				Units:        10500,
			},
			Gst: &moneyPb.Money{
				CurrencyCode: money.RupeeCurrencyCode,
				Units:        45,
			},
			Id:         aggTxn1,
			ExternalId: externalId1,
		},
		{
			ActorId: actorId2,
			AmountInUSD: &moneyPb.Money{
				CurrencyCode: money.USDCurrencyCode,
				Units:        10,
				Nanos:        350_000_000,
			},
			AmountInINR: &moneyPb.Money{
				CurrencyCode: money.RupeeCurrencyCode,
				Units:        2300,
				Nanos:        *********,
			},
			Gst: &moneyPb.Money{
				CurrencyCode: money.RupeeCurrencyCode,
				Units:        45,
			},
			Id:         aggTxn2,
			ExternalId: externalId2,
		},
		{
			ActorId: actorId4,
			AmountInUSD: &moneyPb.Money{
				CurrencyCode: money.USDCurrencyCode,
				Units:        10,
				Nanos:        350_000_000,
			},
			AmountInINR: &moneyPb.Money{
				CurrencyCode: money.RupeeCurrencyCode,
				Units:        2900,
			},
			Gst: &moneyPb.Money{
				CurrencyCode: money.RupeeCurrencyCode,
				Units:        45,
			},
			Id:         aggTxn4,
			ExternalId: externalId4,
		},
	}
	txnTs := timestampPb.New(time.Date(2023, 8, 21, 0, 0, 0, 0, datetime.EST5EDT))
	sampleGstReportingData := []*iftPb.GSTReportingDataModel{
		{
			GstinOfFilingOrganisation: "32AABCT0020H3Z3",
			SectionName:               "GST",
			TransactionTime:           txnTs,
			UidOfReceiver:             "98765",
			GstinOfEcommOperator:      "GSTIN123",
			NameOfRecipient:           "GOVT",
			EmailIdOfRecipient:        "-",
			InvoiceType:               "R",
			CompanyCode:               "-",
			LocationCode:              "24",
			InvoiceNumber:             "FEDFPL001",
			TxnAmount: &moneyPb.Money{
				CurrencyCode: money.RupeeCurrencyCode,
				Units:        14088,
				Nanos:        *********,
			},
			TaxableAmount: &moneyPb.Money{
				CurrencyCode: money.RupeeCurrencyCode,
				Units:        14088,
				Nanos:        *********,
			},
			InvoiceHsn:       "997113",
			GoodsDescription: "EQUITY SALE",
			Uqc:              "",
			GoodsQty:         "",
			ValueOfGoodsSold: "14088.60",
			GstPercent:       9,
			CgstAmount:       money.ZeroINR().GetPb(),
			IgstAmount:       money.ZeroINR().GetPb(),
			SgstAmount: &moneyPb.Money{
				CurrencyCode: money.RupeeCurrencyCode,
				Units:        22,
				Nanos:        *********,
			},
			CessAmount:            money.ZeroINR().GetPb(),
			Pos:                   "21",
			ReverseCharge:         "N",
			Flag:                  "",
			IsSez:                 "N",
			ReceiverAccountNumber: "sgstAccount",
			SgstAccountSolId:      "solid-sgst",
		},
		{
			GstinOfFilingOrganisation: "32AABCT0020H3Z3",
			SectionName:               "GST",
			TransactionTime:           txnTs,
			UidOfReceiver:             "98765",
			GstinOfEcommOperator:      "GSTIN123",
			NameOfRecipient:           "GOVT",
			EmailIdOfRecipient:        "-",
			InvoiceType:               "R",
			CompanyCode:               "-",
			LocationCode:              "24",
			InvoiceNumber:             "FEDFPL001",
			TxnAmount: &moneyPb.Money{
				CurrencyCode: money.RupeeCurrencyCode,
				Units:        14088,
				Nanos:        *********,
			},
			TaxableAmount: &moneyPb.Money{
				CurrencyCode: money.RupeeCurrencyCode,
				Units:        14088,
				Nanos:        *********,
			},
			InvoiceHsn:       "997113",
			GoodsDescription: "EQUITY SALE",
			Uqc:              "",
			GoodsQty:         "",
			ValueOfGoodsSold: "14088.60",
			GstPercent:       9,
			CgstAmount: &moneyPb.Money{
				CurrencyCode: money.RupeeCurrencyCode,
				Units:        22,
				Nanos:        *********,
			},
			IgstAmount:            money.ZeroINR().GetPb(),
			SgstAmount:            money.ZeroINR().GetPb(),
			CessAmount:            money.ZeroINR().GetPb(),
			Pos:                   "21",
			ReverseCharge:         "N",
			Flag:                  "",
			IsSez:                 "N",
			ReceiverAccountNumber: "cgstAccount",
			CgstAccountSolId:      "solid-cgst",
		},
	}
	sampleReq := inward_transactions_file_generator.GenerateAndUploadFileRequest{
		Transactions:      sampleTransactions,
		FileGenAttempt:    sampleFileGenAttempt,
		RoundTransactions: true,
		ExchangeRate:      &moneyPb.Money{CurrencyCode: money.RupeeCurrencyCode, Units: 82, Nanos: 550_000_000},
	}
	sampleReq2 := inward_transactions_file_generator.GenerateAndUploadFileRequest{
		Transactions:      sampleTransactions2,
		FileGenAttempt:    sampleFileGenAttempt,
		RoundTransactions: true,
		ExchangeRate:      &moneyPb.Money{CurrencyCode: money.RupeeCurrencyCode, Units: 82, Nanos: 550_000_000},
	}
	fundTransferFile1Data, err := os.ReadFile("test_fund_transfer_file_1.txt")
	if err != nil {
		t.Errorf("error reading file: %v", err)
		return
	}
	gstReportingFile1Data, err := os.ReadFile("test_gst_reporting_file_1.csv")
	if err != nil {
		t.Errorf("error reading file: %v", err)
		return
	}
	fundTransferFile2Data, err := os.ReadFile("test_fund_transfer_file_2.txt")
	if err != nil {
		t.Errorf("error reading file: %v", err)
		return
	}
	gstReportingFile2Data, err := os.ReadFile("test_gst_reporting_file_2.csv")
	if err != nil {
		t.Errorf("error reading file: %v", err)
		return
	}
	userFundTransferFile1Data, err := os.ReadFile("test_user_fund_transfer_file_1.txt")
	if err != nil {
		t.Errorf("error reading file: %v", err)
		return
	}
	taxFundTransferFile1Data, err := os.ReadFile("test_tax_fund_transfer_file_1.txt")
	if err != nil {
		t.Errorf("error reading file: %v", err)
		return
	}

	type args struct {
		ctx                           context.Context
		req                           *inward_transactions_file_generator.GenerateAndUploadFileRequest
		enableSeparateTTUMFileChanges bool
	}
	tests := []struct {
		name           string
		args           args
		setupMockCalls func(arg args)
		wantErr        bool
		want           *inward_transactions_file_generator.GenerateAndUploadFileResponse
	}{
		{
			name: "successful file creation",
			args: args{
				ctx: context.Background(),
				req: &sampleReq,
			},
			setupMockCalls: func(arg args) {
				for index, actorId := range []string{actorId1, actorId2, actorId3} {
					solId := fmt.Sprintf("solid-%s", strconv.Itoa(index+1))
					mockSavingsAccessor.EXPECT().GetSavingsAccount(gomock.Any(), actorId).Return(&savingsClientPb.Account{
						AccountNo: fmt.Sprintf("account_number_%s", strconv.Itoa(index+1)),
					}, nil)
					mockBankCustomerServiceClient.EXPECT().GetBankCustomer(gomock.Any(), pkgMocks.NewProtoMatcher(&bankCustomerPb.GetBankCustomerRequest{
						Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
						Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{ActorId: actorId},
					})).Return(&bankCustomerPb.GetBankCustomerResponse{
						Status: rpc.StatusOk(),
						BankCustomer: &bankCustomerPb.BankCustomer{VendorMetadata: &bankCustomerPb.VendorMetadata{Vendor: &bankCustomerPb.VendorMetadata_FederalMetadata{FederalMetadata: &bankCustomerPb.FederalMetadata{
							SolId: solId,
						}}}},
					}, nil)
					mockIftClient.EXPECT().GetGstReportingDataForInwardTxn(gomock.Any(), gomock.Any()).Return(&iftPb.GetGstReportingDataForInwardTxnResponse{
						Status:        rpc.StatusOk(),
						ReportingData: sampleGstReportingData,
						Gst: &moneyPb.Money{
							CurrencyCode: money.RupeeCurrencyCode,
							Units:        45,
							Nanos:        0,
						},
					}, nil)
					mockCreditFreezeValidator.EXPECT().Validate(gomock.Any(), actorId).Return(nil)
				}
				// since value added by thread due race condition, soring order is not guarantee
				// so using DoReturn method
				mockReconProcess.EXPECT().ReconTransactions(gomock.Any()).DoAndReturn(func(transactions []*inward_transaction_utils.InwardTransaction) (*recon_processor.ReconProcessResp, error) {
					sort.Slice(transactions, func(i, j int) bool {
						return strings.Compare(transactions[i].ActorId, transactions[j].ActorId) < 0
					})

					if len(transactions) != len(sampleTransactions) {
						return nil, fmt.Errorf("error while mocking")
					}

					for i := 0; i < len(transactions); i++ {
						if transactions[i].Id != sampleTransactions[i].Id {
							return nil, fmt.Errorf("error while mocking in id")
						}
						if transactions[i].ActorId != sampleTransactions[i].ActorId {
							return nil, fmt.Errorf("error while mocking in actor id")
						}
					}

					return &recon_processor.ReconProcessResp{
						Transactions: reconSample1,
						TotalAmountDebitPoolAccount: &moneyPb.Money{
							CurrencyCode: money.RupeeCurrencyCode,
							Units:        15700,
						},
					}, nil
				})

				mockIftFgClient.EXPECT().UploadFile(gomock.Any(), pkgMocks.NewProtoMatcher(&iftFileGenPb.UploadFileRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					FileGenerationAttempt: &iftFileGenPb.FileGenerationAttempt{
						Id:       sampleFileGenAttempt.GetId(),
						FileType: iftFileGenPb.FileType_FILE_TYPE_INWARD_FUND_TRANSFER,
					},
					FileContents: fundTransferFile1Data,
					TotalEntries: 13,
					EntityIds:    []string{"AGGREMTXN_external-id-1", "AGGREMTXN_external-id-2", "AGGREMTXN_external-id-3"},
				})).Return(&iftFileGenPb.UploadFileResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockIftFgClient.EXPECT().UploadFile(gomock.Any(), pkgMocks.NewProtoMatcher(&iftFileGenPb.UploadFileRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					FileGenerationAttempt: &iftFileGenPb.FileGenerationAttempt{
						ClientRequestId: fmt.Sprintf("%s_GST", arg.req.FileGenAttempt.GetClientRequestId()),
						FileType:        iftFileGenPb.FileType_FILE_TYPE_GST_REPORTING_INWARD,
					},
					FileContents: gstReportingFile1Data,
					TotalEntries: 6,
				})).Return(&iftFileGenPb.UploadFileResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			wantErr: false,
			want: &inward_transactions_file_generator.GenerateAndUploadFileResponse{
				EligibleTransactions:     reconSample1,
				CreditFreezeTransactions: nil,
			},
		},
		{
			name: "error while uploading gst reporting file",
			args: args{
				ctx: context.Background(),
				req: &sampleReq,
			},
			setupMockCalls: func(arg args) {
				for index, actorId := range []string{actorId1, actorId2, actorId3} {
					solId := fmt.Sprintf("solid-%s", strconv.Itoa(index+1))
					mockSavingsAccessor.EXPECT().GetSavingsAccount(gomock.Any(), actorId).Return(&savingsClientPb.Account{
						AccountNo: fmt.Sprintf("account_number_%s", strconv.Itoa(index+1)),
					}, nil)
					mockBankCustomerServiceClient.EXPECT().GetBankCustomer(gomock.Any(), pkgMocks.NewProtoMatcher(&bankCustomerPb.GetBankCustomerRequest{
						Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
						Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{ActorId: actorId},
					})).Return(&bankCustomerPb.GetBankCustomerResponse{
						Status: rpc.StatusOk(),
						BankCustomer: &bankCustomerPb.BankCustomer{VendorMetadata: &bankCustomerPb.VendorMetadata{Vendor: &bankCustomerPb.VendorMetadata_FederalMetadata{FederalMetadata: &bankCustomerPb.FederalMetadata{
							SolId: solId,
						}}}},
					}, nil)
					mockIftClient.EXPECT().GetGstReportingDataForInwardTxn(gomock.Any(), gomock.Any()).Return(&iftPb.GetGstReportingDataForInwardTxnResponse{
						Status:        rpc.StatusOk(),
						ReportingData: sampleGstReportingData,
						Gst: &moneyPb.Money{
							CurrencyCode: money.RupeeCurrencyCode,
							Units:        45,
							Nanos:        0,
						},
					}, nil)
					mockCreditFreezeValidator.EXPECT().Validate(gomock.Any(), actorId).Return(nil)
				}
				// since value added by thread due race condition, soring order is not guarantee
				// so using DoReturn method
				mockReconProcess.EXPECT().ReconTransactions(gomock.Any()).DoAndReturn(func(transactions []*inward_transaction_utils.InwardTransaction) (*recon_processor.ReconProcessResp, error) {
					sort.Slice(transactions, func(i, j int) bool {
						return strings.Compare(transactions[i].ActorId, transactions[j].ActorId) < 0
					})

					if len(transactions) != len(sampleTransactions) {
						return nil, fmt.Errorf("error while mocking")
					}

					for i := 0; i < len(transactions); i++ {
						if transactions[i].Id != sampleTransactions[i].Id {
							return nil, fmt.Errorf("error while mocking in id")
						}
						if transactions[i].ActorId != sampleTransactions[i].ActorId {
							return nil, fmt.Errorf("error while mocking in actor id")
						}
					}

					return &recon_processor.ReconProcessResp{
						Transactions: reconSample1,
						TotalAmountDebitPoolAccount: &moneyPb.Money{
							CurrencyCode: money.RupeeCurrencyCode,
							Units:        15700,
						},
					}, nil
				})

				mockIftFgClient.EXPECT().UploadFile(gomock.Any(), pkgMocks.NewProtoMatcher(&iftFileGenPb.UploadFileRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					FileGenerationAttempt: &iftFileGenPb.FileGenerationAttempt{
						Id:       sampleFileGenAttempt.GetId(),
						FileType: iftFileGenPb.FileType_FILE_TYPE_INWARD_FUND_TRANSFER,
					},
					FileContents: fundTransferFile1Data,
					TotalEntries: 13,
					EntityIds:    []string{"AGGREMTXN_external-id-1", "AGGREMTXN_external-id-2", "AGGREMTXN_external-id-3"},
				})).Return(&iftFileGenPb.UploadFileResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockIftFgClient.EXPECT().UploadFile(gomock.Any(), pkgMocks.NewProtoMatcher(&iftFileGenPb.UploadFileRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					FileGenerationAttempt: &iftFileGenPb.FileGenerationAttempt{
						ClientRequestId: fmt.Sprintf("%s_GST", arg.req.FileGenAttempt.GetClientRequestId()),
						FileType:        iftFileGenPb.FileType_FILE_TYPE_GST_REPORTING_INWARD,
					},
					FileContents: gstReportingFile1Data,
					TotalEntries: 6,
				})).Return(&iftFileGenPb.UploadFileResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			wantErr: true,
		},
		{
			name: "error while uploading fund transfer file",
			args: args{
				ctx: context.Background(),
				req: &sampleReq,
			},
			setupMockCalls: func(arg args) {
				for index, actorId := range []string{actorId1, actorId2, actorId3} {
					solId := fmt.Sprintf("solid-%s", strconv.Itoa(index+1))
					mockSavingsAccessor.EXPECT().GetSavingsAccount(gomock.Any(), actorId).Return(&savingsClientPb.Account{
						AccountNo: fmt.Sprintf("account_number_%s", strconv.Itoa(index+1)),
					}, nil)
					mockBankCustomerServiceClient.EXPECT().GetBankCustomer(gomock.Any(), pkgMocks.NewProtoMatcher(&bankCustomerPb.GetBankCustomerRequest{
						Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
						Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{ActorId: actorId},
					})).Return(&bankCustomerPb.GetBankCustomerResponse{
						Status: rpc.StatusOk(),
						BankCustomer: &bankCustomerPb.BankCustomer{VendorMetadata: &bankCustomerPb.VendorMetadata{Vendor: &bankCustomerPb.VendorMetadata_FederalMetadata{FederalMetadata: &bankCustomerPb.FederalMetadata{
							SolId: solId,
						}}}},
					}, nil)
					mockIftClient.EXPECT().GetGstReportingDataForInwardTxn(gomock.Any(), gomock.Any()).Return(&iftPb.GetGstReportingDataForInwardTxnResponse{
						Status:        rpc.StatusOk(),
						ReportingData: sampleGstReportingData,
						Gst: &moneyPb.Money{
							CurrencyCode: money.RupeeCurrencyCode,
							Units:        45,
							Nanos:        0,
						},
					}, nil)
					mockCreditFreezeValidator.EXPECT().Validate(gomock.Any(), actorId).Return(nil)
				}
				// since value added by thread due race condition, soring order is not guarantee
				// so using DoReturn method
				mockReconProcess.EXPECT().ReconTransactions(gomock.Any()).DoAndReturn(func(transactions []*inward_transaction_utils.InwardTransaction) (*recon_processor.ReconProcessResp, error) {
					sort.Slice(transactions, func(i, j int) bool {
						return strings.Compare(transactions[i].ActorId, transactions[j].ActorId) < 0
					})

					if len(transactions) != len(sampleTransactions) {
						return nil, fmt.Errorf("error while mocking")
					}

					for i := 0; i < len(transactions); i++ {
						if transactions[i].Id != sampleTransactions[i].Id {
							return nil, fmt.Errorf("error while mocking in id")
						}
						if transactions[i].ActorId != sampleTransactions[i].ActorId {
							return nil, fmt.Errorf("error while mocking in actor id")
						}
					}

					return &recon_processor.ReconProcessResp{
						Transactions: reconSample1,
						TotalAmountDebitPoolAccount: &moneyPb.Money{
							CurrencyCode: money.RupeeCurrencyCode,
							Units:        15700,
						},
					}, nil
				})

				mockIftFgClient.EXPECT().UploadFile(gomock.Any(), pkgMocks.NewProtoMatcher(&iftFileGenPb.UploadFileRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					FileGenerationAttempt: &iftFileGenPb.FileGenerationAttempt{
						Id:       sampleFileGenAttempt.GetId(),
						FileType: iftFileGenPb.FileType_FILE_TYPE_INWARD_FUND_TRANSFER,
					},
					FileContents: fundTransferFile1Data,
					TotalEntries: 13,
					EntityIds:    []string{"AGGREMTXN_external-id-1", "AGGREMTXN_external-id-2", "AGGREMTXN_external-id-3"},
				})).Return(&iftFileGenPb.UploadFileResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			wantErr: true,
		},
		{
			name: "unsuccessful file creation and error during calculating gst for one transaction",
			args: args{
				ctx: context.Background(),
				req: &sampleReq,
			},
			setupMockCalls: func(arg args) {
				for index, actorId := range []string{actorId1, actorId2} {
					solId := fmt.Sprintf("solid-%s", strconv.Itoa(index+1))
					mockSavingsAccessor.EXPECT().GetSavingsAccount(gomock.Any(), actorId).Return(&savingsClientPb.Account{
						AccountNo: fmt.Sprintf("account_number_%s", strconv.Itoa(index+1)),
					}, nil)
					mockBankCustomerServiceClient.EXPECT().GetBankCustomer(gomock.Any(), pkgMocks.NewProtoMatcher(&bankCustomerPb.GetBankCustomerRequest{
						Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
						Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{ActorId: actorId},
					})).Return(&bankCustomerPb.GetBankCustomerResponse{
						Status: rpc.StatusOk(),
						BankCustomer: &bankCustomerPb.BankCustomer{VendorMetadata: &bankCustomerPb.VendorMetadata{Vendor: &bankCustomerPb.VendorMetadata_FederalMetadata{FederalMetadata: &bankCustomerPb.FederalMetadata{
							SolId: solId,
						}}}},
					}, nil)
					mockIftClient.EXPECT().GetGstReportingDataForInwardTxn(gomock.Any(), gomock.Any()).Return(&iftPb.GetGstReportingDataForInwardTxnResponse{
						Status:        rpc.StatusOk(),
						ReportingData: sampleGstReportingData,
						Gst: &moneyPb.Money{
							CurrencyCode: money.RupeeCurrencyCode,
							Units:        45,
							Nanos:        0,
						},
					}, nil).MaxTimes(1)
					mockCreditFreezeValidator.EXPECT().Validate(gomock.Any(), actorId).Return(nil)
				}
				mockSavingsAccessor.EXPECT().GetSavingsAccount(gomock.Any(), actorId3).Return(&savingsClientPb.Account{
					AccountNo: "account_number_3",
				}, nil).Times(1)
				mockBankCustomerServiceClient.EXPECT().GetBankCustomer(gomock.Any(), pkgMocks.NewProtoMatcher(&bankCustomerPb.GetBankCustomerRequest{
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{ActorId: actorId3},
				})).Return(&bankCustomerPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankCustomerPb.BankCustomer{VendorMetadata: &bankCustomerPb.VendorMetadata{Vendor: &bankCustomerPb.VendorMetadata_FederalMetadata{FederalMetadata: &bankCustomerPb.FederalMetadata{
						SolId: "solid-3",
					}}}},
				}, nil)

				mockIftClient.EXPECT().GetGstReportingDataForInwardTxn(gomock.Any(), gomock.Any()).Return(&iftPb.GetGstReportingDataForInwardTxnResponse{
					Status: rpc.StatusInternal()}, nil).Times(1)
				mockCreditFreezeValidator.EXPECT().Validate(gomock.Any(), actorId3).Return(nil).Times(1)
			},
			wantErr: true,
		},
		{
			name: "handling when one account is in credit freeze state",
			args: args{
				ctx: context.Background(),
				req: &sampleReq,
			},
			setupMockCalls: func(arg args) {
				for index, actorId := range []string{actorId1, actorId2, actorId3} {
					solId := fmt.Sprintf("solid-%s", strconv.Itoa(index+1))
					var restrictions []savingsClientPb.Restriction
					if actorId == actorId3 {
						restrictions = []savingsClientPb.Restriction{savingsClientPb.Restriction_RESTRICTION_CREDIT_FREEZE}
						mockCreditFreezeValidator.EXPECT().Validate(gomock.Any(), actorId).Return(ussErrors.ErrCreditFrozenForUser)
					} else {
						mockCreditFreezeValidator.EXPECT().Validate(gomock.Any(), actorId).Return(nil)
					}
					mockSavingsAccessor.EXPECT().GetSavingsAccount(gomock.Any(), actorId).Return(&savingsClientPb.Account{
						AccountNo: fmt.Sprintf("account_number_%s", strconv.Itoa(index+1)),
						Constraints: &savingsClientPb.AccountConstraints{
							Restrictions: restrictions,
						},
					}, nil)
					mockBankCustomerServiceClient.EXPECT().GetBankCustomer(gomock.Any(), pkgMocks.NewProtoMatcher(&bankCustomerPb.GetBankCustomerRequest{
						Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
						Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{ActorId: actorId},
					})).Return(&bankCustomerPb.GetBankCustomerResponse{
						Status: rpc.StatusOk(),
						BankCustomer: &bankCustomerPb.BankCustomer{VendorMetadata: &bankCustomerPb.VendorMetadata{Vendor: &bankCustomerPb.VendorMetadata_FederalMetadata{FederalMetadata: &bankCustomerPb.FederalMetadata{
							SolId: solId,
						}}}},
					}, nil)
					mockIftClient.EXPECT().GetGstReportingDataForInwardTxn(gomock.Any(), gomock.Any()).Return(&iftPb.GetGstReportingDataForInwardTxnResponse{
						Status:        rpc.StatusOk(),
						ReportingData: sampleGstReportingData,
						Gst: &moneyPb.Money{
							CurrencyCode: money.RupeeCurrencyCode,
							Units:        45,
							Nanos:        0,
						},
					}, nil)
				}
				// since value added by thread due race condition, soring order is not guarantee
				// so using DoReturn method
				mockReconProcess.EXPECT().ReconTransactions(gomock.Any()).DoAndReturn(func(transactions []*inward_transaction_utils.InwardTransaction) (*recon_processor.ReconProcessResp, error) {
					sort.Slice(transactions, func(i, j int) bool {
						return strings.Compare(transactions[i].ActorId, transactions[j].ActorId) < 0
					})

					if len(transactions) != len(sampleTransactionsCreditFreeze) {
						return nil, fmt.Errorf("error while mocking")
					}

					for i := 0; i < len(transactions); i++ {
						if transactions[i].Id != sampleTransactionsCreditFreeze[i].Id {
							return nil, fmt.Errorf("error while mocking in id")
						}
						if transactions[i].ActorId != sampleTransactionsCreditFreeze[i].ActorId {
							return nil, fmt.Errorf("error while mocking in actor id")
						}
					}

					return &recon_processor.ReconProcessResp{
						Transactions: reconSample2,
						TotalAmountDebitPoolAccount: &moneyPb.Money{
							CurrencyCode: money.RupeeCurrencyCode,
							Units:        12800,
						},
					}, nil
				})

				mockIftFgClient.EXPECT().UploadFile(gomock.Any(), pkgMocks.NewProtoMatcher(&iftFileGenPb.UploadFileRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					FileGenerationAttempt: &iftFileGenPb.FileGenerationAttempt{
						Id:       sampleFileGenAttempt.GetId(),
						FileType: iftFileGenPb.FileType_FILE_TYPE_INWARD_FUND_TRANSFER,
					},
					FileContents: fundTransferFile2Data,
					TotalEntries: 9,
					EntityIds:    []string{"AGGREMTXN_external-id-1", "AGGREMTXN_external-id-2"},
				})).Return(&iftFileGenPb.UploadFileResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockIftFgClient.EXPECT().UploadFile(gomock.Any(), pkgMocks.NewProtoMatcher(&iftFileGenPb.UploadFileRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					FileGenerationAttempt: &iftFileGenPb.FileGenerationAttempt{
						ClientRequestId: fmt.Sprintf("%s_GST", arg.req.FileGenAttempt.GetClientRequestId()),
						FileType:        iftFileGenPb.FileType_FILE_TYPE_GST_REPORTING_INWARD,
					},
					FileContents: gstReportingFile2Data,
					TotalEntries: 4,
				})).Return(&iftFileGenPb.UploadFileResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			wantErr: false,
			want: &inward_transactions_file_generator.GenerateAndUploadFileResponse{
				EligibleTransactions: reconSample2,
				CreditFreezeTransactions: []*inward_transaction_utils.InwardTransaction{{
					ActorId: actorId3,
					AmountInUSD: &moneyPb.Money{
						CurrencyCode: money.USDCurrencyCode,
						Units:        10,
						Nanos:        350_000_000,
					},
					AmountInINR: &moneyPb.Money{
						CurrencyCode: money.RupeeCurrencyCode,
						Units:        2900,
					},
					Gst: &moneyPb.Money{
						CurrencyCode: money.RupeeCurrencyCode,
						Units:        45,
					},
					Id:         aggTxn3,
					ExternalId: externalId3,
				}},
			},
		},
		{
			name: "unsuccessful file creation and error during reconciliation for pool account",
			args: args{
				ctx: context.Background(),
				req: &sampleReq,
			},
			setupMockCalls: func(arg args) {
				for index, actorId := range []string{actorId1, actorId2, actorId3} {
					solId := fmt.Sprintf("solid-%s", strconv.Itoa(index+1))
					mockSavingsAccessor.EXPECT().GetSavingsAccount(gomock.Any(), actorId).Return(&savingsClientPb.Account{
						AccountNo: fmt.Sprintf("account_number_%s", strconv.Itoa(index+1)),
					}, nil)
					mockBankCustomerServiceClient.EXPECT().GetBankCustomer(gomock.Any(), pkgMocks.NewProtoMatcher(&bankCustomerPb.GetBankCustomerRequest{
						Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
						Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{ActorId: actorId},
					})).Return(&bankCustomerPb.GetBankCustomerResponse{
						Status: rpc.StatusOk(),
						BankCustomer: &bankCustomerPb.BankCustomer{VendorMetadata: &bankCustomerPb.VendorMetadata{Vendor: &bankCustomerPb.VendorMetadata_FederalMetadata{FederalMetadata: &bankCustomerPb.FederalMetadata{
							SolId: solId,
						}}}},
					}, nil)
					mockIftClient.EXPECT().GetGstReportingDataForInwardTxn(gomock.Any(), gomock.Any()).Return(&iftPb.GetGstReportingDataForInwardTxnResponse{
						Status:        rpc.StatusOk(),
						ReportingData: sampleGstReportingData,
						Gst: &moneyPb.Money{
							CurrencyCode: money.RupeeCurrencyCode,
							Units:        45,
							Nanos:        0,
						},
					}, nil)
					mockCreditFreezeValidator.EXPECT().Validate(gomock.Any(), actorId).Return(nil)
				}
				// since value added by thread due race condition, soring order is not guarantee
				// so using DoReturn method
				mockReconProcess.EXPECT().ReconTransactions(gomock.Any()).DoAndReturn(func(transactions []*inward_transaction_utils.InwardTransaction) (*recon_processor.ReconProcessResp, error) {
					sort.Slice(transactions, func(i, j int) bool {
						return strings.Compare(transactions[i].ActorId, transactions[j].ActorId) < 0
					})

					if len(transactions) != len(sampleTransactions) {
						return nil, fmt.Errorf("error while mocking")
					}

					for i := 0; i < len(transactions); i++ {
						if transactions[i].Id != sampleTransactions[i].Id {
							return nil, fmt.Errorf("error while mocking in id")
						}
						if transactions[i].ActorId != sampleTransactions[i].ActorId {
							return nil, fmt.Errorf("error while mocking in actor id")
						}
					}

					return nil, epifierrors.ErrRecordNotFound
				})
			},
			wantErr: true,
		},
		{
			name: "successful separate ttum file creation and gst reporting file",
			args: args{
				ctx:                           context.Background(),
				req:                           &sampleReq,
				enableSeparateTTUMFileChanges: true,
			},
			setupMockCalls: func(arg args) {
				for index, actorId := range []string{actorId1, actorId2, actorId3} {
					solId := fmt.Sprintf("solid-%s", strconv.Itoa(index+1))
					mockSavingsAccessor.EXPECT().GetSavingsAccount(gomock.Any(), actorId).Return(&savingsClientPb.Account{
						AccountNo: fmt.Sprintf("account_number_%s", strconv.Itoa(index+1)),
					}, nil)
					mockBankCustomerServiceClient.EXPECT().GetBankCustomer(gomock.Any(), pkgMocks.NewProtoMatcher(&bankCustomerPb.GetBankCustomerRequest{
						Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
						Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{ActorId: actorId},
					})).Return(&bankCustomerPb.GetBankCustomerResponse{
						Status: rpc.StatusOk(),
						BankCustomer: &bankCustomerPb.BankCustomer{VendorMetadata: &bankCustomerPb.VendorMetadata{Vendor: &bankCustomerPb.VendorMetadata_FederalMetadata{FederalMetadata: &bankCustomerPb.FederalMetadata{
							SolId: solId,
						}}}},
					}, nil)
					mockIftClient.EXPECT().GetGstReportingDataForInwardTxn(gomock.Any(), gomock.Any()).Return(&iftPb.GetGstReportingDataForInwardTxnResponse{
						Status:        rpc.StatusOk(),
						ReportingData: sampleGstReportingData,
						Gst: &moneyPb.Money{
							CurrencyCode: money.RupeeCurrencyCode,
							Units:        45,
							Nanos:        0,
						},
					}, nil)
					mockCreditFreezeValidator.EXPECT().Validate(gomock.Any(), actorId).Return(nil)
				}
				// since value added by thread due race condition, soring order is not guarantee
				// so using DoReturn method
				mockReconProcess.EXPECT().ReconTransactions(gomock.Any()).DoAndReturn(func(transactions []*inward_transaction_utils.InwardTransaction) (*recon_processor.ReconProcessResp, error) {
					sort.Slice(transactions, func(i, j int) bool {
						return strings.Compare(transactions[i].ActorId, transactions[j].ActorId) < 0
					})

					if len(transactions) != len(sampleTransactions) {
						return nil, fmt.Errorf("error while mocking")
					}

					for i := 0; i < len(transactions); i++ {
						if transactions[i].Id != sampleTransactions[i].Id {
							return nil, fmt.Errorf("error while mocking in id")
						}
						if transactions[i].ActorId != sampleTransactions[i].ActorId {
							return nil, fmt.Errorf("error while mocking in actor id")
						}
					}

					return &recon_processor.ReconProcessResp{
						Transactions: reconSample1,
						TotalAmountDebitPoolAccount: &moneyPb.Money{
							CurrencyCode: money.RupeeCurrencyCode,
							Units:        15700,
						},
					}, nil
				})

				mockIftFgClient.EXPECT().GenerateInwardFundTransferFile(gomock.Any(), pkgMocks.NewProtoMatcher(&iftFileGenPb.GenerateInwardFundTransferFileRequest{
					Vendor:          commonvgpb.Vendor_FEDERAL_BANK,
					ClientRequestId: fmt.Sprintf("%s_GST_TTUM", sampleFileGenAttempt.GetClientRequestId()),
					BatchId:         sampleFileGenAttempt.GetClientRequestId(),
					FileType:        iftFileGenPb.FileType_FILE_TYPE_INWARD_GST_TTUM,
				})).Return(&iftFileGenPb.GenerateInwardFundTransferFileResponse{
					Status:                rpc.StatusOk(),
					FileGenerationAttempt: sampleFileGenAttempt2,
				}, nil)

				mockIftFgClient.EXPECT().UploadFile(gomock.Any(), pkgMocks.NewProtoMatcher(&iftFileGenPb.UploadFileRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					FileGenerationAttempt: &iftFileGenPb.FileGenerationAttempt{
						Id:       sampleFileGenAttempt.GetId(),
						FileType: iftFileGenPb.FileType_FILE_TYPE_INWARD_FUND_TRANSFER,
					},
					FileContents: userFundTransferFile1Data,
					TotalEntries: 4,
					EntityIds:    []string{"AGGREMTXN_external-id-1", "AGGREMTXN_external-id-2", "AGGREMTXN_external-id-3"},
				})).Return(&iftFileGenPb.UploadFileResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mockIftFgClient.EXPECT().UploadFile(gomock.Any(), pkgMocks.NewProtoMatcher(&iftFileGenPb.UploadFileRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					FileGenerationAttempt: &iftFileGenPb.FileGenerationAttempt{
						Id:       sampleFileGenAttempt2.GetId(),
						FileType: iftFileGenPb.FileType_FILE_TYPE_INWARD_GST_TTUM,
					},
					FileContents: taxFundTransferFile1Data,
					TotalEntries: 7,
					EntityIds:    []string{"AGGREMTXN_external-id-1_GST", "AGGREMTXN_external-id-2_GST", "AGGREMTXN_external-id-3_GST"},
				})).Return(&iftFileGenPb.UploadFileResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mockIftFgClient.EXPECT().UploadFile(gomock.Any(), pkgMocks.NewProtoMatcher(&iftFileGenPb.UploadFileRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					FileGenerationAttempt: &iftFileGenPb.FileGenerationAttempt{
						ClientRequestId: fmt.Sprintf("%s_GST", arg.req.FileGenAttempt.GetClientRequestId()),
						FileType:        iftFileGenPb.FileType_FILE_TYPE_GST_REPORTING_INWARD,
					},
					FileContents: gstReportingFile1Data,
					TotalEntries: 6,
				})).Return(&iftFileGenPb.UploadFileResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			wantErr: false,
			want: &inward_transactions_file_generator.GenerateAndUploadFileResponse{
				EligibleTransactions: reconSample1,
			},
		},
		{
			name: "handling when one account is marked credit freeze for ttum file generation",
			args: args{
				ctx: context.Background(),
				req: &sampleReq2,
			},
			setupMockCalls: func(arg args) {
				for index, actorId := range []string{actorId1, actorId2, actorId4} {
					solId := fmt.Sprintf("solid-%s", strconv.Itoa(index+1))
					mockCreditFreezeValidator.EXPECT().Validate(gomock.Any(), actorId).Return(nil)
					var restrictions []savingsClientPb.Restriction
					if actorId == actorId4 {
						restrictions = []savingsClientPb.Restriction{savingsClientPb.Restriction_RESTRICTION_CREDIT_FREEZE}
					}
					mockSavingsAccessor.EXPECT().GetSavingsAccount(gomock.Any(), actorId).Return(&savingsClientPb.Account{
						AccountNo: fmt.Sprintf("account_number_%s", strconv.Itoa(index+1)),
						Constraints: &savingsClientPb.AccountConstraints{
							Restrictions: restrictions,
						},
					}, nil)
					mockBankCustomerServiceClient.EXPECT().GetBankCustomer(gomock.Any(), pkgMocks.NewProtoMatcher(&bankCustomerPb.GetBankCustomerRequest{
						Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
						Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{ActorId: actorId},
					})).Return(&bankCustomerPb.GetBankCustomerResponse{
						Status: rpc.StatusOk(),
						BankCustomer: &bankCustomerPb.BankCustomer{VendorMetadata: &bankCustomerPb.VendorMetadata{Vendor: &bankCustomerPb.VendorMetadata_FederalMetadata{FederalMetadata: &bankCustomerPb.FederalMetadata{
							SolId: solId,
						}}}},
					}, nil)
					mockIftClient.EXPECT().GetGstReportingDataForInwardTxn(gomock.Any(), gomock.Any()).Return(&iftPb.GetGstReportingDataForInwardTxnResponse{
						Status:        rpc.StatusOk(),
						ReportingData: sampleGstReportingData,
						Gst: &moneyPb.Money{
							CurrencyCode: money.RupeeCurrencyCode,
							Units:        45,
							Nanos:        0,
						},
					}, nil)
				}
				// since value added by thread due race condition, soring order is not guarantee
				// so using DoReturn method
				mockReconProcess.EXPECT().ReconTransactions(gomock.Any()).DoAndReturn(func(transactions []*inward_transaction_utils.InwardTransaction) (*recon_processor.ReconProcessResp, error) {
					sort.Slice(transactions, func(i, j int) bool {
						return strings.Compare(transactions[i].ActorId, transactions[j].ActorId) < 0
					})

					if len(transactions) != len(sampleTransactionsCreditFreeze) {
						return nil, fmt.Errorf("error while mocking")
					}

					for i := 0; i < len(transactions); i++ {
						if transactions[i].Id != sampleTransactionsCreditFreeze[i].Id {
							return nil, fmt.Errorf("error while mocking in id")
						}
						if transactions[i].ActorId != sampleTransactionsCreditFreeze[i].ActorId {
							return nil, fmt.Errorf("error while mocking in actor id")
						}
					}

					return &recon_processor.ReconProcessResp{
						Transactions: reconSample2,
						TotalAmountDebitPoolAccount: &moneyPb.Money{
							CurrencyCode: money.RupeeCurrencyCode,
							Units:        12800,
						},
					}, nil
				})

				mockIftFgClient.EXPECT().UploadFile(gomock.Any(), pkgMocks.NewProtoMatcher(&iftFileGenPb.UploadFileRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					FileGenerationAttempt: &iftFileGenPb.FileGenerationAttempt{
						Id:       sampleFileGenAttempt.GetId(),
						FileType: iftFileGenPb.FileType_FILE_TYPE_INWARD_FUND_TRANSFER,
					},
					FileContents: fundTransferFile2Data,
					TotalEntries: 9,
					EntityIds:    []string{"AGGREMTXN_external-id-1", "AGGREMTXN_external-id-2"},
				})).Return(&iftFileGenPb.UploadFileResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockIftFgClient.EXPECT().UploadFile(gomock.Any(), pkgMocks.NewProtoMatcher(&iftFileGenPb.UploadFileRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					FileGenerationAttempt: &iftFileGenPb.FileGenerationAttempt{
						ClientRequestId: fmt.Sprintf("%s_GST", arg.req.FileGenAttempt.GetClientRequestId()),
						FileType:        iftFileGenPb.FileType_FILE_TYPE_GST_REPORTING_INWARD,
					},
					FileContents: gstReportingFile2Data,
					TotalEntries: 4,
				})).Return(&iftFileGenPb.UploadFileResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			wantErr: false,
			want: &inward_transactions_file_generator.GenerateAndUploadFileResponse{
				EligibleTransactions: reconSample2,
				CreditFreezeTransactions: []*inward_transaction_utils.InwardTransaction{{
					ActorId: actorId4,
					AmountInUSD: &moneyPb.Money{
						CurrencyCode: money.USDCurrencyCode,
						Units:        10,
						Nanos:        350_000_000,
					},
					AmountInINR: &moneyPb.Money{
						CurrencyCode: money.RupeeCurrencyCode,
						Units:        2900,
					},
					Gst: &moneyPb.Money{
						CurrencyCode: money.RupeeCurrencyCode,
						Units:        45,
					},
					Id:         aggTxn4,
					ExternalId: externalId4,
				}},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err = conf.Flags().SetEnableSeparateTTUMFileChanges(tt.args.enableSeparateTTUMFileChanges, true, nil)
			if err != nil {
				t.Fatalf("failed to set dynamic config for enable ttum file changes")
			}
			tt.setupMockCalls(tt.args)
			s := &GenerateFileForTransaction{
				iftClient:                 mockIftClient,
				reconProcessor:            mockReconProcess,
				iftFileGenClient:          mockIftFgClient,
				genConf:                   conf,
				bankCustomerClient:        mockBankCustomerServiceClient,
				userCreditFreezeValidator: mockCreditFreezeValidator,
				savingsAccessor:           mockSavingsAccessor,
			}
			got, genErr := s.GenerateAndUploadFile(tt.args.ctx, tt.args.req)
			if (genErr != nil) != tt.wantErr {
				t.Errorf("GenerateAndUploadFile() error = %v, wantErr %v", genErr, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
			}
			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("GenerateAndUploadFile() diff=%v", diff)
			}
		})
	}
}
